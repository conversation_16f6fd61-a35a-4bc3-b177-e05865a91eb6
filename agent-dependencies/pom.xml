<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.4.1</version>
        <relativePath/>
    </parent>

    <groupId>com.fytec</groupId>
    <artifactId>agent-dependencies</artifactId>
    <name>${project.artifactId}</name>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>

    <properties>
        <java.version>21</java.version>
        <resource.delimiter>@</resource.delimiter>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <!--Spring 家族-->
        <spring-boot-dependencies.version>3.4.1</spring-boot-dependencies.version>
        <spring-cloud.version>2024.0.0</spring-cloud.version>
        <spring-cloud-alibaba.version>2023.0.3.2</spring-cloud-alibaba.version>
        <mybatis-plus-boot-starter.version>3.5.9</mybatis-plus-boot-starter.version>
        <druid-spring-boot-starter.version>1.2.14</druid-spring-boot-starter.version>
        <hutool.version>5.8.24</hutool.version>
        <springdoc.version>2.7.0</springdoc.version>
        <sa-token.version>1.39.0</sa-token.version>
        <commons-collections4.version>4.4</commons-collections4.version>
        <commons-compress.version>1.27.1</commons-compress.version>
        <commons-io.version>2.20.0</commons-io.version>
        <fastjson.version>2.0.53</fastjson.version>
        <guava.version>33.3.1-jre</guava.version>
        <milvus.version>2.5.4</milvus.version>
        <unirest.version>1.4.9</unirest.version>
        <volcengine-sdk-ark-runtime.version>LATEST</volcengine-sdk-ark-runtime.version>
        <volcengine-sdk.version>1.0.207</volcengine-sdk.version>
        <dashscope-sdk.version>2.18.2</dashscope-sdk.version>
        <openai-sdk.version>2.0.0</openai-sdk.version>
        <junit.version>5.11.4</junit.version>
        <okhttp3-version>4.12.0</okhttp3-version>
        <pinyin4j-version>2.5.1</pinyin4j-version>
<!--        <pdfbox.version>3.0.1</pdfbox.version>-->
        <pdfbox.version>2.0.27</pdfbox.version>
        <poi-ooxml.version>5.2.3</poi-ooxml.version>
        <poi-scratchpad.version>5.2.3</poi-scratchpad.version>
        <jsoup.version>1.18.3</jsoup.version>
        <minio.version>8.2.1</minio.version>
        <thumbnailator.version>0.4.20</thumbnailator.version>
        <fastexcel.version>1.1.0</fastexcel.version>
        <jodconverter.version>4.4.8</jodconverter.version>
        <httpclient.version>4.5.13</httpclient.version>
        <bytedance.stream.tob.version>1.1.5</bytedance.stream.tob.version>
        <rocketmq.java.version>5.0.7</rocketmq.java.version>
        <rocketmq.spring.version>2.3.3</rocketmq.spring.version>
        <ofdrw.version>2.3.7</ofdrw.version>
        <tika.version>3.2.1</tika.version>
        <spring-boot-starter-data-mongodb.version>3.5.4</spring-boot-starter-data-mongodb.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency><!-- spring cloud 依赖 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-bom</artifactId>
                <version>1.0.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.modelcontextprotocol.sdk</groupId>
                <artifactId>mcp-bom</artifactId>
                <version>0.10.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-bom</artifactId>
                <version>${mybatis-plus-boot-starter.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- spring cloud alibaba 依赖 -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.junit</groupId>
                <artifactId>junit-bom</artifactId>
                <version>${junit.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-webflux</artifactId>
                <version>${spring-boot-dependencies.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot3-starter</artifactId>
                <version>${sa-token.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-redis</artifactId>
                <version>${sa-token.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-oauth2</artifactId>
                <version>${sa-token.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.apache.commons/commons-collections4 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons-collections4.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/com.alibaba/fastjson -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.volcengine</groupId>
                <artifactId>volcengine-java-sdk-ark-runtime</artifactId>
                <version>${volcengine-sdk-ark-runtime.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.volcengine</groupId>
                <artifactId>volc-sdk-java</artifactId>
                <version>${volcengine-sdk.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>junit</groupId>
                        <artifactId>junit</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>dashscope-sdk-java</artifactId>
                <version>${dashscope-sdk.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>io.milvus</groupId>
                <artifactId>milvus-sdk-java</artifactId>
                <version>${milvus.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/com.mashape.unirest/unirest-java -->
            <dependency>
                <groupId>com.mashape.unirest</groupId>
                <artifactId>unirest-java</artifactId>
                <version>${unirest.version}</version>
            </dependency>
            <dependency>
                <groupId>com.openai</groupId>
                <artifactId>openai-java</artifactId>
                <version>${openai-sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp3-version}</version>
            </dependency>
            <dependency>
                <groupId>com.belerweb</groupId>
                <artifactId>pinyin4j</artifactId>
                <version>${pinyin4j-version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox</artifactId>
                <version>${pdfbox.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi-ooxml.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-scratchpad</artifactId>
                <version>${poi-scratchpad.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup.version}</version>
            </dependency>
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>
            <dependency>
                <groupId>net.coobird</groupId>
                <artifactId>thumbnailator</artifactId>
                <version>${thumbnailator.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.idev.excel</groupId>
                <artifactId>fastexcel</artifactId>
                <version>${fastexcel.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jodconverter</groupId>
                <artifactId>jodconverter-spring-boot-starter</artifactId>
                <version>${jodconverter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jodconverter</groupId>
                <artifactId>jodconverter-local</artifactId>
                <version>${jodconverter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jodconverter</groupId>
                <artifactId>jodconverter-remote</artifactId>
                <version>${jodconverter.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.bytedance.speechengine.linux</groupId>
                <artifactId>speechengine_asr_streaming_tob</artifactId>
                <version>${bytedance.stream.tob.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-client-java</artifactId>
                <version>${rocketmq.java.version}</version> <!-- 版本需与Broker一致 -->
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq.spring.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.ofdrw</groupId>
                <artifactId>ofdrw-converter</artifactId>
                <version>${ofdrw.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>${commons-compress.version}</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/commons-io/commons-io -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            
            <!-- Apache Tika Dependencies -->
            <dependency>
                <groupId>org.apache.tika</groupId>
                <artifactId>tika-core</artifactId>
                <version>${tika.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tika</groupId>
                <artifactId>tika-parsers-standard-package</artifactId>
                <version>${tika.version}</version>
            </dependency>
            

            
            <!-- MongoDB Dependencies -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-mongodb</artifactId>
                <version>${spring-boot-starter-data-mongodb.version}</version>
            </dependency>
        </dependencies>

    </dependencyManagement>
</project>