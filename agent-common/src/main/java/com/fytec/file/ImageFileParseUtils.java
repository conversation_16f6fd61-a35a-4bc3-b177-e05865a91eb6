package com.fytec.file;

import cn.hutool.core.util.StrUtil;
import com.fytec.dto.parse.FileParseDTO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.net.URI;
import java.net.URL;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class ImageFileParseUtils {
    public static String parseToBase64(String fileUrl) {
        try {
            // 创建URL对象
            URI uri = new URI(fileUrl);
            URL url = uri.toURL();

            double scale = 1;
            BufferedImage image = Thumbnails.of(url).scale(1).asBufferedImage();
            // 获取图片的宽度和高度
            if (image.getWidth() > 2048 || image.getHeight() > 2048) {
                scale = 0.6;
            }

            // 读取图片数据到字节数组
            BufferedImage resizedImage = Thumbnails.of(url)
                    .scale(scale)
                    .outputQuality(0.8)
                    .asBufferedImage();

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ImageIO.write(resizedImage, "png", outputStream);

            // 将字节数组转换为Base64编码
            byte[] imageBytes = outputStream.toByteArray();
            String base64Image = Base64.getEncoder().encodeToString(imageBytes);

            outputStream.close();
//            return base64Image;
            return StrUtil.format("data:image/{};base64,{}", "png", base64Image);
        } catch (Exception e) {
            log.error("Error parsing image file: ", e);
        }
        return null;
    }


    @SneakyThrows
    public static Map<Integer, String> parseUrlPerPage(String fileUrl) {
        Map<Integer, String> map = new HashMap<>();
        map.putIfAbsent(1, parseToBase64(fileUrl));
        return map;
    }


    @SneakyThrows
    public static Map<Integer, String> parseUrlPerPage(FileParseDTO dto) {
        Map<Integer, String> map = new HashMap<>();
        if (StrUtil.equals("vision", dto.getImageProcessType())) {
            map.putIfAbsent(1, parseToBase64(dto.getFileUrl()));
        }
        return map;
    }


    public static void main(String[] args) {
        String fileUrl = "http://172.16.109.241/files/group1/10/31eb36c7f14c4807a1becfbabbb5f931.png";
        System.out.println(parseToBase64(fileUrl));
    }
}
