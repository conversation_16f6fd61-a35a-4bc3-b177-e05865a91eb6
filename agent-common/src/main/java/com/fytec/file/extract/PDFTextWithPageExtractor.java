package com.fytec.file.extract;

import lombok.Data;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.pdfbox.text.TextPosition;

import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class PDFTextWithPageExtractor extends PDFTextStripper {

    // 用于存储提取的文字及其页码
    public final List<TextWithPage> textWithPages = new ArrayList<>();

    public PDFTextWithPageExtractor() throws IOException {
        super();
    }

    @Override
    public void processPage(PDPage page) throws IOException {
        // 处理页面内容
        super.processPage(page);
    }

    @Override
    protected void writeString(String text, List<TextPosition> textPositions) throws IOException {
        // 获取当前页码
        int pageNumber = getCurrentPageNo();

        // 将文字和页码存储到列表中
        for (TextPosition textPosition : textPositions) {
            String character = textPosition.getUnicode();
            float x = textPosition.getXDirAdj(); // 文字的 X 坐标
            float y = textPosition.getYDirAdj(); // 文字的 Y 坐标
            float width =textPosition.getWidthDirAdj();
            textWithPages.add(new TextWithPage(character, pageNumber, x, y, width));
        }
        sortTextPositions(textWithPages);

        // 调用父类方法继续处理
        super.writeString(text, textPositions);
    }

    /**
     * 对 TextPosition 进行排序
     */
    private void sortTextPositions(List<TextWithPage> textWithPages) {
        // 自定义比较器：先按 Y 坐标从大到小排序，再按 X 坐标从小到大排序
        textWithPages.sort((tp1, tp2) -> {
            if (tp1.getPageNumber() != tp2.getPageNumber()) {
                return Integer.compare(tp1.getPageNumber(), tp2.getPageNumber());
            }
            // 比较 Y 坐标（从上到下）
            int yComparison = Float.compare(tp1.getY(), tp2.getY());
            if (yComparison != 0) {
                return yComparison;
            }
            // 比较 X 坐标（从左到右）
            return Float.compare(tp1.getX(), tp2.getX());
        });
    }


    public Map<Integer, List<List<TextWithPage>>> groupTextWithPagesByLine(List<TextWithPage> textWithPages) {
        Map<Integer, List<List<TextWithPage>>> lines = new LinkedHashMap<>();
        List<TextWithPage> currentLine = new ArrayList<>();
        float lastY = -1;

        for (TextWithPage textWithPage : textWithPages) {
            if (textWithPage.getY() != lastY) {
                // 新的一行
                currentLine = new ArrayList<>();
                lines.computeIfAbsent(textWithPage.getPageNumber(), k -> new ArrayList<>()).add(currentLine);
                lastY = textWithPage.getY();
            }
            currentLine.add(textWithPage);
        }

        return lines;
    }

    public String buildLineWithSpaces(List<TextWithPage> line) {
        StringBuilder lineText = new StringBuilder();
        float lastX = -1;

        for (TextWithPage textWithPage : line) {
            // 如果当前文字的 X 坐标与上一个文字的 X 坐标间隔较大，则插入空格
            if (lastX != -1 && (textWithPage.x - lastX) > textWithPage.getWidth() * 0.3) {
                lineText.append(" ");
            }
            lineText.append(textWithPage.getText());
            lastX = textWithPage.x + textWithPage.getWidth(); // 更新上一个文字的结束位置
        }

        return lineText.toString();
    }

    @Data
    public static class TextWithPage {
        private String text;
        private float x;
        private float y;
        private int pageNumber;
        private float width; // 文字的宽度

        public TextWithPage(String text, int pageNumber, float x, float y, float width) {
            this.text = text;
            this.x = x;
            this.y = y;
            this.pageNumber = pageNumber;
            this.width =width;
        }
    }
}
