package com.fytec.file;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URI;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class TxtFileParseUtils {

    @SneakyThrows
    public static String parseUrl(String fileUrl) {
        URI uri = new URI(fileUrl);
        URL url = uri.toURL();
        try (InputStream inputStream = url.openStream()) {
            return parse(inputStream);
        }
    }

    @SneakyThrows
    public static Map<Integer, String> parseUrlPerPage(String fileUrl) {
        Map<Integer, String> map = new HashMap<>();
        map.putIfAbsent(1, parseUrl(fileUrl));
        return map;
    }

    @SneakyThrows
    public static String parseByte(byte[] fileBytes) {
        // 将字节数组转换为 InputStream
        InputStream inputStream = new ByteArrayInputStream(fileBytes);
        return parse(inputStream);
    }

    @SneakyThrows
    private static String parse(InputStream inputStream) {
        StringBuilder sb = new StringBuilder();
        BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
        String line;
        while ((line = reader.readLine()) != null) {
            sb.append(line);
            sb.append("\n");
        }
        reader.close();
        log.info("txt文件解析结果：{}", sb);
        return sb.toString();
    }


    public static void main(String[] args) {
        //本地文件转byte[]
        byte[] bytes = FileParseUtils.convertFileToByteArray("/Users/<USER>/Desktop/关于公开遴选苏州市化工新材料产业链高质量发展路径研究报告服务机构的通知.txt");
        parseByte(bytes);
    }
}
