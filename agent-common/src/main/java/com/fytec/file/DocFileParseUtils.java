package com.fytec.file;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.model.PicturesTable;
import org.apache.poi.hwpf.usermodel.*;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.URL;
import java.util.Map;


@Slf4j
public class DocFileParseUtils {

    @SneakyThrows
    public static String parseUrl(String fileUrl) {
        URI uri = new URI(fileUrl);
        URL url = uri.toURL();
        try (InputStream inputStream = url.openStream()) {
            return parse(inputStream);
        } catch (Exception e) {
            log.warn("解析.doc文件失败，尝试解析为.docx文件");
            try (InputStream inputStream = url.openStream()) {
                return DocxFileParseUtils.parse(inputStream);
            }
        }
    }


    @SneakyThrows
    public static Map<Integer, String> parseUrlPerPage(String fileUrl, String jodUrl) {
        return DocxFileParseUtils.parseUrlPerPage(fileUrl, jodUrl);
    }

    @SneakyThrows
    public static String parseByte(byte[] fileBytes) {
        InputStream inputStream = new ByteArrayInputStream(fileBytes);
        try {
            return parse(inputStream);
        } catch (Exception e) {
            log.warn("解析.doc文件失败，尝试解析为.docx文件");
            return DocxFileParseUtils.parse(inputStream);
        }
    }


    public static String parse(InputStream inputStream) throws IOException {
        StringBuilder result = new StringBuilder();
        HWPFDocument document = new HWPFDocument(inputStream);
        Range range = document.getRange();
        PicturesTable picturesTable = document.getPicturesTable();
        for (int i = 0; i < range.numParagraphs(); i++) {
            Paragraph paragraph = range.getParagraph(i);
            // 检查段落是否属于表格
            if (paragraph.isInTable()) {
                Table table = range.getTable(paragraph);
                for (int rowIndex = 0; rowIndex < table.numRows(); rowIndex++) {
                    TableRow row = table.getRow(rowIndex);
                    for (int cellIndex = 0; cellIndex < row.numCells(); cellIndex++) {
                        TableCell cell = row.getCell(cellIndex);
                        String cellText = cell.text().trim();
                        result.append(cellText).append("\t");

                        for (int j = 0; j < cell.numCharacterRuns(); j++) {
                            CharacterRun run = cell.getCharacterRun(j);
                            if (picturesTable.hasPicture(run)) {
                                Picture picture = picturesTable.extractPicture(run, false);
                                result.append(picture.getDescription()).append("\n");
                                //TODO 图片处理逻辑
                            }
                        }

                    }
                    result.append("\n");
                }
                i += table.numParagraphs() - 1; // 跳过表格中的其他段落
            } else {
                // 处理普通段落
                String text = paragraph.text().trim();
                result.append(text).append("\n");

                for (int j = 0; j < paragraph.numCharacterRuns(); j++) {
                    CharacterRun run = paragraph.getCharacterRun(j);
                    if (picturesTable.hasPicture(run)) {
                        Picture picture = picturesTable.extractPicture(run, false);
                        result.append(picture.getDescription()).append("\n");
                        //TODO 图片处理逻辑
                    }
                }
            }
        }
        return result.toString();
    }


    public static void main(String[] args) {
        byte[] bytes = FileParseUtils.convertFileToByteArray("/Users/<USER>/Downloads/关于公布2024年苏州工业园区首台（套）重大装备认定名单的通知.doc");
        System.out.println(parseByte(bytes));
    }
}
