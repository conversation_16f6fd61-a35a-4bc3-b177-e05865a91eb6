package com.fytec.file;

import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.fytec.dto.parse.FileParseDTO;
import com.fytec.file.extract.PDFTextWithPageExtractor;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.cos.COSName;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.apache.pdfbox.text.PDFTextStripper;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URI;
import java.net.URL;
import java.util.*;

@Slf4j
public class PdfFileParseUtils {
    private static final int MAX_SIZE_BYTES = 8 * 1024 * 1024;

    @SneakyThrows
    public static String parseUrl(String fileUrl) {
        URI uri = new URI(fileUrl);
        URL url = uri.toURL();
        try (InputStream inputStream = url.openStream()) {
            return parse(inputStream);
        }
    }

    @SneakyThrows
    public static Map<Integer, String> parseUrlPerPage(String fileUrl) {
        URI uri = new URI(fileUrl);
        URL url = uri.toURL();
        try (InputStream inputStream = url.openStream()) {
            return parsePerPage(inputStream);
        }
    }


    @SneakyThrows
    public static String parseByte(byte[] fileBytes) {
        InputStream inputStream = new ByteArrayInputStream(fileBytes);
        return parse(inputStream);
    }

    @SneakyThrows
    public static Map<Integer, String> parseBytePerPage(byte[] fileBytes) {
        InputStream inputStream = new ByteArrayInputStream(fileBytes);
        return parsePerPage(inputStream);
    }


    @SneakyThrows
    private static List<String> parseToBase64(String fileUrl) throws IOException {
        URI uri = new URI(fileUrl);
        URL url = uri.toURL();

        List<String> chunks = new ArrayList<>();
        try (InputStream inputStream = url.openStream();
             ByteArrayOutputStream buffer = new ByteArrayOutputStream()) {

            byte[] readBuffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = inputStream.read(readBuffer)) != -1) {
                buffer.write(readBuffer, 0, bytesRead);
                if (buffer.size() >= MAX_SIZE_BYTES) {
                    splitIntoChunks(buffer, chunks);
                }
            }

            if (buffer.size() > 0) {
                splitIntoChunks(buffer, chunks);
            }
            return chunks;
        }
    }

    @SneakyThrows
    private static void splitIntoChunks(ByteArrayOutputStream buffer, List<String> chunks) {
        String encoded = Base64.getEncoder().encodeToString(buffer.toByteArray());
        chunks.add(encoded);
        buffer.reset();
    }

    private static String parse(InputStream inputStream) throws IOException {
        String text;

        try (PDDocument document = PDDocument.load(inputStream.readAllBytes())) {
            // 创建 PDFTextStripper 对象用于提取文本
            PDFTextStripper pdfTextStripper = new PDFTextStripper();
            // 提取 PDF 文档中的文本内容
            text = pdfTextStripper.getText(document);
        }
        return text;
    }

    private static Map<Integer, String> parsePerPage(InputStream inputStream) throws IOException {
        Map<Integer, String> contentPerPage = new LinkedHashMap<>();
        try (PDDocument document = PDDocument.load(inputStream.readAllBytes())) {
            // 创建 PDFTextStripper 对象用于提取文本
            PDFTextWithPageExtractor pdfTextStripper = new PDFTextWithPageExtractor();
            // 提取 PDF 文档中的文本内容
            pdfTextStripper.getText(document);

            Map<Integer, List<List<PDFTextWithPageExtractor.TextWithPage>>> linesMap = pdfTextStripper.groupTextWithPagesByLine(pdfTextStripper.textWithPages);
            for (int pageNumer : linesMap.keySet()) {
                List<List<PDFTextWithPageExtractor.TextWithPage>> lines = linesMap.get(pageNumer);
                List<String> contentLines = new ArrayList<>();
                for (List<PDFTextWithPageExtractor.TextWithPage> line : lines) {
                    String lineText = pdfTextStripper.buildLineWithSpaces(line);
                    contentLines.add(lineText);
                }
                contentPerPage.putIfAbsent(pageNumer, StrUtil.join("\n", contentLines));
            }
        }
        return contentPerPage;
    }

    private static List<String> extractImagesFromPage(PDPage page, int pageIndex) throws IOException {
        List<String> imagePaths = new ArrayList<>();
        int imageCounter = 0;
        for (COSName xObjectName : page.getResources().getXObjectNames()) {
            if (page.getResources().isImageXObject(xObjectName)) {
                PDImageXObject imageXObject = (PDImageXObject) page.getResources().getXObject(xObjectName);
                BufferedImage bufferedImage = imageXObject.getImage();
                String imagePath = String.format("page%d_image%d.png", pageIndex + 1, imageCounter + 1);
                File output = new File(imagePath);
                ImageIO.write(bufferedImage, "png", output);
                imagePaths.add(output.getAbsolutePath());
                imageCounter++;
            }
        }
        return imagePaths;
    }

    @SneakyThrows
    public static Map<Integer, String> parsePerPage(FileParseDTO dto) {
        if (StrUtil.equals(dto.getMode(), "normal")) {
            return parseUrlPerPage(dto.getFileUrl());
        }

        Map<Integer, String> contentPerPage = new LinkedHashMap<>();

        Map<String, Object> body = new HashMap<>();
        body.put("bucket_name", dto.getBucketName());
        body.put("ak", dto.getAccessKey());
        body.put("sk", dto.getSecretKey());
        body.put("endpoint_url", dto.getEndpointUrl());
        body.put("show_url", dto.getShowUrl());
        body.put("filename", dto.getFileName());
        System.out.println("dto = " + dto);

        HttpResponse<String> response = Unirest.post(dto.getSubmitUrl())
                .header("X-API-Key", dto.getApiKey())
                .header("Content-Type", "application/json")
                .body(JSON.toJSONString(body))
                .asString();
        if (response.getStatus() != 200) {
            throw new ValidateException("提交解析任务失败");
        }
        JSONObject task = JSON.parseObject(response.getBody());
        String taskId = task.getString("task_id");
        System.out.println(taskId);

        String resultUrl = StrUtil.format(dto.getTaskUrl(), taskId);
        System.out.println(resultUrl);

        int count = 0;
        while (true) {
            if (count > 180) {
                throw new ValidateException("解析任务超时");
            }

            response = Unirest.get(resultUrl)
                    .header("X-API-Key", dto.getApiKey())
                    .header("Content-Type", "application/json")
                    .asString();
            if (response.getStatus() != 200) {
                throw new ValidateException("查询解析结果失败");
            }

            String responseBody = response.getBody();
            if (!StrUtil.equals("null", responseBody)) {
                System.out.println(responseBody);
                JSONObject result = JSON.parseObject(responseBody);
                String status = result.getString("status");
                if (StrUtil.equals(status, "completed")) {
                    JSONArray jsonArray = JSON.parseArray(result.getString("result"));
                    int page = 1;
                    StringBuilder sb = new StringBuilder();
                    for (Object o : jsonArray) {
                        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(o));
                        String type = jsonObject.getString("type");
                        StringBuilder content = new StringBuilder();
                        if (type.equals("image")) {
                            content = new StringBuilder();
                            content.append("<img src=\"").append(jsonObject.getString("img_path")).append("\">");
                        } else if (type.equals("table")) {
                            JSONArray tableCaptions = jsonObject.getJSONArray("table_caption");
                            String tableBody = jsonObject.getString("table_body");
                            JSONArray tableFootnotes = jsonObject.getJSONArray("table_footnote");
                            if (!tableCaptions.isEmpty()) {
                                for (Object tableCaption : tableCaptions) {
                                    content.append(JSON.toJSONString(tableCaption));
                                }
                            }
                            content.append(tableBody);
                            if (!tableFootnotes.isEmpty()) {
                                for (Object tableFootnote : tableFootnotes) {
                                    content.append(JSON.toJSONString(tableFootnote));
                                }
                            }
                        } else {
                            content = new StringBuilder(jsonObject.getString("text"));
                        }

                        int currentPage = jsonObject.getIntValue("page_idx") + 1;
                        if (currentPage == page) {
                            sb.append(content).append("\n");
                        } else {
                            contentPerPage.put(page, sb.toString());
                            sb = new StringBuilder();
                            page = currentPage;
                            sb.append(content).append("\n");
                        }
                    }
                    contentPerPage.put(page, sb.toString());
                    break;
                }
            } else {
                System.out.println("解析中，请稍后...");
                Thread.sleep(10000);
                count++;
            }
        }
        return contentPerPage;
    }

    @SneakyThrows
    public static void parsePerPageSubmitTask(FileParseDTO dto) {
        Map<String, Object> body = new HashMap<>();
        body.put("bucket_name", dto.getBucketName());
        body.put("ak", dto.getAccessKey());
        body.put("sk", dto.getSecretKey());
        body.put("endpoint_url", dto.getEndpointUrl());
        body.put("show_url", dto.getShowUrl());
        body.put("filename", dto.getFileName());

        body.put("callback_url", dto.getCallBackUrl());
        body.put("callback_token", dto.getCallBackToken());
        body.put("doc_id", String.valueOf(dto.getDocId()));

        HttpResponse<String> response = Unirest.post(dto.getSubmitCallBackUrl())
                .header("X-API-Key", dto.getApiKey())
                .header("Content-Type", "application/json")
                .body(JSON.toJSONString(body))
                .asString();
        if (response.getStatus() != 200) {
            throw new ValidateException("提交解析任务失败");
        }
        log.info("提交解析任务成功");
    }
}
