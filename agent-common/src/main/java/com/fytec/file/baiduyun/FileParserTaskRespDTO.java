package com.fytec.file.baiduyun;

import lombok.Data;

@Data
public class FileParserTaskRespDTO extends FileParserRespBaseDTO {

    /**
     * 返回的结果对象
     */
    private Result result;

    /**
     * 结果对象内部类
     */
    @Data
    public static class Result {
        /**
         * 该请求生成的 task_id，后续使用该 task_id 获取审查结果
         */
        private String task_id;

        /**
         * 任务状态：pending, processing, success, failed
         */
        private String status;

        /**
         * 解析报错信息，包含任务失败、额度不够等
         */
        private String task_error;

        /**
         * 文档解析结果的 markdown 格式链接，有效期30天
         */
        private String markdown_url;

        /**
         * 文档解析结果的 bos 链接，有效期30天
         */
        private String parse_result_url;
    }


}
