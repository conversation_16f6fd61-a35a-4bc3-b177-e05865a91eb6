package com.fytec.file.baiduyun;


import lombok.Builder;
import lombok.Data;

/**
 * 百度云文档解析请求参数
 */
@Builder
@Data
public class FileParserReqDTO {
    /**
     * 文件的base64编码数据：
     * - 支持格式：pdf、jpg、jpeg、png、bmp、tif、tiff、ofd、ppt、pptx、doc、docx、txt、xls、xlsx、wps、html、mhtml
     * - PDF 不超过 300M，非PDF 不超过 50M
     * - 页数不超过 2000 页（流式文档按 2000 字为一页）
     * - 优先级高于 file_url
     */
    private String file_data;

    /**
     * 文件的URL地址：
     * - URL长度不超过1024字节
     * - 文件大于50M时必须使用该方式上传
     * - 需关闭防盗链
     * - 当file_data存在时失效
     */
    private String file_url;

    /**
     * 文件名，需保证后缀正确，例如 "1.pdf"
     */
    private String file_name;

    /**
     * 文档切分参数，JSON字符串格式：
     * 示例: "{\"switch\": true, \"chunk_size\": -1}"
     */
    private String return_doc_chunks;

    /**
     * 是否识别公式（适用于版式文档）
     */
    private Boolean recognize_formula;

    /**
     * 是否解析统计图表
     */
    private Boolean analysis_chart;

    /**
     * 指定识别语种类型，默认为 CHN_ENG
     * 可选值包括：CHN_ENG、JAP、KOR、FRE、SPA、POR、GER、ITA、RUS 等
     */
    private String language_type;
}
