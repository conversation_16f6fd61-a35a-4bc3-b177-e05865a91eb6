package com.fytec.file.baiduyun;

import lombok.Data;

import java.util.List;

@Data
public class FileParserResultRespDTO {

    /**
     * 文档名称
     */
    private String file_name;

    /**
     * 文档ID
     */
    private String file_id;

    /**
     * 文件单页解析内容列表
     */
    private List<Page> pages;

    /**
     * 文件内容切分结果，当 return_doc_chunks.switch = true 时有值
     */
    private List<Chunk> chunks;

    /**
     * 页面解析内容
     */
    @Data
    public static class Page {
        /**
         * 页面唯一标识 ID
         */
        private String page_id;

        /**
         * 页码数（从1开始）
         */
        private Integer page_num;

        /**
         * 当前页的所有纯文字内容
         */
        private String text;

        /**
         * 页面内容版式分析的结果
         */
        private List<Layout> layouts;

        /**
         * 页面表格解析结果
         */
        private List<Table> tables;

        /**
         * 页面中图片解析结果
         */
        private List<Image> images;

        /**
         * 页面元信息
         */
        private Meta meta;
    }

    /**
     * Layout 版式元素
     */
    @Data
    public static class Layout {
        /**
         * layout ID，layout元素唯一标志
         */
        private String layout_id;

        /**
         * layout对应的文本内容。注：当type为table, image时该字段为空
         */
        private String text;

        /**
         * layout元素在页面中的位置，[x, y, w, h] box框
         */
        private List<Integer> position;

        /**
         * layout元素类型
         * 可取值：para、table、head_tail、image、contents、seal、title、formula
         */
        private String type;

        /**
         * layout元素子类型
         * title类的 subtype 包含：title_{n}, image_title, table_title
         * image类的 subtype 包含：chart, figure, QR_code, Bar_code
         */
        private String sub_type;

        /**
         * 标题层级树中父节点的layout ID，若当前layout为一级标题，其parent为 "root"
         */
        private String parent;

        /**
         * 标题层级树中子节点的layout ID
         */
        private List<String> children;
    }

    /**
     * 表格解析结果
     */
    @Data
    public static class Table {
        /**
         * layout ID，与layouts中type为table的元素对应
         */
        private String layout_id;

        /**
         * 表格内容的markdown形式
         */
        private String markdown;

        /**
         * 表格标题对应的layout_id，默认为null
         */
        private List<String> table_title_id;

        /**
         * 边框数据 「x, y, w, h」
         */
        private List<Integer> position;

        /**
         * 单元格的内嵌版面信息
         */
        private List<Layout> cells;

        /**
         * 二维数组表示表格内布局位置信息
         */
        private List<List<Integer>> matrix;

        /**
         * 跨页表格标记：begin, inner, end；非跨页表格为空
         */
        private String merge_table;
    }

    /**
     * 图片解析结果
     */
    @Data
    public static class Image {
        /**
         * layout ID，与layouts中type为image的元素对应
         */
        private String layout_id;

        /**
         * 图片标题对应的layout_id，默认为null
         */
        private List<String> image_title_id;

        /**
         * 边框数据 「x, y, w, h」
         */
        private List<Integer> position;

        /**
         * 图片的内嵌版面信息
         */
        private List<Layout> content_layouts;

        /**
         * 图片存储链接
         */
        private String data_url;

        /**
         * 对统计图表进行内容解析和描述，输出结果为json字符串
         */
        private String image_description;
    }

    /**
     * 页面元信息
     */
    @Data
    public static class Meta {
        /**
         * 页面宽度
         */
        private Integer page_width;

        /**
         * 页面高度
         */
        private Integer page_height;

        /**
         * 是否扫描件
         */
        private Boolean is_scan;

        /**
         * 页面倾斜角度
         */
        private Integer page_angle;

        /**
         * 页面属性：text、contents、appendix、others
         */
        private String page_type;

        /**
         * excel的sheet名
         */
        private String sheet_name;
    }

    /**
     * 内容切片
     */
    @Data
    public static class Chunk {
        /**
         * 切片的ID
         */
        private String chunk_id;

        /**
         * 切片的内容
         */
        private String content;

        /**
         * 切片类型: text 或 table
         */
        private String type;

        /**
         * 切片元信息
         */
        private ChunkMeta meta;
    }

    /**
     * 切片元信息
     */
    @Data
    public static class ChunkMeta {
        /**
         * chunk所属的多级标题内容
         */
        private List<String> title;

        /**
         * chunk的位置，根据分块算法有可能chunk跨多个页
         */
        private List<Integer> position;

        /**
         * chunk的位置坐标
         */
        private List<Integer> box;

        /**
         * chunk内容所在页数
         */
        private Integer page_num;
    }
}
