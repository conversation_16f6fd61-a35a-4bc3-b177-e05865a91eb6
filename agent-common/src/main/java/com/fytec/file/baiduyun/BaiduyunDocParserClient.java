package com.fytec.file.baiduyun;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Base64;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
@Slf4j
public class BaiduyunDocParserClient {

    private final RedisTemplate<String, String> redisTemplate;

    /**
     * 百度云试卷切题分析
     *
     * @param json 试卷请求数据
     * @return 解析结果
     */
    public JSONObject parsePaperCut(String json) {
        String resp = sendHttpPost(BaiduyunConstants.PARSER_PAPER_CUT_URL, json);
        log.info("提交百度试卷切题解析任务，返回结果：{}", resp);
        return JSONObject.parseObject(resp);
    }

    /**
     * 百度云试卷分析
     *
     * @param json 试卷请求数据
     * @return 解析结果
     */
    public JSONObject parsePaper(String json) {
        String resp = sendHttpPost(BaiduyunConstants.PARSER_PAPER_URL, json);
        log.info("提交百度试卷解析任务，返回结果：{}", resp);
        return JSONObject.parseObject(resp);
    }

    /**
     * 百度云文档解析
     *
     * @param fileBytes
     * @return
     */
    public String parseDoc(byte[] fileBytes, String fileType, String url) {

        FileParserReqDTO parserReqDTO = FileParserReqDTO.builder()
                .file_data(Base64.getEncoder().encodeToString(fileBytes))
                .file_url(url)
                .file_name(UUID.randomUUID().toString() + System.currentTimeMillis() + "." + fileType)
                .build();

        String parseTaskJsonStr = sendHttpPost(BaiduyunConstants.PARSER_FILE_TASK_URL, JSONObject.toJSONString(parserReqDTO));
        FileParserRespDTO fileParserRespDTO = JSONUtil.toBean(parseTaskJsonStr, FileParserRespDTO.class);
        if (!fileParserRespDTO.isSuccess()) {
            throw new RuntimeException("请求百度云文档解析接口异常，详情：" + fileParserRespDTO.getError_msg());
        }

        String taskId = fileParserRespDTO.getResult().getTask_id();
        Boolean isFinished = false;
        String parseResultUrl = null;
        String markdownUrl = null;
        // 判断任务是否解析完成, 每隔1秒钟调用一次
        while (!isFinished) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("task_id", taskId);
            String queryTaskJsonStr = sendHttpPost(BaiduyunConstants.QUERY_FILE_TASK_URL, jsonObject.toJSONString());

            FileParserTaskRespDTO taskRespDTO = JSONUtil.toBean(queryTaskJsonStr, FileParserTaskRespDTO.class);
            if (taskRespDTO.isSuccess() && taskRespDTO.getResult() != null) {
                if ("success".equals(taskRespDTO.getResult().getStatus())) {
                    isFinished = true;
//                    parseResultUrl = taskRespDTO.getResult().getParse_result_url();
                    markdownUrl = taskRespDTO.getResult().getMarkdown_url();
                    log.info("百度云文档解析任务完成，解析结果markdownURL：{}", markdownUrl);
                    log.info("百度云文档解析任务完成，解析结果parseResultUrl：{}", taskRespDTO.getResult().getParse_result_url());
                } else if ("failed".equals(taskRespDTO.getResult().getStatus())) {
                    throw new RuntimeException("百度云文档解析任务失败，详情：" + taskRespDTO.getResult().getTask_error());
                }
            } else {
                throw new RuntimeException("百度云文档解析任务异常，详情：" + taskRespDTO.getError_msg());
            }
            try {
                if (isFinished) {
                    break;
                }
                Thread.sleep(1000);
            } catch (InterruptedException e) {
            }
        }

        if (parseResultUrl != null) {
            String parseResultJson = HttpUtil.get(parseResultUrl);
            FileParserResultRespDTO contentResp = JSONUtil.toBean(parseResultJson, FileParserResultRespDTO.class);
            return contentResp.getPages().stream().map(FileParserResultRespDTO.Page::getText).collect(Collectors.joining("\\n"));
        }
        if (markdownUrl != null) {
            String parseResultJson = HttpUtil.get(markdownUrl);
            return parseResultJson;
        }

        return null;
    }

    public String sendHttpPost(String url, String json) {
        HttpRequest postRequest = HttpRequest.post(buildUrl(url));
        postRequest.header("Content-Type", "application/x-www-form-urlencoded");
        return postRequest.form(JSON.parseObject(json)).timeout(10000).execute().body();
    }

    private String buildUrl(String url) {
        String accessToken = getBaiduyunAccessToken();
        return url + "?access_token=" + accessToken;
    }

    /**
     * 获取百度云accessToken
     *
     * @return
     */
    private String getBaiduyunAccessToken() {
        String accessToken = redisTemplate.opsForValue().get(BaiduyunConstants.REDIS_KEY_ACCESS_TOKEN);
        if (accessToken != null) {
            return accessToken;
        }
        String jsonStr = HttpUtil.get(BaiduyunConstants.GET_ACCESS_TOKEN_URL, 10000);
        JSONObject jsonObj = JSONObject.parseObject(jsonStr);
        if (jsonObj.containsKey("error") && jsonObj.containsKey("error_description")) {
            throw new RuntimeException(jsonObj.getString("error_description"));
        }
        if (jsonObj.containsKey("access_token")) {
            accessToken = jsonObj.getString("access_token");
            redisTemplate.opsForValue().set(BaiduyunConstants.REDIS_KEY_ACCESS_TOKEN, accessToken, jsonObj.getInteger("expires_in"), TimeUnit.SECONDS);
            return accessToken;
        }
        throw new RuntimeException("获取百度云accessToken失败");
    }

}
