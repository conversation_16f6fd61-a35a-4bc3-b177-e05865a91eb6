package com.fytec.file.baiduyun;

/**
 * 百度云常量
 */
public interface BaiduyunConstants {

    String CLIENT_ID = "vxDaWInVtfXKRD0nOEtbfveE";

    String CLIENT_SECRET = "IjJEQI4ygJPuYlR83ztlpudY9X4BN9p0";

    // 获取百度云access token
    String GET_ACCESS_TOKEN_URL = "https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=" + CLIENT_ID + "&client_secret=" + CLIENT_SECRET + "&";

    // 百度云文档解析接口
    String PARSER_FILE_TASK_URL = "https://aip.baidubce.com/rest/2.0/brain/online/v2/parser/task";

    // 获取百度云文档解析任务结果
    String QUERY_FILE_TASK_URL = "https://aip.baidubce.com/rest/2.0/brain/online/v2/parser/task/query";

    // access_token 缓存key
    String REDIS_KEY_ACCESS_TOKEN = "baiduyun:access_token";

    // 百度云试卷解析接口
    String PARSER_PAPER_URL = "https://aip.baidubce.com/rest/2.0/ocr/v1/doc_analysis";

    // 百度云试卷切题解析接口
    String PARSER_PAPER_CUT_URL = "https://aip.baidubce.com/rest/2.0/ocr/v1/paper_cut_edu";
}
