package com.fytec.file;


import cn.hutool.core.util.StrUtil;
import com.fytec.dto.parse.FileParseDTO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;
import org.jodconverter.core.document.DefaultDocumentFormatRegistry;
import org.jodconverter.core.office.OfficeManager;
import org.jodconverter.remote.RemoteConverter;
import org.jodconverter.remote.office.RemoteOfficeManager;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class DocxFileParseUtils {

    @SneakyThrows
    public static String parseUrl(String fileUrl) {
        URI uri = new URI(fileUrl);
        URL url = uri.toURL();
        try (InputStream inputStream = url.openStream()) {
            return parse(inputStream);
        } catch (Exception e) {
            log.warn("解析.docx文件失败，尝试解析为.doc文件");
            try (InputStream inputStream = url.openStream()) {
                return DocFileParseUtils.parse(inputStream);
            }
        }
    }


    @SneakyThrows
    public static Map<Integer, String> parseUrlPerPage(String fileUrl, String jodUrl) {
        URI uri = new URI(fileUrl);
        URL url = uri.toURL();
        try (InputStream inputStream = url.openStream()) {
            OfficeManager officeManager = RemoteOfficeManager.builder()
                    .urlConnection(jodUrl)
                    .build();
            officeManager.start();
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                RemoteConverter.make(officeManager).convert(inputStream).to(outputStream).as(DefaultDocumentFormatRegistry.PDF).execute();
                byte[] convertedBytes = outputStream.toByteArray();
                return PdfFileParseUtils.parseBytePerPage(convertedBytes);
            } catch (Exception e) {
                log.warn("转换失败，尝试解析为.doc文件");
                return new HashMap<>();
            } finally {
                officeManager.stop();
            }
        }
    }

    @SneakyThrows
    public static String parseByte(byte[] fileBytes) {
        InputStream inputStream = new ByteArrayInputStream(fileBytes);
        try {
            return parse(inputStream);
        } catch (Exception e) {
            log.warn("解析.docx文件失败，尝试解析为.doc文件");
            return DocFileParseUtils.parse(inputStream);
        }
    }


    public static String parse(InputStream inputStream) throws IOException {
        StringBuilder text = new StringBuilder();
        XWPFDocument document = null;
        try {
            document = new XWPFDocument(inputStream);
        } catch (Exception e) {
            e.printStackTrace();
        }

        List<IBodyElement> bodyElements = document.getBodyElements();

        for (IBodyElement element : bodyElements) {
            if (element instanceof XWPFParagraph paragraph) {
                // 处理段落
                text.append(paragraph.getText()).append("\n");
                // 检查段落中是否包含图片
                for (XWPFRun run : paragraph.getRuns()) {
                    if (!run.getEmbeddedPictures().isEmpty()) {
                        for (XWPFPicture picture : run.getEmbeddedPictures()) {
                            XWPFPictureData pictureData = picture.getPictureData();
                            if (pictureData != null) {
                                text.append(pictureData.getFileName()).append("\n");
                                //TODO 图片处理逻辑
                            }
                        }
                    }
                }

            } else if (element instanceof XWPFTable table) {
                for (XWPFTableRow row : table.getRows()) {
                    for (XWPFTableCell cell : row.getTableCells()) {
                        text.append(cell.getText()).append("\t");
                    }
                    text.append("\n");
                }
            }
        }
        return text.toString();
    }

    @SneakyThrows
    public static Map<Integer, String> parsePerPage(FileParseDTO dto) {
        if (StrUtil.equals(dto.getMode(), "normal")) {
            //对于doc，docx来说，fileUrl为原链接，fileName为转换后的pdf链接
            return PdfFileParseUtils.parseUrlPerPage(dto.getFileName());
        }

        return PdfFileParseUtils.parsePerPage(dto);
    }
}
