package com.fytec.file;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import lombok.SneakyThrows;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

public class XlsxFileParseUtils {

    @SneakyThrows
    public static String parse(String fileUrl, String headerParam) {
        URI uri = new URI(fileUrl);
        URL url = uri.toURL();
        try (InputStream inputStream = url.openStream()) {
            return JSON.toJSONString(parseExcelWithColumnGroups(inputStream, headerParam));
        }
    }

    @SneakyThrows
    public static Map<Integer, String> parseUrlPerPage(String fileUrl, String headerParam) {
        Map<Integer, String> map = new HashMap<>();
        map.putIfAbsent(1, parse(fileUrl, headerParam));
        return map;
    }


    private static List<Map<String, String>> parseExcelWithColumnGroups(InputStream inputStream, String headerParam) throws IOException {
        // 解析参数：格式为"表头行范围:列分割点" 示例："1-2:3" 或 "1:3,5"
        HeaderParams params = parseHeaderParam(headerParam);
        int[] headerRows = params.headerRows;
        List<Integer> splitColumns = params.columnSplits;

        List<Map<String, String>> result = new ArrayList<>();
        try (Workbook workbook = WorkbookFactory.create(inputStream)) {
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                // 收集原始表头数据
                List<List<String>> rawHeaderData = collectHeaderData(sheet, headerRows[0], headerRows[1]);

                // 按列分割点分组处理表头
                List<List<List<String>>> groupedHeaderData = splitHeaderByColumns(rawHeaderData, splitColumns);

                // 生成各组列名
                List<List<String>> columnGroups = groupedHeaderData.stream()
                        .map(XlsxFileParseUtils::mergeColumnNames)
                        .collect(Collectors.toList());

                // 读取数据并按列分组处理
                List<List<Map<String, String>>> sheetResult = readDataByColumnGroups(sheet, headerRows[1] + 1, columnGroups, splitColumns);
                if (CollUtil.isEmpty(sheetResult)) {
                    continue;
                }

                if (CollUtil.isEmpty(splitColumns)) {
                    result.addAll(sheetResult.getFirst());
                } else {
                    for (int n = 0; n < splitColumns.size(); n++) {
                        result.addAll(sheetResult.get(n));
                    }
                }
            }
        }
        return result;
    }

    private static HeaderParams parseHeaderParam(String param) {
        String[] parts = param.split(":");
        // 解析行范围，例如"1-2"或"3"，转换为{0,1}或{2,2}
        int[] rows = parseRowRange(parts[0]);

        // 解析列分割点，例如"3,5"，转换为列表[2,4]
        List<Integer> splits = new ArrayList<>();
        if (parts.length > 1) {
            splits = Arrays.stream(parts[1].split(","))
                    .map(s -> Integer.parseInt(s.trim()) - 1)
                    .collect(Collectors.toList());
        }
        return new HeaderParams(rows, splits);
    }

    private static int[] parseRowRange(String rowStr) {
        String[] parts = rowStr.split("-");
        int start = Integer.parseInt(parts[0]) - 1;
        int end = parts.length > 1 ? Integer.parseInt(parts[1]) - 1 : start;
        return new int[]{start, end};
    }

    private static List<List<String>> collectHeaderData(Sheet sheet, int startRow, int endRow) {
        // 收集指定行范围内的表头数据
        return IntStream.rangeClosed(startRow, endRow)
                .mapToObj(sheet::getRow)
                .map(row -> processHeaderRow(sheet, row))
                .collect(Collectors.toList());
    }

    private static List<String> processHeaderRow(Sheet sheet, Row row) {
        if (row == null) return Collections.emptyList();

        // 处理合并单元格，获取所有单元格的值
        int maxCol = row.getLastCellNum();
        return IntStream.range(0, maxCol)
                .mapToObj(col -> getMergedCellValue(sheet, row.getRowNum(), col))
                .collect(Collectors.toList());
    }

    private static String getMergedCellValue(Sheet sheet, int rowNum, int colNum) {
        for (CellRangeAddress merged : sheet.getMergedRegions()) {
            if (merged.isInRange(rowNum, colNum)) {
                Cell cell = sheet.getRow(merged.getFirstRow()).getCell(merged.getFirstColumn());
                return getCellValueAsString(cell);
            }
        }
        Cell cell = sheet.getRow(rowNum) == null ? null
                : sheet.getRow(rowNum).getCell(colNum, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
        return getCellValueAsString(cell);
    }

    private static List<List<List<String>>> splitHeaderByColumns(List<List<String>> headerData, List<Integer> splits) {
        List<Integer> splitPoints = new ArrayList<>(splits);
        splitPoints.sort(Integer::compare);

        List<List<List<String>>> groups = new ArrayList<>();
        int start = 0;

        for (int split : splitPoints) {
            groups.add(extractColumns(headerData, start, split));
            start = split + 1;
        }
        groups.add(extractColumns(headerData, start, Integer.MAX_VALUE - 1)); // 剩余列

        return groups.stream()
                .filter(g -> !g.isEmpty() && !g.getFirst().isEmpty())
                .collect(Collectors.toList());
    }

    private static List<List<String>> extractColumns(List<List<String>> data, int start, int end) {
        return data.stream()
                .map(row -> row.subList(start, Math.min(end + 1, row.size())))
                .collect(Collectors.toList());
    }

    private static List<String> mergeColumnNames(List<List<String>> headerData) {
        int maxCols = headerData.stream()
                .mapToInt(List::size)
                .max()
                .orElse(0);

        return IntStream.range(0, maxCols)
                .mapToObj(col -> headerData.stream()
                        .map(row -> col < row.size() ? row.get(col) : "")
                        .filter(s -> !s.isEmpty())
                        .distinct()
                        .collect(Collectors.joining("-")))
                .map(s -> s.isEmpty() ? "Column" : s)
                .collect(Collectors.toList());
    }

    private static List<List<Map<String, String>>> readDataByColumnGroups(Sheet sheet, int startRow,
                                                                          List<List<String>> columnGroups,
                                                                          List<Integer> splits) {
        List<Row> rows = new ArrayList<>();
        for (int i = startRow; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row != null) rows.add(row);
        }

        List<List<Map<String, String>>> result = new ArrayList<>();
        for (List<String> columns : columnGroups) {
            result.add(new ArrayList<>());
        }

        for (Row row : rows) {
            List<String> cellValues = IntStream.range(0, getMaxColumn(row))
                    .mapToObj(col -> getCellValueAsString(row.getCell(col)))
                    .collect(Collectors.toList());
            if (cellValues.isEmpty()) continue;

            int groupStart = 0;
            for (int i = 0; i < columnGroups.size(); i++) {
                int groupEnd = i < splits.size() ? splits.get(i) : cellValues.size();
                List<String> groupValues = cellValues.subList(groupStart, Math.min(groupEnd + 1, cellValues.size()));

                Map<String, String> mapped = new LinkedHashMap<>();
                List<String> columns = columnGroups.get(i);
                for (int j = 0; j < columns.size(); j++) {
                    if (j < groupValues.size()) {
                        if (mapped.containsKey(columns.get(j))) {
                            mapped.put(columns.get(j) + j, groupValues.get(j));
                        } else {
                            mapped.put(columns.get(j), groupValues.get(j));
                        }
                    } else {
                        mapped.put(columns.get(j), "");
                    }
                }
                result.get(i).add(mapped);
                groupStart = groupEnd + 1;
            }
        }
        return result;
    }

    private static int getMaxColumn(Row row) {
        return row == null ? 0 : row.getLastCellNum();
    }

    private static String getCellValueAsString(Cell cell) {
        return cell == null ? "" : new DataFormatter().formatCellValue(cell)
//                .replace("\n", "")    // 去掉换行符
//                .replace("\r", "")
                .trim();
    }


    public static void main(String[] args) throws IOException {
        try (InputStream inputStream = new FileInputStream("/Users/<USER>/Downloads/管理提升追踪2025.xlsx");) {
            System.out.println(JSON.toJSONString(parseExcelWithColumnGroups(inputStream, "1")));
        }
    }

    // 参数解析结果容器
    private static class HeaderParams {
        int[] headerRows;
        List<Integer> columnSplits;

        HeaderParams(int[] rows, List<Integer> splits) {
            this.headerRows = rows;
            this.columnSplits = splits;
        }
    }
}
