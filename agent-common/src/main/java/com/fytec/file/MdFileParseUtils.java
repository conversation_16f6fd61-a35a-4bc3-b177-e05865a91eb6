package com.fytec.file;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URI;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class MdFileParseUtils {
    @SneakyThrows
    public static String parseUrl(String fileUrl) {
        URI uri = new URI(fileUrl);
        URL url = uri.toURL();
        try (InputStream inputStream = url.openStream()) {
            return parse(inputStream);
        }
    }


    @SneakyThrows
    public static Map<Integer, String> parseUrlPerPage(String fileUrl) {
        Map<Integer, String> map = new HashMap<>();
        map.putIfAbsent(1, parseUrl(fileUrl));
        return map;
    }

    @SneakyThrows
    public static String parseByte(byte[] fileBytes) {
        InputStream inputStream = new ByteArrayInputStream(fileBytes);
        return parse(inputStream);
    }



    @SneakyThrows
    private static String parse(InputStream inputStream) {
        StringBuilder sb = new StringBuilder();
        BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
        String line;
        while ((line = reader.readLine()) != null) {
            sb.append(line);
            sb.append("\n");
        }
        reader.close();
        log.info("md文档解析结果：{}", sb);
        return sb.toString();
    }

    public static void main(String[] args) {
//        parse("http://172.16.109.241/files/group1/a5/4010a27e1c5a4ebe99d4d6a76264bcc0.md");
        byte[] bytes = FileParseUtils.convertFileToByteArray("/Users/<USER>/Desktop/prompt.md");
        parseByte(bytes);
    }
}
