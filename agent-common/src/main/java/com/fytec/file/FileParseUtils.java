package com.fytec.file;

import cn.hutool.core.exceptions.ValidateException;
import com.fytec.dto.parse.FileParseDTO;

import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URL;
import java.util.*;

public class FileParseUtils {
    public static final List<String> FILE_TYPE = Arrays.asList("md", "txt", "pdf", "doc", "docx");

    public static String fileParse(String fileUrl, String fileType) {
        return switch (fileType) {
            case "txt" -> TxtFileParseUtils.parseUrl(fileUrl);
            case "pdf" -> PdfFileParseUtils.parseUrl(fileUrl);
            case "doc" -> DocFileParseUtils.parseUrl(fileUrl);
            case "docx" -> DocxFileParseUtils.parseUrl(fileUrl);
            case "md" -> MdFileParseUtils.parseUrl(fileUrl);
            case "ppt" -> PptFileParseUtils.parse(fileUrl);
            case "pptx" -> PptxFileParseUtils.parse(fileUrl);
            case "png", "jpg", "jpeg" -> ImageFileParseUtils.parseToBase64(fileUrl);
            default -> null;
        };
    }


    public static Map<Integer, String> fileParsePerPage(FileParseDTO dto) {
        return switch (dto.getFileType()) {
            case "txt" -> TxtFileParseUtils.parseUrlPerPage(dto.getFileUrl());
            case "md" -> MdFileParseUtils.parseUrlPerPage(dto.getFileUrl());
            case "pdf" -> PdfFileParseUtils.parsePerPage(dto);
            case "doc", "docx" -> DocxFileParseUtils.parsePerPage(dto);
            case "png", "jpg", "jpeg" -> ImageFileParseUtils.parseUrlPerPage(dto);
            case "xls", "xlsx" -> XlsxFileParseUtils.parseUrlPerPage(dto.getFileUrl(), dto.getHeaderParam());
            default -> null;
        };
    }

    public static void fileParsePerPageAsyncTask(FileParseDTO dto) {
        PdfFileParseUtils.parsePerPageSubmitTask(dto);
    }


    public static String parseDoc(byte[] fileBytes, String fileType) {
        return switch (fileType) {
            case "txt" -> TxtFileParseUtils.parseByte(fileBytes);
            case "pdf" -> PdfFileParseUtils.parseByte(fileBytes);
            case "doc" -> DocFileParseUtils.parseByte(fileBytes);
            case "docx" -> DocxFileParseUtils.parseByte(fileBytes);
            case "md" -> MdFileParseUtils.parseByte(fileBytes);
            default -> null;
        };
    }


    public static byte[] convertFileToByteArray(String filePath) {
        try (FileInputStream fis = new FileInputStream(filePath);
             ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = fis.read(buffer)) != -1) {
                bos.write(buffer, 0, bytesRead);
            }
            return bos.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static List<String> parseFileByUrl(LinkedList<HashMap<String, Object>> files) {
        // 根据url下载文件
        List<String> contents = new LinkedList<>();
        if (files != null) {
            // name,size,status,uid,url
            for (Map<String, Object> file : files) {
                String fileUrl = file.get("url").toString();
                String[] items = fileUrl.split("\\.");
                String fileType = items[items.length - 1];
                if (!FILE_TYPE.contains(fileType)) {
                    throw new ValidateException("文件类型不支持");
                }
                byte[] fileBytes = downloadFromFileUrl(fileUrl);
                String s = parseDoc(fileBytes, fileType);
                contents.add(s);
            }
        }
        return contents;

    }


    private static byte[] downloadFromFileUrl(String fileUrl) {
        try {
            URL url = new URL(fileUrl);
            try (BufferedInputStream in = new BufferedInputStream(url.openStream());
                 ByteArrayOutputStream out = new ByteArrayOutputStream()) {
                byte[] dataBuffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = in.read(dataBuffer, 0, 1024)) != -1) {
                    out.write(dataBuffer, 0, bytesRead);
                }
                return out.toByteArray();
            }
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }


}
