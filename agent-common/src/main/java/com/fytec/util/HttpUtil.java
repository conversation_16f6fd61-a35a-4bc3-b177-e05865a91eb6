package com.fytec.util;

import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;
import org.apache.http.HttpHeaders;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

public class HttpUtil {
    public static HttpResponse<String> sendPatchRequest(String url, String body, String auth) throws UnirestException {
        return Unirest.patch(url)
                .headers(getCommonHeaders(auth))
                .body(body)
                .asString();
    }

    public static HttpResponse<String> sendPostRequest(String url, String body, String auth) throws UnirestException {
        return Unirest.post(url)
                .headers(getCommonHeaders(auth))
                .body(body)
                .asString();
    }

    public static HttpResponse<String> sendGetRequest(String url, String auth) throws UnirestException {
        Map<String, String> headers = new HashMap<>();
        headers.put(HttpHeaders.AUTHORIZATION, auth);
        headers.put(HttpHeaders.ACCEPT, "*/*");
        return Unirest.get(url)
                .headers(headers)
                .asString();
    }

    public static Map<String, String> getCommonHeaders(String auth) {
        Map<String, String> headers = new HashMap<>();
        headers.put(HttpHeaders.CONTENT_TYPE, "application/json");
        headers.put(HttpHeaders.AUTHORIZATION, auth);
        headers.put(HttpHeaders.ACCEPT, "*/*");
        return headers;
    }

    public static void main(String[] args) throws UnirestException {
        HttpResponse<String> response =  sendGetRequest("http://172.16.109.241:8080/api/v1/dags/ttt_58_1.0.1/dagRuns/%s?fields=state"
                .formatted(URLEncoder.encode("manual__2025-02-11T06:58:30.477984+00:00", StandardCharsets.UTF_8)), "Basic YWlyZmxvdzphaXJmbG93");
        System.out.println(response.getBody());
    }
}
