package com.fytec.util;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fytec.dto.Node;
import lombok.experimental.UtilityClass;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;


@UtilityClass
public class TreeBuilder {
    public List<? extends Node> buildListToTree(List<? extends Node> dirs) {
        List<Node> roots = findRoots(dirs, null);
        List<Node> notRoots = (List<Node>) CollectionUtils.subtract(dirs, roots);
        for (Node root : roots) {
            root.setChildren(findChildren(root, notRoots));
        }
        return roots;
    }

    public List<? extends Node> buildListToTree(List<? extends Node> dirs, Long rootParentIdValue) {
        List<Node> roots = findRoots(dirs, rootParentIdValue);
        List<Node> notRoots = (List<Node>) CollectionUtils.subtract(dirs, roots);
        for (Node root : roots) {
            root.setChildren(findChildren(root, notRoots));
        }
        return roots;
    }


    private List<Node> findRoots(List<? extends Node> allNodes, Long rootParentIdValue) {
        List<Node> results = new ArrayList<Node>();
        for (Node node : allNodes) {
            if ((ObjectUtil.isEmpty(rootParentIdValue) && ObjectUtil.isEmpty(node.getParentId()))
                    || (ObjectUtil.isNotEmpty(rootParentIdValue) && rootParentIdValue.equals(node.getParentId()))) {
                node.setLevel(0);
                results.add(node);
                node.setRootId(node.getId());
            }
        }
        return results;
    }

    private List<Node> findRoots(List<? extends Node> allNodes) {
        List<Node> results = new ArrayList<Node>();
        for (Node node : allNodes) {
            boolean isRoot = true;
            for (Node comparedOne : allNodes) {
                if (ObjectUtil.isNotEmpty(node.getParentId()) && node.getParentId().equals(comparedOne.getId())) {
                    isRoot = false;
                    break;
                }
            }
            if (isRoot) {
                node.setLevel(0);
                results.add(node);
                node.setRootId(node.getId());
            }
        }
        return results;
    }

    private List<Node> findChildren(Node root, List<Node> allNodes) {
        List<Node> children = new ArrayList<>();

        for (Node comparedOne : allNodes) {
            if (ObjectUtil.isNotEmpty(comparedOne.getParentId()) && comparedOne.getParentId().equals(root.getId())) {
                comparedOne.setLevel(root.getLevel() + 1);
                children.add(comparedOne);
            }
        }
        List<Node> notChildren = (List<Node>) CollectionUtils.subtract(allNodes, children);
        for (Node child : children) {
            List<Node> tmpChildren = findChildren(child, notChildren);
            child.setLeaf(CollUtil.isEmpty(tmpChildren));
            child.setChildren(tmpChildren);
        }
        return children;
    }

}
