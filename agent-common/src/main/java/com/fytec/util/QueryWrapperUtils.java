package com.fytec.util;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.fytec.annotation.Query;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.reflect.FieldUtils;

import java.lang.reflect.Field;
import java.util.*;

/**
 * Copyright (C), 2025-2099
 * mybatis 查询工具类
 *
 * <AUTHOR> lix
 * @date :   2025/6/10 14:02
 * Version: V1.0.0
 */
@Slf4j
public class QueryWrapperUtils {

    /**
     * 根据DTO自动构建QueryWrapper
     * @param queryDTO  查询DTO
     * @return
     * @param <T> 返回实体
     * @param <Q> 查询DTO
     */
    public static <T, Q> QueryWrapper<T> buildQueryWrapper(Q queryDTO) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        if (queryDTO == null) {
            return queryWrapper;
        }
        List<Field> fields = FieldUtils.getAllFieldsList(queryDTO.getClass());
        try {
            for (Field field : fields) {
                field.setAccessible(true);
                Query query = field.getAnnotation(Query.class);
                // 忽略没有注解的字段
                if (query == null) {
                    continue;
                }
                Object value = field.get(queryDTO);
                if (query.ignoreNull() && isNullOrEmpty(value)) {
                    continue;
                }
                String fieldName = getFieldName(query, field);
                switch (query.type()) {
                    case EQUAL:
                        queryWrapper.eq(fieldName, value);
                        break;
                    case NOT_EQUAL:
                        queryWrapper.ne(fieldName, value);
                        break;
                    case LIKE:
                        queryWrapper.like(fieldName, value);
                        break;
                    case LEFT_LIKE:
                        queryWrapper.likeLeft(fieldName, value);
                        break;
                    case RIGHT_LIKE:
                        queryWrapper.likeRight(fieldName, value);
                        break;
                    case GT:
                        queryWrapper.gt(fieldName, value);
                        break;
                    case GE:
                        queryWrapper.ge(fieldName, value);
                        break;
                    case LT:
                        queryWrapper.lt(fieldName, value);
                        break;
                    case LE:
                        queryWrapper.le(fieldName, value);
                        break;
                    case IN:
                        handleInCondition(queryWrapper, fieldName, value);
                        break;
                    case BETWEEN:
                        handleBetweenCondition(queryWrapper, fieldName, value);
                        break;
                    default:
                        break;
                }
            }
        } catch (IllegalAccessException e) {
            log.error("构建查询条件异常", e);
            throw new RuntimeException("构建查询条件异常", e);
        }
        return queryWrapper;
    }
    /**
     * 获取数据库字段名
     */
    private static String getFieldName(Query query, Field field) {
        // 如果注解中指定了字段名，直接使用
        if (StringUtils.isNotBlank(query.field())) {
            return query.field();
        }
        // 否则将驼峰转为下划线
        return camelToUnderline(field.getName());
    }
    /**
     * 驼峰转下划线
     */
    private static String camelToUnderline(String camelStr) {
        if (StringUtils.isBlank(camelStr)) {
            return camelStr;
        }
        StringBuilder builder = new StringBuilder();
        for (char c : camelStr.toCharArray()) {
            if (Character.isUpperCase(c)) {
                builder.append('_').append(Character.toLowerCase(c));
            } else {
                builder.append(c);
            }
        }
        return builder.toString();
    }
    /**
     * 处理IN查询条件
     */
    private static <T> void handleInCondition(QueryWrapper<T> queryWrapper, String fieldName, Object value) {
        if (value instanceof Collection) {
            queryWrapper.in(fieldName, (Collection<?>) value);
        } else if (value.getClass().isArray()) {
            queryWrapper.in(fieldName, (Object[]) value);
        } else {
            queryWrapper.in(fieldName, value);
        }
    }
    /**
     * 处理BETWEEN查询条件
     */
    private static <T> void handleBetweenCondition(QueryWrapper<T> queryWrapper, String fieldName, Object value) {
        if (value == null) {
            return;
        }

        if (value instanceof Object[] values) {
            if (values.length >= 2) {
                queryWrapper.between(fieldName, values[0], values[1]);
            } else {
                log.warn("BETWEEN条件需要两个值，但提供的值是: {}", Arrays.toString(values));
            }
        } else if (value instanceof Collection<?> collection) {
            if (collection.size() >= 2) {
                Iterator<?> iterator = collection.iterator();
                queryWrapper.between(fieldName, iterator.next(), iterator.next());
            } else {
                log.warn("BETWEEN条件需要两个值，但提供的集合大小是: {}", collection.size());
            }
        } else {
            log.warn("BETWEEN条件需要数组或集合类型，但实际类型是: {}", value.getClass());
        }
    }
    /**
     * 判断值是否为null或空
     */
    private static boolean isNullOrEmpty(Object value) {
        if (value == null) {
            return true;
        }
        if (value instanceof String) {
            return StringUtils.isBlank((String) value);
        }
        if (value instanceof Collection) {
            return ((Collection<?>) value).isEmpty();
        }
        if (value.getClass().isArray()) {
            return ((Object[]) value).length == 0;
        }
        return false;
    }
}
