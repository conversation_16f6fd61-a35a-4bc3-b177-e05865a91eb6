package com.fytec.util;

import lombok.SneakyThrows;
import org.apache.commons.codec.binary.Hex;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

public class HmacUtils {
    @SneakyThrows
    public static String generateHmacToken(String secretKey, String message) {
        // 获取 HMAC-SHA256 算法实例
        Mac mac = Mac.getInstance("HmacSHA256");
        // 创建密钥规格对象
        SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(), "HmacSHA256");
        // 初始化 Mac 对象
        mac.init(secretKeySpec);
        // 计算 HMAC
        byte[] hmacBytes = mac.doFinal(message.getBytes(StandardCharsets.UTF_8));
        // 将结果进行 Base64 编码
//        return Base64.getEncoder().encodeToString(hmacBytes);
        return Hex.encodeHexString(hmacBytes);
    }
}
