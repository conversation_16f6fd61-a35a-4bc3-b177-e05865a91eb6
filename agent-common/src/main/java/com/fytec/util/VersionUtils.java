package com.fytec.util;

public class VersionUtils {

    /**
     * @return
     */
    public static String next(String currentVersion,int maxLength) {
        if(currentVersion == null || currentVersion.isEmpty()){
            return "1.0.0";
        }
        String[] version = currentVersion.split("\\.");
        int v1 = Integer.parseInt(version[0]);
        int v2 = Integer.parseInt(version[1]);
        int v3 = Integer.parseInt(version[2]);

        v3++;
        if (v3 > maxLength) {
            v3 = 0;
            v2++;
            if (v2 > maxLength) {
                v2 = 0;
                v1++;
            }
        }
        return String.format("%s.%s.%s",v1,v2,v3);
    }
}
