package com.fytec.util;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;

public class AESCBCCrypto {

    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/CBC/PKCS5Padding";
    private static final int KEY_SIZE = 128;
    private static final int BLOCK_SIZE = 16;

    /**
     * 生成 AES 密钥
     *
     * @return 密钥的 Base64 编码字符串
     * @throws Exception 异常
     */
    public static String generateKey() throws Exception {
        KeyGenerator keyGenerator = KeyGenerator.getInstance(ALGORITHM);
        keyGenerator.init(KEY_SIZE);
        SecretKey secretKey = keyGenerator.generateKey();
        return Base64.getEncoder().encodeToString(secretKey.getEncoded());
    }

    /**
     * 生成初始化向量（IV）
     *
     * @return IV 的 Base64 编码字符串
     */
    public static String generateIV() {
        byte[] iv = new byte[BLOCK_SIZE];
        SecureRandom secureRandom = new SecureRandom();
        secureRandom.nextBytes(iv);
        return Base64.getEncoder().encodeToString(iv);
    }

    /**
     * AES/CBC/PKCS5Padding 加密
     *
     * @param plainText 明文
     * @param key       密钥的 Base64 编码字符串
     * @param iv        IV 的 Base64 编码字符串
     * @return 密文的 Base64 编码字符串
     * @throws Exception 异常
     */
    public static String encrypt(String plainText, String key, String iv) throws Exception {
        byte[] keyBytes = Base64.getDecoder().decode(key);
        byte[] ivBytes = Base64.getDecoder().decode(iv);
        SecretKeySpec secretKeySpec = new SecretKeySpec(keyBytes, ALGORITHM);
        IvParameterSpec ivParameterSpec = new IvParameterSpec(ivBytes);

        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, ivParameterSpec);

        byte[] encryptedBytes = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    /**
     * AES/CBC/PKCS5Padding 解密
     *
     * @param cipherText 密文的 Base64 编码字符串
     * @param key        密钥的 Base64 编码字符串
     * @param iv         IV 的 Base64 编码字符串
     * @return 明文
     * @throws Exception 异常
     */
    public static String decrypt(String cipherText, String key, String iv) throws Exception {
        byte[] keyBytes = Base64.getDecoder().decode(key);
        byte[] ivBytes = Base64.getDecoder().decode(iv);
        byte[] cipherBytes = Base64.getDecoder().decode(cipherText);
        SecretKeySpec secretKeySpec = new SecretKeySpec(keyBytes, ALGORITHM);
        IvParameterSpec ivParameterSpec = new IvParameterSpec(ivBytes);

        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ivParameterSpec);

        byte[] decryptedBytes = cipher.doFinal(cipherBytes);
        return new String(decryptedBytes, StandardCharsets.UTF_8);
    }

    public static void main(String[] args) {
        try {
            // 生成密钥和 IV
            String key = "hQRqV31qHCqFg9Ss968bog==";
            String iv = "q7s6LoaUE0sNVVz/eVDJHA==";




            // 明文
            String plainText = "Hello, AES/CBC/PKCS5Padding!";

            // 加密
            String cipherText = encrypt(plainText, key, iv);
            System.out.println("加密后的密文: " + cipherText);

            // 解密
            String decryptedText = decrypt(cipherText, key, iv);
            System.out.println("解密后的明文: " + decryptedText);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}