package com.fytec.util;

import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;

public class FileUtil 
{
	public static String getFileEndfix(String fileName){
		int idx = fileName.lastIndexOf(".");
		String end = "";
		if(idx > -1){
			end = fileName.substring(idx);
		}
		
		return end;
	}
	
	public static String prepareSubFolder(Long id){
		String tt = "0" + id;
		return tt.substring(0, 2);
	}
	
	public static String saveFile(String path, byte[] filebytes, String fileName, String group){
		String fileDir = group + "/";
		String uuid = UUIDGenerator.getUUID();
		fileDir += uuid.substring(0, 2) + "/";

		path = path + fileDir;
		File dir = new File(path);
		if(!dir.exists()){
			dir.mkdirs();
		}
		
		File newFile = new File(path + fileName);
		
		InputStream inputStream = null;
		OutputStream outputStream = null;
		
		try {
			inputStream = new ByteArrayInputStream(filebytes);
			
			if (!newFile.exists()) {
				newFile.createNewFile();
			}
			
			outputStream = new FileOutputStream(newFile);
			int read = 0;
			byte[] bytes = new byte[1024];

			while ((read = inputStream.read(bytes)) != -1) {
				outputStream.write(bytes, 0, read);
			}
		} catch (Exception e) {
			e.printStackTrace();
			try{
				newFile.delete();
			}catch(Exception e1){
			}
			return null;
		} finally{
			if(inputStream != null){
				try {
					inputStream.close();
				} catch (IOException e) {
				}
			}
			if(outputStream != null){
				try {
					outputStream.close();
				} catch (IOException e) {
				}
			}
		}
		if(newFile.length() == 0){
			return null;
		}
		String imageUrl = fileDir + fileName;
		
		return imageUrl;
	}
	
	public static String saveFile(String path, MultipartFile file, String fileName, String group){
		String fileDir = group + "/";
		path = path + fileDir;
		File dir = new File(path);
		if(!dir.exists()){
			dir.mkdirs();
		}
		
		File newFile = new File(path + fileName);
		
		InputStream inputStream = null;
		OutputStream outputStream = null;
		
		try {
			inputStream = file.getInputStream();
			
			if (!newFile.exists()) {
				newFile.createNewFile();
			}
			
			outputStream = new FileOutputStream(newFile);
			int read = 0;
			byte[] bytes = new byte[1024];

			while ((read = inputStream.read(bytes)) != -1) {
				outputStream.write(bytes, 0, read);
			}
		} catch (Exception e) {
			try{
				newFile.delete();
			}catch(Exception e1){
			}
			return null;
		} finally{
			if(inputStream != null){
				try {
					inputStream.close();
				} catch (IOException e) {
				}
			}
			if(outputStream != null){
				try {
					outputStream.close();
				} catch (IOException e) {
				}
			}
		}
		if(newFile.length() == 0){
			return null;
		}
		String imageUrl = fileDir + fileName;
		
		return imageUrl;
	}
	
	public static String getImageWh(File file){
		if(file == null || !file.exists()){
			return null;
		}
		
		String str = null;
		InputStream inputStream1 = null;
		try{
			inputStream1 = new FileInputStream(file);
			BufferedImage image = ImageIO.read(inputStream1);
			Integer width = image.getWidth();
			Integer height = image.getHeight();
			str = "#" + width + "," + height;
		} catch (Exception e) {
		} finally{
			if(inputStream1 != null){
				try {
					inputStream1.close();
				} catch (IOException e) {
				}
			}
		}
		
		return str;
	}
	
	public static String saveFile(String path, File file, String fileName, String group){
		String fileDir = group + "/";
		path = path + fileDir;
		File dir = new File(path);
		if(!dir.exists()){
			dir.mkdirs();
		}
		
		File newFile = new File(path + fileName);
		
		InputStream inputStream = null;
		OutputStream outputStream = null;
		
		try {
			inputStream = new FileInputStream(file);
			
			if (!newFile.exists()) {
				newFile.createNewFile();
			}
			
			outputStream = new FileOutputStream(newFile);
			int read = 0;
			byte[] bytes = new byte[1024];

			while ((read = inputStream.read(bytes)) != -1) {
				outputStream.write(bytes, 0, read);
			}
		} catch (Exception e) {
			try{
				newFile.delete();
			}catch(Exception e1){
			}
			return null;
		} finally{
			if(inputStream != null){
				try {
					inputStream.close();
				} catch (IOException e) {
				}
			}
			if(outputStream != null){
				try {
					outputStream.close();
				} catch (IOException e) {
				}
			}
		}
		
		if(newFile.length() == 0){
			return null;
		}
		
		String imageUrl = fileDir + fileName;
		
		return imageUrl;
	}

	public static File createTempFile(String prefix, String suffix) throws IOException
	{
        return File.createTempFile(prefix, suffix);
	}

    public static void copyFile(String srcPath, String destPath)throws Exception {
    	copyFile(new File(srcPath), new File(destPath));
    }

    public static void copyFile(File srcPath, File destPath)throws Exception {
        try{
            FileInputStream inPut = new FileInputStream(srcPath);
            FileOutputStream outPut = new FileOutputStream(destPath);
            byte[] bytes=new byte[1024];
            int i;
            while((i=inPut.read(bytes))!= -1){
                outPut.write(bytes,0,i);
            }
            inPut.close();
            outPut.close();
        } catch(Exception e){
            throw e;
        }
    }

}
