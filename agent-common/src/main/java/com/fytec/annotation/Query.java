package com.fytec.annotation;

/**
 * Copyright (C), 2025-2099
 * 查询条件注解
 *
 * <AUTHOR> lix
 * @date :   2025/6/10 13:59
 * Version: V1.0.0
 */

import java.lang.annotation.*;

@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface Query {

    /**
     * 查询方式，默认为等于
     */
    Type type() default Type.EQUAL;
    /**
     * 查询的字段名，默认为空，表示和属性名一致
     */
    String field() default "";
    /**
     * 是否忽略空值
     */
    boolean ignoreNull() default true;
    public enum Type {
        /** 等于 */
        EQUAL,
        /** 不等于 */
        NOT_EQUAL,
        /** 模糊查询 */
        LIKE,
        /** 左模糊查询 */
        LEFT_LIKE,
        /** 右模糊查询 */
        RIGHT_LIKE,
        /** 大于 */
        GT,
        /** 大于等于 */
        GE,
        /** 小于 */
        LT,
        /** 小于等于 */
        LE,
        /** IN查询 */
        IN,
        /** 在两个值之间 */
        BETWEEN
    }
}
