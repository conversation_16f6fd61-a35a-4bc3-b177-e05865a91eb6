package com.fytec.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "flow")
@Data
public class FlowProperties {
    private String type;

    private Dag dag;

    private CondaEnv condaEnv;
    private Airflow airflow;

    private Api api;
    private LangGraph langGraph;

    @Data
    public static class Dag {
        private String path;
    }

    @Data
    public static class CondaEnv {
        private String condaPath;
        private String envName;
    }

    @Data
    public static class Airflow {
        private String basicAuth;
        private String runUrl;
        private String stateUrl;
        private String unpauseUrl;
        private String xcomEntryUrl;
    }

    @Data
    public static class Api {
        private Prefix prefix;
    }

    @Data
    public static class Prefix {
        private String clientUrl;
        private String serverUrl;
    }

    @Data
    public static class LangGraph {
        private CheckPointer checkPointer;
    }

    @Data
    public static class CheckPointer {
        private String host;
        private int port;
        private String user;
        private String password;
        private String database;
    }
}
