package com.fytec.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.web.cors.CorsConfiguration;

import java.util.List;

@Data
@Component
@ConfigurationProperties(prefix = "upload")
public class UploadProperties {

    private String storage;
    private String fileServerPath;
    private String fileGroup;
    private String localServerPath;

}
