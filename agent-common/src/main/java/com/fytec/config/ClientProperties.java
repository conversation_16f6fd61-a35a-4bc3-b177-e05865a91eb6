package com.fytec.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "client")
@Data
public class ClientProperties {

    private String clientId;

    private String clientSecret;

    private String clientName;

    private String tokenUrl;

    private String tokenPyUrl;

    private Doc doc;
    private Image image;
    private Text text;
    private Deer deer;
    private OcrConfig ocrConfig;

    @Data
    public static class Doc {
        private boolean enabledAdvance;
        private String submitUrl;
        private String taskUrl;
        private String submitCallBackUrl;
        private String callBackUrl;
    }

    @Data
    public static class Image {
        private String processType;
        private String visionModel;
        private String orcNormalUrl;
    }

    @Data
    public static class Text {
        private String maxLength = "1000";
        private String rerankModel;
        private String rerankUrl;
    }

    @Data
    public static class Deer {
        private boolean enabled;
        private String urlPrefix;
        private String filePath;
    }

    @Data
    public static class OcrConfig {
        private String orcVatInvoiceUrl;
    }
}
