package com.fytec.cache;

import lombok.AllArgsConstructor;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class DictCacheClient {

    private static final String DICT_KEY = "dict";

    private final RedisTemplate<String,String> redisTemplate;

    public String getCacheKey(String... keys) {
        StringBuilder result = new StringBuilder();
        for (String key : keys) {
            result.append(":" + key);
        }
        result.deleteCharAt(0);
        return result.toString();
    }

    public String getDict(String typeCode, String value) {
        return redisTemplate.opsForValue().get(getCacheKey(DICT_KEY,typeCode,value));
    }


    public void addDict(String typeCode, String value,String label) {
        redisTemplate.opsForValue().setIfAbsent(getCacheKey(DICT_KEY,typeCode,value),label);
    }

    public void deleteAll() {
        redisTemplate.delete(getCacheKey(DICT_KEY));
    }


}
