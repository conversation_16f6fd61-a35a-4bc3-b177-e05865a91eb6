package com.fytec.entity.llm;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fytec.entity.BaseEntity;
import lombok.Data;

@Data
@TableName("ai_model")
public class AiModel extends BaseEntity {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("group_id")
    private Long groupId;

    @TableField("name")
    private String name;

    @TableField("size")
    private String size;

    @TableField("code")
    private String code;

    @TableField("tags")
    private String tags;

    @TableField("description")
    private String description;

    @TableField("stream_url")
    private String streamUrl;

    private String streamMethod;

    @TableField("non_stream_url")
    private String nonStreamUrl;

    private String nonStreamMethod;

    @TableField("enable")
    private boolean enable;

    @TableField("defaulted")
    private boolean defaulted;

    @TableField("diversity_params")
    private String diversityParams;

    @TableField("io_params")
    private String ioParams;

    @TableField(exist = false)
    private String logo;

    private boolean open;

    private String type;
}
