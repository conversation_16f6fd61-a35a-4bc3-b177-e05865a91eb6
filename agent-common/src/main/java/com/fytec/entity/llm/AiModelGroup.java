package com.fytec.entity.llm;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fytec.entity.BaseEntity;
import lombok.Data;

@Data
@TableName("ai_model_group")
public class AiModelGroup extends BaseEntity {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("group_name")
    private String groupName;

    @TableField("logo")
    private String logo;

    @TableField("description")
    private String description;

    @TableField("seq")
    private Integer seqNo;

}
