package com.fytec.entity.llm;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fytec.entity.BaseEntity;
import lombok.Data;

@Data
@TableName("ai_model_embedded")
public class EmbeddedModel extends BaseEntity {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("name")
    private String name;

    @TableField("size")
    private String size;

    @TableField("code")
    private String code;

    @TableField("tags")
    private String tags;

    @TableField("description")
    private String description;

    @TableField("url")
    private String url;

    @TableField("enable")
    private boolean enable;

    @TableField("defaulted")
    private boolean defaulted;

    @TableField("dimension")
    private int dimension;

    @TableField("method_name")
    private String methodName;

}
