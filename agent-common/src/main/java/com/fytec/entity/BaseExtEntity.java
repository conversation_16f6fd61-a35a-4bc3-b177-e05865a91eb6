package com.fytec.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 抽象实体
 *
 * 
 * @date 2021/8/9
 */
@Getter
@Setter
public class BaseExtEntity extends BaseEntity {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

}
