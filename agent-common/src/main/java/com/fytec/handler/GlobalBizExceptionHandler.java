/*
 * Copyright (c) 2020 fytec Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.fytec.handler;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.oauth2.exception.SaOAuth2ClientTokenException;
import cn.hutool.core.exceptions.ValidateException;
import com.fytec.constant.enums.Status;
import com.fytec.excetpion.ApiException;
import com.fytec.util.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.HandlerMethod;

import java.text.MessageFormat;
import java.util.List;

@Slf4j
@Order(10000)
@RestControllerAdvice
public class GlobalBizExceptionHandler {
    @ExceptionHandler(value = ValidateException.class)
    public R<?> handleValidationException(ValidateException exception) {
        return R.failed(exception.getMessage());
    }

    /**
     * 全局异常.
     *
     * @param e the e
     * @return R
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public R<?> exceptionHandler(Exception e, HandlerMethod hm) {
        ApiException ce = hm.getMethodAnnotation(ApiException.class);
        if (ce == null) {
            log.error("全局异常信息 ex={}", e.getMessage(), e);
            return R.failed(Status.INTERNAL_SERVER_ERROR_ARGS.getMsg());
        }
        Status st = ce.value();
        log.error(st.getMsg(), e);
        return R.failed(Status.INTERNAL_SERVER_ERROR_ARGS.getMsg());
    }

    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.OK)
    public R<?> handleIllegalArgumentException(IllegalArgumentException exception) {
        log.error("非法参数,ex = {}", exception.getMessage(), exception);
        return R.failed("非法参数");
    }

    @ExceptionHandler(SaOAuth2ClientTokenException.class)
    @ResponseStatus(HttpStatus.OK)
    public R<?> handSaOAuth2ClientTokenException(SaOAuth2ClientTokenException e) {
        log.error("全局异常信息 ex={}", e.getMessage(), e);
        return R.unauthorized(e.getMessage());
    }


    /**
     * validation Exception
     *
     * @param exception
     * @return R
     */
    @ExceptionHandler({MethodArgumentNotValidException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R<?> handleBodyValidException(MethodArgumentNotValidException exception) {
        List<FieldError> fieldErrors = exception.getBindingResult().getFieldErrors();
        log.warn("参数绑定异常,ex = {}", fieldErrors.getFirst().getDefaultMessage());
        return R.failed("非法参数");
    }

    /**
     * validation Exception (以form-data形式传参)
     *
     * @param exception
     * @return R
     */
    @ExceptionHandler({BindException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R<?> bindExceptionHandler(BindException exception) {
        List<FieldError> fieldErrors = exception.getBindingResult().getFieldErrors();
        log.warn("参数绑定异常,ex = {}", fieldErrors.getFirst().getDefaultMessage());
        return R.failed("非法参数");
    }


    /**
     * validation Exception (以form-data形式传参)
     *
     * @param exception
     * @return R
     */
    @ExceptionHandler({NotLoginException.class})
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public R<?> bindExceptionHandler(NotLoginException exception) {
        log.warn("非法token = {}", exception.getMessage(), exception);
        return R.failed("非法token");
    }


}
