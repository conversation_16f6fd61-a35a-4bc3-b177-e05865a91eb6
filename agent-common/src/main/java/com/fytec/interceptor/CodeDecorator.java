package com.fytec.interceptor;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.annotation.DictData;
import com.fytec.cache.DictCacheClient;
import com.fytec.dto.AbstractDTO;
import com.fytec.util.R;
import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CodeDecorator {
    private static final String COMMA = ",";

    @Autowired
    private DictCacheClient dictCacheClient;

    public <T> T decorate(T object) {
        if (object instanceof R) {
            Object data = ((R) object).getData();
            if (data instanceof Page) {
                for (Object o : ((Page) data).getRecords()) {
                    if (o instanceof AbstractDTO) {
                        initCodeValue(o);
                    }
                }
            } else if (data instanceof AbstractDTO) {
                initCodeValue(data);
            } else if (data instanceof List) {
                checkList((List) data);
            }
        } else if (object instanceof AbstractDTO) {
            initCodeValue(object);
        } else if (object instanceof List) {
            checkList((List) object);
        }
        return object;
    }

    private void checkList(List list) {
        for (Object o : list) {
            if (o instanceof AbstractDTO) {
                initCodeValue(o);
            }
        }
    }

    private void initCodeValue(Object content) {
        if (null == content) {
            return;
        }
        try {
            Field[] fields = FieldUtils.getAllFields(content.getClass());
            for (Field field : fields) {
                field.setAccessible(true);
                if (AbstractDTO.class.isAssignableFrom(field.getType())) {
                    initCodeValue(field.get(content));
                } else if (Collection.class.isAssignableFrom(field.getType()) &&
                        field.getGenericType() instanceof ParameterizedType &&
                        ArrayUtils.isNotEmpty(((ParameterizedType) field.getGenericType()).getActualTypeArguments()) &&
                        AbstractDTO.class.isAssignableFrom((Class<?>) ((ParameterizedType) field.getGenericType()).getActualTypeArguments()[0])) {
                    if (null != field.get(content)) {
                        Optional.of((Collection) field.get(content)).ifPresent(objects -> objects.forEach(this::initCodeValue));
                    }
                } else {
                     if (field.isAnnotationPresent(DictData.class)) {
                        field.setAccessible(true);
                        DictData dictData = field.getAnnotation(DictData.class);
                        Object value = field.get(content);
                        if(value == null){
                            continue;
                        }
                        String codeType = dictData.codeType();
                        if (codeType.trim().length() == 0 && StringUtils.isNotEmpty(dictData.cascadeField())) {
                            codeType = String.valueOf(FieldUtils.getField(content.getClass(), dictData.cascadeField(), true).get(content));
                        }
                        String finalCodeType = codeType;
                        String stringValue = null;
                        if (value instanceof String || value instanceof Number) {
                            stringValue = String.valueOf(value);
                        } else if (value instanceof Enum) {
                            stringValue = ((Enum) value).name();
                        } else if (value instanceof List) {
                            if(((List)value).size() > 0){
                                stringValue = "";
                                int i = 0;
                                for(Object o : ((List)value)){
                                    if(i > 0){
                                        stringValue += ",";
                                    }
                                    stringValue += o;
                                    i++;
                                }
                            }
                        }
                        if (stringValue != null) {
                            String dictValue = null;
                            if (stringValue.contains(COMMA)) {
                                dictValue = StringUtils.join(ImmutableList.copyOf(stringValue.split(COMMA)).stream().map(String::trim).filter(StringUtils::isNoneEmpty)
                                        .map(temp -> dictCacheClient.getDict(finalCodeType, temp)).collect(Collectors.toList()), COMMA);
                            } else {
                                dictValue = dictCacheClient.getDict(codeType, stringValue);
                            }
                            Field targetField = FieldUtils.getField(content.getClass(), dictData.target(), true);
                            if (dictValue != null) {
                                targetField.set(content, dictValue);
                            } else {
                                targetField.set(content, stringValue);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

}
