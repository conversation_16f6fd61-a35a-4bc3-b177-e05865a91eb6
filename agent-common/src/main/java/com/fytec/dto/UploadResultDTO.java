package com.fytec.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class UploadResultDTO {
    @Schema(description = "文件大小")
    private Long size;
    @Schema(description = "文件类型")
    private String type;
    @Schema(description = "文件访问路径")
    private String url;
    @Schema(description = "相对路径")
    private String path;

    private String width;
    private String height;

    @Schema(description = "文件名称")
    private String fileName;

    private String fileId;

}
