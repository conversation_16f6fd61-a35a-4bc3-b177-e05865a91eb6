package com.fytec.dto.llm;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class BasicProcessDTO {
    @Schema(description = "采样温度")
    private Double temperature = 0.7;

    @Schema(description = "采样温度替代方案")
    private Double topP = 0.7;

    @Schema(description = "频率惩罚系数")
    private Double frequencyPenalty = 0.0;

    @Schema(description = "存在惩罚系数")
    private Double presencePenalty = 0.0;

    @Schema(description = "最大 token 数")
    private int maxTokens;

    @Schema(description = "是否流失SSE输出")
    private boolean stream;

    @Schema(description = "是否包含token 用量统计信息，stream为true时使用")
    private boolean includeUsage;

    private String systemMessage;

    private String userMessage;

    private List<String> imageUrls;

    private List<ChatToolDTO> tools;

    @Schema(description = "是否多轮问答")
    private boolean multiTurn;

    private List<Map<String, String>> histories;
}
