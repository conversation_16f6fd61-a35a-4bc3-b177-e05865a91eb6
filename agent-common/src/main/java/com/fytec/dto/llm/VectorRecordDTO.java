package com.fytec.dto.llm;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class VectorRecordDTO {
    private Long id;
    private List<Float> vector;
    private List<Float> vectorExt;
    private String text;
    private Long docId;
    private Long groupId;
    private Long segId;
    private Long pageNumber;
    private String doc;
    private String url;
    private String createBy;
    //    private String updateBy;
    private Map<String, String> metadata;

//    private String tag;
//    private String overview;

//    private List<Float> vectorTag;
//    private List<Float> vectorOverview;

}
