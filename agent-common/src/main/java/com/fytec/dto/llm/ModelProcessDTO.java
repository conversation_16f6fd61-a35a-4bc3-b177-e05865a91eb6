package com.fytec.dto.llm;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ModelProcessDTO extends BasicProcessDTO {
    @Schema(description = "大模型类型")
    private String modelType;

    @Schema(description = "流式客户端随机id")
    private String clientId;

    @Schema(description = "大模型节点", hidden = true)
    private String modelId;

    @Schema(description = "大模型URl", hidden = true)
    private String modelBaseUrl;

    @Schema(description = "大模型是否支持function call,默认支持", hidden = true)
    private boolean enableFunctionCall = true;

    @Schema(hidden = true)
    private String oauthClientId;

    private String decryptApiKey;

    private boolean enablePluginSearch;

    private String thinkingType = "auto";

    private int index;

    private boolean multimodal = false;

    private boolean urlSupport = false;
}
