package com.fytec.dto.llm;

import com.alibaba.fastjson2.JSONArray;
import com.fytec.handler.IResponseHandlerProxy;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ModelCallDTO {
    private String methodName;
    private HttpServletResponse response;
    private String url;
    private Map<String, Object> paramBody;
    private StringBuffer history;
    private StringBuffer reasonHistory;
    private StringBuffer referenceHistory;
    private List<VectorResultDTO> docsList;//保证id和知识库内容text，2个字段，用来给前端做引用
    private JSONArray webSearchResult;

    private boolean enableCitation = true;

//    private boolean lastNode = false;
    //是否是最后一个节点，默认是，如果不是最后一个节点，不需要输出[DONE]吧，委托给之后的智能体输出或者

    private IResponseHandlerProxy responseHandlerProxy;

    // 执行开始时间
    private Long startTime;
    // 执行结束时间
    private Long endTime;
}
