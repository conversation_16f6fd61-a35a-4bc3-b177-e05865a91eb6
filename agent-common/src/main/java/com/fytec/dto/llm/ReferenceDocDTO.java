package com.fytec.dto.llm;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

@Data
public class ReferenceDocDTO {
    private Long docId;

    @Schema(description = "文档名称")
    private String doc;

    @Schema(description = "页码")
    private Long pageNumber;

    @Schema(description = "知识库id")
    private Long knowledgeId;

    @Schema(description = "临时的索引，给大模型时候的知识库index排序的")
    private Integer index;

    @Schema(description = "元数据")
    private Map<String, String> metadata;

    private Long groupId;

    private String type;
}
