package com.fytec.dto.llm;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ModelParamDTO {

    @Schema(description = "参数名称，如：生成随机性")
    private String name;

    @Schema(description = "参数名称，如：temperature")
    private String code;

    @Schema(description = "参数名称，如：temperature: 调高温度会使得模型的输出更多样性和创新性，" +
            "反之，降低温度会使输出内容更加遵循指令要求但减少多样性。建议不要与 “Top p” 同时调整。")
    private String description;

    @Schema(description = "参数名称，如：0")
    private Object minValue;

    @Schema(description = "参数名称，如：1")
    private Object maxValue;

    @Schema(description = "参数值，如：0.7")
    private Object value;

    @Schema(description = "参数加减值，如：0.01")
    private Object step = 1;
}
