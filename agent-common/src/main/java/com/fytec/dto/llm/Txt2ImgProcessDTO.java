package com.fytec.dto.llm;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Data
public class Txt2ImgProcessDTO {

    @NotBlank(message = "model不能为空")
    private String modelKey;

    private String modelId;

    @NotEmpty(message = "inputs不能为空")
    private String inputs;

    private String size = "512x512";

    private String format = "b64_json";// b64_json 与 url

    private Double guidanceScale = 2.5;

    private Boolean watermark = false;

    @Schema(hidden = true)
    private String oauthClientId;
}
