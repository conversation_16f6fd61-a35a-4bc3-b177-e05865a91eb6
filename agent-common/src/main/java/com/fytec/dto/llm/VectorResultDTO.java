package com.fytec.dto.llm;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

@Data
public class VectorResultDTO {

    @Schema(description = "向量ID")
    private Long id;

    @Schema(description = "文本内容")
    private String text;

    private Long docId;

    private Long groupId;

    @Schema(description = "文档名称")
    private String doc;

    @Schema(description = "文档链接")
    private String url;

    @Schema(description = "相似度")
    private Float score;

    @Schema(description = "相似度")
    private Long pageNumber;

    @Schema(description = "知识库id")
    private Long knowledgeId;

    @Schema(description = "临时的索引，给大模型时候的知识库index排序的")
    private Integer index;
}
