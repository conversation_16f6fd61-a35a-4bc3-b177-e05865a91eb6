package com.fytec.dto.llm;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

@Data
public class EmbeddedProcessDTO {

    @NotBlank(message = "model不能为空")
    private String modelKey;

    private String modelId;

    @NotEmpty(message = "inputs不能为空")
    private List<String> inputs;

    private List<String> imageUrls;

    @Schema(hidden = true)
    private String oauthClientId;
}
