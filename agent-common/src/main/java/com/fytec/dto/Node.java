package com.fytec.dto;


import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class Node extends AbstractDTO {

    private Long id;
    private Long parentId;
    private int level;
    private List<Node> children;
    private Long rootId;
    private boolean leaf;

    public Node() {
    }

    public Node(Long id, Long parentId) {
        this.id = id;
        this.parentId = parentId;
    }
}
