cd /home/<USER>"grep" | awk '{print $4}' | xargs kill
nohup java -jar agent-client-1.0-SNAPSHOT.jar --spring.profiles.active=dev >> client.log 2>&1 &


## 修改记录
> 2025-6-10
* 新增 AgentHandlerConfig相关配置，从数据库读取sql处理智能体配置
* 新增 QueryWrapperUtils 工具类，封装查询条件简化查询代码
* 新增表 agent_handler_config
* 修改 MybatisAutoConfiguration 新增 ConfigurationCustomizer 自定义配置，解决map映射问题
* 修改 AgentFlowService 在init 方法中追加注册智能体处理程序配置信息
```sql
create table agent_handler_config
(
    id                     bigint auto_increment comment '主键'
        primary key,
    env_type               varchar(255)            null comment '环境',
    agent_id               bigint                  null comment '智能体ID',
    description            text                    null comment '描述',
    think_agent_id         bigint                  null comment '思考智能体ID',
    format_agent_id        bigint                  null comment '格式化输出智能体ID',
    error_msg              text                    null comment '错误信息',
    sql_configs            text                    null comment '数据库配置信息，键值对形式',
    block_but_print_thinks bit        default b'0' null comment '阻止并打印思考信息',
    enable_citation        bit        default b'0' null comment '是否开启引用',
    create_by              varchar(255)            null comment '创建人',
    create_time            datetime                null comment '创建时间',
    update_by              varchar(255)            null comment '更新人',
    update_time            datetime                null on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                tinyint(1) default 0    null comment '是否删除'
)
    comment 'Agent处理程序配置' charset = utf8mb4;
```
