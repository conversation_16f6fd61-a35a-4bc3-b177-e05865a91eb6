精品语音合成服务（长文本）用量计算规则说明：
1.文本说明
语音合成服务中，长文本一般指超过1024GBK字节（512个汉字含标点符点）的文本内容。
2.基础计量单位
计费字符：以一个中文字符、英文字母、数字或标点符号为1个计费单位（即"字”），统一按字符数计算，不区分中英文。
3.定制化服务溢价规则
若最终用户提出声音定制需求，则服务单价按精品语音合成服务（长文本）投标单价的2.5倍计算。
4.技术实现
系统自动识别合成长文本字数，统一体现在系统统计报表中。

精品语音合成服务（短文本）用量计算规则说明： 1.文本说明 语音合成服务中，短文本一般指不超过120个GBK字节（约60个汉字或字母数字）的文本内容，这种限制是为了保证合成效率和实时性。同时单个文本最长可支持1024GBK字 节（512个汉字含标点符号）的文本，对于单个文本超出120GBK字节，系统自动拆分，分开调用，并智能合成输出最终结果。同时考虑到语速等因素，为了统一计价，本项目 以合成语音时长5秒/次，进行计价统计。 2.基础计量规则： 单次合成时长s5秒：按1次计费（即按5秒标准计费）； 单次合成时长>5秒：以5秒为阶梯向上取整计算合成次数（不足5秒的部分按5秒计）。 计算公式：合成次数=文本实际语音时长（秒）+5 (表示向上取整） 3.定制化服务溢价规则 若最终用户提出声音定制需求，则服务单价按精品语音合成服务（短文本）投标单价的2.5倍计算。

语音识别服务（文件）详情-基础计量单位
每5秒语音识别记为1次有效调用次数（不足5秒按5秒计算）。
2.超额时长折算：
当单次语音文件时长超过5秒时，按实际时长向上取整折算（如7秒语音记为2次，12秒语音记为3次）
折算公式：调用次数=语音时长（秒）/5
（表示向上取整）
分段说明：
示例1：3秒语音文件计为1次（不足5秒按5秒计算）
示例2：15秒语音文件计为3次（15+5=3次）
示例3：17秒语音文件计为4次（17-5=3.4向上取整）

教育场景文字识别服务用量计算规则说明：
1.按token用量计算（一个token为一次）：
模型中token和字数的换算比例为
4个英文字符~1个token
2个中文字符=1个token

通用场景文字识别服务用量计算规则说明： 1.按token用量计算（一个token为一次）： 模型中token和字数的换算比例为 4个英文字符=1个token 2个中文字符=1个token

语音识别服务（实时）用量计算规则说明：
1.计量单位
以秒为最小计量单位，不足1秒的部分按1秒计算（例如：用户输入语音时长为3.2秒，则按4秒计费）。
2处理规则
系统从用户发起语音输入开始计时，至语音结束（系统检测到静音或用户主动终止）时停止计时，该时间段内的总时长即为本次服务的用量。
若服务支持连续对话或多轮交互，每次用户语音输入均独立计算时长，最终用量为各轮次时长的累加值。
示例说明：
用户单次语音输入时长为15秒→计时15秒；
用户连续进行3轮对话，时长分别为8秒、12秒、5秒→总计时25秒（8+12+5）。
