# 讯飞语音听写功能使用说明

## 功能概述

本项目集成了讯飞语音听写功能，支持实时语音识别和一次性语音识别两种模式。项目采用 OkHttp3 WebSocket 实现，具备高性能和稳定性。

## 技术架构

### 核心组件

#### XfyunAsrWebSocketClient
- **职责**: WebSocket 连接管理和事件处理
- **继承**: `WebSocketListener`
- **功能**: 连接建立、消息接收、错误处理、连接关闭

#### 音频处理功能
- **职责**: 集成在XfyunAsrWebSocketClient中的音频数据处理
- **功能**: 音频编码、分帧、发送、文件处理
- **特性**: 支持异步文件发送、状态管理



### 架构优势

- **单一职责**: 每个类专注于特定功能
- **可维护性**: 业务逻辑清晰分离
- **可测试性**: 各组件可独立测试
- **高性能**: 基于 OkHttp3，减少资源消耗
- **线程安全**: 所有组件都是线程安全的

## 配置说明

### 1. 配置文件

在 `application.yml` 中配置讯飞语音参数：

```yaml
xfyun:
  iat:
    app-id: 你的应用ID
    api-secret: 你的API密钥
    api-key: 你的API Key
    host-url: wss://iat-api.xfyun.cn/v2/iat
```

### 2. 依赖说明

项目已添加以下依赖：
- `okhttp3`: HTTP客户端库，包含WebSocket支持
- `hutool`: 工具类库
- `spring-boot-starter-web`: Web支持

## WebSocket 客户端使用

### 基本使用

```java
// 创建客户端
XfyunAsrWebSocketClient client = new XfyunAsrWebSocketClient(
    hostUrl, appId, apiSecret, apiKey, encoding,
    message -> System.out.println("识别结果: " + message),
    error -> System.err.println("识别错误: " + error.getMessage()),
    () -> System.out.println("识别完成")
);

// 建立连接
client.connect();

// 发送音频文件
File audioFile = new File("path/to/audio.wav");
client.sendAudioFile(audioFile);
```

### 高级使用

```java
// 检查连接状态
if (client.isOpen()) {
    // 手动发送音频数据
    byte[] audioData = getAudioData();
    client.sendAudioData(audioData, true, false);
}

// 主动关闭连接
client.close();
```

## API接口说明

### 1. 音频文件识别

```http
POST /api/iat-stream/file
Content-Type: multipart/form-data

file: 音频文件
timeout: 超时时间（毫秒，可选，默认600000）
```

返回：SSE流，实时接收识别结果

### 2. 检查服务状态

```http
GET /api/iat-stream/status
```

返回：
```json
{
  "provider": "讯飞语音",
  "available": true,
  "timestamp": 1234567890123
}
```



## 设计模式分析

### 1. 策略模式 (Strategy Pattern)
- **应用场景**: `StreamAsrService` 接口定义了语音识别的统一策略
- **实现**: `XfyunStreamAsrServiceImpl` 实现了讯飞语音识别策略
- **优势**: 便于后续扩展其他语音识别服务提供商（如百度、阿里云等）

### 2. 观察者模式 (Observer Pattern)
- **应用场景**: WebSocket事件处理和回调机制
- **实现**: 通过 `Consumer<String> onMessage`、`Consumer<Throwable> onError`、`Runnable onComplete` 实现
- **优势**: 解耦事件产生者和消费者，支持多种事件处理方式

### 3. 单一职责原则 (Single Responsibility Principle)
- **XfyunAsrWebSocketClient**: 集成WebSocket连接管理和音频数据处理
- **响应处理**: 直接在WebSocket客户端中处理响应消息解析
- **XfyunAudioUtil**: 专注于音频格式转换
- **优势**: 代码职责清晰，减少类间依赖，易于维护和测试

### 4. 依赖注入模式 (Dependency Injection)
- **应用场景**: Controller层注入Service，Service层注入Properties
- **实现**: 使用 `@RequiredArgsConstructor` 和 `@Autowired` 注解
- **优势**: 降低耦合度，便于单元测试和模块替换

### 5. 模板方法模式 (Template Method Pattern)
- **应用场景**: `WebSocketListener` 的继承和重写
- **实现**: `XfyunAsrWebSocketClient` 继承 `WebSocketListener` 并重写关键方法
- **优势**: 定义了WebSocket处理的骨架，子类只需实现特定步骤

### 6. 工厂方法模式 (Factory Method Pattern)
- **应用场景**: WebSocket客户端的创建
- **实现**: `XfyunStreamAsrServiceImpl.createXfyunAsrWebSocketClient()` 方法
- **优势**: 封装对象创建逻辑，便于配置管理

### 7. 异步处理模式 (Asynchronous Pattern)
- **应用场景**: 音频文件处理和WebSocket通信
- **实现**: 使用 `CompletableFuture`、`ScheduledExecutorService` 和 SSE
- **优势**: 提高系统响应性，避免阻塞主线程

### 8. 枚举单例模式 (Enum Singleton Pattern)
- **应用场景**: `ExternalConstants.AudioFormatEncoding` 枚举
- **实现**: 使用枚举定义音频格式编码映射，提供静态方法获取编码格式
- **优势**: 线程安全、防止反射攻击，提供了类型安全的常量定义

### 9. 建造者模式 (Builder Pattern)
- **应用场景**: `AudioFileInfoDTO` 使用 `@Builder` 注解
- **实现**: Lombok自动生成建造者模式代码
- **优势**: 提供了灵活的对象构建方式，特别适合多参数的复杂对象

### 10. 适配器模式 (Adapter Pattern)
- **应用场景**: `XfyunAudioUtil.getEncoding()` 方法
- **实现**: 将文件后缀名适配为讯飞API所需的编码格式
- **优势**: 隐藏了不同系统间的接口差异，提供了统一的调用方式

### 11. 内聚设计模式 (Cohesive Design Pattern)
- **应用场景**: `XfyunAsrWebSocketClient` 内聚了WebSocket连接管理和音频处理逻辑
- **实现**: 将相关功能集中在单一类中，避免过度设计
- **优势**: 提高了代码的内聚性和可维护性，减少了类间依赖

## 内部实现细节

### 1. 事件处理流程

```
WebSocket事件 → XfyunAsrWebSocketClient → 业务处理器
    ↓
 onOpen    → 连接状态管理
 onMessage → 内置handleResponse()方法
 onError   → 错误处理和资源清理
 onClosed  → 状态重置和资源释放
```

### 2. 音频处理流程

```
音频文件/数据 → XfyunIatWebSocketClient
    ↓
 分帧处理 → JSON构建 → Base64编码 → WebSocket发送
    ↓
 状态管理 → 完成检查 → 资源清理
```

### 3. 响应处理流程

```
WebSocket消息 → 内置响应处理
    ↓
 JSON解析 → 错误验证 → 状态检查 → 回调触发
```

## 音频格式要求

- 采样率：16000Hz
- 位深：16bit
- 声道：单声道
- 编码：PCM
- 数据格式：Base64编码

## 注意事项

1. **音频处理**: 音频数据需要进行Base64编码
2. **数据分片**: 流式识别时，音频数据应分片发送，每片建议不超过8KB
3. **结束标识**: 最后一帧音频数据的`isLast`参数应设置为`true`
4. **连接超时**: SSE连接超时时间为5分钟
5. **配置安全**: 请确保讯飞语音服务的配置信息正确
6. **环境变量**: 建议在生产环境中将敏感配置信息放在环境变量中
7. **线程安全**: 所有组件都是线程安全的
8. **资源管理**: 自动管理线程池和连接资源
9. **状态同步**: 各组件状态保持同步
10. **错误恢复**: 支持连接中断后的状态恢复

## 错误处理

- **连接失败**: 检查网络连接和配置信息
- **鉴权失败**: 检查appId、apiSecret、apiKey是否正确
- **音频格式错误**: 确保音频格式符合要求
- **会话超时**: 重新创建会话
- **WebSocket异常**: 自动重连和状态恢复
- **音频处理异常**: 详细的错误日志和状态追踪

## 测试建议

1. **单元测试**: 分别测试各个组件的功能
2. **集成测试**: 测试组件间的协作
3. **压力测试**: 验证高并发场景下的稳定性
4. **异常测试**: 测试各种异常情况的处理
5. **音频质量测试**: 验证不同音频格式和质量的识别效果
6. **网络异常测试**: 测试网络中断和恢复场景