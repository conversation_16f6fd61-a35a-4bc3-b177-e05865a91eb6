# 外部服务调用日志AOP实现说明

## 概述

本项目实现了基于Spring AOP的外部服务调用日志记录功能框架，支持通过注解方式记录外部服务调用情况。目前处于基础框架阶段，具体的业务逻辑和计费统计功能尚未实现。

## 核心组件

### 1. 切面类 - ExternalServiceLogAspect

**位置**: `agent-client/src/main/java/com/fytec/aspect/ExternalServiceLogAspect.java`

**功能**:
- 拦截带有 `@ExternalServiceLog` 注解的方法
- 记录调用开始时间、结束时间、执行时长
- 分析输入参数类型（支持多种文件输入类型）
- 输出调用日志信息

**切点表达式**:
```java
@Around("@annotation(com.fytec.aspect.annotation.ExternalServiceLog)")
```

### 2. 实体类 - ExternalUseLog

**位置**: `agent-client/src/main/java/com/fytec/entity/external/ExternalUseLog.java`

**字段说明**:
- `clientName`: 客户端名称（从配置文件读取）
- `callTime`: 调用时间
- `durationSeconds`: 执行时长（秒）
- `serviceStatus`: 服务状态（SUCCESS/FAILED/PENDING）
- `logType`: 日志类型（DOC_PARSE）
- `calculateCount`: 计费次数
- `remarks`: 备注信息（接口名称和方法名）

### 3. 服务层 - ExternalUseLogService

**接口位置**: `agent-client/src/main/java/com/fytec/service/external/ExternalUseLogService.java`
**实现位置**: `agent-client/src/main/java/com/fytec/service/external/MongoExternalUseLogServiceImpl.java`

**功能**: 外部服务日志业务逻辑处理接口（接口和实现类存在，但具体业务逻辑待完善）

**主要方法**:
- `saveExternalUseLog()`: 保存日志记录
- `countByClientAndService()`: 统计调用次数

### 4. 常量类 - ExternalConstants

**位置**: `agent-client/src/main/java/com/fytec/constant/ExternalConstants.java`

**定义内容**:
- 服务状态常量
- 日志类型常量
- 计费规则常量
- 文件类型费用常量

## 注解说明

### @ExternalServiceLog 注解

**位置**: `agent-client/src/main/java/com/fytec/annotation/ExternalServiceLog.java`

**参数**:
- `inputType`: 输入类型枚举，支持 FILE、STREAM、TEXT、JSON、XML、BINARY 等
- `dataParamType`: 数据参数类型枚举
- `value`: 服务描述信息

**使用示例**:
```java
@ExternalServiceLog(inputType = InputType.FILE, value = "文档解析服务")
public String parseDocument(MultipartFile file) {
    // 业务逻辑
}
```

## 配置要求

### 1. Maven依赖

在 `agent-client/pom.xml` 中添加：
```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-aop</artifactId>
</dependency>
```

### 2. AOP配置

**配置类**: `agent-client/src/main/java/com/fytec/config/AopConfig.java`
```java
@Configuration
@EnableAspectJAutoProxy
public class AopConfig {
    // 启用AspectJ自动代理
}
```

### 3. 应用配置

在 `application-local-hr.yml` 中配置客户端信息：
```yaml
client:
  clientName: "测试客户端"
  clientId: "test_client_001"
```

## 使用方法

### 1. 添加注解

在需要记录日志的方法上添加 `@ExternalServiceLog` 注解：

```java
@ExternalServiceLog(inputType = InputType.FILE, value = "文档解析服务")
public String parseDocument(MultipartFile file) {
    // 业务逻辑实现
    return result;
}
```

### 2. 自动记录

当调用带有 `@ExternalServiceLog` 注解的方法时，AOP切面会自动：
- 记录调用开始时间
- 分析输入参数类型
- 执行原方法
- 记录执行时长
- 输出调用日志信息

### 3. 日志输出

切面会在控制台输出详细的调用日志信息，包括：
- 方法名称和参数信息
- 输入类型分析结果
- 执行时长统计
- 调用状态信息

## 注意事项

1. **性能影响**: AOP会增加少量性能开销，建议仅在需要监控的关键方法上使用
2. **异常处理**: 切面中的异常不会影响原方法执行
3. **扩展性**: 可以通过修改注解参数来适配不同的输入类型和业务场景
4. **开发阶段**: 目前处于基础框架阶段，具体的数据持久化和计费逻辑需要后续开发

## 后续开发计划

1. **数据持久化**: 实现日志数据保存到MongoDB的功能
2. **计费逻辑**: 根据业务需求实现具体的计费规则
3. **统计查询**: 提供日志查询和统计分析接口
4. **监控告警**: 集成监控系统，提供实时告警功能