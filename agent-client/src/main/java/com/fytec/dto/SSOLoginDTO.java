package com.fytec.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class SSOLoginDTO {
    @Schema(description = "统一门户accessToken")
    @NotBlank(message = "统一门户accessToken不能为空")
    private String accessToken;

    private String appId ="aeca6faaf36f40bb984c830bc194ee5d";
    private String appSecret ="ef81c39a2dfb4213aaf522ef8ba8383f";


}
