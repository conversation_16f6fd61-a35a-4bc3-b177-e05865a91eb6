package com.fytec.dto.flow;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class DevelopWorkflowDTO {

    @Schema(description = "工作流资源ID")
    @NotNull(message = "工作流资源ID不能为空")
    private Long resourceId;


    @Schema(description = "工作流配置")
    @NotBlank(message = "工作流配置不能为空")
    private String configJson;
}
