package com.fytec.dto.flow;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

@Data
public class WorkflowExecuteDTO {
    @Schema(description = "资源ID")
    private Long resourceId;

    @Schema(description = "工作流发布ID")
    private Long id;

    @Schema(description = "工作流参数")
    private Map<String, Object> params;

    @Schema(description = "是否开启调试模式", hidden = true)
    private boolean debug = false;

    private String configJson;

    private String configTreeJson;

    private String runId;
}
