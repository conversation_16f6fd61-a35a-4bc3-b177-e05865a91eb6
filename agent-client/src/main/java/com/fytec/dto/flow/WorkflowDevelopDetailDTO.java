package com.fytec.dto.flow;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class WorkflowDevelopDetailDTO {

    private Long id;

    @Schema(description = "工作流资源ID")
    private Long resourceId;

    @Schema(description = "工作流配置")
    private String configJson;


    @Schema(description = "名称")
    private String name;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "图片")
    private String logo;

    @Schema(description = "类型：workflow、dialogue、plugin")
    private String type;

    private LocalDateTime updateTime;
}
