package com.fytec.dto.flow;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class PublishWorkflowDTO {

    @Schema(description = "资源ID")
    @NotNull(message = "资源ID不能为空")
    private Long resourceId;

    @Schema(description = "工作流配置")
    @NotBlank(message = "工作流配置不能为空")
    private String configJson;

    private String configTreeJson;

    @Schema(description = "发布记录")
    @NotBlank(message = "发布记录不能为空")
    private String pubRecord;
}
