package com.fytec.dto.agent;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AgentPublishDTO {

    @Schema(description = "智能体ID")
    private String agentId;

    @Schema(description = "发布智能体ID")
    private Long id;


    @Schema(description = "发布时间")
    private LocalDateTime publishTime;

    @Schema(description = "开场白")
    private String prologue;

    @Schema(description = "默认问题(数组)")
    private String tips;

}
