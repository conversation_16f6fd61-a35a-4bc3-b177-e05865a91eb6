package com.fytec.dto.agent;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class AgentDebugDTO {
    @Schema(description = "智能体ID")
    @NotNull(message = "智能体ID不能为空")
    private Long agentId;

    private Long messageId;

    @Schema(description = "用户输入")
    @NotBlank(message = "用户输入不能为空")
    private String userInput;


    @Schema(description = "文件Id")
    private List<Long> fileDocIds;
}
