package com.fytec.dto.agent;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AgentPublishRecordDTO {

    @Schema(description = "智能体ID")
    private String agentId;

    @Schema(description = "发布智能体ID")
    private Long agentPublishId;

    @Schema(description = "版本号")
    private String version;

    @Schema(description = "发布时间")
    private LocalDateTime publishTime;

    @Schema(description = "发布记录")
    private String publishRecord;
}
