package com.fytec.dto.agent;

import lombok.Data;

@Data
public class AgentParamsDto {
    // 智能体的参数配置
    /*
    可以先配置的：
    1. 是否允许输出思考过程（结果阻塞，但是若存在流，支持流输出思考过程） printThink
    2. 是否记录历史： 1. 只记录用户输入 2.记录输入和输出 3. 记录原始输入？同步到历史记录了？
    */
//    private Boolean blockButPrintThink = false;

    /*
    优先级低的（可能做成工作流形式即可）
    1. 需要配置智能体类型，比如sql的，可以直接查询出数据的
    3. 思考过程关联的智能体
    4. 并行附属智能体
     */
//    private String types;//
}
