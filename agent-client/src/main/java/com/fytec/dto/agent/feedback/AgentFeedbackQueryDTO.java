package com.fytec.dto.agent.feedback;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class AgentFeedbackQueryDTO {
    @Schema(description = "智能体id")
    private Long agentId;

    @Schema(description = "反馈处理状态。AGENT_FEEDBACK_STATUS")
    private String status;

    @Schema(description = "反馈处理结果。AGENT_FEEDBACK_RESULT")
    private String type;

    @Schema(description = "历史ID")
    private Long historyId;
//    private String historyId;// 根据历史查询反馈记录？
}
