package com.fytec.dto.agent;

import com.fytec.constant.Constants;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class AddAgentDTO {
    private String type;

    @Schema(description = "智能体名称")
    @NotBlank(message = "智能体名称不能为空")
    @Size(max = 20, message = "智能体名称最大长度20")
    private String name;

    @Schema(description = "智能体功能介绍")
    @Size(max = 500, message = "智能体功能介绍最大长度500")
    private String description;

    @Schema(description = "图标")
    @NotBlank(message = "图标不能为空")
    private String logo;

    @Schema(description = "标签（AGENT_TAGS）")
    private String tags;

}
