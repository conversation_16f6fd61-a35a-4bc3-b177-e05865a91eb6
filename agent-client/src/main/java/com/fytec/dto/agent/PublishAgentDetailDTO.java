package com.fytec.dto.agent;

import com.fytec.annotation.DictData;
import com.fytec.dto.AbstractDTO;
import com.fytec.dto.flow.WorkflowDTO;
import com.fytec.dto.knowledge.KnowledgeConfigDTO;
import com.fytec.dto.model.ModelDTO;
import com.fytec.dto.plugin.PluginDTO;
import com.fytec.dto.voice.VoiceDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class PublishAgentDetailDTO extends AbstractDTO {

    @Schema(description = "智能体ID")
    private Long agentId;

    @Schema(description = "发布智能体ID")
    private Long agentPublishId;


    @Schema(description = "智能体类型(agent_type)")
    private String type;

    @Schema(description = "开场白")
    private String prologue;

    @Schema(description = "默认问题")
    private List<String> tips;

    private LocalDateTime updateTime;


    @Schema(description = "智能体名称")
    private String name;

    @Schema(description = "智能体介绍")
    private String description;

    @Schema(description = "智能体图标")
    private String logo;

    @DictData(target = "tagNames",codeType = "AGENT_TAGS")
    private String tags;

    private String tagNames;

    @Schema(description = "智能体发布历史")
    private List<AgentPublishRecordDTO> publishRecords;

}
