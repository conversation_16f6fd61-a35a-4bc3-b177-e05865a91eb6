package com.fytec.dto.agent;

import com.fytec.dto.knowledge.KnowledgeDocHistoryDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class AgentDevelopHistoryDTO {
    private Long agentId;

    private Long messageId;

    @Schema(description = "用户输入")
    private String userInput;

    @Schema(description = "回答")
    private String answer;

    @Schema(description = "思考过程")
    private String reasoning;

    private String reference;

    @Schema(description = "调试用户ID")
    private Long debugId;

    private List<KnowledgeDocHistoryDTO> knowledgeDocInfo;//知识库id

    private LocalDateTime createTime;


}

