package com.fytec.dto.agent.feedback;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class AgentFeedbackHandleDTO {
    @Schema(description = "反馈id。")
    private Long id;

    @Schema(description = "处理备注。")
    private String remark;

    @Schema(description = "反馈id。")
    private Long feedbackId;

    @Schema(description = "处理状态。AGENT_FEEDBACK_STATUS，设置为uncomplete未处理时，处理结果就不用填写了")
    private String status;

    @Schema(description = "处理结果。AGENT_FEEDBACK_RESULT")
    private String result;


    @Schema(description = "处理结果为positive时，才有的textarea文本框。必填")
    private String content;//
    @Schema(description = "处理结果为positive时，才有的input框。不必填")
    private String overview;//

}
