package com.fytec.dto.agent;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AgentBasicDTO {

    @Schema(description = "智能体ID")
    private Long id;

    @Schema(description = "智能体名称")
    private String name;

    @Schema(description = "智能体功能介绍")
    private String description;

    @Schema(description = "图标")
    private String logo;

    private String status;

    @Schema(description = "用户头像")
    private String imageUrl;

    @Schema(description = "用户名称")
    private String updateName;

    @Schema(description = "最近编辑时间")
    private LocalDateTime updateTime;
}
