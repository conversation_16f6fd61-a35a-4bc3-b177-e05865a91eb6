package com.fytec.dto.agent;

import com.fytec.constant.AgentFlowConstants;
import com.fytec.handler.IResponseHandlerProxy;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
public class AgentExecuteDTO {
    private String conversationId;

    @Schema(description = "智能体发布ID")
    @NotNull(message = "智能体发布ID不能为空")
    private Long agentPublishId;

    private Long agentId;

    private Long messageId;

    @Schema(description = "用户输入")
    @NotBlank(message = "用户输入不能为空")
    private String userInput;

    @Schema(description = "用户原始输入")
    private String rawInput;

    private boolean enablePluginSearch = false;

    @Schema(description = "是否启用引用")
    private boolean enableCitation = false; // 默认不开启，aiwork默认设置了开启

    private Long modelId;

    @Schema(hidden = true)
    private boolean enableFunctionCall = true;

    private List<String> imageUrls;

    @Schema(description = "知识库文档过滤条件", hidden = true)
    private String filter;

    @Schema(hidden = true)
    private String appendContent;

    @Schema(description = "使用模型名称", hidden = true)
    private String modelName;

    private Map<String, String> constants;

    private String thirdUserId;

    private int historySize = 3;

    private String flowId;

    private IResponseHandlerProxy responseHandlerProxy;

    @Schema(description = "第三方id，可在历史记录时进行查询")
    private String objectId;

    private Long historyKnowledgeId;// 历史知识库id

    private String agentRunType = AgentFlowConstants.AGENT_RUN_TYPE.customer.name();//执行类型

    @Schema(description = "系统提示词")
    private String sysPrompt;

    private boolean enableCurrentDate = true;

    private List<Long> knowledgeIds;//知识库id

    @Schema(description = "文件Id")
    private List<Long> fileDocIds;

    private Map<Long, String> knowledgefilterMap = new HashMap<>();

    private Set<Long> knowledgeDocIds;
    private Map<Long, Set<Long>> knowledgeDocMap;
}
