package com.fytec.dto.agent;

import com.fytec.annotation.DictData;
import lombok.Data;

@Data
public class AgentFlowHistoryDTO extends AgentPublishHistoryDTO {
    private String clientId;

    private String clientName;

    //    @DictData(target = "statusName", codeType = "AGENT_RUN_STATUS")
    private String status;
    private String statusName;

    //    @DictData(target = "typeName", codeType = "AGENT_RUN_TYPE")
    private String type;
    private String typeName;


    private String agentName;

    private Integer feedbackNum = 0;
}
