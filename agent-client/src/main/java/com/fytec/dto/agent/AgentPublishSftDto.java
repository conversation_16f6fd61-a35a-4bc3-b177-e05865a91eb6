package com.fytec.dto.agent;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class AgentPublishSftDto extends AgentSftDto{
    //    @Schema(description = "需要做好问题改写，得有上下文，才比较合适")
//    @NotBlank(message = "发布智能体ID不能为空")
//    private Long agentPublishId;
    @Schema(description = "需要做好问题改写，得有上下文，才比较合适")
    @NotBlank(message = "历史id")
    private Long historyId;

    @Schema(description = "备注信息，标注为什么是正确还是错误的,可选，如果是错误的，必须选择")
    private String content;

    @Schema(hidden = true)
    private String type;//知识库类型,KNOWLEDGE_BASE_TYPE
    @Schema(hidden = true)
    private String userInput;
    @Schema(hidden = true)
    private String answer;
}
