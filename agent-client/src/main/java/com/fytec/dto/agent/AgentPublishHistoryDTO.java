package com.fytec.dto.agent;

import com.fytec.dto.knowledge.KnowledgeDocHistoryDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class AgentPublishHistoryDTO {
    private Long id;
    @Schema(description = "会话ID")
    private String conversationId;

    @Schema(description = "智能体发布ID")
    private Long agentPublishId;
    private Long agentId;

    private Long messageId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户输入")
    private String userInput;
    @Schema(description = "用户原始输入")
    private String rawInput;

    @Schema(description = "回答")
    private String answer;

    @Schema(description = "思考过程")
    private String reasoning;

    private String reference;

    private String modelName;

    private List<KnowledgeDocHistoryDTO> knowledgeDocInfo;//知识库id

    private LocalDateTime createTime;

    private String flowId;

    private String extraInfo;
    @Schema(description = "思维过程链")
    private String reasoningchain;

    private String status;// 执行后的状态，可能后续节点断了，也算是失败;自己成功，后续失败的也有可能

    private Long runtime;// 运行时间（毫秒数）

    private String error;// 错误信息

    private List<Long> feedbackId;// 反馈id
}

