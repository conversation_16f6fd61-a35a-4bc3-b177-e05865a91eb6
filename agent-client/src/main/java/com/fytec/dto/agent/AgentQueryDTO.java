package com.fytec.dto.agent;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class AgentQueryDTO {

    private String name;

    @Schema(description = "标签（AGENT_TAGS）")
    private String tags;

    private String status;

    private boolean onlySelf;

    private Long applicationId;

    private List<Long> agentIds;

    @Schema(hidden = true)
    private Long createBy;

}
