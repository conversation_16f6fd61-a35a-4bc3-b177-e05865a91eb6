package com.fytec.dto.agent.feedback;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fytec.dto.AbstractDTO;

import com.fytec.annotation.DictData;
import com.fytec.entity.agent.AgentHistoryFeedback;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AgentHistoryFeedbackDTO extends AbstractDTO {

    @Schema(description = "反馈ID")
    private Long id;

    @DictData(target = "resultName", codeType = "AGENT_FEEDBACK_RESULT")
    private String result;
    @Schema(description = "反馈处理结果。AGENT_FEEDBACK_STATUS")
    private String resultName;


    @DictData(target = "typeName", codeType = "AGENT_FEEDBACK_TYPE")
    private String type;
    @Schema(description = "反馈处理类型。AGENT_FEEDBACK_STATUS")
    private String typeName;

    @DictData(target = "statusName", codeType = "AGENT_FEEDBACK_STATUS")
    private String status;
    @Schema(description = "反馈处理状态。AGENT_FEEDBACK_STATUS")
    private String statusName;

    private String agentName;

    @Schema(description = "历史ID")
    private Long historyId;//关联是哪个历史处理有问题（一个思维链都可以详细反馈）

    @Schema(description = "处理备注")
    private String remark;//处理备注

    @Schema(description = "用户提交的备注")
    private String userRemark;//用户提交的备注

    @Schema(description = "文档ID")
    private String docIds;//可以对应到某一个片段。如果这个片段被删除了，这里其实需要同步更新。所以建议之后只考虑通过反馈管理管理知识库好一些

    @Schema(description = "知识库id")
    private Long knowledgeId;//知识库id。

    @Schema(description = "智能体id")
    private Long agentId; // 直接根据智能体id检索。不会变得字段，冗余存储

    @Schema(description = "流程ID")
    private String flowId; //属于哪个流程

    //可以根据反馈记录，直接查处整个流程链
    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "客户端ID")
    private String clientId;

    @Schema(description = "第三方用户Id")
    private String thirdUserId;

    private String message_id;//第几条

    @Schema(description = "用户原始输入")
    private String rawInput;
    @Schema(description = "实际输入")
    private String input;
    @Schema(description = "回答内容")
    private String answer;//相同输入、改写、回答情况下，不允许重复提交？ 或者之后添加一个检索关联，这样可以方便修改。

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
