package com.fytec.dto.agent;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class AgentSftDto {
    @Schema(description = "需要做好问题改写，得有上下文，才比较合适")
    @NotBlank(message = "agentId不能为空")
    private Long agentId;

    @Schema(description = "备注信息，标注为什么是正确还是错误的,可选，如果是错误的，必须选择")
    private String content;

    @Schema(hidden = true)
    private String type;//知识库类型,KNOWLEDGE_BASE_TYPE
    private String userInput;
    private String answer;
    private String rawInput;

    @Schema(hidden = true)
    private String thirdUserId;

    private String source;//来源必须是用户才会记录到表里

    @Schema(description = "流程ID")
    private String flowId; //属于哪个流程

    private Long historyId;
}
