package com.fytec.dto.agent;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AgentFlowHistoryQueryDTO {

    // 共享知识库时间范围
    @Schema(description = "响应时间(毫秒)")
    private Long runtimeStart;
    @Schema(description = "响应时间(毫秒)")
    private Long runtimeEnd;

    @Schema(description = "客户端id（通过访问令牌管理。/api/token/oauth_client/list接口")
    private String clientId;
    @Schema(description = "第三方用户")
    private String thirdUserId;
    @Schema(description = "AGENT_RUN_STATUS。运行结果:complete(\"运行完成\"), // 虽然自己成功，但是流没断，就不行。如果是涉及流程的，默认运行完成，单智能体的就默认成功\n" +
            "        embedded(\"运行中\"),\n" +
            "        success(\"运行成功\"),\n" +
            "        timeout(\"运行超时\"),\n" +
            "        failed(\"运行失败\");")
    private String status;

    @Schema(description = "智能体id。可以和智能体列表页面一样获取，前端支持根据名称过滤")
    private Long agentId;
    @Schema(description = "历史id")
    private Long historyId;

    @Schema(description = "开始时间 yyyy-MM-dd")
    private String startTimeStr;
    @Schema(hidden = true)
    private LocalDateTime startTime;

    @Schema(description = "结束时间 yyyy-MM-dd")
    private String endTimeStr;
    @Schema(hidden = true)
    private LocalDateTime endTime;



}
