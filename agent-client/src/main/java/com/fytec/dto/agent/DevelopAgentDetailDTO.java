package com.fytec.dto.agent;

import com.fytec.annotation.DictData;
import com.fytec.dto.AbstractDTO;
import com.fytec.dto.flow.WorkflowDTO;
import com.fytec.dto.knowledge.KnowledgeConfigDTO;
import com.fytec.dto.knowledge.KnowledgeDTO;
import com.fytec.dto.model.ModelDTO;
import com.fytec.dto.plugin.PluginDTO;
import com.fytec.dto.voice.VoiceDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class DevelopAgentDetailDTO extends AbstractDTO {

    @Schema(description = "智能体ID")
    @NotNull(message = "智能体ID不能为空")
    private Long agentId;

    @Schema(description = "智能体类型(agent_type)")
    private String type;

    @Schema(description = "开场白")
    private String prologue;

    @Schema(description = "默认问题")
    private List<String> tips;

    @Schema(description = "提示词")
    private String prompt;

    @Schema(description = "模型配置")
    private ModelDTO model;

    @Schema(description = "插件")
    private List<PluginDTO> plugins;

    @Schema(description = "工作流")
    private List<WorkflowDTO> workflows;

    @Schema(description = "知识库")
    private KnowledgeConfigDTO knowledge;

    @Schema(description = "用户问题建议")
    private SuggestionsDTO suggestions;

    @Schema(description = "语音")
    private VoiceDTO voice;

    private LocalDateTime updateTime;


    @Schema(description = "智能体名称")
    private String name;

    @Schema(description = "智能体介绍")
    private String description;

    @Schema(description = "智能体图标")
    private String logo;

    @DictData(target = "tagNames", codeType = "AGENT_TAGS")
    private String tags;

    private String tagNames;

    @Schema(description = "标注知识库")
    private KnowledgeConfigDTO positiveKnowledge;
    @Schema(description = "标注知识库")
    private KnowledgeConfigDTO negativeKnowledge;

    @Schema(description = "智能体参数配置")
    private AgentParamsDto params;

    @Schema(description = "历史知识库")
    private KnowledgeConfigDTO historyKnowledge;
}
