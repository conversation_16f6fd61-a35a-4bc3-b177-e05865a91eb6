package com.fytec.dto.plugin;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class PluginApiListDTO {
    private Long id;
    private Long pluginId;
    private Long resourceId;
    private String name;
    private String description;
    private String path;
    private String method;
    private String status;
    private String debugStatus;
    private boolean enable;
    private List<PluginApiRequestParamDTO> requestParams;
    private List<PluginApiResponseParamDTO> responseParams;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
