package com.fytec.dto.prompt;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fytec.annotation.DictData;
import com.fytec.dto.AbstractDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class PromptContentListDTO extends AbstractDTO {
    private Long id;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "适用类型")
    @DictData(codeType = "PROMPT_CONTENT_TYPE", target = "typeStr")
    private String type;
    private String typeStr;

    @Schema(description = "提示词详情")
    private String content;

    private String description;

    @Schema(description = "提示词分类")
    private String categories;

    private String createBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
