package com.fytec.dto.prompt;

import com.fytec.annotation.DictData;
import com.fytec.dto.AbstractDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class PromptContentDetailDTO extends AbstractDTO {
    private Long id;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "适用类型")
    @DictData(codeType = "PROMPT_CONTENT_TYPE", target = "typeStr")
    private String type;
    private String typeStr;

    @Schema(description = "提示词详情")
    private String content;

    private String description;

    @Schema(description = "提示词分类Id")
    private List<Long> categoryIds;

    @Schema(description = "提示词分类")
    private String categories;
}
