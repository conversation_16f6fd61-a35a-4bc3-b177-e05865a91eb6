package com.fytec.dto.prompt;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

@Data
public class PromptContentCreateDTO {
    @Schema(description = "名称")
    @NotBlank(message = "名称不能为空")
    private String name;

    @Schema(description = "适用类型（PROMPT_CONTENT_TYPE），多值用逗号分隔")
    private String type;

    @Schema(description = "提示词详情")
    @NotBlank(message = "提示词详情不能为空")
    private String content;

    private String description;

    @Schema(description = "提示词分类Id")
    private List<Long> categoryIds;
}
