package com.fytec.dto.llm;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class AiModelCreateDTO {
    @Schema(description = "模型组Id")
    private Long groupId;

    @Schema(description = "模型名称")
    private String name;

    @Schema(description = "模型参数")
    private String size;

    @Schema(description = "模型标识")
    private String code;

    @Schema(description = "模型标签")
    private String tags;

    @Schema(description = "模型描述")
    private String description;

    @Schema(description = "模型流式URL")
    private String streamUrl;

    @Schema(description = "非模型流式URL")
    private String nonStreamUrl;

    private boolean defaulted;

    private String diversityParams;

    private String ioParams;

    private String type = "text";
}
