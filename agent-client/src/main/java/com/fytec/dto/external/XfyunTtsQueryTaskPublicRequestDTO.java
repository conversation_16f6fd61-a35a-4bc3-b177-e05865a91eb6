package com.fytec.dto.external;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;

/**
 * 讯飞长文本语音合成查询任务对外请求DTO
 */
@Data
@Accessors(chain = true)
@Schema(description = "讯飞长文本语音合成查询任务请求")
public class XfyunTtsQueryTaskPublicRequestDTO {

    @Schema(description = "协议头部")
    private Header header;

    @Data
    @Accessors(chain = true)
    @Schema(description = "协议头部")
    public static class Header {
        @JsonProperty("app_id")
        @Schema(description = "应用唯一标识", requiredMode = Schema.RequiredMode.REQUIRED)
        private String appId;

        @JsonProperty("task_id")
        @Schema(description = "任务ID", requiredMode = Schema.RequiredMode.REQUIRED)
        private String taskId;
    }


}