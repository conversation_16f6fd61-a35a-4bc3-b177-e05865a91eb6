package com.fytec.dto.external;

import com.fytec.constant.ExternalConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 语音合成请求参数DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "语音合成请求参数")
public class TtsRequestDTO {

    @NotBlank(message = "合成文本不能为空")
    @Size(max = 2000, message = "文本长度不能超过2000个汉字")
    @Schema(description = "待合成的文本内容", example = "欢迎来到讯飞开放平台", required = true)
    private String text;

    @Schema(description = "音频编码格式", example = "raw",
            allowableValues = {"raw", "lame", "opus", "opus-wb", "speex-org-wb;7", "speex-org-nb;7", "speex;7", "speex-wb;7"})
    private String aue = ExternalConstants.XfyunStreamTtsDefaults.DEFAULT_AUE;

    @Schema(description = "流式返回mp3格式音频开关，需要配合aue=lame使用", example = "1", allowableValues = {"1"})
    private Integer sfl;

    @Schema(description = "音频采样率", example = "audio/L16;rate=16000", 
            allowableValues = {"audio/L16;rate=8000", "audio/L16;rate=16000"})
    private String auf = ExternalConstants.XfyunStreamTtsDefaults.DEFAULT_AUF;

    @Schema(description = "发音人参数", example = "xiaoyan")
    private String vcn = ExternalConstants.XfyunStreamTtsDefaults.DEFAULT_VCN;

    @Min(value = 0, message = "语速范围为0-100")
    @Max(value = 100, message = "语速范围为0-100")
    @Schema(description = "语速，范围0-100", example = "50", minimum = "0", maximum = "100")
    private Integer speed = ExternalConstants.XfyunStreamTtsDefaults.DEFAULT_SPEED;

    @Min(value = 0, message = "音量范围为0-100")
    @Max(value = 100, message = "音量范围为0-100")
    @Schema(description = "音量，范围0-100", example = "50", minimum = "0", maximum = "100")
    private Integer volume = ExternalConstants.XfyunStreamTtsDefaults.DEFAULT_VOLUME;

    @Min(value = 0, message = "音高范围为0-100")
    @Max(value = 100, message = "音高范围为0-100")
    @Schema(description = "音高，范围0-100", example = "50", minimum = "0", maximum = "100")
    private Integer pitch = ExternalConstants.XfyunStreamTtsDefaults.DEFAULT_PITCH;

    @Schema(description = "合成音频的背景音", example = "0", allowableValues = {"0", "1"})
    private Integer bgs = ExternalConstants.XfyunStreamTtsDefaults.DEFAULT_BGS;

    @Schema(description = "文本编码格式", example = "UTF8",
            allowableValues = {"GB2312", "GBK", "BIG5", "UNICODE", "GB18030", "UTF8"})
    private String tte = ExternalConstants.XfyunStreamTtsDefaults.DEFAULT_TTE;

    @Schema(description = "英文发音方式", example = "0", allowableValues = {"0", "1", "2"})
    private String reg = ExternalConstants.XfyunStreamTtsDefaults.DEFAULT_REG;

    @Schema(description = "合成音频数字发音方式", example = "0", allowableValues = {"0", "1", "2", "3"})
    private String rdn = ExternalConstants.XfyunStreamTtsDefaults.DEFAULT_RDN;

    @Schema(description = "超时时间（毫秒）", example = "600000")
    private Long timeout = ExternalConstants.XfyunStreamTtsDefaults.DEFAULT_REQUEST_TIMEOUT;
}
