package com.fytec.dto.external;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 音频文件信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AudioFileInfoDTO {
    
    /**
     * 音频时长（秒）
     */
    private Double duration;
    
    /**
     * 采样率（Hz）
     */
    private Integer sampleRate;
    
    /**
     * 声道数
     */
    private Integer channels;
    
    /**
     * 文件大小（字节）
     */
    private Long fileSize;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 音频格式
     */
    private String format;
    
    /**
     * 比特率
     */
    private Integer bitRate;
    
    /**
     * 每样本位数
     */
    private Integer bitsPerSample;

}