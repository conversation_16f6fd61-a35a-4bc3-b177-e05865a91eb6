package com.fytec.dto.external;

import com.fytec.annotation.DictData;
import com.fytec.dto.AbstractDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 外部计费每日统计结果 DTO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "外部计费每日统计结果")
public class ExternalBillingDailyStatsResultDTO extends AbstractDTO {

    /**
     * 能力服务编码
     */
    @Schema(description = "能力服务编码")
    @DictData(codeType = "EXTERNAL_SERVICE_TYPE", target = "serviceName")
    private String serviceCode;

    /**
     * 能力服务名称（通过字典翻译获得）
     */
    @Schema(description = "能力服务名称")
    private String serviceName;

    /**
     * 服务次数汇总
     */
    @Schema(description = "服务次数汇总")
    private Long serviceCount;

    /**
     * 使用时长汇总（分钟）
     */
    @Schema(description = "使用时长汇总（分钟）")
    private Long usageDurationMinutes;

    /**
     * 服务字数汇总
     */
    @Schema(description = "服务字数汇总")
    private Long serviceWordCount;

    /**
     * 使用次数汇总
     */
    @Schema(description = "使用次数汇总")
    private Long useCount;
}
