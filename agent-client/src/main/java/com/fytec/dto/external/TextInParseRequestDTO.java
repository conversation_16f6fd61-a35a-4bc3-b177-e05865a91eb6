package com.fytec.dto.external;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * TextIn文档解析请求DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "TextIn文档解析请求参数")
public class TextInParseRequestDTO {

    /**
     * 文件URL（用于URL方式解析）
     */
    @Schema(description = "要解析的文档文件URL地址", example = "https://example.com/document.pdf", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String fileUrl;

    /**
     * PDF密码（当PDF为加密文档时需要提供）
     */
    @JsonProperty("pdf_pwd")
    @Schema(description = "PDF密码，当PDF为加密文档时需要提供", example = "123456", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String pdfPwd;

    /**
     * 页码开始（PDF从第几页开始解析，从1开始）
     */
    @JsonProperty("page_start")
    @Schema(description = "页码开始，PDF从第几页开始解析，从1开始", example = "1", defaultValue = "1", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer pageStart;

    /**
     * 页码数量（要解析的PDF页数，默认1000页）
     */
    @JsonProperty("page_count")
    @Schema(description = "页码数量，要解析的PDF页数", example = "10", defaultValue = "1000", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer pageCount;

    /**
     * 解析模式（auto: 综合模式, scan: 仅文字识别模式）
     */
    @JsonProperty("parse_mode")
    @Schema(description = "解析模式", example = "auto", defaultValue = "auto", allowableValues = {"auto", "scan"}, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String parseMode;

    /**
     * DPI（PDF文档的坐标基准，默认144）
     */
    @Schema(description = "PDF文档的坐标基准DPI", example = "144", defaultValue = "144", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer dpi;

    /**
     * 标题层级（markdown中是否生成标题层级，默认1）
     */
    @JsonProperty("apply_document_tree")
    @Schema(description = "标题层级，markdown中是否生成标题层级", example = "1", defaultValue = "1", allowableValues = {"0", "1"}, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer applyDocumentTree;

    /**
     * 表格格式（md: markdown语法, html: html语法, none: 不识别表格）
     */
    @JsonProperty("table_flavor")
    @Schema(description = "表格格式", example = "md", defaultValue = "md", allowableValues = {"md", "html", "none"}, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String tableFlavor;

    /**
     * 图片获取（none: 不返回, page: 整页图像, objects: 子图像, both: 两者都返回）
     */
    @JsonProperty("get_image")
    @Schema(description = "图片获取方式", example = "none", defaultValue = "none", allowableValues = {"none", "page", "objects", "both"}, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String getImage;

    /**
     * 图像输出类型（base64str: base64字符串, default: 默认）
     */
    @JsonProperty("image_output_type")
    @Schema(description = "图像输出类型", example = "default", defaultValue = "default", allowableValues = {"base64str", "default"}, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String imageOutputType;

    /**
     * 图像分析（0: 不分析, 1: 分析）
     */
    @JsonProperty("apply_image_analysis")
    @Schema(description = "图像分析，0: 不分析, 1: 分析", example = "0", defaultValue = "0", allowableValues = {"0", "1"}, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer applyImageAnalysis;

    /**
     * 非正文文本模式（none: 不展示, annotation: 注释格式, body: 正文格式）
     */
    @JsonProperty("paratext_mode")
    @Schema(description = "非正文文本模式", example = "none", defaultValue = "none", allowableValues = {"none", "annotation", "body"}, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String paratextMode;

    /**
     * 公式识别等级（0: 全识别, 1: 仅行间公式, 2: 不识别）
     */
    @JsonProperty("formula_level")
    @Schema(description = "公式识别等级，0: 全识别, 1: 仅行间公式, 2: 不识别", example = "0", defaultValue = "0", allowableValues = {"0", "1", "2"}, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer formulaLevel;

    /**
     * 合并段落表格（0: 不合并, 1: 合并）
     */
    @JsonProperty("apply_merge")
    @Schema(description = "合并段落表格，0: 不合并, 1: 合并", example = "0", defaultValue = "0", allowableValues = {"0", "1"}, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer applyMerge;

    /**
     * Markdown详细信息（0: 不返回, 1: 返回detail字段）
     */
    @JsonProperty("markdown_details")
    @Schema(description = "Markdown详细信息，0: 不返回, 1: 返回detail字段", example = "0", defaultValue = "0", allowableValues = {"0", "1"}, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer markdownDetails;

    /**
     * 页面详细信息（0: 不返回, 1: 返回pages字段）
     */
    @JsonProperty("page_details")
    @Schema(description = "页面详细信息，0: 不返回, 1: 返回pages字段", example = "0", defaultValue = "0", allowableValues = {"0", "1"}, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer pageDetails;

    /**
     * 原始OCR结果（0: 不返回, 1: 返回）
     */
    @JsonProperty("raw_ocr")
    @Schema(description = "原始OCR结果，0: 不返回, 1: 返回", example = "0", defaultValue = "0", allowableValues = {"0", "1"}, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer rawOcr;

    /**
     * 字符位置（0: 不返回, 1: 返回字符位置信息）
     */
    @JsonProperty("char_details")
    @Schema(description = "字符位置，0: 不返回, 1: 返回字符位置信息", example = "0", defaultValue = "0", allowableValues = {"0", "1"}, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer charDetails;

    /**
     * 目录信息（0: 不返回, 1: 返回catalog字段）
     */
    @JsonProperty("catalog_details")
    @Schema(description = "目录信息，0: 不返回, 1: 返回catalog字段", example = "0", defaultValue = "0", allowableValues = {"0", "1"}, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer catalogDetails;

    /**
     * Excel结果（0: 不返回, 1: 返回excel_base64字段）
     */
    @JsonProperty("get_excel")
    @Schema(description = "Excel结果，0: 不返回, 1: 返回excel_base64字段", example = "0", defaultValue = "0", allowableValues = {"0", "1"}, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer getExcel;

    /**
     * 切边矫正（0: 不进行, 1: 进行切边矫正）
     */
    @JsonProperty("crop_image")
    @Schema(description = "切边矫正，0: 不进行, 1: 进行切边矫正", example = "0", defaultValue = "0", allowableValues = {"0", "1"}, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer cropImage;

    /**
     * 去水印（0: 不去水印, 1: 去水印）
     */
    @JsonProperty("remove_watermark")
    @Schema(description = "去水印，0: 不去水印, 1: 去水印", example = "0", defaultValue = "0", allowableValues = {"0", "1"}, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer removeWatermark;

    /**
     * 图表识别（0: 不开启, 1: 开启图表识别）
     */
    @JsonProperty("apply_chart")
    @Schema(description = "图表识别，0: 不开启, 1: 开启图表识别", example = "0", defaultValue = "0", allowableValues = {"0", "1"}, requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer applyChart;
}