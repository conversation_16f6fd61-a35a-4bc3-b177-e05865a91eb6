package com.fytec.dto.external;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;

/**
 * 讯飞长文本语音合成创建任务对外请求DTO
 */
@Data
@Accessors(chain = true)
@Schema(description = "讯飞长文本语音合成创建任务请求")
public class XfyunTtsCreateTaskPublicRequestDTO {

    @Schema(description = "协议头部")
    private Header header;

    @Schema(description = "AI特性参数")
    private Parameter parameter;

    @Schema(description = "数据段")
    private Payload payload;

    @Data
    @Accessors(chain = true)
    @Schema(description = "协议头部")
    public static class Header {
        @JsonProperty("app_id")
        @Schema(description = "应用唯一标识", requiredMode = Schema.RequiredMode.REQUIRED)
        private String appId;

        @JsonProperty("callback_url")
        @Schema(description = "任务结果回调服务地址")
        private String callbackUrl;

        @JsonProperty("request_id")
        @Schema(description = "客户端用于标记任务的唯一id，最大长度64字符")
        private String requestId;
    }

    @Data
    @Accessors(chain = true)
    @Schema(description = "AI特性参数")
    public static class Parameter {
        @Schema(description = "TTS服务参数")
        private Dts dts;
    }

    @Data
    @Accessors(chain = true)
    @Schema(description = "TTS服务参数")
    public static class Dts {
        @Schema(description = "发音人", requiredMode = Schema.RequiredMode.REQUIRED, example = "x3_mingge")
        private String vcn;

        @Schema(description = "合成文本语言", allowableValues = {"zh", "en"}, defaultValue = "zh")
        private String language;

        @Schema(description = "语速", minimum = "0", maximum = "100", defaultValue = "50")
        private Integer speed;

        @Schema(description = "音量", minimum = "0", maximum = "100", defaultValue = "50")
        private Integer volume;

        @Schema(description = "音调", minimum = "0", maximum = "100", defaultValue = "50")
        private Integer pitch;

        @Schema(description = "是否读出标点", allowableValues = {"0", "1"}, defaultValue = "0")
        private Integer ram;

        @Schema(description = "控制是否返回拼音标注", allowableValues = {"0", "1"}, defaultValue = "0")
        private Integer rhy;

        @Schema(description = "音频格式配置")
        private Audio audio;

        @Schema(description = "拼音标注配置")
        private Pybuf pybuf;
    }

    @Data
    @Accessors(chain = true)
    @Schema(description = "音频格式配置")
    public static class Audio {
        @JsonProperty("encoding")
        @Schema(description = "音频编码", requiredMode = Schema.RequiredMode.REQUIRED,
                allowableValues = {"raw", "lame", "opus", "opus-wb", "speex-org-nb;8", "speex-org-wb;8", "speex;7", "speex-wb;7"})
        private String encoding;

        @JsonProperty("sample_rate")
        @Schema(description = "采样率", allowableValues = {"16000", "8000", "24000"}, defaultValue = "16000")
        private Integer sampleRate;
    }

    @Data
    @Accessors(chain = true)
    @Schema(description = "拼音标注配置")
    public static class Pybuf {
        @JsonProperty("encoding")
        @Schema(description = "文本编码", allowableValues = {"utf8", "gb2312"}, defaultValue = "utf8")
        private String encoding;

        @JsonProperty("compress")
        @Schema(description = "文本压缩格式", allowableValues = {"raw"}, defaultValue = "raw")
        private String compress;

        @JsonProperty("format")
        @Schema(description = "文本格式", allowableValues = {"plain"}, defaultValue = "plain")
        private String format;
    }

    @Data
    @Accessors(chain = true)
    @Schema(description = "数据段")
    public static class Payload {
        @Schema(description = "输入文本")
        private Text text;
    }

    @Data
    @Accessors(chain = true)
    @Schema(description = "输入文本")
    public static class Text {
        @Schema(description = "文本编码", requiredMode = Schema.RequiredMode.REQUIRED, allowableValues = {"utf8", "gb2312"}, defaultValue = "utf8")
        private String encoding;

        @Schema(description = "文本压缩格式", allowableValues = {"raw", "gzip"}, defaultValue = "raw")
        private String compress;

        @Schema(description = "文本格式", allowableValues = {"plain", "json", "xml"}, defaultValue = "plain")
        private String format;

        @Schema(description = "文本数据，最大支持10万字符，文本大小：0-1M", requiredMode = Schema.RequiredMode.REQUIRED)
        private String text;
    }


}