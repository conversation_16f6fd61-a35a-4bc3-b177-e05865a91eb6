package com.fytec.dto.external;

import com.fytec.constant.ExternalConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * 语音听写请求参数DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "语音听写请求参数")
public class IatRequestDTO {

    @Schema(description = "语种", example = "zh_cn",
            allowableValues = {"zh_cn", "en_us"})
    private String language = ExternalConstants.XfyunStreamIatDefaults.DEFAULT_LANGUAGE;

    @Schema(description = "应用领域", example = "iat",
            allowableValues = {"iat", "xfime-mianqie", "medical", "gov-seat-assistant", "seat-assistant", 
                              "gov-ansys", "gov-nav", "fin-nav", "fin-ansys"})
    private String domain = ExternalConstants.XfyunStreamIatDefaults.DEFAULT_DOMAIN;

    @Schema(description = "方言", example = "mandarin")
    private String accent = ExternalConstants.XfyunStreamIatDefaults.DEFAULT_ACCENT;

    @Min(value = 1000, message = "后端点检测静默时间范围为1000-10000毫秒")
    @Max(value = 10000, message = "后端点检测静默时间范围为1000-10000毫秒")
    @Schema(description = "后端点检测静默时间（毫秒），即静默多长时间后引擎认为音频结束", 
            example = "2000", minimum = "1000", maximum = "10000")
    private Integer vadEos = ExternalConstants.XfyunStreamIatDefaults.DEFAULT_VAD_EOS;

    @Schema(description = "动态修正（仅中文普通话支持）", example = "wpgs", allowableValues = {"wpgs"})
    private String dwa;

    @Schema(description = "领域个性化参数（仅中文支持）", example = "game",
            allowableValues = {"game", "health", "shopping", "trip"})
    private String pd;

    @Schema(description = "是否开启标点符号添加", example = "1", allowableValues = {"0", "1"})
    private Integer ptt = ExternalConstants.XfyunStreamIatDefaults.DEFAULT_PTT;

    @Schema(description = "标点返回位置控制，开启后标点会缓存到下一句句首返回", 
            example = "1", allowableValues = {"0", "1"})
    private Integer pcm = ExternalConstants.XfyunStreamIatDefaults.DEFAULT_PCM;

    @Schema(description = "字体（仅中文支持）", example = "zh-cn", 
            allowableValues = {"zh-cn", "zh-hk"})
    private String rlang = ExternalConstants.XfyunStreamIatDefaults.DEFAULT_RLANG;

    @Schema(description = "返回子句结果对应的起始和结束的端点帧偏移值", 
            example = "0", allowableValues = {"0", "1"})
    private Integer vinfo = ExternalConstants.XfyunStreamIatDefaults.DEFAULT_VINFO;

    @Schema(description = "将返回结果的数字格式规则为阿拉伯数字格式（中文普通话和日语支持）", 
            example = "1", allowableValues = {"0", "1"})
    private Integer nunum = ExternalConstants.XfyunStreamIatDefaults.DEFAULT_NUNUM;

    @Schema(description = "speex音频帧长，仅在speex音频时使用", example = "70")
    private Integer speexSize;

    @Min(value = 1, message = "句子多候选范围为1-5")
    @Max(value = 5, message = "句子多候选范围为1-5")
    @Schema(description = "句子多候选，获取在发音相似时的句子多候选结果", 
            example = "1", minimum = "1", maximum = "5")
    private Integer nbest;

    @Min(value = 1, message = "词语多候选范围为1-5")
    @Max(value = 5, message = "词语多候选范围为1-5")
    @Schema(description = "词语多候选，获取在发音相似时的词语多候选结果", 
            example = "1", minimum = "1", maximum = "5")
    private Integer wbest;

    @Schema(description = "音频采样率", example = "audio/L16;rate=16000",
            allowableValues = {"audio/L16;rate=8000", "audio/L16;rate=16000"})
    private String format = ExternalConstants.XfyunStreamIatDefaults.DEFAULT_FORMAT;

    @Schema(description = "音频数据格式", example = "raw",
            allowableValues = {"raw", "speex", "speex-wb", "lame"})
    private String encoding = ExternalConstants.XfyunStreamIatDefaults.DEFAULT_ENCODING;

    @Schema(description = "超时时间（毫秒）", example = "600000")
    private Long timeout = ExternalConstants.XfyunStreamIatDefaults.DEFAULT_REQUEST_TIMEOUT;
}
