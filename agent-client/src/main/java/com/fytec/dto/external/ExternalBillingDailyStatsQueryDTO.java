package com.fytec.dto.external;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

/**
 * 外部计费每日统计查询参数 DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "外部计费每日统计查询参数")
public class ExternalBillingDailyStatsQueryDTO {

    /**
     * 开始日期
     */
    @Schema(description = "开始日期", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @Schema(description = "结束日期", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate endDate;
}
