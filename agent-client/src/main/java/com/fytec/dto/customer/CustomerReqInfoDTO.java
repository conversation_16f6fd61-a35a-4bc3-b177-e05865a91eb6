package com.fytec.dto.customer;


import com.fytec.config.validate.Mandatory;
import com.fytec.config.validate.Optional;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.util.List;

@Data
public class CustomerReqInfoDTO {
    private String customerCompanyName;
    private String customerName;
    private String customerTel;
    private String customerReqInfo;
}
