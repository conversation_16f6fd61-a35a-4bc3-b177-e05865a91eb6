package com.fytec.dto.resource;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class AddResourceDTO {

    @Schema(description = "名称")
    @NotBlank(message = "名称不能为空")
    @Size(max = 20, message = "名称最大长度30")
    private String name;

    @Schema(description = "描述")
    @Size(max = 600, message = "描述最大长度600")
    private String description;


    @Schema(description = "图标")
    private String logo;

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "类型")
    @NotBlank(message = "类型不能为空")
    private String type;

    private Long parentId;

    // 知识库相关字段开始
    @Schema(description = "知识库类型：personal, team")
    private String knowledgeType;

    @Schema(description = "向量模型")
    private Long embeddedId = 2L;

    @Schema(description = "知识库种类：common,positive,rule,negative")
    private String knowledgeBaseType = "common";

    // 插件相关字段开始
    @Schema(description = "插件类型(http/mcp)")
    private String pluginType;

    @Schema(description = "插件URL")
    private String pluginUrl;

    @Schema(description = "插件header")
    private List<Map<String, String>> pluginHeaders;

    @Schema(description = "授权方式")
    private String pluginAuthType;

    @Schema(description = "授权信息")
    private String pluginAuthInfo;

    //  数据库相关字段开始
    @Schema(description = "数据库类型")
    private String dbType;

    @Schema(description = "参数信息：host、port、schema、username、password")
    private Map<String,String> dbConfigs;

}
