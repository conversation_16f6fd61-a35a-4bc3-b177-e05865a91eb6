package com.fytec.dto.resource;

import com.fytec.annotation.DictData;
import com.fytec.dto.AbstractDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ResourceDTO extends AbstractDTO {

    private Long id;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "图片")
    private String logo;

    @Schema(description = "类型：workflow、dialogue、plugin")
    @DictData(target = "typeStr",codeType = "RESOURCE_TYPE")
    private String type;

    private String typeStr;

    @Schema(description = "编辑时间")
    private LocalDateTime updateTime;

    @Schema(description = "状态：0-未发布，1-已发布")
    private String status;

    private String pluginType;

}
