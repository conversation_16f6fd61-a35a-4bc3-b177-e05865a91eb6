package com.fytec.dto.resource;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class QueryResourceDTO {

    @Schema(description = "资源名称")
    private String name;

    @Schema(description = "资源类型,多个逗号分隔")
    private String types;

    @Schema(description = "资源状态：0-未发布，1-已发布")
    private String status;

    @Schema(hidden = true)
    private Long createBy;

    @Schema(description = "知识库种类,如果types为knowledge")
    private String knowledgeBaseType;
}
