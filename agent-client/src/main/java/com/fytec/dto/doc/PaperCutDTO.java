package com.fytec.dto.doc;

import lombok.Data;

/**
 * 试卷切题解析请求参数
 *
 * <AUTHOR>
 */
@Data
public class PaperCutDTO {

    /**
     * 图像数据，base64编码后进行urlencode，要求base64编码和urlencode后大小不超过10M，最短边至少15px，最长边最大8192px，支持jpg/jpeg/png/bmp格式
     * 优先级：image > url > pdf_file，当image字段存在时，url、pdf_file字段失效
     */
    private String image;

    /**
     * 图片完整url，url长度不超过1024字节，url对应的图片base64编码后大小不超过10M，最短边至少15px，最长边最大8192px，支持jpg/jpeg/png/bmp格式
     * 优先级：image > url > pdf_file，当image字段存在时，url字段失效
     * 请注意关闭URL防盗链
     */
    private String url;


    /**
     * PDF文件，base64编码后进行urlencode，要求base64编码和urlencode后大小不超过10M，最短边至少15px，最长边最大8192px
     * 优先级：image > url > pdf_file，当image、url字段存在时，pdf_file字段失效
     */
    private String pdf_file;

    /**
     * 需要识别的PDF文件的对应页码，当 pdf_file 参数有效时，识别传入页码的对应页面内容，若不传入，则默认识别第 1 页
     */
    private String pdf_file_num;

    /**
     * 识别语言类型，默认为CHN_ENG。可选值包括：
     * = CHN_ENG：中英文
     * = ENG：英文，纯英文场景下建议开启
     */
    private String language_type;

    /**
     * 是否检测图像朝向，默认不检测，即：false。朝向是指输入图像是正常方向、逆时针旋转90/180/270度。可选值包括：
     * true ：检测朝向，输入非正向图片时建议开启
     * false：不检测朝向
     */
    private String detect_direction;

    /**
     * 识别文字类型，默认为手写印刷混排识别，即：handprint_mix。可选值包括：
     * = handprint_mix：手写印刷混排
     * = handwring_only：手写，纯手写场景下建议开启
     */
    private String words_type;

    /**
     * 是否拼接题目元素内每行的文本信息后输出，默认不拼接，即：false。开启该参数后，处理耗时预计会增加 1s。可选值包括：
     * true ：拼接题目元素每行的文本信息，在elem_text 内输出；
     * false：不拼接，仅按行输出文本信息
     */
    private String splice_text;

}
