package com.fytec.dto.doc;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class DocParseDTO implements Serializable {

//    @NotBlank(message = "文件类型不能为空")
    @Schema(description = "文件类型,如pdf、doc、docx、txt、md")
    private String fileType;

    @Schema(description = "文件字节")
    private byte[] fileBytes;

    @Schema(description = "Base64文件编码")
    private String fileBase64;

    private String fileUrl;
}
