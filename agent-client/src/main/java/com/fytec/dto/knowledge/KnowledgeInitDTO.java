package com.fytec.dto.knowledge;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class KnowledgeInitDTO {
    @Schema(description = "知识库名称")
    private String name;
    @Schema(description = "知识库描述")
    private String description;
    @Schema(description = "知识库logo")
    private String logo;
    @Schema(description = "知识库类型（个人知识库：personal、团队知识库：team）")
    private String knowledgeType;

    private String userId;

    private String expireOption;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
}
