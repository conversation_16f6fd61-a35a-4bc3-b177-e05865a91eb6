package com.fytec.dto.knowledge;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fytec.annotation.DictData;
import com.fytec.dto.AbstractDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class KnowledgeDocDTO extends AbstractDTO {

    private Long id;

    @Schema(description = "知识id")
    private Long knowledgeId;

    private Long groupId;
    private String groupName;

    @Schema(description = "添加文档方式（KNOWLEDGE_DOC_TYPE）")
    @DictData(target = "docTypeStr", codeType = "KNOWLEDGE_DOC_TYPE")
    private String docType;
    private String docTypeStr;

    @DictData(target = "docSourceTypeStr", codeType = "KNOWLEDGE_DOC_SOURCE_TYPE")
    private String docSourceType;
    private String docSourceTypeStr;


    @Schema(description = "文件名")
    private String fileName;

    @Schema(description = "文件类型")
    private String fileType;

    @Schema(description = "文件url")
    private String fileUrl;

    private String fileContent;

    @Schema(description = "文件大小")
    private Long fileSize;

    @Schema(description = "文件状态")
    @DictData(target = "statusStr", codeType = "KNOWLEDGE_DOC_STATUS")
    private String status;
    private String statusStr;

    private String createBy;

    private String createName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    private boolean defaultSegment;

    private Long noteId;

}
