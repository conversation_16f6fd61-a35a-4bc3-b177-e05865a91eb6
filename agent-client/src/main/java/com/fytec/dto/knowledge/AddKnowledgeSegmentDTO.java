package com.fytec.dto.knowledge;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class AddKnowledgeSegmentDTO {

    @Schema(description = "知识库ID")
    @NotNull(message = "知识库ID不能为空")
    private Long knowledgeId;

    @Schema(description = "知识文档id")
    @NotNull(message = "知识文档id不能为空")
    private Long docId;

    @Schema(description = "分段内容")
    @NotBlank(message = "分段内容不能为空")
    private String text;

    private int vectorIndex;



//    @Schema(description = "标签内容(不超过200字)")
//    private String tag;

    @Schema(description = "概述(不超过500字)")
    private String overview;
}
