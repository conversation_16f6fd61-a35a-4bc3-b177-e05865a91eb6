package com.fytec.dto.knowledge.deprecated;

import com.fytec.constant.Constants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Deprecated
public class AddKnowledgeDocDTO {
    @Schema(description = "知识库id")
    @NotNull(message = "知识库id不能为空")
    private Long knowledgeId;

    @Schema(description = "添加文档类型（KNOWLEDGE_DOC_TYPE）")
    @NotBlank(message = "添加文档类型不能为空")
    private String docType;

    @Schema(description = "添加文档方式（KNOWLEDGE_DOC_SOURCE_TYPE）")
    @NotBlank(message = "添加文档方式不能为空")
    private String docSourceType;

    @Schema(description = "文件名")
    @NotBlank(message = "文件名不能为空")
    private String fileName;

    @Schema(description = "文件类型")
    private String fileType;

    @Schema(description = "文件内容")
    private String fileContent;

    @Schema(description = "文件url")
    private String fileUrl;

    @Schema(description = "文件大小")
    private Long fileSize;
}
