package com.fytec.dto.knowledge;

import com.fytec.annotation.DictData;
import com.fytec.entity.knowledge.KnowledgeDoc;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class KnowledgeDTO {

    private Long id;

    private Long resourceId;

    private String logo;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "文档类型")
    private String type;

    private int topK;
    private String strategy;
    private float minScore;
    private Integer minDocLen = 0;

    private String baseType;

    private Float weight;

}
