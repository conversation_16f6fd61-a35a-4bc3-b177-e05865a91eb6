package com.fytec.dto.knowledge.deprecated;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Deprecated
public class SegmentConfigDTO {

    @Schema(description = "文档id")
    private Long docId;

    @Schema(description = "是否自动分段")
    private Boolean autoSegment;

    @Schema(description = "分段策略")
    private String segmentStrategy;

    @Schema(description = "自定义分段字符")
    private String customizeChar;

    @Schema(description = "最大分段长度")
    private int maxLength;

    @Schema(description = "分段重叠度%")
    private int overlapLength;
}
