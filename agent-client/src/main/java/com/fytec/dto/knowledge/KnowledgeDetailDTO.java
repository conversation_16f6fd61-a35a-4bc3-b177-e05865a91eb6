package com.fytec.dto.knowledge;

import com.fytec.annotation.DictData;
import com.fytec.dto.Node;
import com.fytec.entity.knowledge.KnowledgeDoc;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class KnowledgeDetailDTO extends Node {

    private Long id;

    private Long resourceId;

    private String logo ;

    @Schema(description = "名称")
    private String name;

    private String knowledgeType;

    @Schema(description = "描述")
    private String description;

    private boolean systemResource;

    @Schema(description = "总文档大小")
    private long totalSize;

    @Schema(description = "总文档数量")
    private long totalDoc;

    @Schema(description = "总分段数量")
    private long totalSegment;

    @Schema(description = "知识库文档")
    private List<KnowledgeDoc> docs;

    @Schema(description = "知识库种类")
    private String knowledgeBaseType;
}
