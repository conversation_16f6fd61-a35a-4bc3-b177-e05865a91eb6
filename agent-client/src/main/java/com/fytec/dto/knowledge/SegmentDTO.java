package com.fytec.dto.knowledge;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class SegmentDTO {

    private Long knowledgeId;

    @Schema(description = "知识文档id")
    private Long docId;

    @TableField(value = "page_number")
    private Long pageNumber;

    @Schema(description = "分段内容")
    private String text;

    @Schema(description = "向量ID")
    private String vectorId;

    private int vectorIndex;
}
