package com.fytec.dto.knowledge;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;

@Data
public class UpdateKnowledgeSegmentDTO{
    private Long id;

    @Schema(description = "分段内容")
    @NotBlank(message = "分段内容不能为空")
    @Max(value = 1000, message = "分段内容不能超过1000个字符")
    private String text;

    @Schema(description = "分段概要")
    @Max(value = 1000, message = "分段概要不能超过1000个字符")
    private String overview;
}
