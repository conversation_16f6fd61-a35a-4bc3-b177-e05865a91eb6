package com.fytec.dto.knowledge;

import io.swagger.v3.oas.annotations.Hidden;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class KnowLedgeDocSearchDTO implements Serializable {
    private Long conversationName;//会话id
    private List<Long> knowledgeIds;//知识库id
    private String query;//用户搜索
    private Map<String, Object> configs;// 查询的一些配置
    private Integer topK;//查询多少条
    private Boolean searchHistory;//是否启用上下文
    private Float minScore;
    private String docSourceType;

    @Hidden
    private String searchType;//搜索类型，默认
    @Hidden
    private String rerankerType;//重排算法，如果非空的话，Weight和RRF，
    private Integer minDocLen = 0;//最小文档数，如果minDocLen大于0且实际查出来的文档小于这个参数就视为不调用大模型

    @Hidden
    private List<Map<String, String>> history;//带有上下文历史的查询


    private String filter;

    private Map<Long, String> knowledgeFilterMap; // 知识库id和对应的过滤条件

}
