package com.fytec.dto.knowledge;

import com.fytec.entity.llm.EmbeddedModel;
import com.fytec.satoken.StpClientUserUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class KnowledgeDocAutoDTO {
    @Schema(description = "知识库id")
    @NotNull(message = "知识库id不能为空")
    private Long knowledgeId;

    @Schema(description = "添加文档方式（KNOWLEDGE_DOC_SOURCE_TYPE）")
    @NotBlank(message = "添加文档方式不能为空")
    private String docSourceType;

    @Schema(description = "文件")
    private List<KnowledgeFileDTO> files;

    @Schema(hidden = true)
    private KnowledgeFileDTO file;

    @Schema(description = "嵌入模型", hidden = true)
    private EmbeddedModel embeddedModel;

    @Schema(description = "集合名称", hidden = true)
    private String collectionName;

    @Schema(description = "用户id", hidden = true)
    private String userId;

    @Schema(description = "客户端id", hidden = true)
    private String clientId;

    @Schema(description = "true - 默认模式/ false - 高级模式")
    private boolean defaultSegment = true;

    @Schema(description = "是否自动分段")
    private boolean autoSegment = true;

    //高级模式相关参数
    @Schema(description = "文本-解析策略")
    private String parseStrategy = "advance";

    @Schema(description = "分段策略")
    private String segmentStrategy;

    @Schema(description = "自定义分段字符")
    private String customizeChar;

    @Schema(description = "最大分段长度")
    private int maxLength;

    @Schema(description = "分段重叠度%")
    private int overlapLength;

    @Schema(hidden = true)
    private boolean preview = false;

    @Schema(description = "是否开启增强")
    private boolean docEnhancement = false;

    @Schema(description = "增强类型")
    private String docEnhancementStrategy;

    @Schema(description = "概述")
    private String overview;

    @Schema(description = "第三方用户ID")
    private String thirdUserId;

    public void setUserId(String userId, String clientId) {
        this.userId = userId;
        this.clientId = clientId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
        this.clientId = StpClientUserUtil.getClientId();
    }

    public void setThirdUserId(String userId) {
        this.thirdUserId = userId;
        this.clientId = StpClientUserUtil.getClientId();
        this.userId = StpClientUserUtil.getLoginIdAsString();
    }
}
