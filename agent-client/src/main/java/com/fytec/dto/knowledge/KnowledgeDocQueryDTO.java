package com.fytec.dto.knowledge;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class KnowledgeDocQueryDTO {
    @Schema(description = "知识库id")
    private Long knowledgeId;

    @Schema(description = "知识库组id")
    private Long groupId;

    @Schema(description = "文档id,多个逗号分割")
    private String docIds;

    private String keywords;

    private boolean successFilter = false;

    @Schema(hidden = true)
    private Long createBy;

    private String filterContent;

}
