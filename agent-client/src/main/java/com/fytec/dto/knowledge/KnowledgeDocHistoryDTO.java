package com.fytec.dto.knowledge;

import com.fytec.dto.AbstractDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class KnowledgeDocHistoryDTO extends AbstractDTO {
    private Long id;

    @Schema(description = "知识id")
    private Long knowledgeId;

    private String docSourceType;

    @Schema(description = "文件名")
    private String fileName;

    @Schema(description = "文件url")
    private String fileUrl;

    private String fileType;

    private Long fileSize;;
}
