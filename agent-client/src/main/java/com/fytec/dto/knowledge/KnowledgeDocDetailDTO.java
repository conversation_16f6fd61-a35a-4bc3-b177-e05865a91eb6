package com.fytec.dto.knowledge;

import com.fytec.entity.knowledge.KnowledgeSegment;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class KnowledgeDocDetailDTO extends KnowledgeDocDTO {

    @Schema(description = "文件内容")
    private String fileContent;

    @Schema(description = "是否自动分段")
    private Boolean autoSegment;

    @Schema(description = "分段策略")
    private String segmentStrategy;

    @Schema(description = "自定义分段字符")
    private String customizeChar;

    @Schema(description = "最大分段长度")
    private int maxLength;

    @Schema(description = "分段重叠度%")
    private int overlapLength;

    @Schema(description = "分段结果")
    private List<KnowledgeSegment> segments;
}
