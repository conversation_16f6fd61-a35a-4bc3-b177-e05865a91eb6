package com.fytec.dto.knowledge;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class KnowledgeConfigDTO {
    private int topK;
    private String strategy;
    private float minScore;
    private List<KnowledgeDTO> knowledgeList;
    private Integer minDocLen = 0;//最小文档数，如果minDocLen大于0且实际查出来的文档小于这个参数就视为不调用大模型

    @Schema(description = "重排模型名称，如果存在，会进行重排")
    private String rerankerType; // sip和gte两种目前，RERANKER_TYPE
    private Boolean rerank;
}
