package com.fytec.dto.knowledge.group;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class KnowledgeGroupDTO {
    private Long id;

    private Long parentId;

    @NotBlank(message = "名称不能为空")
    @Schema(description = "名称")
    private String name;

    @Schema(description = "类型")
    private String type;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "封面")
    private String logo;

    private String expireOption;

    // 共享知识库时间范围
    @Schema(description = "有效期开始时间")
    private String startTimeStr;
    @Schema(hidden = true)
    private LocalDateTime startTime;

    @Schema(description = "有效期结束时间")
    private String endTimeStr;
    @Schema(hidden = true)
    private LocalDateTime endTime;

    @Schema(hidden = true)
    private Long knowledgeId;

    @Schema(hidden = true)
    private String userId;
}
