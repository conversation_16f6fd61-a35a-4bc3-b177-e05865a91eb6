package com.fytec.dto.knowledge;

import com.fytec.constant.Constants;
import com.fytec.entity.knowledge.KnowledgeDoc;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class KnowledgeFileDTO {
    private Long noteId;
    private Long groupId;

    @Schema(description = "添加文档类型（KNOWLEDGE_DOC_TYPE）")
    @NotBlank(message = "添加文档类型不能为空")
    private String docType;

    @Schema(description = "添加文档类型枚举", hidden = true)
    private Constants.KNOWLEDGE_DOC_TYPE docTypeEnum;

    @Schema(description = "文件名")
    private String fileName;

    @Schema(description = "文件类型")
    private String fileType;

    @Schema(description = "文件内容")
    private String fileContent;

    @Schema(description = "文件url")
    private String fileUrl;

    @Schema(description = "文件大小")
    private Long fileSize;

    private boolean visible = true;

    @Schema(description = "知识库文档对象", hidden = true)
    private KnowledgeDoc doc;
}
