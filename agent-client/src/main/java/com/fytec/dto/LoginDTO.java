package com.fytec.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class LoginDTO {
    @Schema(description = "名称")
    @NotBlank(message = "名称不能为空")
    private String name;

    @Schema(description = "密码")
    @NotBlank(message = "密码不能为空")
    private String password;

    private String loginType;

    @Schema(description = "图形验证码")
    private String captchaCode;

    @Schema(description = "图形验证码ID")
    private String captchaCodeId;


}
