package com.fytec.dto;

import com.fytec.dto.system.SysMenuTreeDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class CurrentUserDTO {

    private Long id;
    private String thirdUserId;

    @Schema(description = "用户名")
    private String name;

    @Schema(description = "登录名")
    private String loginName;

    @Schema(description = "手机号")
    private String tel;

    @Schema(description = "头像")
    private String imageUrl;

    @Schema(description = "状态值")
//    @DictData(codeType = "COMMON_STATUS", target = "statusStr")
    private String status;

    @Schema(description = "状态名")
    private String statusStr;

    @Schema(description = "角色")
    private List<String> roles;

    private List<String> groups;

    @Schema(description = "菜单")
    private List<SysMenuTreeDTO> menus;

}
