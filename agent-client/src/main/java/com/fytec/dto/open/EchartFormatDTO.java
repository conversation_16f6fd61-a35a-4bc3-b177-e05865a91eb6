package com.fytec.dto.open;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.HashMap;

@Data
public class EchartFormatDTO {


    @Schema(description = "回答的历史id", required = true)
    private Long historyId;

    @Schema(description = "图表类型。目前就bar、pie、line,三种格式", required = true)
    private String type;

    @Schema(description = "数据库链接方式，如果不传，根据实际执行的sql节点的处理类里的sql配置连接数据库", required = false)
    private HashMap<String, String> configs = new HashMap<>();
}
