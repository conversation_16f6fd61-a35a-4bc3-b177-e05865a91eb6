package com.fytec.dto.system;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 * @version version 1.0
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SysMenuUpdateDTO extends SysMenuCreateDTO {

    @Schema(description = "菜单ID")
    @NotBlank(message = "菜单ID不能为空")
    private Long id;
}
