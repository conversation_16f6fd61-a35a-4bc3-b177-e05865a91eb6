package com.fytec.dto.system;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fytec.dto.Node;
import com.fytec.entity.system.SysUser;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version version 1.0
 */
@Data
public class SysOrganizationTreeDTO extends Node {
    @Schema(description = "组名称")
    private String name;

    private String code;

    private List<JSONObject> users;
}
