package com.fytec.dto.system;

import com.fytec.config.validate.Mandatory;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 * @version version 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysUserUpdateDTO extends SysUserCreateDTO {
    @Schema(description = "用户ID")
    @NotBlank(message = "用户ID不能为空", groups = {Mandatory.class})
    private Long id;
}
