package com.fytec.dto.system;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;


/**
 * <AUTHOR>
 * @version version 1.0
 */
@Data
public class SysMenuCreateDTO {
    @Schema(description = "父节点ID")
    private Long parentId;

    @Schema(description = "菜单名称")
    @NotBlank(message = "菜单名称不能为空")
    private String menuName;

    @Schema(description = "菜单类型")
    @NotBlank(message = "菜单类型不能为空")
    private String menuType;

    @Schema(description = "菜单图标")
    private String menuIcon;

    @Schema(description = "排序")
    private Integer menuSort;

    @Schema(description = "前端组件")
    private String menuComponent;

    @Schema(description = "前端地址")
    private String menuHref;

    @Schema(description = "权限标识")
    private String menuPermission;
}
