package com.fytec.dto.system;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version version 1.0
 */
@Data
public class SysRoleCreateDTO {

    @Schema(description = "角色标识")
    @NotBlank(message = "角色不能为空}")
    private String role;

    @Schema(description = "角色名称")
    @NotBlank(message = "角色名称不能为空")
    private String roleName;

    @Schema(description = "角色描述")
    private String roleDesc;

    @Schema(description = "菜单权限")
    private List<Long> menuIds;
}
