package com.fytec.dto.system;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fytec.annotation.DictData;
import com.fytec.dto.AbstractDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class SysUserListDTO extends AbstractDTO {
    private Long id;

    @Schema(description = "用户名称")
    private String name;

    @Schema(description = "用户名")
    private String loginName;

    @Schema(description = "手机号")
    private String tel;

    @Schema(description = "状态值")
    @DictData(codeType = "COMMON_STATUS", target = "statusStr")
    private String status;

    @Schema(description = "状态名")
    private String statusStr;

    @Schema(description = "角色")
    private String roleDesc;

    @Schema(hidden = true)
    private String createBy;

    @Schema(description = "创建者")
    private String createByName;

    @Schema(description = "创建日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "标识位")
    private String isSys;
}
