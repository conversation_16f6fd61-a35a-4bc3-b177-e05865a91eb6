package com.fytec.dto.system;

import com.fytec.config.validate.Optional;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;


/**
 * <AUTHOR>
 * @version version 1.0
 */
@Data
public class SysUserPasswordUpdateDTO {
    @Schema(description = "用户ID")
    @NotBlank(message = "用户ID不能为空")
    private String id;

    @Schema(description = "原密码")
    @NotBlank(message = "原密码不能为空")
    private String password;

    @Schema(description = "新密码")
    @NotBlank(message = "新密码不能为空")
    @Pattern(regexp = "^(?![a-z]+$)(?!\\d+$)(?![\\W_]+$)[\\s\\S]{8,20}$", message = "密码不符合要求")
    private String newPassword;
}
