package com.fytec.dto.system;

import com.fytec.annotation.DictData;
import com.fytec.dto.Node;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version version 1.0
 */
@Data
public class SysMenuTreeDTO extends Node {
    @Schema(description = "菜单名称")
    private String menuName;

    @Schema(description = "菜单类型")
    @DictData(codeType = "MENU_TYPE", target = "menuTypeStr")
    private String menuType;

    @Schema(description = "菜单类型名")
    private String menuTypeStr;

    @Schema(description = "菜单图标")
    private String menuIcon;

    @Schema(description = "菜单前端组件")
    private String menuComponent;

    @Schema(description = "菜单前端地址")
    private String menuHref;

    @Schema(description = "菜单排序")
    private Integer menuSort;
}
