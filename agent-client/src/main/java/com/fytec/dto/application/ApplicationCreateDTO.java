package com.fytec.dto.application;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

@Data
public class ApplicationCreateDTO {

    @Schema(description = "应用名称")
    @NotBlank(message = "应用名称不能为空")
    private String name;

    @Schema(description = "应用介绍")
    private String description;

    @Schema(description = "图标")
    private String logo;

    @Schema(description = "发布智能体Id")
    private List<ApplicationAgentGroupDTO> agents;

    private List<ApplicationModelDTO> models;
}
