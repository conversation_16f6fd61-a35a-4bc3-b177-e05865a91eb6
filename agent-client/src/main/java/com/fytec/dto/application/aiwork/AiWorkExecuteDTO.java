package com.fytec.dto.application.aiwork;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class AiWorkExecuteDTO {

    @Schema(description = "会话ID")
    @NotBlank(message = "会话ID不能为空")
    private String conversationId;

    @NotBlank(message = "应用ID不能为空")
    private Long applicationId;

    private Long messageId;

    private Long modelId;

    private String applicationRelatedFunction;

    private boolean enablePluginSearch = false;

    private boolean enablePluginKnowledge = false;

    private boolean enableCitation = true;

    @Schema(description = "智能体发布ID")
    @NotNull(message = "智能体发布ID不能为空")
    private Long agentPublishId;

    @Schema(description = "用户输入")
    @NotBlank(message = "用户输入不能为空")
    private String userInput;

    @Schema(description = "文件Id")
    private List<Long> fileDocIds;

    @Schema(description = "图片Id")
    private List<Long> fileImageIds;

    @Schema(description = "笔记Id")
    private Long noteId;

    private List<Long> knowledgeId;

    private List<Long> groupId;
}

