package com.fytec.dto.application.aiwork;

import com.fytec.dto.UploadResultDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class AiWorkParseFileDTO {
    private Long knowledgeId;

    @Schema(description = "文件内容")
    private List<DocParseFileDTO> files;
}
