package com.fytec.dto.application;

import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
public class ApplicationBasicDTO {
    private Long id;

    @Schema(description = "智能体名称")
    private String name;

    @Schema(description = "智能体功能介绍")
    private String description;

    @Schema(description = "图标")
    private String logo;

    private List<ApplicationAgentGroupDTO> agents;
    private List<ApplicationModelDTO> models;
}
