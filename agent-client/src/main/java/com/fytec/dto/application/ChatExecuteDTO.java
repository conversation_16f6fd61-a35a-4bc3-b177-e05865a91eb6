package com.fytec.dto.application;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ChatExecuteDTO {
    private Long applicationId;

    private Long agentId;

    private String conversationId;

    private String conversationTitle;

    private String conversationAgents;

    private Map<String, Object> chatRequest;

    @Schema(hidden = true)
    private Long userId;

    @Schema(hidden = true)
    private Long historyId;

    private List<Long> knowledgeId;

    private List<Long> groupId;

    private List<Long> fileDocIds;
}
