package com.fytec.dto.application;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fytec.annotation.DictData;
import com.fytec.dto.AbstractDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ApplicationAgentDTO extends AbstractDTO {
    private Long agentPublishId;

    private Long agentId;

    private String name;

    private String description;

    private String logo;

    private String version;

    @DictData(target = "tagNames", codeType = "AGENT_TAGS")
    private String tags;

    private String tagNames;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime publishTime;

    private String publisher;

    @Schema(description = "开场白")
    private String prologue;

    @Schema(description = "默认问题(数组)")
    private String tips;

    private boolean assistant;

    private String imageUrl;
}
