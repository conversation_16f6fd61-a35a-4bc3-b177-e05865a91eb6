package com.fytec.dto.model;

import com.fytec.dto.llm.ModelParamDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class ModelListDTO {

    @Schema(description = "模型分组Id")
    private Long groupId;

    @Schema(description = "模型分组logo")
    private String groupLogo;

    @Schema(description = "模型分组名称，如：豆包系列")
    private String groupName;

    @Schema(description = "模型Id")
    private Long id;

    @Schema(description = "模型名称，如：豆包·视觉理解·Pro")
    private String name;

    @Schema(description = "模型大小，如：32K")
    private String size;

    @Schema(description = "模型code，如：ep-20241010172728-279wq")
    private String code;

    @Schema(description = "模型标签，如：限额使用、旗舰")
    private String tags;

    @Schema(description = "模型描述，如：豆包系列效果最好的主力模型")
    private String description;

    @Schema(description = "模型多样性参数")
    private String diversityParams;

    @Schema(description = "模型输入输出参数")
    private String ioParams;
}
