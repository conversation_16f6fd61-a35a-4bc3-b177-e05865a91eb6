package com.fytec.dto.model;

import com.alibaba.fastjson.JSON;
import com.fytec.dto.llm.ModelParamDTO;
import com.fytec.entity.llm.AiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.List;

@Data
public class ModelDTO {

    private Long id;

    @Schema(description = "模型名称，如：豆包·视觉理解·Pro")
    private String name;

    @Schema(description = "模型大小，如：32K")
    private String size;

    @Schema(description = "模型code，如：ep-20241010172728-279wq")
    private String code;

    @Schema(description = "模型标签，如：限额使用、旗舰")
    private String tags;

    @Schema(description = "模型描述，如：豆包系列效果最好的主力模型")
    private String description;

    @Schema(description = "模型多样性参数")
    private List<ModelParamDTO> diversityParams;

    @Schema(description = "模型输入输出参数")
    private List<ModelParamDTO> ioParams;


    public static ModelDTO fromEntity(AiModel model) {
        ModelDTO dto = new ModelDTO();
        BeanUtils.copyProperties(model,dto);
        dto.setDiversityParams(JSON.parseArray(model.getDiversityParams(),ModelParamDTO.class));
        dto.setIoParams(JSON.parseArray(model.getIoParams(),ModelParamDTO.class));
        return dto;
    }
}
