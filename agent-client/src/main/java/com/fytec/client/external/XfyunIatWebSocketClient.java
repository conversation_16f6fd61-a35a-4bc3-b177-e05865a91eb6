package com.fytec.client.external;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fytec.dto.external.IatRequestDTO;
import com.fytec.utils.external.XfyunSignUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.io.FileInputStream;
import java.util.Arrays;
import java.util.Base64;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * 讯飞语音识别WebSocket客户端
 * 统一的WebSocket客户端，支持ASR和IAT功能
 *
 * <AUTHOR>
 */
@Slf4j
public class XfyunIatWebSocketClient extends WebSocketListener {

    private static final String AUDIO_FORMAT = "audio/L16;rate=16000";
    private static final int CHUNK_SIZE = 1280;
    private static final int SEND_INTERVAL_MS = 40;

    private final String wsUrl;
    private final String appId;
    private final String encoding;
    private final IatRequestDTO iatRequest;
    private final OkHttpClient client;
    private final ObjectMapper objectMapper = new ObjectMapper();

    private final Consumer<JsonNode> onMessage;
    private final Consumer<Throwable> onError;
    private final Runnable onComplete;
    
    private volatile boolean isConnected = false;
    private final CompletableFuture<Void> websocketConnectedFuture = new CompletableFuture<>();
    private WebSocket webSocket;
    
    @Getter
    private volatile boolean isCompleted = false;

    public XfyunIatWebSocketClient(String hostUrl, String appId, String apiSecret, String apiKey,
                                   IatRequestDTO iatRequest,
                                   Consumer<JsonNode> onMessage, Consumer<Throwable> onError,
                                   Runnable onComplete) {
        this.wsUrl = XfyunSignUtil.buildAuthUrl(hostUrl, appId, apiSecret, apiKey);
        this.appId = appId;
        this.encoding = iatRequest.getEncoding();
        this.iatRequest = iatRequest;
        this.client = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();

        this.onMessage = onMessage;
        this.onError = onError;
        this.onComplete = onComplete;
    }

    @Override
    public void onOpen(@NotNull WebSocket webSocket, @NotNull Response response) {
        log.info("讯飞语音识别WebSocket连接已建立");
        isConnected = true;
        websocketConnectedFuture.complete(null);
    }

    @Override
    public void onMessage(@NotNull WebSocket webSocket, @NotNull String text) {
        handleResponse(text);
    }

    @Override
    public void onClosing(@NotNull WebSocket webSocket, int code, @NotNull String reason) {
        log.info("讯飞语音识别WebSocket连接正在关闭: {} - {}", code, reason);
        isConnected = false;
    }

    @Override
    public void onClosed(@NotNull WebSocket webSocket, int code, @NotNull String reason) {
        log.info("讯飞语音识别WebSocket连接已关闭: {} - {}", code, reason);
        isConnected = false;
        if (!websocketConnectedFuture.isDone()) {
            websocketConnectedFuture.completeExceptionally(new RuntimeException("WebSocket连接已关闭"));
        }
    }

    @Override
    public void onFailure(@NotNull WebSocket webSocket, @NotNull Throwable t, Response response) {
        log.error("讯飞语音识别WebSocket连接失败", t);
        isConnected = false;
        websocketConnectedFuture.completeExceptionally(t);
        onError.accept(t);
    }

    public void connect() {
        try {
            Request request = new Request.Builder().url(wsUrl).build();
            this.webSocket = client.newWebSocket(request, this);
            log.info("开始连接讯飞语音识别WebSocket: {}", wsUrl);
        } catch (Exception e) {
            log.error("连接讯飞语音识别WebSocket失败", e);
            onError.accept(e);
        }
    }

    public void close() {
        if (webSocket != null) {
            webSocket.close(1000, "正常关闭");
        }
    }

    public boolean isOpen() {
        return isConnected && webSocket != null;
    }

    public void sendAudioData(byte[] audioData, boolean isFirstFrame, boolean isLastFrame) {
        if (webSocket == null || isCompleted) {
            log.warn("WebSocket未连接或已完成，跳过发送音频数据");
            return;
        }

        try {
            ObjectNode frame = buildAudioFrame(audioData, isFirstFrame, isLastFrame);
            webSocket.send(frame.toString());
            log.debug("发送音频数据: {} bytes, firstFrame: {}, lastFrame: {}",
                    audioData.length, isFirstFrame, isLastFrame);
        } catch (Exception e) {
            log.error("发送音频数据失败", e);
            onError.accept(e);
        }
    }

    public void sendAudioFileAsync(File audioFile) {
        if (webSocket == null) {
            log.warn("WebSocket未连接，无法发送音频文件");
            return;
        }

        CompletableFuture.runAsync(() -> {
            try (FileInputStream fis = new FileInputStream(audioFile)) {
                sendAudioFileInternal(fis, audioFile.getName(), audioFile.length());
            } catch (Exception e) {
                log.error("发送音频文件失败: {}", audioFile.getName(), e);
                onError.accept(e);
            }
        }).exceptionally(throwable -> {
            log.error("异步发送音频文件失败: {}", audioFile.getName(), throwable);
            onError.accept(throwable);
            return null;
        });
    }

    /**
     * 处理WebSocket响应消息
     */
    private void handleResponse(String text) {
        try {
            JsonNode response = objectMapper.readTree(text);
            
            // 检查错误码
            int code = response.path("code").asInt();
            if (code != 0) {
                String message = response.path("message").asText();
                String sid = response.path("sid").asText();
                log.error("语音识别错误，code: {}, message: {}, sid: {}", code, message, sid);
                onError.accept(new RuntimeException("语音识别错误: " + message));
                return;
            }

            JsonNode data = response.path("data");
            // 直接传递原始响应数据到service层处理
            onMessage.accept(data);
            // 检查是否完成

            if (data.path("status").asInt() == 2) {
                log.info("语音识别完成");
                markCompleted();
                onComplete.run();
                close();
            }
        } catch (Exception e) {
            log.error("处理语音识别响应失败", e);
            onError.accept(e);
        }
    }
    
    /**
     * 构建音频数据帧
     */
    private ObjectNode buildAudioFrame(byte[] audioData, boolean isFirstFrame, boolean isLastFrame) {
        ObjectNode frame = objectMapper.createObjectNode();

        // 公共参数
        ObjectNode common = objectMapper.createObjectNode();
        common.put("app_id", appId);
        frame.set("common", common);

        // 业务参数
        ObjectNode business = objectMapper.createObjectNode();
        if (isFirstFrame) {
            // 使用传入的完整参数
            business.put("language", iatRequest.getLanguage());
            business.put("domain", iatRequest.getDomain());
            business.put("accent", iatRequest.getAccent());

            if (iatRequest.getVadEos() != null) {
                business.put("vad_eos", iatRequest.getVadEos());
            }
            if (iatRequest.getDwa() != null) {
                business.put("dwa", iatRequest.getDwa());
            }
            if (iatRequest.getPd() != null) {
                business.put("pd", iatRequest.getPd());
            }
            if (iatRequest.getPtt() != null) {
                business.put("ptt", iatRequest.getPtt());
            }
            if (iatRequest.getPcm() != null) {
                business.put("pcm", iatRequest.getPcm());
            }
            if (iatRequest.getRlang() != null) {
                business.put("rlang", iatRequest.getRlang());
            }
            if (iatRequest.getVinfo() != null) {
                business.put("vinfo", iatRequest.getVinfo());
            }
            if (iatRequest.getNunum() != null) {
                business.put("nunum", iatRequest.getNunum());
            }
            if (iatRequest.getSpeexSize() != null) {
                business.put("speex_size", iatRequest.getSpeexSize());
            }
            if (iatRequest.getNbest() != null) {
                business.put("nbest", iatRequest.getNbest());
            }
            if (iatRequest.getWbest() != null) {
                business.put("wbest", iatRequest.getWbest());
            }
        }
        frame.set("business", business);

        // 数据参数
        ObjectNode data = objectMapper.createObjectNode();
        data.put("status", isFirstFrame ? 0 : (isLastFrame ? 2 : 1));
        data.put("format", iatRequest.getFormat());
        data.put("encoding", encoding);
        data.put("audio", Base64.getEncoder().encodeToString(audioData));
        frame.set("data", data);

        return frame;
    }

    /**
     * 内部音频文件发送逻辑
     */
    private void sendAudioFileInternal(FileInputStream fis, String fileName, long fileSize) throws Exception {
        byte[] buffer = new byte[CHUNK_SIZE];
        int bytesRead;
        boolean isFirst = true;

        log.info("开始发送音频文件: {}, 大小: {} bytes", fileName, fileSize);

        while ((bytesRead = fis.read(buffer)) != -1 && !isCompleted) {
            byte[] audioChunk = Arrays.copyOf(buffer, bytesRead);

            if (isFirst) {
                sendAudioData(audioChunk, true, false);
                isFirst = false;
            } else {
                sendAudioData(audioChunk, false, false);
            }
            // DEMO建议每40ms发送1280字节，即122B
            Thread.sleep(SEND_INTERVAL_MS);
        }

        if (!isCompleted) {
            sendAudioData(new byte[0], false, true);
            log.info("音频文件发送完成: {}", fileName);
        } else {
            log.warn("音频文件发送中断: {}, 已完成状态: {}", fileName, isCompleted);
        }
    }

    /**
     * 标记处理完成
     */
    public void markCompleted() {
        this.isCompleted = true;
    }

    /**
     * 获取WebSocket连接状态的CompletableFuture
     *
     * @return 连接状态的CompletableFuture，连接成功时完成，连接失败时异常完成
     */
    public CompletableFuture<Void> getWebSocketConnectedFuture() {
        return websocketConnectedFuture;
    }
}