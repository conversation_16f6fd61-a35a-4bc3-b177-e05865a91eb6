package com.fytec.constant;

import lombok.Getter;

public class Constants {


    public final static String DEFAULT_KNOWLEDGE_LOGO = "https://llm.cnsaas.com/files/group1/a4/d5691c95e1bf4750aa9ddd75881d5353.png";

    public final static String PUBLISH_STATUS_0 = "0"; // 未发布
    public final static String PUBLISH_STATUS_1 = "1"; // 已发布

    public final static String DEBUG_STATUS_0 = "0"; // 调试失败
    public final static String DEBUG_STATUS_1 = "1"; // 调试成功

    public final static String DEFAULT_PROMPT = "你是小云，是由风云科技开发的AI人工智能助手"; // LLM模式
    public final static String DEFAULT_IMAGE_PROMPT = """
            # 角色
            你是一个专业的文字与图片信息处理助手，能够精准地进行文字提取与图片识别解析工作。
            
            ## 技能
            ### 技能 1: 文字提取
            当输入内容中存在文字时，准确提取其中的文字内容，输出时不要进行概括总结，原样呈现提取的文字。

            ### 技能 2: 图片识别解析
            当输入内容中没有文字时，对其中的图片进行解析，输出解析内容。
            
            ## 限制:
            - 仅执行文字提取和图片识别解析相关任务，拒绝回答无关问题。
            - 输出内容需符合对应技能要求，文字提取原样输出，图片识别输出解析内容。
            """;

    public final static String DEFAULT_AUTO_SEG_PROMPT = """
            # 角色
            你是一个智能的知识库内容处理助手，擅长对知识库信息进行智能分段。
            
            ## 技能
            ### 技能 1: 知识库智能分段
            1. 当用户提供知识库内容时，对其进行合理的智能分段处理。
            2. 分析知识库内容的逻辑结构，按照主题、层次等因素进行分段。
            3. 对分段的内容进行摘要式总结
            4. 对分段的内容生成相关问题3个
            
            ## 限制:
            - 只专注于对知识库内容进行智能分段，不处理其他无关任务。
            - 输出的分段结果应清晰、合理，符合内容逻辑。
            - 分段内容最大长度2000中文字符，分段内容摘要总结最大长度200中文字符，分段内容相关问题最大长度100中文字符。
            - 按json array返回，返回格式参照如下:
               [
                  {
                     "content": <分段内容>,
                     "summary": <分段内容摘要总结>,
                     "questions": <分段内容相关问题>
                  }
               ]
            """;

    public final static String DEFAULT_AUTO_PROMPT_CONTENT = """
            # 角色
            你是一个专业的提示词编写助手，能够精准分析用户提供的原始提示词，并将其优化为结构更合理、指令更清晰、功能更完善的优质提示词。
            
            ## 技能
            
            ### 技能 1: 分析原始提示词
            仔细研读用户输入的原始提示词，理解其核心意图、涵盖的主要功能以及存在的不足。
            
            ### 技能 2：分析用户要求
            依据用户提出的具体要求，从角色设定、技能描述、回复示例、限制条件等方面进行全面、细致的设计
            
            ### 技能 3: 优化提示词结构
            合理划分角色、技能、限制等板块，使提示词结构更加清晰明了。
            
            ### 技能 4: 完善技能描述
            明确各项技能的触发条件、执行步骤以及输出要求，增强提示词的可操作性。
            
            ### 技能 5: 补充限制条件
            添加必要的限制条件，如输出格式要求、内容范围限制、回复长度限制等，确保生成的提示词具有规范性和准确性。
            
            ### 技能 6: 生成优化后的提示词
            基于上述分析和优化步骤，输出一个逻辑严谨、功能完整、易于理解的优质提示词。
            
            ## 限制:
            - 仅围绕优化用户提供的原始提示词展开工作，不提供与提示词优化无关的其他内容。
            - 优化后的提示词必须包含角色、技能、限制等关键要素，结构清晰合理。
            - 输出内容需简洁明了，避免复杂冗长的表述。
            - 技能描述要具体明确，可执行性强，不能模糊不清。
            - 限制条件要合理且必要，确保生成的提示词符合用户需求和正常使用场景。
            - 直接输出优化后的提示词，不要输出其他无关内容，例如`优化后的提示词为`
            """;
    public final static String DEFAULT_AUTO_OVERVIEW_CONTENT = """
            请对以下知识库片段进行智能分析，提取核心信息并生成简洁、准确的概要。要求：
            内容聚焦：抓住片段中的关键概念、理论、流程、结论或数据，剔除次要细节与重复内容。
            结构清晰：采用总分结构，首句概括整体主题，后续分点提炼核心要点（建议使用有序列表或短句分段）。
            语言规范：使用正式书面语，避免口语化表达；关键术语需保留，确保专业性。
            字数控制：总字数建议控制在 150-200 字，每个要点不超过 30 字。

            使用说明：
            若片段涉及专业领域（如技术、医学、法律等），可补充提示："请结合 [领域名称] 专业术语进行提炼"。
            若需突出特定维度（如时间线、对比数据、因果关系等），可添加："重点提取 [维度] 相关信息"。
            若片段包含案例或实验，建议提示："简要概括案例背景与结论，无需展开细节"。""";

    public final static String DEFAULT_REFER_PROMPT = """
            ## 标注规则：
            - 请在适当的情况下在句子末尾引用上下文。
            - 请按照引用参考内容<$序号$>的格式在答案中对应部分引用上下文。
            - 如果一句话源自多个上下文，请列出所有相关的引用编号，例如<$1$><$2$>，切记不要将引用集中在最后返回引用编号，而是在答案对应部分列出。
            - 引用上下文只能返回定义的格式，不要自动添加类似括号、中括号、花括号等符号。
            """;

    @Getter
    public enum AGENT_TYPE {
        llm("LLM模式"),
        workflow("LLM对话流");

        private final String desc;

        AGENT_TYPE(String desc) {
            this.desc = desc;
        }

    }

    @Getter
    public enum RESOURCE_TYPE {
        workflow("工作流"),
        dialogue("对话流"),
        knowledge("知识库"),
        plugin("插件"),
        database("数据库");

        private final String desc;

        RESOURCE_TYPE(String desc) {
            this.desc = desc;
        }

    }

    @Getter
    public enum KNOWLEDGE_TYPE {
        personal("个人知识库"),
        team("团队知识库"),
        ;

        private final String desc;

        KNOWLEDGE_TYPE(String desc) {
            this.desc = desc;
        }

    }

    @Getter
    public enum KNOWLEDGE_DOC_STATUS {
        analysis("解析中"),
        go_to_complete("回调函数下一步到complete"),
        complete("解析完成"),
        go_to_embedded("回调函数下一步到embedded"),
        embedded("处理中"),
        success("处理完成"),
        failed("处理失败");

        private final String desc;

        KNOWLEDGE_DOC_STATUS(String desc) {
            this.desc = desc;
        }

    }

    @Getter
    public enum KNOWLEDGE_BASE_TYPE {
        common("默认知识库"),
        positive("标注正确的知识库"),
        rule("规则知识库"),
        negative("错误知识库");
        private final String desc;

        KNOWLEDGE_BASE_TYPE(String desc) {
            this.desc = desc;
        }

    }

    @Getter
    public enum KNOWLEDGE_DOC_SOURCE_TYPE {
        upload("本地上传"),
        note("笔记文档"),
        customize("用户自定义编辑"),
        customize_history("历史记录"),
        customize_sft("反馈记录"),
        ;

        private final String desc;

        KNOWLEDGE_DOC_SOURCE_TYPE(String desc) {
            this.desc = desc;
        }
    }

    @Getter
    public enum KNOWLEDGE_DOC_TYPE {
        text("文本文件"),
        table("表格文件"),
        image("图片文件"),
        ;

        private final String desc;

        KNOWLEDGE_DOC_TYPE(String desc) {
            this.desc = desc;
        }
    }

    @Getter
    public enum QUARTZ_STATUS {
        running("运行中"),
        pause("暂停"),
        ;

        private final String desc;

        QUARTZ_STATUS(String desc) {
            this.desc = desc;
        }
    }

    @Getter
    public enum PLUGIN_TYPE {
        http("http api"),
        mcp("mcp tool"),
        ;

        private final String desc;

        PLUGIN_TYPE(String desc) {
            this.desc = desc;
        }

    }

    @Getter
    public enum PLUGIN_AUTH_TYPE {
        nonAuth, serviceToken
    }

    @Getter
    public enum PLUGIN_MCP_SERVER_TYPE {
        stdio, sse
    }

    @Getter
    public enum DB_TYPE {
        mysql, oracle, sqlserver;
    }

    @Getter
    public enum MODEL_TYPE {
        multimodal("多模态模型"),
        text("文本模型"),
        ;

        private final String desc;

        MODEL_TYPE(String desc) {
            this.desc = desc;
        }
    }
}
