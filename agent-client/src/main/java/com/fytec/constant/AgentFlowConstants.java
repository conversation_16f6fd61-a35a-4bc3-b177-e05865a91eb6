package com.fytec.constant;

import lombok.Data;
import lombok.Getter;

public class AgentFlowConstants {


    @Getter
    public enum AGENT_RUN_STATUS {
        complete("运行完成"), // 虽然自己成功，但是流没断，就不行。如果是涉及流程的，默认运行完成，单智能体的就默认成功
        running("运行中"),
        success("运行成功"),
        timeout("运行超时"),
        failed("运行失败");

        private final String desc;

        AGENT_RUN_STATUS(String desc) {
            this.desc = desc;
        }

    }

    @Getter
    public enum AGENT_FEEDBACK_TYPE {
        positive("正反馈"),
        negative("负反馈");

        private final String desc;

        AGENT_FEEDBACK_TYPE(String desc) {
            this.desc = desc;
        }

    }

    //    '智能体执行类型，最后一个节点，最开始的节点（入口）、并行的、默认等';
    @Getter
    public enum AGENT_RUN_TYPE {
        customer("默认节点"),
        first("入口节点"),
        parallel("并行节点"),
        think("思考节点"),
        format("格式化节点"),
        last("最后节点");

        private final String desc;

        AGENT_RUN_TYPE(String desc) {
            this.desc = desc;
        }

    }

    //     反馈处理结果
    @Getter
    public enum AGENT_FEEDBACK_RESULT {
        close("关闭/删除"),
        other("其他"),
        positive("修改并设置为正样本"),
        prompt("修改提示词"),
        data("修改数据源"),
        defined("补充定义/解释");
//        delete("删除知识库记录");

        private final String desc;

        AGENT_FEEDBACK_RESULT(String desc) {
            this.desc = desc;
        }
    }

    //     反馈处理状态
    @Getter
    public enum AGENT_FEEDBACK_STATUS {
        complete("已处理"), // 虽然自己成功，但是流没断，就不行。如果是涉及流程的，默认运行完成，单智能体的就默认成功
        uncomplete("未处理");
        private final String desc;

        AGENT_FEEDBACK_STATUS(String desc) {
            this.desc = desc;
        }
    }

    @Getter
    public enum AGENT_FEEDBACK_SOURCE {
        sys("系统生成"), // 虽然自己成功，但是流没断，就不行。如果是涉及流程的，默认运行完成，单智能体的就默认成功
        user("用户反馈");
        private final String desc;

        AGENT_FEEDBACK_SOURCE(String desc) {
            this.desc = desc;
        }
    }

    @Getter
    public enum EchartsType {
        // 图表类型
        pie(new String[]{"饼图", "饼状图"}),
        line(new String[]{"折线"}),
        bar(new String[]{"柱状", "条状", "柱形"}),
        table(new String[]{"表格"});
        private final String[] desc;

        EchartsType(String[] desc) {
            this.desc = desc;
        }
    }

    @Data
    public static class CacheExtraInfo{
        private String protocol;
        private String value;
        private boolean write = false;
        public CacheExtraInfo(String protocol,String value){
            this.protocol = protocol;
            this.value = value;
        }
    }
    @Getter
    public enum AgentExtraInfoProtocol{
        error("错误信息"),
        echart("图表模板文件");

        private final String desc;
        // 智能体的协议,扩展的字段
        AgentExtraInfoProtocol(String  desc) {
            this.desc = desc;
        }
    }
    @Getter
    public enum DataSourceType{
        api("接口"),
        sql("数据库");
        private final String desc;
        // 智能体的协议,扩展的字段
        DataSourceType(String  desc) {
            this.desc = desc;
        }
    }
}
