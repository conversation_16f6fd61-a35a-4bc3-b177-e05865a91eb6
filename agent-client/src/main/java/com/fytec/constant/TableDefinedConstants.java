package com.fytec.constant;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class TableDefinedConstants {
    /**
     * CREATE TABLE `v_teacher_title` (
     * `uuid` varchar(100) NOT NULL COMMENT '主键',
     * `xm` varchar(100) DEFAULT NULL COMMENT '教师姓名',
     * `teacherid` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '教师ID',
     * `zc` varchar(100) DEFAULT NULL COMMENT '职称',
     * `get_date` date DEFAULT NULL COMMENT '获得时间',
     * `update_time` timestamp(6) NULL DEFAULT NULL COMMENT '数据更新时间',
     * PRIMARY KEY (`uuid`)
     * ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='教师职称信息';
     */
    public static final String v_teacher_title = "CREATE TABLE `v_teacher_title` (\n" +
            "     `uuid` varchar(100) NOT NULL COMMENT '主键',\n" +
            "     `xm` varchar(100) DEFAULT NULL COMMENT '教师姓名',\n" +
            "     `teacherid` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '教师ID',\n" +
            "     `zc` varchar(100) DEFAULT NULL COMMENT '职称',\n" +
            "     `get_date` date DEFAULT NULL COMMENT '获得时间',\n" +
            "     `update_time` timestamp(6) NULL DEFAULT NULL COMMENT '数据更新时间',\n" +
            "     PRIMARY KEY (`uuid`)\n" +
            "     ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='教师职称信息';";

    public static final String v_school_baseinfo = "CREATE TABLE `v_school_baseinfo` (\n" +
            "     `school_id` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '学校ID',\n" +
            "     `school_name` varchar(100) DEFAULT NULL COMMENT '学校名称',\n" +
            "     `school_jc` varchar(100) DEFAULT NULL COMMENT '学校简称',\n" +
            "     `school_location` varchar(200) DEFAULT NULL COMMENT '学校地址',\n" +
            "     `school_lb` varchar(100) DEFAULT NULL COMMENT '学校类别(小学,特殊教育学校,其他,幼儿园,普通初中,中等职业学校,普通高中)',\n" +
//            "     `school_xz` varchar(100) DEFAULT NULL COMMENT '学校性质(民办、公办)',\n" +
            "     `leader_xz` varchar(100) DEFAULT NULL COMMENT '现任校长',\n" +
            "     `leader_sj` varchar(100) DEFAULT NULL COMMENT '现任书记',\n" +
            "     `school_jbz` varchar(100) DEFAULT NULL COMMENT '组织性质(公办、民办、直属、街道)',\n" +
            "     `rs_school_name` varchar(100) DEFAULT NULL COMMENT '合作学校名称(统计学校数量时根据这个字段distinct，因为多个学校会隶属于算一个学校）',\n" +
            "     `jt_name` varchar(100) DEFAULT NULL COMMENT '所属集团',\n" +
            "     `student_num` int DEFAULT NULL COMMENT '在校学生人数',\n" +
            "     `teacher_num` int DEFAULT NULL COMMENT '在校教职工人数',\n" +
            "     `class_num` int DEFAULT NULL COMMENT '班级数量',\n" +
            "     `update_time` varchar(100) DEFAULT NULL COMMENT '数据更新时间',\n" +
            "     `data_status` varchar(100) DEFAULT NULL COMMENT '数据状态 0-正常   9-删除',\n" +
            "     PRIMARY KEY (`school_id`)\n" +
            "     ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='学校基本信息';";


    public static final String v_school_reward = "CREATE TABLE `v_school_reward` (\n" +
            "     `uuid` varchar(100) NOT NULL COMMENT '主键',\n" +
            "     `school_name` varchar(100) DEFAULT NULL COMMENT '学校名称',\n" +
            "     `school_id` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '学校ID',\n" +
            "     `rymc` varchar(100) DEFAULT NULL COMMENT '荣誉名称',\n" +
            "     `level_` varchar(100) DEFAULT NULL COMMENT '荣誉级别',\n" +
            "     `sysj` date DEFAULT NULL COMMENT '授予时间',\n" +
            "     `bfdw` varchar(200) DEFAULT NULL COMMENT '颁发单位',\n" +
            "     `update_time` timestamp(6) NULL DEFAULT NULL COMMENT '数据更新时间',\n" +
            "     PRIMARY KEY (`uuid`)\n" +
            "     ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='学校荣誉信息';";


    public static final String v_teacher_thesis = "     CREATE TABLE `v_teacher_thesis` (\n" +
            "     `uuid` varchar(100) NOT NULL COMMENT '主键',\n" +
            "     `xm` varchar(100) DEFAULT NULL COMMENT '教师姓名',\n" +
            "     `teacherid` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '教师ID',\n" +
            "     `lwmc` varchar(100) DEFAULT NULL COMMENT '论文名称',\n" +
            "     `kwdj` varchar(100) DEFAULT NULL COMMENT '刊物等级',\n" +
            "     `kwmc` varchar(100) DEFAULT NULL COMMENT '刊物名称',\n" +
            "     `fbrq` date DEFAULT NULL COMMENT '发表日期',\n" +
            "     `cbdwmc` varchar(100) DEFAULT NULL COMMENT '出版单位名称',\n" +
            "     `update_time` timestamp(6) NULL DEFAULT NULL COMMENT '数据更新时间'\n" +
            "     ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='教师论文信息';";

    public static final String v_student_baseinfo = "     CREATE TABLE `v_student_baseinfo` (\n" +
            "     `uuid` varchar(100) NOT NULL COMMENT '学生ID',\n" +
            "     `xm` varchar(100) DEFAULT NULL COMMENT '学生姓名',\n" +
            "     `xb` varchar(100) DEFAULT NULL COMMENT '性别',\n" +
            "     `jg` varchar(100) DEFAULT NULL COMMENT '籍贯',\n" +
            "     `mz` varchar(100) DEFAULT NULL COMMENT '民族',\n" +
            "     `school` varchar(200) DEFAULT NULL COMMENT '所在学校',\n" +
            "     `school_id` varchar(100) DEFAULT NULL COMMENT '所在学校ID',\n" +
            "     `class_name` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '所在班级',\n" +
            "     `class_id` varchar(100) DEFAULT NULL COMMENT '所在班级ID',\n" +
            "     `age` int DEFAULT NULL COMMENT '年龄',\n" +
            "     `dyjhrxm` varchar(100) DEFAULT NULL COMMENT '第一监护人姓名',\n" +
            "     `dyjhrgx` varchar(100) DEFAULT NULL COMMENT '第一监护人关系',\n" +
            "     `dejhrxm` varchar(100) DEFAULT NULL COMMENT '第二监护人姓名',\n" +
            "     `dejhrgx` varchar(100) DEFAULT NULL COMMENT '第二监护人关系',\n" +
            "     `xzz` varchar(100) DEFAULT NULL COMMENT '现住址',\n" +
            "     `update_time` timestamp(6) NULL DEFAULT NULL COMMENT '数据更新时间',\n" +
            "     `data_status` varchar(100) DEFAULT NULL COMMENT '数据状态 0-正常  3-锁定  9-删除',\n" +
            "     PRIMARY KEY (`uuid`)\n" +
            "     ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='学生基本信息';";


    public static final String v_teacher_rcjl = "     CREATE TABLE `v_teacher_rcjl` (\n" +
            "     `uuid` varchar(100) NOT NULL COMMENT '主键',\n" +
            "     `xm` varchar(100) DEFAULT NULL COMMENT '教师姓名',\n" +
            "     `jlmc` varchar(200) DEFAULT NULL COMMENT '奖励名称',\n" +
            "     `jllb` varchar(100) DEFAULT NULL COMMENT '奖励类别',\n" +
            "     `level_` varchar(100) DEFAULT NULL COMMENT '奖励级别',\n" +
            "     `jldj` varchar(100) DEFAULT NULL COMMENT '奖励等级',\n" +
            "     `sysj` date DEFAULT NULL COMMENT '授予时间',\n" +
            "     `bfdw` varchar(200) DEFAULT NULL COMMENT '颁发单位',\n" +
            "     `update_time` timestamp NULL DEFAULT NULL COMMENT '数据更新时间',\n" +
            "     `teacherid` varchar(100) DEFAULT NULL COMMENT '教师ID',\n" +
            "     PRIMARY KEY (`uuid`)\n" +
            "     ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='教师人才奖励';";
    public static final String v_teacher_baseinfo = "     CREATE TABLE `v_teacher_baseinfo` (\n" +
            "     `uuid` varchar(100) NOT NULL COMMENT '教师ID',\n" +
            "     `xm` varchar(100) DEFAULT NULL COMMENT '教师姓名',\n" +
            "     `school_name` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '所在学校',\n" +
            "     `school_id` varchar(100) DEFAULT NULL COMMENT '所在学校ID',\n" +
            "     `organizeschool_name` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '编制学校',\n" +
            "     `organizeschool_id` varchar(100) DEFAULT NULL COMMENT '编制学校ID',\n" +
            "     `jg` varchar(100) DEFAULT NULL COMMENT '籍贯',\n" +
            "     `csrq` varchar(100) DEFAULT NULL COMMENT '出生年月(yyyy年mm月dd日)',\n" +
            "     `age` int DEFAULT NULL COMMENT '年龄',\n" +
            "     `yrxs` varchar(100) DEFAULT NULL COMMENT '用人形式',\n" +
            "     `xb` varchar(100) DEFAULT NULL COMMENT '性别',\n" +
            "     `mz` varchar(100) DEFAULT NULL COMMENT '民族',\n" +
            "     `zzmm` varchar(100) DEFAULT NULL COMMENT '政治面貌',\n" +
            "     `hyzk` varchar(100) DEFAULT NULL COMMENT '婚姻状况',\n" +
            "     `rjkc` varchar(100) DEFAULT NULL COMMENT '任教学科',\n" +
            "     `rjxd` varchar(100) DEFAULT NULL COMMENT '任教学段',\n" +
            "     `rylb` varchar(100) DEFAULT NULL COMMENT '人员类别(专任教师、在编职工)',\n" +
            "     `ryqzgsj` varchar(100) DEFAULT NULL COMMENT '入园区工作时间(yyyy年mm月dd日)',\n" +
            "     `cjgzny` varchar(100) DEFAULT NULL COMMENT '参加工作年月(yyyy年mm月dd日)',\n" +
            "     `zgxl` varchar(100) DEFAULT NULL COMMENT '最高学历',\n" +
            "     `zgzc` varchar(100) DEFAULT NULL COMMENT '最高职称',\n" +
            "     `bzlx` varchar(100) DEFAULT NULL COMMENT '编制类型(其他,参编管理,企业化,事业编)',\n" +
            "     `zgxkry` varchar(100) DEFAULT NULL COMMENT '最高学科荣誉',\n" +
            "     `update_time` timestamp(6) NULL DEFAULT NULL,\n" +
            "     `data_status` varchar(100) DEFAULT NULL COMMENT '数据状态 0-正常  3-锁定  9-删除',\n" +
            "     `zj` varchar(100) COMMENT '职级',\n" +
            "     `jtzw` varchar(100) COMMENT '具体职务',\n" +
            "     `jszgzzl` varchar(100) COMMENT '教师资格证种类',\n" +
            "     PRIMARY KEY (`uuid`)\n" +
            "     ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='教师基本信息(yrxs为“临聘员工”的不是老师忽略不计)';";

    public static final String v_school_campus = "CREATE TABLE `v_school_campus` (\n" +
            "     `uuid` varchar(100) NOT NULL COMMENT '主键',\n" +
            "     `school_name` varchar(100) DEFAULT NULL COMMENT '学校名称',\n" +
            "     `school_id` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '学校ID',\n" +
            "     `campus` varchar(100) DEFAULT NULL COMMENT '校区名称',\n" +
            "     `xqdz` varchar(200) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '校区地址',\n" +
            "     `update_time` timestamp(6) NULL DEFAULT NULL COMMENT '数据更新时间',\n" +
            "     PRIMARY KEY (`uuid`)\n" +
            "     ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='学校校区信息';";
//    .replaceAll("\n","").replaceAll(" DEFAULT NULL "," ").replaceAll("\n","")
    public static final String v_teacher_reward = "CREATE TABLE `v_teacher_reward` (\n" +
            "     `uuid` varchar(100) NOT NULL COMMENT '主键',\n" +
            "     `xm` varchar(100) DEFAULT NULL COMMENT '教师姓名',\n" +
            "     `teacherid` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '教师ID',\n" +
            "     `rymc` varchar(200) DEFAULT NULL COMMENT '荣誉资质名称',\n" +
            "     `rylb` varchar(100) DEFAULT NULL COMMENT '荣誉资质类别',\n" +
            "     `level_` varchar(100) DEFAULT NULL COMMENT '荣誉级别',\n" +
            "     `sysj` date DEFAULT NULL COMMENT '授予时间',\n" +
            "     `bfdw` varchar(200) DEFAULT NULL COMMENT '颁发单位',\n" +
            "     `update_time` timestamp(6) NULL DEFAULT NULL COMMENT '数据更新时间',\n" +
            "     PRIMARY KEY (`uuid`)\n" +
            "     ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='教师荣誉信息';";

    public static final String tj_v_area_edu_size = "CREATE TABLE `v_area_edu_size` (\n" +
            "  `year` int DEFAULT NULL COMMENT '年份',\n" +
            "  `xxsl` int DEFAULT NULL COMMENT '学校数量',\n" +
            "  `gbxxsl` int DEFAULT NULL COMMENT '公办学校数量',\n" +
            "  `mbxxsl` int DEFAULT NULL COMMENT '民办学校数量',\n" +
            "  `jzgsl` int DEFAULT NULL COMMENT '教职工数量',\n" +
            "  `njzgsl` int DEFAULT NULL COMMENT '男教师数量',\n" +
            "  `vjzgsl` int DEFAULT NULL COMMENT '女教师数量',\n" +
            "  `xssl` int DEFAULT NULL COMMENT '学生数量',\n" +
            "  `nxssl` int DEFAULT NULL COMMENT '男学生数量',\n" +
            "  `vxssl` int DEFAULT NULL COMMENT '女学生数量',\n" +
            "  `gbjss` int DEFAULT NULL COMMENT '公办教师数',\n" +
            "  `mbjss` int DEFAULT NULL COMMENT '民办教师数',\n" +
            "  `gbsybjzgs` int DEFAULT NULL COMMENT '公办事业编教职工数',\n" +
            "  `gbqyhjzgs` int DEFAULT NULL COMMENT '公办企业化教职工数'\n" +
            ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='教育规模';";

    public static final String tj_area_teacher_analysis = "CREATE TABLE `v_area_teacher_analysis` (\n" +
            "  `xxlbname` varchar(100) DEFAULT NULL COMMENT '学校类型',\n" +
            "  `xxjbzname` varchar(100) DEFAULT NULL COMMENT '举办者类型（公/民办）',\n" +
            "  `njssl` int DEFAULT NULL COMMENT '男教师数量',\n" +
            "  `vjssl` int DEFAULT NULL COMMENT '女教师数量',\n" +
            "  `njs_30` int DEFAULT NULL COMMENT '30岁以下男教师数量',\n" +
            "  `njs_30_35` int DEFAULT NULL COMMENT '30-35岁男教师数量',\n" +
            "  `njs_35_40` int DEFAULT NULL COMMENT '35-40岁男教师数量',\n" +
            "  `njs_40_45` int DEFAULT NULL COMMENT '40-45岁男教师数量',\n" +
            "  `njs_45_50` int DEFAULT NULL COMMENT '45-50岁男教师数量',\n" +
            "  `njs_50` int DEFAULT NULL COMMENT '50岁以上男教师数量',\n" +
            "  `vjs_30` int DEFAULT NULL COMMENT '30岁以下女教师数量',\n" +
            "  `vjs_30_35` int DEFAULT NULL COMMENT '30-35岁女教师数量',\n" +
            "  `vjs_35_40` int DEFAULT NULL COMMENT '35-40岁女教师数量',\n" +
            "  `vjs_40_45` int DEFAULT NULL COMMENT '40-45岁女教师数量',\n" +
            "  `vjs_45_50` int DEFAULT NULL COMMENT '45-50岁女教师数量',\n" +
            "  `vjs_50` int DEFAULT NULL COMMENT '50岁以上女教师数量',\n" +
            "  `bksl` int DEFAULT NULL COMMENT '本科学历教师数量',\n" +
            "  `yjssl` int DEFAULT NULL COMMENT '研究生学历教师数量',\n" +
            "  `bssl` int DEFAULT NULL COMMENT '博士学历教师数量',\n" +
            "  `yjjssl` int DEFAULT NULL COMMENT '一级教师数量',\n" +
            "  `gjjssl` int DEFAULT NULL COMMENT '高级教师数量',\n" +
            "  `zgjjssl` int DEFAULT NULL COMMENT '正高级教师数量',\n" +
            "  `stjjssl` int DEFAULT NULL COMMENT '省特级教师数量',\n" +
            "  `qxkdtrsl` int DEFAULT NULL COMMENT '区学科带头人数量',\n" +
            "  `sxkdtrsl` int DEFAULT NULL COMMENT '市学科带头人数量',\n" +
            "  `sxkdtrsl_` int DEFAULT NULL COMMENT '省学科带头人数量',\n" +
            "  `qmjssl` int DEFAULT NULL COMMENT '区名教师数量',\n" +
            "  `smjssl` int DEFAULT NULL COMMENT '市名教师数量'\n" +
            ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='学段教师统计';";

    public static final String tj_area_teacher_reward = "CREATE TABLE `v_area_teacher_reward` (\n" +
            "  `year` int DEFAULT NULL COMMENT '年份',\n" +
            "  `jllx` varchar(100) DEFAULT NULL COMMENT '奖励类型',\n" +
            "  `level_` varchar(100) DEFAULT NULL COMMENT '奖励级别',\n" +
            "  `hjrs` varchar(100) DEFAULT NULL COMMENT '获奖教师人数'\n" +
            ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='教师获奖统计';";
    public static final Map<String, String> tableDictMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {

//        表v_teacher_baseinfo的bzlx字段的字典值为其他,参编管理,企业化,事业编
        tableDictMap.put("v_student_baseinfo","表v_student_baseinfo的mz字段的字典值为汉族,拉祜族,朝鲜族,保安族,维吾尔族,纳西族,黎族,高山族,侗族,苗族,白族,蒙古族,满族,鄂伦春族,仫佬族,外国血统中国籍人士,哈萨克族,东乡族,阿昌族,裕固族,撒拉族,珞巴族,毛难族,瑶族,达斡尔族,基诺族,仡佬族,塔塔尔族,土族,彝族,门巴族,俄罗斯族,回族,独龙族,布依族,京族,傣族,壮族,傈僳族,土家族,布朗族,佤族,鄂温克族,怒族,景颇族,藏族,赫哲族,柯尔克孜族,穿青人族,羌族,畲族,水族,普米族,德昂族,其他,锡伯族,塔吉克族,哈尼族,乌孜别克族");
        tableDictMap.put("v_student_reward","表v_student_reward的level_字段的字典值为地（市,厅,局）级荣誉称号,校级荣誉称号,国家级荣誉称号,其他荣誉称号,省（自治区,直辖市）级荣誉称号,区（县,局）级荣誉称号");
        tableDictMap.put("v_teacher_baseinfo","表v_teacher_baseinfo的zzmm字段的字典值为共青团员,中共党员,民进会员,群众,致公党党员,民建会员,中共预备党员,民革会员,民盟盟员,农工党党员,九三学社社员,台盟盟员,无党派民主人士\n表v_teacher_baseinfo的hyzk字段的字典值为未说明的婚姻状况,初婚,未婚,已婚,复婚,再婚,离婚,丧偶\n表v_teacher_baseinfo的rjkc字段的字典值为道德与法治,语文,数学,化学,生物,物理,历史,音乐,心理健康教育,科学,特殊教育,校本,综合实践,体育,英语,政治,信息科技,劳动,美术,其他,通用技术,地理,幼教\n表v_teacher_baseinfo的rjxd字段的字典值为中职,学前,小学,初中,高中,其他\n表v_teacher_baseinfo的zgxl字段的字典值为博士研究生,高中,硕士研究生,大学专科,中等专科,其他,大学本科,初中\n表v_teacher_baseinfo的zgzc字段的字典值为一级教师,三级教师,未定级,高级教师,其他,二级教师,正高级教师\n表v_teacher_baseinfo的yrxs字段的字典值为临聘员工,教职员工\n表v_teacher_baseinfo的zgxkry字段的字典值为高职院校中的具有博士学历学位和教授专业技术职务的人才,高职院校中的省双创人才（高技能创新类）,苏州市优秀双师型教师,苏州市“高技能人才”,姑苏教育青年拔尖人才,全国模范教师,“江苏人民教育家”培养工程培养对象,姑苏高技能领军人才,全国优秀教师,省高校青蓝工程中青年学术带头人,无,区学科带头人,市学科带头人,教学能手,教坛新秀,教科研能手,教科研带头人,特级教师,市教坛新苗,省教学名师,国家万人计划教学名师,其他,省学科带头人,区名教师,市教坛新秀,教科研新秀,市教学标兵,市名校长,市名教师");
        tableDictMap.put("v_teacher_reward","表v_teacher_reward的rymc字段的字典值为区教师教育基地,区乡村骨干教师培育站导师,区数据分析师,全国教育系统先进工作者,区数据安全师,省级教师培训先进个人,园区教育人才支持计划中分层培养的重点人才,省“四有”好教师团队主持人,市“四有”好教师团队主持人,市教师发展示范基地校,教师发展研究基地校,教师（校长）培训基地校负责人,全国优秀班主任,江苏省最美班主任,园区教育人才支持计划中分层培养的领军人才,区乡村骨干教师培育站主持人,区骨干教师共同体负责人,市乡村教育带头人培育站导师,全国时代楷模,区学科培训基地,区级教师培训先进个人,区正高级/特级工作站主持人,区教师发展示范基地校,教师发展研究基地校,教师（校长）培训基地校负责人,“圣陶园丁”,区级兼职培训员,园区先进教育工作者,专项特长导师站,市级教师培训先进个人,园区德育教坛新秀,市数据分析师,区“四有”好教师团队主持人,市AI教师,省教师发展示范基地校,教师发展研究基地校,教师（校长）培训基地校负责人,园区德育教学能手,园区教育人才支持计划中分层培养的杰出人才,高职院校中的省双创人才（高技能创新类）,高职院校中的具有博士学历学位和教授专业技术职务的人才,姑苏高技能领军人才,“江苏人民教育家”培养工程培养对象,苏州市“高技能人才”,省高校青蓝工程中青年学术带头人,苏州市优秀双师型教师,姑苏教育青年拔尖人才,区数字教师,区数字教研员,市数字教师,区AI教师,市首席信息官,区首席信息官,市数据安全师,市数字教研员,教坛新秀,其他区级荣誉,省333高层次人才培养对豿中青年科学技术带头人,区优秀党务工作者,金鸡湖教育创优人才,区学科带头人,姑苏教育领军人才,区优秀共产党员,苏教名家培养对象,省人民教育家工程培养对象,姑苏教育紧缺人才,姑苏教育特聘人才,区名师工作室成员,苏州市名师领航培养工程项目成员,省教育名家,其他省级荣誉,姑苏教育名家,金鸡湖教育领军人才-杰出教师,市教坛新苗,金鸡湖教育领军人才-杰出青年教师,省领航名师培养工程项目成员,教科研能手,区劳动模范,省优秀党务工作者,省劳动模范,市优秀共产党员,省333高层次人才培养对豿中青年科技领军人才,省教学名师,省特级教师,标兵,金鸡湖教育领军人才-优秀教师,其他市级荣誉,省领航名师培养工程首席专家,市名教师,市学科带头人,国家名师工作室主持人,学科奥林匹克竞赛高级教练,“国培计划”中小学名师名校长领航工程首席专家,教科研带头人,省优秀共产党员,区名师工作坊主持人,全国优秀共产党员,人民教育家,“国培计划”中小学名师名校长领航工程项目成员,金鸡湖教育特聘人才,教科研新秀,市劳动模范,全国优秀教育工作者名教师,全国劳动模范,全国优秀教师,全国模范教师,省优秀教育工作者,省名师工作室主持人,市名师工作室主持人,教学能手,市优秀教育工作者,国家万人计划教学名师,市优秀党务工作者,区优秀教育工作者,其他国家级荣誉,全国教书育人楷模,姑苏教育中青年拔尖人才,区优秀工作者,省五一劳动奖章,市教坛新秀,省学科带头人,市优秀班主任,全国道德教育先进个人,周氏德育奖,市名校长,市共青团先进个人,区十佳教师,区名优班主任,区十杰教师,区优秀德育工作者,市“学科双优之星”,区名教师,区优秀班主任,其他,市十佳少先队辅导员,省职业教育领军人才,区时代新人,市爱生模范教师,市教学标兵,省333高层次人才培养对豿中青年首席科学家,市优秀德育工作者,市双十佳青年教师,省师德先进个人,市青年教师双十佳,区师德标兵,市“中小学教育技术能手”,区先进工作耿\n表v_teacher_reward的level_字段的字典值为其他,市级,省级,区级,校级,国家级\n表v_teacher_reward的rylb字段的字典值为学科荣誉称号,综合荣誉系列,教育人才系列,名师工作室系列,其他");
        tableDictMap.put("v_teacher_rcjl","表v_teacher_rcjl的jllb字段的字典值为教学工作,科技竞赛,工作业绩,专业技术,优秀教案,市级名师工作室,班主任基本功,苏州市教育信息化先进个人,信息技术与课程整合,周氏德育,苏州市教育技术能力竞赛,苏州市教坛新苗,NOC网络教研竞赛,苏州十佳网络教学团队,优秀班主任,江苏省优秀少先队辅导员,江苏省教师网络团队竞赛,把握学科能力竞赛(教师专业素养),微课,苏州市中小学教师网络团队竞赛,苏州市中小学教育技术应用能手,共青团工作先进个人,信息化教学能手,一师一优课 一课一名师,国家职业心理咨询师,教学成果奖,评优课,名优班主任,德育学科带头人,优秀德育工作者,未来教室应用课堂,教学基本功\n表v_teacher_rcjl的level_字段的字典值为其他,市级,省级,区级,校级,国家级\n表v_teacher_rcjl的jldj字段的字典值为优秀组织奖,二等,其他,优秀奖,三等,一等,特等");
        tableDictMap.put("v_teacher_thesis","表v_teacher_thesis的kwdj字段的字典值为国家级期刊,省级期刊,其他,核心期刊,市级期刊");
        tableDictMap.put("v_school_baseinfo","表v_school_baseinfo的school_lb字段的字典值为小学,特殊教育学校,其他,幼儿园,普通初中,中等职业学校,普通高中\n表v_school_baseinfo的school_xz字段的字典值为公办,民办");
        tableDictMap.put("v_school_reward","表v_school_reward的level_字段的字典值为地（市,厅,局）级荣誉称号,校级荣誉称号,国家级荣誉称号,其他荣誉称号,省（自治区,直辖市）级荣誉称号,区（县,局）级荣誉称号");
        tableDictMap.put("v_area_teacher_reward","表v_area_teacher_reward的level_字段的字典值为其他,市级,省级,区级,校级,国家级\n表v_area_teacher_reward的jllx字段的字典值为教学工作,科技竞赛,工作业绩,专业技术,优秀教案,市级名师工作室,班主任基本功,苏州市教育信息化先进个人,信息技术与课程整合,周氏德育,苏州市教育技术能力竞赛,苏州市教坛新苗,NOC网络教研竞赛,苏州十佳网络教学团队,优秀班主任,江苏省优秀少先队辅导员,江苏省教师网络团队竞赛,把握学科能力竞赿教师专业素养),微课,苏州市中小学教师网络团队竞赛,苏州市中小学教育技术应用能手,共青团工作先进个人,信息化教学能手,一师一优课 一课一名师,国家职业心理咨询师,教学成果奖,评优课,名优班主任,德育学科带头人,优秀德育工作者,未来教室应用课堂,教学基本功");
        tableDictMap.put("v_area_teacher_analysis","表v_area_teacher_analysis的xxlbname字段的字典值为小学,特殊教育学校,其他,幼儿园,普通初中,中等职业学校,普通高中\n表v_area_teacher_analysis的xxjbzname字段的字典值为公办,民办");

    }
}
