package com.fytec.splitter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;

public class RecursiveCharacterTextSplitter {
    private final int chunkSize;
    private final int chunkOverlap;
    private final List<String> separators;
    private final Function<String, Integer> lengthFunction;

    public RecursiveCharacterTextSplitter(
            int chunkSize,
            int chunkOverlap,
            List<String> separators,
            Function<String, Integer> lengthFunction
    ) {
        this.chunkSize = chunkSize;
        this.chunkOverlap = chunkOverlap;
        this.separators = separators != null ? separators : Arrays.asList("\n\n", "\n", " ", "");
        this.lengthFunction = lengthFunction != null ? lengthFunction : String::length;
    }

    // 默认构造函数，提供默认值
    public RecursiveCharacterTextSplitter() {
        this(500, 50, null, null);
    }

    public List<String> splitText(String text) {
        // 先递归分割文本，不处理 chunkOverlap
        List<String> chunks = _split(text, this.separators);
        return applyChunkOverlap(chunks);
    }

    // 递归分割函数
    private List<String> _split(String text, List<String> separators) {
        if (lengthFunction.apply(text) <= chunkSize) {
            return Collections.singletonList(text);
        }

        String separator = separators.getFirst();
        String[] chunksArray = text.split(separator);
        List<String> chunks = new ArrayList<>(Arrays.asList(chunksArray));

        List<String> newChunks = new ArrayList<>();
        for (String chunk : chunks) {
            if (lengthFunction.apply(chunk) > chunkSize) {
                newChunks.addAll(_split(chunk, separators.subList(1, separators.size())));
            } else {
                newChunks.add(chunk);
            }
        }

        return newChunks;
    }

    // 在最外层处理 chunkOverlap
    private List<String> applyChunkOverlap(List<String> chunks) {
        List<String> finalChunks = new ArrayList<>();
        StringBuilder currentChunk = new StringBuilder();
        String overlapBuffer = "";

        for (String chunk : chunks) {
            if (currentChunk.length() + lengthFunction.apply(chunk) > chunkSize) {
                if (!currentChunk.isEmpty()) {
                    finalChunks.add(currentChunk.toString());
                }
                // 处理 chunkOverlap
                if (chunkOverlap > 0 && currentChunk.length() > chunkOverlap) {
                    overlapBuffer = currentChunk.substring(currentChunk.length() - chunkOverlap);
                } else {
                    overlapBuffer = "";
                }
                currentChunk = new StringBuilder(overlapBuffer + chunk);
            } else {
                currentChunk.append(chunk);
            }
        }

        if (!currentChunk.isEmpty()) {
            finalChunks.add(currentChunk.toString());
        }

        return finalChunks;
    }
}