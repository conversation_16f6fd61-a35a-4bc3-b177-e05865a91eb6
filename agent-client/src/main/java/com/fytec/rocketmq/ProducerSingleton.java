package com.fytec.rocketmq;

import jakarta.annotation.PostConstruct;
import org.apache.rocketmq.client.apis.ClientConfiguration;
import org.apache.rocketmq.client.apis.ClientException;
import org.apache.rocketmq.client.apis.ClientServiceProvider;
import org.apache.rocketmq.client.apis.producer.Producer;
import org.apache.rocketmq.client.apis.producer.ProducerBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class ProducerSingleton {
    private static volatile Producer PRODUCER;
    private static String ENDPOINTS;

    @Value("${rocketmq.endpoint}")
    private String dynamicEndpoints;

    @PostConstruct
    public void init() {
        ENDPOINTS = dynamicEndpoints; // 初始化静态变量
    }

    private ProducerSingleton() {
    }

    private static Producer buildProducer(String... topics) throws ClientException {
        final ClientServiceProvider provider = ClientServiceProvider.loadService();
        ClientConfiguration clientConfiguration = ClientConfiguration.newBuilder()
                .setEndpoints(ENDPOINTS)
                .build();
        final ProducerBuilder builder = provider.newProducerBuilder()
                .setClientConfiguration(clientConfiguration)
                // Set the topic name(s), which is optional but recommended. It makes producer could prefetch
                // the topic route before message publishing.
                .setTopics(topics);
        return builder.build();
    }

    public static Producer getInstance(String... topics) throws ClientException {
        if (null == PRODUCER) {
            synchronized (ProducerSingleton.class) {
                if (null == PRODUCER) {
                    PRODUCER = buildProducer(topics);
                }
            }
        }
        return PRODUCER;
    }

}
