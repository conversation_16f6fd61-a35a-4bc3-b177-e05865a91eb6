package com.fytec.config.init;


import com.fytec.service.system.SysDictService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@AllArgsConstructor
public class DataCacheInitComponent implements CommandLineRunner {


    private final SysDictService sysDictService;
    @Override
    public void run(String... args)  {

        log.info("初始化字典数据到缓存");
        sysDictService.refreshDictCache();
    }
}
