package com.fytec.config.milvus;


import io.milvus.v2.client.ConnectConfig;
import io.milvus.v2.client.MilvusClientV2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;

@Configuration
public class MilvusConfig {
    /**
     * milvus ip addr
     */
    @Value("${milvus.config.host}")
    private String host;

    /**
     * milvus   port
     */
    @Value("${milvus.config.port}")
    private Integer port;

    @Value("${milvus.config.dbName}")
    private String dbName;

    @Bean
    @Scope("singleton")
    public MilvusClientV2 milvusClientV2() {
        return new MilvusClientV2(
                ConnectConfig.builder()
                        .uri("http://" + host + ":" + port)
                        .dbName(dbName)
                        .build()
        );
    }

}