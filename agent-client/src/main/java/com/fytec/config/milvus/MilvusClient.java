package com.fytec.config.milvus;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fytec.dto.knowledge.KnowLedgeDocSearchDTO;
import com.fytec.dto.llm.VectorRecordDTO;
import com.fytec.dto.llm.VectorResultDTO;
import com.fytec.entity.knowledge.KnowledgeSegment;
import com.fytec.mapper.knowledge.KnowledgeSegmentMapper;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import io.milvus.v2.client.MilvusClientV2;
import io.milvus.v2.common.ConsistencyLevel;
import io.milvus.v2.common.DataType;
import io.milvus.v2.common.IndexParam;
import io.milvus.v2.service.collection.request.*;
import io.milvus.v2.service.vector.request.*;
import io.milvus.v2.service.vector.request.data.BaseVector;
import io.milvus.v2.service.vector.request.data.FloatVec;
import io.milvus.v2.service.vector.request.ranker.BaseRanker;
import io.milvus.v2.service.vector.request.ranker.WeightedRanker;
import io.milvus.v2.service.vector.response.DeleteResp;
import io.milvus.v2.service.vector.response.InsertResp;
import io.milvus.v2.service.vector.response.QueryResp;
import io.milvus.v2.service.vector.response.SearchResp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
@Slf4j
public class MilvusClient {
    public static final String GROUP_ID = "groupId";
    public static final String DOC_ID = "docId";
    // 2就是新加了tag、version、overview
    private static final String CURRENT_VERSION = "3";
    // 3添加了segId，区别某个片段
    private static final String ID = "id";
    private static final String VECTOR = "vector";
    private static final String VECTOR_EXT = "vectorExt";
    private static final String TEXT = "text";
    private static final String DOC = "doc";
    private static final String URL = "url";

    private static final String TAG = "tag";
    private static final String OVERVIEW = "overview";
    private static final String VERSION = "version";

    private static final String SEG_ID = "segId";


    private static final String CREATE_BY = "createBy";
    private static final String PAGE_NUMBER = "pageNumber";


    private final MilvusClientV2 milvusClientV2;
    private final KnowledgeSegmentMapper knowledgeSegmentMapper;

    //todo 考虑之后加个升级知识库版本的功能，可以是新建一个知识库，然后拷贝一个内容，把缺失的字段补上

    public void createCollection(String collectionName, int vectorDim) {
        CreateCollectionReq.CollectionSchema schema = milvusClientV2.createSchema();

        schema.addField(AddFieldReq.builder()
                .fieldName(ID)
                .dataType(DataType.Int64)
                .isPrimaryKey(true)
                .autoID(true)
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(VECTOR)
                .dataType(DataType.FloatVector)
                .dimension(vectorDim)
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(VECTOR_EXT)
                .dataType(DataType.FloatVector)
                .dimension(vectorDim)
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(TEXT)
                .dataType(DataType.VarChar)
                .maxLength(8000)
                .isNullable(true)
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(DOC_ID)
                .dataType(DataType.Int64)
                .isNullable(true)
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(DOC)
                .dataType(DataType.VarChar)
                .isNullable(true)
                .maxLength(500)
                .build());


        schema.addField(AddFieldReq.builder()
                .fieldName(GROUP_ID)
                .dataType(DataType.Int64)
                .isNullable(true)
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(URL)
                .dataType(DataType.VarChar)
                .isNullable(true)
                .maxLength(1000)
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(CREATE_BY)
                .isNullable(true)
                .dataType(DataType.Int64)
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(PAGE_NUMBER)
                .isNullable(true)
                .dataType(DataType.Int64)
                .build());

        //2
        schema.addField(AddFieldReq.builder()
                .fieldName(VERSION)
                .dataType(DataType.VarChar)
                .defaultValue(CURRENT_VERSION)
                .isNullable(true)
                .maxLength(20)
                .build());
        schema.addField(AddFieldReq.builder()
                .fieldName(TAG)
                .dataType(DataType.VarChar)
                .isNullable(true)
                .maxLength(500)
                .build());
        schema.addField(AddFieldReq.builder()
                .fieldName(OVERVIEW)
                .dataType(DataType.VarChar)
                .isNullable(true)
                .maxLength(500)
                .build());
        //3
        schema.addField(AddFieldReq.builder()
                .fieldName(SEG_ID)
                .dataType(DataType.Int64)
                .isNullable(true)
                .build());


        IndexParam idIndex = IndexParam.builder()
                .fieldName(ID)
                .indexType(IndexParam.IndexType.STL_SORT)
                .build();

        IndexParam defaultScalarIndex = IndexParam.builder()
                .fieldName(TEXT) // Name of the scalar field to be indexed
                .indexName("default_scalar_index") // Name of the index to be created
                .indexType(IndexParam.IndexType.INVERTED)
                .build();

        IndexParam vectorIndex = IndexParam.builder()
                .fieldName(VECTOR)
                .indexType(IndexParam.IndexType.AUTOINDEX)
                .metricType(IndexParam.MetricType.COSINE)
                .build();

        IndexParam vectorExtIndex = IndexParam.builder()
                .fieldName(VECTOR_EXT)
                .indexType(IndexParam.IndexType.AUTOINDEX)
                .metricType(IndexParam.MetricType.COSINE)
                .build();

        //3
        IndexParam docIdIndex = IndexParam.builder()
                .fieldName(DOC_ID)
                .indexType(IndexParam.IndexType.STL_SORT)
                .build();
        IndexParam segIdIndex = IndexParam.builder()
                .fieldName(SEG_ID)
                .indexType(IndexParam.IndexType.STL_SORT)
                .build();

        List<IndexParam> indexParams = new ArrayList<>();
        indexParams.add(idIndex);
        indexParams.add(defaultScalarIndex);
        indexParams.add(vectorIndex);
        indexParams.add(vectorExtIndex);
        //3
        indexParams.add(docIdIndex);
        indexParams.add(segIdIndex);

        CreateCollectionReq createCollectionReq = CreateCollectionReq.builder()
                .collectionName(collectionName)
                .collectionSchema(schema)
                .indexParams(indexParams)
                .build();

        milvusClientV2.createCollection(createCollectionReq);

        GetLoadStateReq stateReq = GetLoadStateReq.builder()
                .collectionName(collectionName)
                .build();

        Boolean loaded = milvusClientV2.getLoadState(stateReq);
        System.out.println(loaded);
    }

    public void createDynamicCollection(String collectionName, int vectorDim, List<String> metaDataList) {
        CreateCollectionReq.CollectionSchema schema = milvusClientV2.createSchema();

        schema.addField(AddFieldReq.builder()
                .fieldName(ID)
                .dataType(DataType.Int64)
                .isPrimaryKey(true)
                .autoID(true)
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(VECTOR)
                .dataType(DataType.FloatVector)
                .dimension(vectorDim)
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(VECTOR_EXT)
                .dataType(DataType.FloatVector)
                .dimension(vectorDim)
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(TEXT)
                .dataType(DataType.VarChar)
                .maxLength(8000)
                .build());
        schema.addField(AddFieldReq.builder()
                .fieldName(DOC)
                .dataType(DataType.VarChar)
                .isNullable(true)
                .maxLength(500)
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(DOC_ID)
                .dataType(DataType.Int64)
                .isNullable(true)
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(URL)
                .dataType(DataType.VarChar)
                .isNullable(true)
                .maxLength(1000)
                .build());

        schema.addField(AddFieldReq.builder()
                .fieldName(PAGE_NUMBER)
                .isNullable(true)
                .dataType(DataType.Int64)
                .build());

        if (metaDataList != null && !metaDataList.isEmpty()) {
            //扩展字段，存储一些过滤条件字段，不建议放长文本字段
            metaDataList.forEach(metaData -> {
                schema.addField(AddFieldReq.builder()
                        .fieldName(metaData)
                        .dataType(DataType.VarChar)
                        .isNullable(true)
                        .maxLength(500)
                        .build());
            });
        }


        IndexParam idIndex = IndexParam.builder()
                .fieldName(ID)
                .indexType(IndexParam.IndexType.STL_SORT)
                .build();

        IndexParam defaultScalarIndex = IndexParam.builder()
                .fieldName(TEXT) // Name of the scalar field to be indexed
                .indexName("default_scalar_index") // Name of the index to be created
                .indexType(IndexParam.IndexType.INVERTED)
                .build();

        IndexParam vectorIndex = IndexParam.builder()
                .fieldName(VECTOR)
                .indexType(IndexParam.IndexType.AUTOINDEX)
                .metricType(IndexParam.MetricType.COSINE)
                .build();

        IndexParam vectorExtIndex = IndexParam.builder()
                .fieldName(VECTOR_EXT)
                .indexType(IndexParam.IndexType.AUTOINDEX)
                .metricType(IndexParam.MetricType.COSINE)
                .build();

        List<IndexParam> indexParams = new ArrayList<>();
        indexParams.add(idIndex);
        indexParams.add(defaultScalarIndex);
        indexParams.add(vectorIndex);
        indexParams.add(vectorExtIndex);

        CreateCollectionReq createCollectionReq = CreateCollectionReq.builder()
                .collectionName(collectionName)
                .collectionSchema(schema)
                .indexParams(indexParams)
                .build();

        milvusClientV2.createCollection(createCollectionReq);

        GetLoadStateReq stateReq = GetLoadStateReq.builder()
                .collectionName(collectionName)
                .build();

        Boolean loaded = milvusClientV2.getLoadState(stateReq);
        System.out.println(loaded);
    }

    public Boolean isExitCollection(String collectionName) {
        HasCollectionReq request = HasCollectionReq.builder()
                .collectionName(collectionName)
                .build();
        return milvusClientV2.hasCollection(request);
    }

    public void dropCollection(String collectionName) {
        DropCollectionReq request = DropCollectionReq.builder().collectionName(collectionName).build();
        milvusClientV2.dropCollection(request);
    }


    public void loadCollection(String collectionName) {
        LoadCollectionReq request = LoadCollectionReq.builder()
                .collectionName(collectionName)
                .build();
        milvusClientV2.loadCollection(request);
    }

    public List<Object> insert(List<VectorRecordDTO> records, String collectionName) {
        return insert(records, collectionName, 500); // Default batch size of 500
    }

    public List<Object> insert(List<VectorRecordDTO> records, String collectionName, int batchSize) {
        try {
            List<Object> allPrimaryKeys = new ArrayList<>();
            Gson gson = new Gson();

            for (int i = 0; i < records.size(); i += batchSize) {
                int end = Math.min(i + batchSize, records.size());
                List<VectorRecordDTO> batch = records.subList(i, end);

                List<JsonObject> data = new ArrayList<>();
                for (VectorRecordDTO record : batch) {
                    JsonObject jsonObject = gson.fromJson(JSON.toJSONString(record), JsonObject.class);
                    if (record.getMetadata() != null) {
                        record.getMetadata().forEach(jsonObject::addProperty);
                    }
                    data.add(jsonObject);
                }

                InsertReq insertReq = InsertReq.builder()
                        .collectionName(collectionName)
                        .data(data)
                        .build();

                InsertResp insertResp = milvusClientV2.insert(insertReq);
                allPrimaryKeys.addAll(insertResp.getPrimaryKeys());
            }
            return allPrimaryKeys;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public List<Object> update(List<VectorRecordDTO> records, String collectionName) {
//        Gson gson = new Gson();
//        List<JsonObject> data = new ArrayList<>();
//        for (VectorRecordDTO record : records) {
//            data.add(gson.fromJson(JSON.toJSONString(record), JsonObject.class));
//        }
//
//        UpsertReq upsertReq = UpsertReq.builder()
//                .collectionName(collectionName)
//                .data(data)
//                .build();
//
//        UpsertResp upsertResp = milvusClientV2.upsert(upsertReq);
//        log.info("upsertResp:{}", upsertResp.getUpsertCnt());
        //todo 调查一下更新问题
        List<String> ids = new ArrayList<>();
        records.forEach(record -> {
            ids.add(record.getId().toString());
            record.setId(null);
        });
        delete(ids, collectionName);
        return insert(records, collectionName);
    }

    public void delete(List<String> ids, String collectionName) {
        try {
            List<Object> params = new ArrayList<>();
            for (String id : ids) {
                params.add(BigInteger.valueOf(Long.parseLong(id)));
            }
            DeleteResp deleteResp = milvusClientV2.delete(DeleteReq.builder()
                    .collectionName(collectionName)
                    .ids(params)
                    .build());
            loadCollection(collectionName);
            System.out.println(deleteResp.getDeleteCnt());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    public List<Map<String, Object>> query(String filter, String collectionName, List<String> columns) {
        try {
            List<String> fields = new ArrayList<>(List.of(ID));
            if (columns != null && !columns.isEmpty()) {
                fields.addAll(columns);
            }

            List<Map<String, Object>> results = new ArrayList<>();
            QueryReq queryReq = QueryReq.builder()
                    .collectionName(collectionName)
                    .filter(filter)
                    .outputFields(fields)
                    .build();
            QueryResp queryResp = milvusClientV2.query(queryReq);
            List<QueryResp.QueryResult> queryResults = queryResp.getQueryResults();
            if (queryResults != null && !queryResults.isEmpty()) {
                queryResults.forEach(queryResult -> {
                    Map<String, Object> entity = queryResult.getEntity();
                    results.add(entity);
                });
                return results;
            }
            return null;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public List<VectorResultDTO> search(Integer topK, List<Float> vector, String collectionName, String filter) {
        try {
            List<VectorResultDTO> dtos = new ArrayList<>();
            FloatVec queryVector = new FloatVec(vector);
            SearchReq.SearchReqBuilder<?, ?> searchReqBuilder = SearchReq.builder()
                    .collectionName(collectionName)
                    .data(Collections.singletonList(queryVector))
                    .annsField(VECTOR_EXT)
                    .topK(topK)
                    .outputFields(Arrays.asList(ID, TEXT, DOC_ID, DOC, URL, PAGE_NUMBER));//GROUP_ID
            if (StringUtils.isNotBlank(filter)) {
                searchReqBuilder.filter(filter);
            }
            SearchResp searchResp = milvusClientV2.search(searchReqBuilder.build());


            List<List<SearchResp.SearchResult>> searchResults = searchResp.getSearchResults();
            System.out.println(searchResults);
            for (List<SearchResp.SearchResult> results : searchResults) {
                System.out.println("TopK results:");
                List<Long> vectorIds = results.stream().map(r -> (Long) r.getEntity().get(ID)).toList();
                if (CollUtil.isEmpty(vectorIds)) {
                    continue;
                }
                Map<String, String> vectorIdToText = knowledgeSegmentMapper.selectList(
                        new LambdaQueryWrapper<KnowledgeSegment>()
                                .in(KnowledgeSegment::getVectorId, vectorIds)
                ).stream().collect(Collectors.toMap(KnowledgeSegment::getVectorId, KnowledgeSegment::getText));

                results.forEach(r -> {
                    VectorResultDTO dto = new VectorResultDTO();
                    Map<String, Object> entity = r.getEntity();
                    dto.setId((Long) entity.get(ID));
                    String text = vectorIdToText.get(dto.getId().toString());
                    dto.setText(StrUtil.isNotEmpty(text) ? text : "");
                    dto.setDocId(ObjectUtil.isNotEmpty(entity.get(DOC_ID)) ? (Long) entity.get(DOC_ID) : 0);
                    dto.setGroupId(ObjectUtil.isNotEmpty(entity.get(GROUP_ID)) ? (Long) entity.get(GROUP_ID) : 0);
                    dto.setDoc(ObjectUtil.isNotEmpty(entity.get(DOC)) ? entity.get(DOC).toString() : "");
                    dto.setUrl(ObjectUtil.isNotEmpty(entity.get(URL)) ? entity.get(URL).toString() : "");
                    dto.setPageNumber(ObjectUtil.isNotEmpty(entity.get(PAGE_NUMBER)) ? (Long) entity.get(PAGE_NUMBER) : 0);
                    dto.setScore(r.getScore());
                    dtos.add(dto);
                });
            }
            return dtos;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    public List<VectorResultDTO> search(KnowLedgeDocSearchDTO configs, List<List<Float>> vectors, String collectionName) {
        if (vectors.size() < 2) {
            return search(configs.getTopK(), vectors.getFirst(), collectionName, configs.getFilter());
        }
        // 混合查询
        List<AnnSearchReq> searchRequests = new ArrayList<>();
        List<Float> rerankerWeightArr = new ArrayList<>();
        for (int i = vectors.size() - 1; i >= 0; i--) {
            List<Float> vector = vectors.get(i);
            FloatVec queryVector = new FloatVec(vector);

            List<BaseVector> queryDenseVectors = Collections.singletonList(queryVector);

            searchRequests.add(AnnSearchReq.builder()
                    .vectorFieldName(VECTOR_EXT)
                    .vectors(queryDenseVectors)
                    .metricType(IndexParam.MetricType.COSINE)
//                    .metricType(IndexParam.MetricType.IP)
//                    .params("{\"nprobe\": 10}")
                    .topK(configs.getTopK())
                    .build());
            // 最后一个最重要，是；join的，其他的，一次减少0.1吧，不能低于0.6，最多是0.8
//            rerankerWeightArr.add(1.0f - 0.1f * (i + 1 - vectors.size()));
//            float e = 1.0f - 0.1f * (i - vectors.size());
            float e = 1.0f - 0.1f * (vectors.size() - i);
            rerankerWeightArr.add(Math.max(e, 0.3f));
        }
        BaseRanker reranker = new WeightedRanker(rerankerWeightArr);

        List<VectorResultDTO> dtos = new ArrayList<>();
//
//        search_params = {
//                "metric_type": "L2",
//                "params": {"nprobe": 10}
//        }


        HybridSearchReq hybridSearchReq = HybridSearchReq.builder()
                .collectionName(collectionName)
                .searchRequests(searchRequests)
                .ranker(reranker)
                .topK(configs.getTopK())
                .consistencyLevel(ConsistencyLevel.BOUNDED)
                .outFields(Arrays.asList(ID, TEXT, DOC, URL, PAGE_NUMBER))
                .build();


//        SearchResp searchResp = milvusClientV2.search(SearchReq.builder()
//                .collectionName(collectionName)
//                .data
//                .annsField(VECTOR)
//                .topK(configs.getTopK())
//                .outputFields(Arrays.asList(ID, TEXT, DOC, URL))
//                .build());
        SearchResp searchResp = milvusClientV2.hybridSearch(hybridSearchReq);


        List<List<SearchResp.SearchResult>> searchResults = searchResp.getSearchResults();
//        System.out.println(searchResults);
        for (List<SearchResp.SearchResult> results : searchResults) {
            System.out.println("TopK results:" + results.size());

            List<Long> vectorIds = results.stream().map(r -> (Long) r.getEntity().get(ID)).toList();
            if (CollUtil.isEmpty(vectorIds)) {
                continue;
            }
            Map<String, String> vectorIdToText = knowledgeSegmentMapper.selectList(
                    new LambdaQueryWrapper<KnowledgeSegment>()
                            .in(KnowledgeSegment::getVectorId, vectorIds)
            ).stream().collect(Collectors.toMap(KnowledgeSegment::getVectorId, KnowledgeSegment::getText));

            results.forEach(r -> {
                VectorResultDTO dto = new VectorResultDTO();
                Map<String, Object> entity = r.getEntity();
                dto.setId((Long) entity.get(ID));
                String text = vectorIdToText.get(dto.getId().toString());
                dto.setText(StrUtil.isNotEmpty(text) ? text : "");
                dto.setDoc(ObjectUtil.isNotEmpty(entity.get(DOC)) ? entity.get(DOC).toString() : "");
                dto.setDocId(ObjectUtil.isNotEmpty(entity.get(DOC_ID)) ? (Long) entity.get(DOC_ID) : 0);
                dto.setUrl(ObjectUtil.isNotEmpty(entity.get(URL)) ? entity.get(URL).toString() : "");
                dto.setPageNumber(ObjectUtil.isNotEmpty(entity.get(PAGE_NUMBER)) ? (Long) entity.get(PAGE_NUMBER) : 0);
                dto.setScore(r.getScore());
                dtos.add(dto);
            });
        }
        return dtos;
    }
}
