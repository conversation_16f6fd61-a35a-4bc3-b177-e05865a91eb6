package com.fytec.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * TextIn配置属性
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "external.text-in")
public class TextInProperties {

    /**
     * API基础URL
     */
    private String baseUrl = "https://api.textin.com/ai/service/v1";

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 密钥
     */
    private String secretCode;

    /**
     * 连接超时时间（毫秒）
     */
    private Integer connectTimeout = 30000;

    /**
     * 读取超时时间（毫秒）
     */
    private Integer readTimeout = 60000;

    /**
     * 是否启用
     */
    private Boolean enabled = true;

    /**
     * 文档解析API端点
     */
    private String parseApi = "/pdf_to_markdown";
    
}