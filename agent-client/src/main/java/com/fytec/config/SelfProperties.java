package com.fytec.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "self")
@Data
public class SelfProperties {
    private ClientProperties client;
    private RsaProperties rsa;

    @Data
    public static class ClientProperties {
        private String clientId;
        private String clientSecret;
    }

    @Data
    public static class RsaProperties {
        private String publicKey;
        private String privateKey;
    }
}
