package com.fytec.config.wechat;

import com.fytec.service.wechat.WechatWorkService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class WechatWorkConfig {
    @Value("${wechat.work.corp-id}")
    private String corpId;

    @Value("${wechat.work.corp-secret}")
    private String corpSecret;

    @Value("${wechat.work.agent-id}")
    private String agentId;

    @Value("${wechat.work.redirect-uri}")
    private String redirectUri;

    @Bean
    public WechatWorkService wechatWorkService() {
        return new WechatWorkService(corpId, corpSecret, agentId, redirectUri);
    }
}
