package com.fytec.config;

import com.fytec.constant.ExternalConstants;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 讯飞流式语音合成配置属性
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "external.xfyun.stream-tts")
public class XfyunStreamTtsProperties {

    /**
     * 应用ID
     */
    private String appId;

    /**
     * API密钥
     */
    private String apiKey;

    /**
     * API密钥
     */
    private String apiSecret;

    /**
     * 流式TTS WebSocket地址
     */
    private String hostUrl;

    /**
     * 连接超时时间（毫秒）
     */
    private Integer connectTimeout;

    /**
     * 读取超时时间（毫秒）
     */
    private Integer readTimeout;

    /**
     * 是否启用
     */
    private Boolean enabled = true;
}
