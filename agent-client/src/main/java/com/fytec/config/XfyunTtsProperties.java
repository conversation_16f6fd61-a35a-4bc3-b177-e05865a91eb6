package com.fytec.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 讯飞长文本语音合成配置属性
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "external.xfyun.tts")
public class XfyunTtsProperties {

    /**
     * 应用ID
     */
    private String appId;

    /**
     * API密钥
     */
    private String apiKey;

    /**
     * API密钥
     */
    private String apiSecret;

    /**
     * 创建任务API端点
     */
    private String createTaskUrl = "https://api-dx.xf-yun.com/v1/private/dts_create";

    /**
     * 查询任务API端点
     */
    private String queryTaskUrl = "https://api-dx.xf-yun.com/v1/private/dts_query";

    /**
     * 连接超时时间（毫秒）
     */
    private Integer connectTimeout = 30000;

    /**
     * 读取超时时间（毫秒）
     */
    private Integer readTimeout = 60000;

    /**
     * 是否启用
     */
    private Boolean enabled = true;
}