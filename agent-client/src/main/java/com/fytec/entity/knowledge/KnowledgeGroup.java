package com.fytec.entity.knowledge;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fytec.entity.BaseAutoEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@TableName("ai_knowledge_group")
public class KnowledgeGroup extends BaseAutoEntity {

    private Long parentId;

    @Schema(description = "组名")
    private String name;

    private String type;

    private Integer seq;

    @TableField(value = "is_sys")
    private boolean systemGroup;

    @TableField(value = "is_default")
    private boolean defaultGroup;

    private Long knowledgeId;
}