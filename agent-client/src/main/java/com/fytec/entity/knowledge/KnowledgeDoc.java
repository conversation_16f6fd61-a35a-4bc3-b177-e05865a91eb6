package com.fytec.entity.knowledge;


import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fytec.entity.BaseAutoEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Map;

@Data
@TableName("ai_knowledge_doc")
public class KnowledgeDoc extends BaseAutoEntity {

    @Schema(description = "知识id")
    private Long knowledgeId;

    @TableField(value = "group_id", updateStrategy = FieldStrategy.NOT_NULL)
    private Long groupId;

    @Schema(description = "添加文档方式（knowledge_doc_type）")
    private String docType;

    @Schema(description = "文档来源类型")
    private String docSourceType;

    @Schema(description = "文件名")
    private String fileName;

    @Schema(description = "文件类型")
    private String fileType;

    @Schema(description = "文件内容")
    private String fileContent;

    @TableField(exist = false)
    private Map<Integer, String> fileContentMap;

    @Schema(description = "文件url")
    private String fileUrl;

    @Schema(description = "文件大小")
    private Long fileSize;

    @TableField(value = "is_visible", updateStrategy = FieldStrategy.NOT_NULL)
    private Boolean visible;

    private Long noteId;

    @Schema(description = "文件解析任务ID")
    private String taskId;

    private boolean defaultSegment;

    @Schema(description = "是否自动分段")
    private Boolean autoSegment;

    @Schema(description = "分段策略")
    private String segmentStrategy;

    @Schema(description = "自定义分段字符")
    private String customizeChar;

    @Schema(description = "最大分段长度")
    private int maxLength;

    @Schema(description = "分段重叠度%")
    private int overlapLength;

    @Schema(description = "状态")
    @TableField(value = "status", updateStrategy = FieldStrategy.NOT_NULL)
    private String status;

    private String reason;

    @Schema(description = "文件内容")
    private String overview;

    @Schema(description = "第三方用户ID")
    private String thirdUserId;
}