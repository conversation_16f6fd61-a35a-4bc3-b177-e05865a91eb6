package com.fytec.entity.knowledge;


import com.baomidou.mybatisplus.annotation.TableName;
import com.fytec.entity.BaseAutoEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("ai_knowledge")
public class Knowledge extends BaseAutoEntity {

    @Schema(description = "资源ID")
    private Long resourceId;

    @Schema(description = "向量模型ID")
    private Long embeddedId;

    private String type;

    private String expireOption;

    private LocalDateTime startTime;
    private LocalDateTime endTime;

    private String baseType;//知识库基本类型，默认、标注、规则、异常的

    private String userId;
}