package com.fytec.entity.knowledge;


import com.baomidou.mybatisplus.annotation.TableName;
import com.fytec.entity.BaseAutoEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@TableName("ai_knowledge_segment_ext")
public class KnowledgeSegmentExt extends BaseAutoEntity {

    @Schema(description = "段落id")
    private Long segmentId;

    private String extName;

    private String extType;

    private String extContent;

    private int extOrder;

}