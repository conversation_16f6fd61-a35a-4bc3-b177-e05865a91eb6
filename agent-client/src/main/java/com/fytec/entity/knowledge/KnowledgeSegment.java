package com.fytec.entity.knowledge;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fytec.entity.BaseAutoEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@TableName("ai_knowledge_segment")
public class KnowledgeSegment extends BaseAutoEntity {

    @Schema(description = "知识id")
    private Long knowledgeId;

    @Schema(description = "知识文档id")
    private Long docId;

    @TableField(value = "page_number")
    private Long pageNumber;

    @Schema(description = "分段内容")
    private String text;

    @Schema(description = "向量ID")
    private String vectorId;

    private int vectorIndex;

    @Schema(description = "标签内容(不超过200字)")
    private String tag;

    @Schema(description = "概述(不超过500字)")
    private String overview;

    @Schema(description = "知识文档名称")
    private String doc;

    private String type;//片段来源，自动分解的，默认null，是否是自己拆分的
}