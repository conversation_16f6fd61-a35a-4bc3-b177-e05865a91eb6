package com.fytec.entity.plugin;


import com.baomidou.mybatisplus.annotation.TableName;
import com.fytec.entity.BaseAutoEntity;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("ai_plugin_basic")
public class PluginBasic extends BaseAutoEntity {
    private Long resourceId;

    private String type;

    private String url;

    private String headers;

    private String authType;

    private String authInfo;

    private String mcpServerType;

    private String mcpServerConfig;

    private String status;

    private String version;

    private LocalDateTime publishTime;

    private String pubRecord;
}