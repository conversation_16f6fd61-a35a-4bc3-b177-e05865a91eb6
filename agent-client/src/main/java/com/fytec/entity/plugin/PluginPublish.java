package com.fytec.entity.plugin;


import com.baomidou.mybatisplus.annotation.TableName;
import com.fytec.entity.BaseAutoEntity;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("ai_plugin_publish")
public class PluginPublish extends BaseAutoEntity {
    private Long toolId;

    private Long pluginId;

    private Long resourceId;

    private String name;

    private String description;

    private String path;

    private String suffixPath;

    private String method;

    private String requestParams;

    private String responseParams;

    private String status;

    private String debugStatus;

    private boolean enable;

    private String version;

    private LocalDateTime publishTime;

    private String pubRecord;

    private String functionCallTool;
}