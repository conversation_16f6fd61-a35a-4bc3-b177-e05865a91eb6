package com.fytec.entity.resource;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fytec.entity.BaseAutoEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@TableName("ai_resource")
public class AiResource extends BaseAutoEntity {

    private Long parentId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "图片")
    private String logo;

    @Schema(description = "类型")
    private String type;

    @Schema(description = "状态：0-未发布，1-已发布")
    private String status;

    @TableField(value = "is_sys")
    private boolean systemResource = false;

    private String code;

}
