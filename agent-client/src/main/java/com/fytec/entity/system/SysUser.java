package com.fytec.entity.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fytec.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Schema(description = "用户")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_sys_user")
public class SysUser extends BaseEntity {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("name")
    private String name;

    @TableField("login_name")
    private String loginName;

    @TableField("tel")
    private String tel;

    @TableField("image_url")
    private String imageUrl;

    @TableField("password")
    private String password;

    @TableField("status")
    private String status;

    @TableField("is_sys")
    private String isSys;

    @TableField("third_user_type")
    private String thirdUserType;

    @TableField("third_user_id")
    private String thirdUserId;

    @TableField("dept_code")
    private String deptCode;
}
