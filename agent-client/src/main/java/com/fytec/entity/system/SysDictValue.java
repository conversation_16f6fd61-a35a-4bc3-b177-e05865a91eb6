package com.fytec.entity.system;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fytec.entity.BaseAutoEntity;
import lombok.Data;

@Data
@TableName("t_code_value")
public class SysDictValue extends BaseAutoEntity {

    @TableField("TYPE_CODE")
    private String typeCode;

    @TableField("VALUE")
    private String value;

    @TableField("LABEL")
    private String label;

    @TableField("DESCRIPTION")
    private String description;

    @TableField("SEQ")
    private Integer dictSort;

}
