package com.fytec.entity.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fytec.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;


@Schema(description = "组")
@Data
@TableName("t_sys_org")
public class SysOrganization extends BaseEntity {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String code;

    @TableField("parent_id")
    private Long parentId;

    @TableField("name")
    private String name;

    @TableField("source_from")
    private String sourceFrom;

    @TableField("sync")
    private boolean sync;

    @TableField("sync_time")
    private LocalDateTime syncTime;
}
