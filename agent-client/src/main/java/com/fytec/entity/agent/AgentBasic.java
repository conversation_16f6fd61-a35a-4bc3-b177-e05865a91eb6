package com.fytec.entity.agent;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fytec.entity.BaseAutoEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("ai_agent_basic")
public class AgentBasic extends BaseAutoEntity {


    @Schema(description = "智能体名称")
    private String name;

    @Schema(description = "智能体介绍")
    private String description;

    @Schema(description = "智能体图标")
    private String logo;

    @Schema(description = "状态：0-未发布，1-已发布")
    private String status;

    @Schema(description = "标签")
    private String tags;

    private Boolean open;
}
