package com.fytec.entity.agent;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fytec.entity.BaseAutoEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * Copyright (C), 2025-2099
 * 智能体处理器配置
 *
 * <AUTHOR> lix
 * @date :   2025/6/9 15:51
 * Version: V1.0.0
 */
@Data
@TableName("agent_handler_config")
public class AgentHandlerConfig extends BaseAutoEntity {

    @Schema(description = "环境")
    private String envType;

    @Schema(description = "智能体ID")
    private Long agentId;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "思考智能体ID")
    private Long thinkAgentId;

    @Schema(description = "格式化输出智能体ID")
    private Long formatAgentId;

    @Schema(description = "错误信息")
    private String errorMsg;

    @Schema(description = "数据库配置信息，键值对形式")
    private Map<String,String>  sqlConfigs;

    @Schema(description = "阻止并打印思考信息")
    private boolean blockButPrintThinks ;

    @Schema(description = "是否开启应用")
    private boolean enableCitation ;
}
