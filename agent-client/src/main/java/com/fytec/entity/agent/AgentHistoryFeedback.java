package com.fytec.entity.agent;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fytec.entity.BaseAutoEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 反馈的历史记录
 */
@Data
@TableName("ai_agent_history_feedback")
public class AgentHistoryFeedback extends BaseAutoEntity {

    @Schema(description = "历史ID")
    private Long historyId;//关联是哪个历史处理有问题（一个思维链都可以详细反馈）

    @Schema(description = "反馈类型，AGENT_FEEDBACK_TYPE")
    private String type;

    @Schema(description = "处理状态，AGENT_FEEDBACK_STATUS")
    private String status;//处理状态；已删除？

    @Schema(description = "处理结果。具体的处理方式，AGENT_FEEDBACK_RESULT")
    private String result;// 处理结果，code，可能包含：专为正样本，已删除，等等

    @Schema(description = "处理备注")
    private String remark;//处理备注

    @Schema(description = "用户提交的备注")
    private String userRemark;//用户提交的备注

    @Schema(description = "文档ID")
    private String docIds;//可以对应到某一个片段。如果这个片段被删除了，这里其实需要同步更新。所以建议之后只考虑通过反馈管理管理知识库好一些

    @Schema(description = "知识库id")
    private Long knowledgeId;//知识库id。

    @Schema(description = "智能体id")
    private Long agentId; // 直接根据智能体id检索。不会变得字段，冗余存储

    @Schema(description = "流程ID")
    private String flowId; //属于哪个流程

    //可以根据反馈记录，直接查处整个流程链
    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "客户端ID")
    private String clientId;

    @Schema(description = "第三方用户Id")
    private String thirdUserId;

    private String message_id;//第几条

    @Schema(description = "用户原始输入")
    private String rawInput;
    @Schema(description = "实际输入")
    private String input;
    @Schema(description = "回答内容")
    private String answer;//相同输入、改写、回答情况下，不允许重复提交？ 或者之后添加一个检索关联，这样可以方便修改。
}