package com.fytec.entity.agent;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fytec.entity.BaseAutoEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@TableName("ai_agent_publish_history")
public class AgentPublishHistory extends BaseAutoEntity {
    @Schema(description = "会话ID")
    private String conversationId;

    @Schema(description = "智能体发布ID")
    private Long agentPublishId;

    private Long messageId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户输入")
    private String userInput;
    @Schema(description = "用户原始输入")
    private String rawInput;

    @Schema(description = "回答")
    private String answer;

    @Schema(description = "思考过程")
    private String reasoning;

    private String reference;

    private String modelName;

    private String knowledgeDocInfo;

    private String clientId;

    private String thirdUserId;

    private Long agentId;

    private String flowId;

    private String extraInfo;

    @Schema(description = "第三方id，可在历史记录时进行查询")
    private String objectId;

    @Schema(description = "思维过程链")
    private String reasoningchain;


    private Long runtime;// 运行时间（毫秒数）

    private String status;// 执行后的状态，可能后续节点断了，也算是失败;自己成功，后续失败的也有可能

    private String error;// 错误信息

    @Schema(description = "执行类型")
    private String type;// 智能体执行类型，最后一个节点，最开始的节点（入口）、并行的、默认等
}