package com.fytec.entity.agent;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fytec.dto.knowledge.KnowledgeDocHistoryDTO;
import com.fytec.entity.BaseAutoEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@TableName("ai_agent_dev_history")
public class AgentDevelopHistory extends BaseAutoEntity {

    @Schema(description = "智能体ID")
    private Long agentId;

    private Long messageId;

    @Schema(description = "用户输入")
    private String userInput;

    @Schema(description = "回答")
    private String answer;

    @Schema(description = "思考过程")
    private String reasoning;

    private String reference;

    @Schema(description = "调试用户ID")
    private Long debugId;

    private String knowledgeDocInfo;
}
