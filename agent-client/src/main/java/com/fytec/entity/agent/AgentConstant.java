package com.fytec.entity.agent;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fytec.entity.BaseAutoEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@TableName("ai_agent_constant")
public class AgentConstant extends BaseAutoEntity {

    @Schema(description = "名称")
    private String name;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "code")
    private String code;

    @Schema(description = "值类型（json、txt、markdown）")
    private String type;

    @Schema(description = "值")
    private String value;

}
