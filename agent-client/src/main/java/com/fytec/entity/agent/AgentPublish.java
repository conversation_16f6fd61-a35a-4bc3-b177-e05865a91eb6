package com.fytec.entity.agent;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fytec.entity.BaseAutoEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("ai_agent_publish")
public class AgentPublish extends BaseAutoEntity {


    @Schema(description = "智能体ID")
    private Long agentId;

    @Schema(description = "智能体类型(agent_type)")
    private String type;

    @Schema(description = "开场白")
    private String prologue;

    @Schema(description = "默认问题(数组)")
    private String tips;

    @Schema(description = "提示词")
    private String prompt;

    @Schema(description = "模型配置")
    private String model;

    @Schema(description = "插件")
    private String plugins;

    @Schema(description = "工作流")
    private String workflows;

    @Schema(description = "知识库")
    private String knowledge;

    @Schema(description = "用户问题建议")
    private String suggestions;

    @Schema(description = "语音")
    private String voice;

    @Schema(description = "版本")
    private String version;

    @Schema(description = "发布时间")
    private LocalDateTime publishTime;

    @Schema(description = "发布记录")
    private String pubRecord;

    @Schema(description = "标注知识库")
    private String positiveKnowledge;

    @Schema(description = "问题知识库")
    private String negativeKnowledge;

    @Schema(description = "历史知识库")
    private String historyKnowledge;
}
