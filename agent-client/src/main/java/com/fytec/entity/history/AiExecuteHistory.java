package com.fytec.entity.history;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fytec.entity.BaseAutoEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@TableName("ai_execute_history")
public class AiExecuteHistory extends BaseAutoEntity {
    @Schema(description = "会话ID")
    private String conversationId;

    private String thirdUserId;

    private String userInput;

    private String content;
}
