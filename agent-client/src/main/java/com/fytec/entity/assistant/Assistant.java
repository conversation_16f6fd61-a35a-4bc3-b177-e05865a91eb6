package com.fytec.entity.assistant;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fytec.entity.BaseAutoEntity;
import com.fytec.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@TableName("ai_assistant")
public class Assistant  extends BaseEntity {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long agentId;

    private Long userId;
}
