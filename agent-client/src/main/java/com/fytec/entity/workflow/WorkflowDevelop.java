package com.fytec.entity.workflow;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fytec.entity.BaseAutoEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@TableName("ai_workflow_dev")
public class WorkflowDevelop extends BaseAutoEntity {


    @Schema(description = "工作流资源ID")
    private Long resourceId;

    @Schema(description = "工作流配置")
    private String configJson;

    private String configTreeJson;

    @Schema(description = "节点配置映射")
    private String nodeConfigMapping;

    @Schema(description = "节点边映射")
    private String nodeEdgesMapping;
}
