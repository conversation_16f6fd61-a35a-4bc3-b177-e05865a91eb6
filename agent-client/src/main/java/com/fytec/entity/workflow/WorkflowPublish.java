package com.fytec.entity.workflow;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fytec.entity.BaseAutoEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("ai_workflow_publish")
public class WorkflowPublish extends BaseAutoEntity {


    @Schema(description = "资源ID")
    private Long resourceId;

    @Schema(description = "工作流配置")
    private String configJson;
    private String configTreeJson;

    @Schema(description = "节点配置映射")
    private String nodeConfigMapping;

    @Schema(description = "节点边映射")
    private String nodeEdgesMapping;

    @Schema(description = "工作流function call tool")
    private String functionCallTool;

    @Schema(description = "版本")
    private String version;

    @Schema(description = "发布时间")
    private LocalDateTime publishTime;

    @Schema(description = "发布记录")
    private String pubRecord;


}
