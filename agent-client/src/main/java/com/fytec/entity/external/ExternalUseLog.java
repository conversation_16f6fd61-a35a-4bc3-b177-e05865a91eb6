package com.fytec.entity.external;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

@Data
@Document
public class ExternalUseLog {

    /**
     * 主键ID
     */
    @Id
    private String id;

    /**
     * 调用时间
     */
    @Field("call_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime callTime;

    /**
     * 调用用户ID - 仅文字识别服务使用
     */
    @Field("user_id")
    private String userId;

    /**
     * 客户端名称
     */
    @Field("client_id")
    private String clientId;

    /**
     * 实际调用方法
     */
    @Field("method")
    private String method;

    /**
     * 语音文件名 - 仅语音识别服务使用
     */
    @Field("file_name")
    private String fileName;

    /**
     * 语音时长（秒）- 仅语音识别服务使用
     */
    @Field("duration_seconds")
    private Integer durationSeconds;

    /**
     * 字符数 - 仅文字识别服务使用
     */
    @Field("character_count")
    private Integer characterCount;

    /**
     * 折算次数/token调用次数
     */
    @Field("calculate_count")
    private Integer calculateCount;

    /**
     * 服务状态（字典值）
     */
    @Field("service_status")
    private String serviceStatus;

    /**
     * 日志类型（字典值）
     */
    @Field("service_type")
    private String serviceType;

    /**
     * 创建时间
     */
    @Field("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Field("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 备注信息
     */
    @Field("remarks")
    private String remarks;

    /**
     * 保存前的处理
     */
    public void prePersist() {
        if (this.createTime == null) {
            this.createTime = LocalDateTime.now();
        }
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 更新前的处理
     */
    public void preUpdate() {
        this.updateTime = LocalDateTime.now();
    }


}
