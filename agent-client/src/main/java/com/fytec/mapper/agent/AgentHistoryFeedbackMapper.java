package com.fytec.mapper.agent;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fytec.entity.agent.AgentHistoryFeedback;
import com.fytec.entity.agent.AgentPublishHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface AgentHistoryFeedbackMapper extends BaseMapper<AgentHistoryFeedback> {

    List<Long> selectByHistoryId(@Param("historyId") Long historyId);
}
