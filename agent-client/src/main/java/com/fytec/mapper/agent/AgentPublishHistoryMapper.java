package com.fytec.mapper.agent;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fytec.entity.agent.AgentPublishHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface AgentPublishHistoryMapper extends BaseMapper<AgentPublishHistory> {
    Long getMaxMessageId(@Param("agentId") Long agentId, @Param("userId") Long userId);

    Long getMaxMessageIdByThirdUserId(@Param("agentId") Long agentId, @Param("thirdUserId") String thirdUserId);


    List<Map<String, Object>> queryPublishedAgentHistoryGroup(@Param("thirdUserId") String thirdUserId,
                                                              @Param("clientId") String clientId,
                                                              @Param("agentId") Long agentId,
                                                              @Param("startDate") String startDate,
                                                              @Param("endDate") String endDate,
                                                              @Param("keyword") String keyword,
                                                              @Param("objectId") String objectId);

    void updateReasoningById(@Param("reason") String reason, @Param("id") Long id);

    void updateAnswerById(@Param("answer") String answer, @Param("id") Long id);

    void updateExtraInfoById(@Param("extraInfo") String extraInfo, Long id);

    void updateReasoningChainById(@Param("reasoningchain") String reasoningchain, Long id);

    void updateAgentsRunCompleteByFlowId(@Param("flowId") String flowId,@Param("status") String statusComplete);

    void updateAgentStatusById(@Param("id") Long id,@Param("status")  String status,@Param("error")  String error);

    void updateAgentType(@Param("id") Long historyId,@Param("type") String type);
}
