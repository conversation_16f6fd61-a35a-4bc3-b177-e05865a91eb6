package com.fytec.mapper.agent;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.agent.AgentBasicDTO;
import com.fytec.dto.agent.AgentQueryDTO;
import com.fytec.dto.application.ApplicationAgentDTO;
import com.fytec.entity.agent.AgentBasic;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AgentBasicMapper extends BaseMapper<AgentBasic> {

    List<AgentBasicDTO> developAgentPage(Page<AgentBasicDTO> page, @Param("param") AgentQueryDTO dto);

    List<ApplicationAgentDTO> queryPublishedAgentList(Page<ApplicationAgentDTO> page, @Param("param") AgentQueryDTO dto);

    List<ApplicationAgentDTO> queryPublishedAgentListByTags(@Param("param")  AgentQueryDTO dto);
}
