package com.fytec.mapper.workflow;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.flow.QueryWorkflowDTO;
import com.fytec.dto.flow.WorkflowDTO;
import com.fytec.entity.workflow.WorkflowPublish;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface WorkflowPublishMapper extends BaseMapper<WorkflowPublish> {

    List<WorkflowDTO> queryWorkflow(@Param("dto") QueryWorkflowDTO dto, Page<WorkflowDTO> page);
}
