package com.fytec.mapper.resource;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.resource.QueryResourceDTO;
import com.fytec.dto.resource.ResourceDTO;
import com.fytec.entity.resource.AiResource;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ResourceMapper extends BaseMapper<AiResource> {
    List<ResourceDTO> queryPage(Page<ResourceDTO> page, @Param("dto") QueryResourceDTO dto);
}
