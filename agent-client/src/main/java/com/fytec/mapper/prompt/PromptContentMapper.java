package com.fytec.mapper.prompt;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.prompt.PromptContentListDTO;
import com.fytec.dto.prompt.PromptContentQueryDTO;
import com.fytec.entity.prompt.PromptCategory;
import com.fytec.entity.prompt.PromptContent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PromptContentMapper extends BaseMapper<PromptContent> {
    List<PromptContentListDTO> queryPromptContent(Page<PromptContentListDTO> page, @Param("param") PromptContentQueryDTO dto);

    List<PromptContentListDTO> queryOutOfCategoryPromptContent(@Param("param") PromptContentQueryDTO dto);
}
