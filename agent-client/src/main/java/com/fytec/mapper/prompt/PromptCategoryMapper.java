package com.fytec.mapper.prompt;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fytec.dto.prompt.PromptCategoryDTO;
import com.fytec.dto.prompt.PromptCategoryQueryDTO;
import com.fytec.entity.prompt.PromptCategory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PromptCategoryMapper extends BaseMapper<PromptCategory> {
    List<PromptCategoryDTO> queryPromptCategory(@Param("param") PromptCategoryQueryDTO dto);
}
