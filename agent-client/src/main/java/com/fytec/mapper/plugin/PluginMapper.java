package com.fytec.mapper.plugin;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.plugin.PluginPublishListDTO;
import com.fytec.dto.plugin.PluginQueryDTO;
import com.fytec.entity.plugin.PluginBasic;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PluginMapper extends BaseMapper<PluginBasic> {
    List<PluginPublishListDTO> queryPublishPluginList(@Param("param") PluginQueryDTO dto, Page<PluginPublishListDTO> page);
}
