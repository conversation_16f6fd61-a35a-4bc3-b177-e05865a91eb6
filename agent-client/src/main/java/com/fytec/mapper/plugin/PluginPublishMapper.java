package com.fytec.mapper.plugin;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fytec.dto.plugin.PluginPublishListDTO;
import com.fytec.entity.plugin.PluginPublish;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PluginPublishMapper extends BaseMapper<PluginPublish> {
    List<PluginPublish> queryLatestPluginApiList(@Param("param") PluginPublishListDTO dto);
}
