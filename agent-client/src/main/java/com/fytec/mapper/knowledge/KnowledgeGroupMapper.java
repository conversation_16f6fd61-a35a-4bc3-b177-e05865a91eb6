package com.fytec.mapper.knowledge;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fytec.dto.knowledge.KnowledgeQueryDTO;
import com.fytec.dto.knowledge.group.KnowledgeGroupTreeDTO;
import com.fytec.entity.knowledge.KnowledgeGroup;
import com.fytec.entity.knowledge.KnowledgeTeamMember;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface KnowledgeGroupMapper extends BaseMapper<KnowledgeGroup> {
    List<KnowledgeGroupTreeDTO> queryKnowledgeTeamCreatedByMine(@Param("param") KnowledgeQueryDTO dto);

    List<KnowledgeGroupTreeDTO> queryKnowledgeTeamJoined(@Param("param") KnowledgeQueryDTO dto);

    List<KnowledgeGroupTreeDTO> queryKnowledgeRootTeamCreatedByMine(@Param("param") KnowledgeQueryDTO dto);
}
