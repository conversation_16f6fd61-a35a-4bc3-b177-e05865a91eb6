package com.fytec.mapper.knowledge;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fytec.dto.knowledge.*;
import com.fytec.entity.knowledge.KnowledgeDoc;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Mapper
public interface KnowledgeDocMapper extends BaseMapper<KnowledgeDoc>{

    List<KnowledgeDocCountDTO> selectDocCountByKnowledgeId(Long knowledgeId);

    List<KnowledgeAllInOneDTO> queryKnowledgeAllInOne(@Param("param") KnowledgeAllInOneQueryDTO dto);

// This method returns a list of KnowledgeDocDTO objects based on the provided set of document IDs
    List<KnowledgeDocDTO> docSearchNameByIds(@Param("docIds") Set<Long> docIds);

    String selectDocNameByDocId(@Param("docId")  Long docId);

    List<KnowledgeDocDTO> selectDocBaseInfoByIds(
            @Param("docIds") Set<Long> docIds,
            @Param("docSourceType")  String docSourceType
    );
}
