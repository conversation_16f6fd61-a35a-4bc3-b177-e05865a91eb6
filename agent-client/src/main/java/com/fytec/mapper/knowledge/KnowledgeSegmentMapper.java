package com.fytec.mapper.knowledge;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fytec.entity.knowledge.KnowledgeSegment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface KnowledgeSegmentMapper extends BaseMapper<KnowledgeSegment> {


    void insertBatch(@Param("list") List<KnowledgeSegment> list);

    List<Long> selectDocIdsByContent(@Param("knowledgeId") Long knowledgeId,
                                     @Param("filterContent") String filterContent);

    void updateVectorIdById(@Param("vectorId") String vectorId,@Param("segId")  Long segId);

    void updateFileNameByDocId(@Param("fileName") String fileName,@Param("id")  Long id);
}
