package com.fytec.mapper.knowledge;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.knowledge.KnowledgeDetailDTO;
import com.fytec.dto.knowledge.KnowledgeQueryDTO;
import com.fytec.entity.knowledge.Knowledge;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface KnowledgeMapper extends BaseMapper<Knowledge>{

    List<KnowledgeDetailDTO> queryKnowledge(@Param("dto") KnowledgeQueryDTO dto, Page<KnowledgeDetailDTO> page);

    List<KnowledgeDetailDTO> queryTeamKnowledgeJoined(@Param("dto") KnowledgeQueryDTO dto, Page<KnowledgeDetailDTO> page);

    void updateBaseTypeByResourceId(@Param("resourceId")  Long resourceId,
                                    @Param("baseType")
                                    String knowledgeBaseType);
}
