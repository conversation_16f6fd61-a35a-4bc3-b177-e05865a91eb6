package com.fytec.mapper.application;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.agent.AgentBasicDTO;
import com.fytec.dto.agent.AgentQueryDTO;
import com.fytec.dto.application.ApplicationBasicDTO;
import com.fytec.dto.application.ApplicationQueryDTO;
import com.fytec.entity.agent.AgentBasic;
import com.fytec.entity.application.ApplicationBasic;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ApplicationBasicMapper extends BaseMapper<ApplicationBasic> {
    List<ApplicationBasicDTO> queryApplicationPage(Page<ApplicationBasicDTO> page, @Param("param") ApplicationQueryDTO dto);
}
