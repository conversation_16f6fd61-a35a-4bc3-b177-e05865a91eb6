package com.fytec.mapper.database;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.database.DatabaseQueryDTO;
import com.fytec.entity.database.DatabaseBasic;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface DatabaseMapper extends BaseMapper<DatabaseBasic> {
    List<JSONObject> queryDatabase(@Param("dto") DatabaseQueryDTO dto, Page<JSONObject> page);
}
