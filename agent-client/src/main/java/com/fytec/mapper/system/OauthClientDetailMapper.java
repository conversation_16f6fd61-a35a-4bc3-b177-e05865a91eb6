package com.fytec.mapper.system;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fytec.entity.system.OauthClientDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface OauthClientDetailMapper extends BaseMapper<OauthClientDetail> {

    List<OauthClientDetail> selectClientNamesByClientIds(@Param("ids") Set<String> ids);

}