package com.fytec.mapper.system;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fytec.dto.system.SysMenuTreeDTO;
import com.fytec.entity.system.SysMenu;
import com.fytec.entity.system.SysUser;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface SysMenuMapper extends BaseMapper<SysMenu> {
    List<SysMenuTreeDTO> selectListOrderBySeq();

    List<SysMenuTreeDTO>  selectMenuListByRoles(List<Long> roles);

    List<String> selectPermissionListByRoles(List<Long> roleIds);
}
