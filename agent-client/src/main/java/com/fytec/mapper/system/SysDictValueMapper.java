package com.fytec.mapper.system;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fytec.dto.system.DictValueDTO;
import com.fytec.entity.system.SysDictValue;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SysDictValueMapper extends BaseMapper<SysDictValue> {
    List<DictValueDTO> selectByTypeCode(String typeCode);

    List<DictValueDTO> selectByMultipleTypeCode(@Param("types") List<String> types);
}
