package com.fytec.mapper.system;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fytec.dto.system.SysOrganizationQueryDTO;
import com.fytec.dto.system.SysOrganizationTreeDTO;
import com.fytec.entity.system.SysOrganization;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SysOrganizationMapper extends BaseMapper<SysOrganization> {
    List<SysOrganizationTreeDTO> queryOrganization(@Param("param") SysOrganizationQueryDTO dto);
}
