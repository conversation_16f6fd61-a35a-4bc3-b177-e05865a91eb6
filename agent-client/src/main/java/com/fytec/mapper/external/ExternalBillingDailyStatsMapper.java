package com.fytec.mapper.external;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.external.ExternalBillingDailyStatsQueryDTO;
import com.fytec.dto.external.ExternalBillingDailyStatsResultDTO;
import com.fytec.entity.external.ExternalBillingDailyStats;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 能力服务每日使用统计表 Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface ExternalBillingDailyStatsMapper extends BaseMapper<ExternalBillingDailyStats> {

    /**
     * 分页查询外部计费每日统计数据
     * 根据指定的日期范围查询统计数据，按 serviceCode 分组并汇总各项指标
     *
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 查询结果列表
     */
    List<ExternalBillingDailyStatsResultDTO> queryBillingStatsPage(Page<ExternalBillingDailyStatsResultDTO> page, @Param("param") ExternalBillingDailyStatsQueryDTO queryDTO);
}
