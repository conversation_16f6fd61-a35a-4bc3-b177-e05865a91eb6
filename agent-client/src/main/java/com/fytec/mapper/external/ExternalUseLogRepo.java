package com.fytec.mapper.external;

import com.fytec.entity.external.ExternalUseLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

@Component
public interface ExternalUseLogRepo extends MongoRepository<ExternalUseLog, String> {

    /**
     * 根据客户端ID分页查询外部服务使用日志
     * 按创建时间倒序排列
     *
     * @param clientId 客户端ID
     * @param pageable 分页参数
     * @return 分页结果
     */
    Page<ExternalUseLog> findByClientIdOrderByCreateTimeDesc(String clientId, Pageable pageable);

    /**
     * 根据服务类型分页查询外部服务使用日志
     * 按创建时间倒序排列
     *
     * @param serviceType 服务类型
     * @param pageable 分页参数
     * @return 分页结果
     */
    Page<ExternalUseLog> findByServiceTypeOrderByCreateTimeDesc(String serviceType, Pageable pageable);

    /**
     * 根据客户端ID和服务类型分页查询外部服务使用日志
     * 按创建时间倒序排列
     *
     * @param clientId 客户端ID
     * @param serviceType 服务类型
     * @param pageable 分页参数
     * @return 分页结果
     */
    Page<ExternalUseLog> findByClientIdAndServiceTypeOrderByCreateTimeDesc(String clientId, String serviceType, Pageable pageable);

    /**
     * 查询所有外部服务使用日志
     * 按创建时间倒序排列
     *
     * @param pageable 分页参数
     * @return 分页结果
     */
    Page<ExternalUseLog> findAllByOrderByCreateTimeDesc(Pageable pageable);

    /**
     * 根据调用时间范围查询外部服务使用日志
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 查询结果列表
     */
    List<ExternalUseLog> findByCallTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据客户端ID统计记录数
     *
     * @param clientId 客户端ID
     * @return 记录数
     */
    long countByClientId(String clientId);

    /**
     * 根据服务类型统计记录数
     *
     * @param serviceType 服务类型
     * @return 记录数
     */
    long countByServiceType(String serviceType);

    /**
     * 根据客户端ID和服务类型统计记录数
     *
     * @param clientId 客户端ID
     * @param serviceType 服务类型
     * @return 记录数
     */
    long countByClientIdAndServiceType(String clientId, String serviceType);

    /**
     * 根据调用时间范围统计记录数
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 记录数
     */
    long countByCallTimeBetween(LocalDateTime startTime, LocalDateTime endTime);


}
