package com.fytec.utils;


import cn.hutool.core.codec.Base64;
import com.fytec.config.SelfProperties;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;

/**
 * 备注，解密前台公钥加密的数据，请调用decryptWithPrivate方法
 * 每次重启之后，都会生成一个一对新的公私钥
 */
@Slf4j
public class RSAUtil {
    private static final String KEY_ALGORITHM = "RSA";
    //密钥长度
    private static final int KEY_SIZE = 4096;
    //最大加密长度
    private static final int MAX_ENCRYPT_BLOCK = KEY_SIZE / 8 - 11;
    //最大解密长度
    private static final int MAX_DECRYPT_BLOCK = KEY_SIZE / 8;


    public static String getPrivateKey() {
        return SpringContextHolder.getBean(SelfProperties.class).getRsa().getPrivateKey();
    }

    public static String getPublicKey() {
        return SpringContextHolder.getBean(SelfProperties.class).getRsa().getPublicKey();
    }

    private static byte[] decryptBASE64(String src) {
        return Base64.decode(src);
    }

    /**
     * 生成公私钥
     *
     * @param keySize 密钥长度
     */
    public static Map<String, String> genKeyPair(int keySize) {
        Map<String, String> keyMap = new HashMap<>();
        try {
            //创建密钥对生成器
            KeyPairGenerator kpg = KeyPairGenerator.getInstance("RSA");
            kpg.initialize(keySize);
            //生成密钥对
            KeyPair keyPair = kpg.generateKeyPair();
            //公钥
            PublicKey publicKey = keyPair.getPublic();
            //私钥
            PrivateKey privateKey = keyPair.getPrivate();
            keyMap.put("publicKey", Base64.encode(publicKey.getEncoded()));
            keyMap.put("privateKey", Base64.encode(privateKey.getEncoded()));
        } catch (NoSuchAlgorithmException e) {
            log.error("生成公私钥信息失败：{}", e.getMessage());
        }
        return keyMap;
    }

    /**
     * 该base64方法会自动换行
     */
    private static String encryptBASE64(byte[] src) {
        return Base64.encode(src);
    }

    /**
     * 公钥分段加密
     *
     * @param data      源数据
     * @param publicKey 公钥(BASE64编码)
     * @param length    段长
     */
    private static byte[] encryptByPublicKey(byte[] data, String publicKey, int length) {
        ByteArrayOutputStream out = null;
        byte[] encryptData = null;
        try {
            byte[] keyBytes = decryptBASE64(publicKey);
            X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            Key publicK = keyFactory.generatePublic(x509KeySpec);
            // 对数据加密
            Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
            cipher.init(Cipher.ENCRYPT_MODE, publicK);
            int inputLen = data.length;
            out = new ByteArrayOutputStream();
            int offSet = 0;
            byte[] cache;
            int i = 0;
            // 对数据分段加密
            while (inputLen - offSet > 0) {
                if (inputLen - offSet > length) {
                    cache = cipher.doFinal(data, offSet, length);
                } else {
                    cache = cipher.doFinal(data, offSet, inputLen - offSet);
                }
                out.write(cache, 0, cache.length);
                i++;
                offSet = i * length;
            }
            encryptData = out.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
            log.error("公钥分段加密失败：" + e.getMessage());
        } finally {
            try {
                out.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return encryptData;
    }


    /**
     * <P>
     * 私钥解密
     * </p>
     *
     * @param data       已加密数据
     * @param privateKey 私钥(BASE64编码)
     */
    private static byte[] decryptByPrivateKey(byte[] data, String privateKey, int length) {
        ByteArrayOutputStream out = null;
        byte[] decryptedData = null;
        try {
            byte[] keyBytes = decryptBASE64(privateKey);
            PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
            Key privateK = keyFactory.generatePrivate(pkcs8KeySpec);
            Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
            cipher.init(Cipher.DECRYPT_MODE, privateK);
            int inputLen = data.length;
            out = new ByteArrayOutputStream();
            int offSet = 0;
            byte[] cache;
            int i = 0;
            // 对数据分段解密
            while (inputLen - offSet > 0) {
                if (inputLen - offSet > length) {
                    cache = cipher.doFinal(data, offSet, length);
                } else {
                    cache = cipher.doFinal(data, offSet, inputLen - offSet);
                }
                out.write(cache, 0, cache.length);
                i++;
                offSet = i * length;
            }
            decryptedData = out.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
            log.error("私钥解密失败：{}", e.getMessage());
        } finally {
            try {
                out.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return decryptedData;
    }

    /**
     * 加密
     */
    public static String encryptByPublicKey(String data) {
        return encryptByPublicKey(data, getPublicKey());
    }

    public static String encryptByPublicKey(String data, String publicKey) {
        return encryptBASE64(encryptByPublicKey(data.getBytes(), publicKey, MAX_ENCRYPT_BLOCK)).replaceAll("[+]", "@");
    }

    /**
     * 解密
     */
    public static String decryptByPrivateKey(String data) {
        return decryptByPrivateKey(data, getPrivateKey());
    }

    public static String decryptByPrivateKey(String data, String privateKey) {
        return new String(decryptByPrivateKey(decryptBASE64(data.replaceAll("@", "+")), privateKey, MAX_DECRYPT_BLOCK));
    }

    public static void main(String[] args) {
        System.out.println(encryptByPublicKey("7890@uiop", "MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA5AKhhFg97XWeR1tB/yDKa98QJhNwBuhsgK7s/Md11hpRIAahqLO0LE+Bs8XN74IcbDb9klGTDzmagFwS2s9luK5INT4/yiLfO9OEdU2hkzt841TfQYLSrRoBoE2fRFUi6yCK1YpatWm8dK+ubHyICJ+CDScEAgut5Hviu1URLoXWaEbw1psKgf15y99/FQHyxMYwjDIhPGlKHX/1yWdpqdKH/RxGgxfgvSM8RfxryMCn9nzGw+O2j/2bGq/Np1xhpcQZHux0sIv+1vaW/E3EvQjLOjfrdO8dRSaO3bRHIWfLI169Tj1Tx/1oyMVRtK6N95PVuATVLOI9L3WQZCrbtcl8xL9lR43YhCHyt58mGVNok+k/bR6tbMaB9X1v/S4LG+XrgMzUmyE3qwpLgYAIP1dgIN3XlCsaFfo1/q2bNFtHOSIPcDiPQ1RqdnH9NtdCC7NJZj7WVGaHvXSSfaoEmvobyX6j2e+A/KEd3Dcg7qmRF4ZD20uRxQJQoNDCaJQ7L+gWSpJ/Wl/XSJztN0CvRVaafHwfpvcFmrYSO/s8e79bsZeMjRLkR+dyeIyMmZuiOdy6Wn+L7EdbJjWDp8+SUroCDM/R7idd9zcAMmvicM0hlFzLFmi8VJ9FdGf3ys9pVORl5FzTleumDT6AHYXUAnlRnV24CLnKntyo8j/+4/0CAwEAAQ=="));
    }
}
