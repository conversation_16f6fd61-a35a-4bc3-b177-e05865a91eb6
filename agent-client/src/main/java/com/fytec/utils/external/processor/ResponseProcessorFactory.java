package com.fytec.utils.external.processor;

import com.fytec.utils.external.ResponseProcessor;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 响应处理器工厂类
 * 使用单例模式管理响应处理器实例，避免重复创建对象
 *
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ResponseProcessorFactory {

    private static final Map<Class<? extends ResponseProcessor>, ResponseProcessor> PROCESSOR_CACHE = new ConcurrentHashMap<>();

    /**
     * 获取原样返回响应处理器
     *
     * @return RawResponseProcessor实例
     */
    public static ResponseProcessor getRawProcessor() {
        return getProcessor(RawResponseProcessor.class);
    }

    /**
     * 获取讯飞IAT响应处理器
     *
     * @return DefaultXfyunIatResponseProcessor实例
     */
    public static ResponseProcessor getXfyunIatProcessor() {
        return getProcessor(DefaultXfyunIatResponseProcessor.class);
    }

    /**
     * 获取讯飞ISE响应处理器
     *
     * @return DefaultXfyunIseResponseProcessor实例
     */
    public static ResponseProcessor getXfyunIseProcessor() {
        return getProcessor(DefaultXfyunIseResponseProcessor.class);
    }

    /**
     * 获取讯飞TTS响应处理器
     *
     * @return DefaultXfyunTtsResponseProcessor实例
     */
    public static ResponseProcessor getXfyunTtsProcessor() {
        return getProcessor(DefaultXfyunTtsResponseProcessor.class);
    }

    /**
     * 通用获取处理器方法
     *
     * @param processorClass 处理器类型
     * @param <T>           处理器类型泛型
     * @return 处理器实例
     */
    @SuppressWarnings("unchecked")
    private static <T extends ResponseProcessor> T getProcessor(Class<T> processorClass) {
        return (T) PROCESSOR_CACHE.computeIfAbsent(processorClass, clazz -> {
            try {
                return clazz.getDeclaredConstructor().newInstance();
            } catch (Exception e) {
                throw new RuntimeException("创建响应处理器实例失败: " + clazz.getSimpleName(), e);
            }
        });
    }

    /**
     * 清空缓存（主要用于测试）
     */
    public static void clearCache() {
        PROCESSOR_CACHE.clear();
    }
}