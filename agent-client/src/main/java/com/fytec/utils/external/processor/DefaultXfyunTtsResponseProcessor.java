package com.fytec.utils.external.processor;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fytec.utils.external.ResponseProcessor;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;

/**
 * 讯飞TTS默认响应处理器
 * 处理讯飞语音合成返回的音频数据，进行Base64解码
 *
 * <AUTHOR>
 */
@Slf4j
public class DefaultXfyunTtsResponseProcessor implements ResponseProcessor {

    @Override
    public Object process(JsonNode jsonNode) {
        if (jsonNode == null || jsonNode.isNull()) {
            return null;
        }
        
        // 提取并处理讯飞TTS的音频数据
//        JsonNode data = jsonNode.path("data");
//        if (!data.isMissingNode() && !data.isNull()) {
//            JsonNode audioNode = data.path("audio");
//            if (!audioNode.isMissingNode() && !audioNode.isNull()) {
//                String audioData = audioNode.asText();
//                if (StrUtil.isNotBlank(audioData)) {
//                    try {
//                        // 解码Base64编码的音频数据
//                        byte[] audioBytes = Base64.decode(audioData);
//
//                        // 创建新的响应对象，包含解码后的音频数据
//                        ObjectNode responseNode = (ObjectNode) jsonNode;
//                        ObjectNode dataNode = (ObjectNode) data;
//
//                        // 保留原始Base64数据，同时添加解码后的字节数组长度信息
//                        dataNode.put("audioSize", audioBytes.length);
//                        dataNode.put("audioDecoded", true);
//
//                        log.debug("TTS音频数据解码成功，大小: {} 字节", audioBytes.length);
//                        return responseNode;
//                    } catch (Exception e) {
//                        log.warn("解码讯飞TTS Base64音频数据失败: {}", e.getMessage());
//                        return jsonNode; // 返回原始数据
//                    }
//                }
//            }
//        }
        
        // 如果没有找到音频数据，返回原始响应
        return jsonNode;
    }
}
