package com.fytec.utils.external;

import lombok.Getter;
import org.springframework.http.MediaType;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

public class RecordingSseEmitter extends SseEmitter {
    private final SseEmitter delegate;
    /**
     * -- GETTER --
     * 如果需要外部再拿到这个列表，也可以提供 getter
     */
    @Getter
    private final List<Object> messages = new CopyOnWriteArrayList<>();

    public RecordingSseEmitter(SseEmitter delegate) {
        super(delegate.getTimeout());
        this.delegate = delegate;
        // 将原有回调也都注册到 delegate 上，保证行为一致
        delegate.onTimeout(delegate::complete);
        delegate.onError(delegate::completeWithError);
    }

    @Override
    public synchronized void send(Object object, MediaType mediaType) throws IOException {
        messages.add(object);
        delegate.send(object, mediaType);
    }

    @Override
    public synchronized void send(SseEventBuilder builder) throws IOException {
        // builder 的 data 里通常带了真正的 payload
        messages.add(builder);
        delegate.send(builder);
    }

    @Override
    public void complete() {
        delegate.complete();
    }

    @Override
    public void completeWithError(Throwable ex) {
        delegate.completeWithError(ex);
    }

}
