package com.fytec.utils.external;

import org.springframework.web.multipart.MultipartFile;
import java.io.*;

public class ReusableMultipartFile implements MultipartFile {

    private final byte[] fileContent;
    private final MultipartFile original;

    public ReusableMultipartFile(MultipartFile original) throws IOException {
        this.original = original;
        this.fileContent = original.getBytes(); // 复制内容到内存
    }

    @Override
    public String getName() {
        return original.getName();
    }

    @Override
    public String getOriginalFilename() {
        return original.getOriginalFilename();
    }

    @Override
    public String getContentType() {
        return original.getContentType();
    }

    @Override
    public boolean isEmpty() {
        return fileContent.length == 0;
    }

    @Override
    public long getSize() {
        return fileContent.length;
    }

    @Override
    public byte[] getBytes() {
        return fileContent;
    }

    @Override
    public InputStream getInputStream() {
        return new ByteArrayInputStream(fileContent);
    }

    @Override
    public void transferTo(File dest) throws IOException, IllegalStateException {
        try (FileOutputStream fos = new FileOutputStream(dest)) {
            fos.write(fileContent);
        }
    }
}
