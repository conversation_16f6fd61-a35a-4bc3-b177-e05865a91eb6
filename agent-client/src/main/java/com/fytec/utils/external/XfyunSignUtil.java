package com.fytec.utils.external;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

/**
 * 讯飞语音识别签名工具类
 *
 * <AUTHOR>
 */
@Slf4j
@UtilityClass
public class XfyunSignUtil {

    private static final String HMAC_SHA256 = "HmacSHA256";
    private static final String ALGORITHM = "hmac-sha256";
    private static final String HEADERS = "host date request-line";
    private static final String DATE_FORMAT = "EEE, dd MMM yyyy HH:mm:ss z";
    private static final String GMT_TIMEZONE = "GMT";

    /**
     * 构建认证URL（默认GET方法）
     *
     * @param hostUrl 主机URL
     * @param appId 应用ID
     * @param apiSecret API密钥
     * @param apiKey API密钥
     * @return 认证URL
     */
    public static String buildAuthUrl(String hostUrl, String appId, String apiSecret, String apiKey) {
        return buildAuthUrl(hostUrl, appId, apiSecret, apiKey, "GET");
    }

    /**
     * 构建认证URL
     *
     * @param hostUrl 主机URL
     * @param appId 应用ID
     * @param apiSecret API密钥
     * @param apiKey API密钥
     * @param method HTTP方法
     * @return 认证URL
     */
    public static String buildAuthUrl(String hostUrl, String appId, String apiSecret, String apiKey, String method) {
        try {
            URI uri = URI.create(hostUrl);
            String host = uri.getHost();
            String path = uri.getPath();
            
            SimpleDateFormat dateFormat = new SimpleDateFormat(DATE_FORMAT, Locale.US);
            dateFormat.setTimeZone(TimeZone.getTimeZone(GMT_TIMEZONE));
            String date = dateFormat.format(new Date());
            
            String preStr = String.format("host: %s\ndate: %s\n%s %s HTTP/1.1", host, date, method.toUpperCase(), path);
            
            Mac mac = Mac.getInstance(HMAC_SHA256);
            SecretKeySpec spec = new SecretKeySpec(apiSecret.getBytes(StandardCharsets.UTF_8), HMAC_SHA256);
            mac.init(spec);
            byte[] hexDigits = mac.doFinal(preStr.getBytes(StandardCharsets.UTF_8));
            String sha = Base64.getEncoder().encodeToString(hexDigits);
            
            String authorization = String.format("api_key=\"%s\", algorithm=\"%s\", headers=\"%s\", signature=\"%s\"",
                    apiKey, ALGORITHM, HEADERS, sha);
            
            String authBase64 = Base64.getEncoder().encodeToString(authorization.getBytes(StandardCharsets.UTF_8));
            
            return String.format("%s?authorization=%s&date=%s&host=%s",
                    hostUrl, authBase64, 
                    java.net.URLEncoder.encode(date, StandardCharsets.UTF_8),
                    java.net.URLEncoder.encode(host, StandardCharsets.UTF_8));
                    
        } catch (Exception e) {
            log.error("构建认证URL失败: {}", e.getMessage(), e);
            throw new IllegalArgumentException("构建认证URL失败: " + e.getMessage(), e);
        }
    }
}
