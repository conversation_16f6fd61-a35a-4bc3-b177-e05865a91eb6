package com.fytec.utils.external.processor;

import com.fasterxml.jackson.databind.JsonNode;
import com.fytec.utils.external.ResponseProcessor;
import lombok.extern.slf4j.Slf4j;

/**
 * 讯飞语音转写响应处理器
 * 专门处理讯飞IAT接口的响应数据
 *
 * <AUTHOR>
 */
@Slf4j
public class DefaultXfyunIatResponseProcessor implements ResponseProcessor {

    @Override
    public Object process(JsonNode jsonNode) {
        if (jsonNode == null || jsonNode.isNull()) {
            return null;
        }
        // 提取讯飞IAT的data字段
        JsonNode dataNode = jsonNode.path("data");
        return !dataNode.isMissingNode() ? dataNode : jsonNode;
    }
}