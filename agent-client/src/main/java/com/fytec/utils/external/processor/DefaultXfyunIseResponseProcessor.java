package com.fytec.utils.external.processor;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fytec.utils.external.ResponseProcessor;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;

/**
 * 讯飞语音评测响应处理器
 * 专门处理讯飞ISE接口的响应数据，包括Base64解码
 *
 * <AUTHOR>
 */
@Slf4j
public class DefaultXfyunIseResponseProcessor implements ResponseProcessor {

    @Override
    public Object process(JsonNode jsonNode) {
        if (jsonNode == null || jsonNode.isNull()) {
            return null;
        }
        
        // 提取并解码讯飞ISE的XML数据
        JsonNode data = jsonNode.path("data");
        if (!jsonNode.isMissingNode() && jsonNode.has("data") && !data.isNull()) {
            String resultData = data.asText();
            if (StrUtil.isNotBlank(resultData)) {
                try {
                    ObjectNode objectNode = (ObjectNode) jsonNode;
                    // 解码Base64编码的XML数据
                    objectNode.put("data", new String(Base64.decode(resultData), StandardCharsets.UTF_8));
                    return objectNode;
                } catch (Exception e) {
                    log.warn("解码讯飞ISE Base64 XML数据失败: {}", e.getMessage());
                    return resultData; // 返回原始Base64字符串
                }
            }
        }
        
        // 如果没有找到XML数据，返回整个data节点或原始响应
        return jsonNode;
    }
}