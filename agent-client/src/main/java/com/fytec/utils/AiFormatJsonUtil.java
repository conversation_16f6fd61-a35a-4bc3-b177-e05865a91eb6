package com.fytec.utils;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class AiFormatJsonUtil {
    private static String extractFirstJson(String text) {
        // 正则表达式，匹配以 { 开头，以 } 结尾的内容，同时考虑嵌套情况
        String regex = "\\{(?:[^{}]*|\\{[^{}]*\\})*\\}";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(text);

        if (matcher.find()) {
            return matcher.group();
        }
        return text;
    }


    public static String formatJson(String json) {
        if (StringUtils.isNotBlank(json) && json.contains("```json")) {
            int start = json.indexOf("```json");
            int end = json.lastIndexOf("```");
            if (start != -1 && end != -1) {
                json = json.substring(start + 7, end);
            }

            json = json.replaceAll("```json", "").replaceAll("```", "").trim();
        }
        try {
            Object parse = JSON.parse(json);
            return json;
        } catch (Exception e) {
            // 如果不能格式化，考虑是不是有个乱七八糟的中文
            //再尝试一次
            System.out.println("error format json:" + json);
            String firstJson = extractFirstJson(json);
            return firstJson;
        }

    }

    public static String formatSql(String json) {
        if (StringUtils.isNotBlank(json) && json.contains("```sql")) {
            int start = json.indexOf("```sql");
            int end = json.lastIndexOf("```");
            if (start != -1 && end != -1) {
                json = json.substring(start + 6, end);
            }

            return json.replaceAll("```sql", "").replaceAll("```", "").replaceAll("\\n", "\n").trim();
        }
        if (json.startsWith("sql")) {
            json = json.substring(3).strip();
        } else {
        }
        return json.replaceAll("\\\\n", "\n").replaceAll("\\n", "\n").trim();
    }
//    public static void main(String[] args){
//        String json=  "aa```json {} ``` ";
//        int start = json.indexOf("```json");
//        int end = json.lastIndexOf("```");
//        if (start != -1 && end != -1) {
//            json = json.substring(start + 7, end);
//        }
//        System.out.println(json);
//    }
}



