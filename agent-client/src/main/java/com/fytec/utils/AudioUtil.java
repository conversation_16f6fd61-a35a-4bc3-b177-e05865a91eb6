package com.fytec.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.tika.metadata.Metadata;
import org.apache.tika.metadata.XMPDM;
import org.apache.tika.parser.AutoDetectParser;
import org.apache.tika.parser.ParseContext;
import org.apache.tika.sax.BodyContentHandler;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Duration;
import java.util.Optional;

/**
 * 音频处理工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class AudioUtil {

    // PCM音频处理默认参数
    /**
     * 默认位深度：16bit
     */
    public static final int DEFAULT_BIT_DEPTH = 16;

    /**
     * 默认声道数：单声道
     */
    public static final int DEFAULT_CHANNELS = 1;

    /**
     * 支持的采样率：8000Hz
     */
    public static final int SUPPORTED_SAMPLE_RATE_8K = 8000;

    /**
     * 支持的采样率：16000Hz
     */
    public static final int SUPPORTED_SAMPLE_RATE_16K = 16000;


    /**
     * 默认采样率：16000Hz (16kHz)
     */
    public static final int DEFAULT_SAMPLE_RATE = SUPPORTED_SAMPLE_RATE_16K;

    /**
     * 获取音频文件时长
     *
     * @param audioFile 音频文件
     * @return 音频时长，获取失败返回空
     */
    public static Optional<Duration> getAudioDuration(File audioFile) {
        if (!FileUtil.exist(audioFile)) {
            log.warn("音频文件不存在: {}", audioFile.getPath());
            return Optional.empty();
        }
        try {
            Metadata metadata = extractMetadata(audioFile);
            String durationStr = metadata.get(XMPDM.DURATION);
            if (StrUtil.isNotBlank(durationStr)) {
                double durationInSeconds = NumberUtil.parseDouble(durationStr, 0.0);
                if (durationInSeconds > 0) {
                    return Optional.of(Duration.ofMillis((long) (durationInSeconds * 1000)));
                }
            }
            log.warn("无法获取音频时长: {}", audioFile.getName());
            return Optional.empty();
        } catch (Exception e) {
            log.warn("获取音频时长失败: {}, 错误: {}", audioFile.getName(), e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * 获取MultipartFile音频文件时长
     * 将MultipartFile转换为临时文件后计算时长
     *
     * @param audioFile MultipartFile音频文件
     * @return 音频时长，获取失败返回空
     */
    public static Optional<Duration> getAudioDuration(MultipartFile audioFile) {
        if (audioFile == null || audioFile.isEmpty()) {
            log.warn("MultipartFile音频文件为空或null");
            return Optional.empty();
        }

        Path tempFile = null;
        try {
            // 创建临时文件
            String originalFilename = audioFile.getOriginalFilename();
            String suffix = originalFilename != null && originalFilename.contains(".")
                ? originalFilename.substring(originalFilename.lastIndexOf("."))
                : ".tmp";

            tempFile = Files.createTempFile("audio_", suffix);
            audioFile.transferTo(tempFile.toFile());

            // 计算时长
            return getAudioDuration(tempFile.toFile());

        } catch (IOException e) {
            log.error("处理MultipartFile音频文件失败: {}", e.getMessage(), e);
            return Optional.empty();
        } finally {
            // 清理临时文件
            if (tempFile != null) {
                try {
                    Files.deleteIfExists(tempFile);
                } catch (IOException e) {
                    log.warn("删除临时文件失败: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 获取InputStream音频文件时长
     * 将InputStream转换为临时文件后计算时长
     *
     * @param audioStream 音频流
     * @param filename    文件名（用于确定文件类型）
     * @return 音频时长，获取失败返回空
     */
    public static Optional<Duration> getAudioDuration(InputStream audioStream, String filename) {
        if (audioStream == null) {
            log.warn("音频流为null");
            return Optional.empty();
        }

        Path tempFile = null;
        try {
            // 创建临时文件
            String suffix = filename != null && filename.contains(".")
                ? filename.substring(filename.lastIndexOf("."))
                : ".tmp";

            tempFile = Files.createTempFile("audio_stream_", suffix);
            Files.copy(audioStream, tempFile, java.nio.file.StandardCopyOption.REPLACE_EXISTING);

            // 计算时长
            return getAudioDuration(tempFile.toFile());

        } catch (IOException e) {
            log.error("处理InputStream音频文件失败: {}", e.getMessage(), e);
            return Optional.empty();
        } finally {
            // 清理临时文件
            if (tempFile != null) {
                try {
                    Files.deleteIfExists(tempFile);
                } catch (IOException e) {
                    log.warn("删除临时文件失败: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 计算音频文件时长（秒）
     * 便捷方法，直接返回秒数，获取失败返回0
     * 对PCM文件进行特殊处理：时长（秒） = 文件字节数 / (采样率 × 位深度（字节） × 声道数)
     *
     * @param audioFile 音频文件
     * @return 音频时长（秒），获取失败返回0
     */
    public static int calculateAudioDurationSeconds(File audioFile) {
        if (audioFile == null || !audioFile.exists()) {
            log.warn("音频文件不存在或为null");
            return 0;
        }

        String fileName = audioFile.getName().toLowerCase();

        // 对PCM文件进行特殊处理
        if (fileName.endsWith(".pcm")) {
            return calculatePcmDurationSeconds(audioFile);
        }

        // 其他格式使用原有方法
        Optional<Duration> duration = getAudioDuration(audioFile);
        if (duration.isPresent()) {
            long seconds = duration.get().getSeconds();
            log.debug("音频文件 {} 时长: {} 秒", audioFile.getName(), seconds);
            return (int) seconds;
        } else {
            log.warn("无法获取音频文件 {} 的时长", audioFile.getName());
            return 0;
        }
    }

    /**
     * 计算MultipartFile音频时长（秒）
     * 便捷方法，直接返回秒数，获取失败返回0
     * 对PCM文件进行特殊处理：时长（秒） = 文件字节数 / (采样率 × 位深度（字节） × 声道数)
     *
     * @param audioFile MultipartFile音频文件
     * @return 音频时长（秒），获取失败返回0
     */
    public static int calculateAudioDurationSeconds(MultipartFile audioFile) {
        if (audioFile == null || audioFile.isEmpty()) {
            log.warn("MultipartFile音频文件为空或null");
            return 0;
        }

        String fileName = audioFile.getOriginalFilename();
        if (fileName != null && fileName.toLowerCase().endsWith(".pcm")) {
            return calculatePcmDurationSeconds(audioFile);
        }

        // 其他格式使用原有方法
        Optional<Duration> duration = getAudioDuration(audioFile);
        if (duration.isPresent()) {
            long seconds = duration.get().getSeconds();
            log.debug("音频文件 {} 时长: {} 秒", audioFile.getOriginalFilename(), seconds);
            return (int) seconds;
        } else {
            log.warn("无法获取音频文件 {} 的时长",
                    audioFile != null ? audioFile.getOriginalFilename() : "null");
            return 0;
        }
    }

    /**
     * 计算InputStream音频时长（秒）
     * 便捷方法，直接返回秒数，获取失败返回0
     *
     * @param audioStream 音频流
     * @param filename    文件名
     * @return 音频时长（秒），获取失败返回0
     */
    public static int calculateAudioDurationSeconds(InputStream audioStream, String filename) {
        Optional<Duration> duration = getAudioDuration(audioStream, filename);
        if (duration.isPresent()) {
            long seconds = duration.get().getSeconds();
            log.debug("音频流 {} 时长: {} 秒", filename, seconds);
            return (int) seconds;
        } else {
            log.warn("无法获取音频流 {} 的时长", filename);
            return 0;
        }
    }

    /**
     * 校验音频格式是否符合PCM处理要求
     *
     * @param sampleRate 采样率（Hz）
     * @param bitDepth   位深度（bit）
     * @param channels   声道数
     * @return true表示符合要求，false表示不符合
     *
     * <p>校验规则：</p>
     * <ul>
     *   <li>采样率：必须为 8000Hz 或 16000Hz</li>
     *   <li>位深度：必须为 16bit</li>
     *   <li>声道数：必须为 1（单声道）</li>
     * </ul>
     */
    public static boolean validateAudioFormat(int sampleRate, int bitDepth, int channels) {
        // 校验采样率：只支持8000Hz或16000Hz
        boolean validSampleRate = (sampleRate == SUPPORTED_SAMPLE_RATE_8K ||
                                  sampleRate == SUPPORTED_SAMPLE_RATE_16K);

        // 校验位深度：只支持16bit
        boolean validBitDepth = (bitDepth == DEFAULT_BIT_DEPTH);

        // 校验声道数：只支持单声道
        boolean validChannels = (channels == DEFAULT_CHANNELS);

        boolean isValid = validSampleRate && validBitDepth && validChannels;

        if (!isValid) {
            log.debug("音频格式校验失败 - 采样率: {}Hz (期望: 8000Hz或16000Hz), " +
                     "位深度: {}bit (期望: 16bit), 声道数: {} (期望: 1)",
                     sampleRate, bitDepth, channels);
        } else {
            log.debug("音频格式校验通过 - 采样率: {}Hz, 位深度: {}bit, 声道数: {}",
                     sampleRate, bitDepth, channels);
        }

        return isValid;
    }

    /**
     * 校验音频格式是否符合PCM处理要求（使用默认采样率）
     *
     * @param bitDepth 位深度（bit）
     * @param channels 声道数
     * @return true表示符合要求，false表示不符合
     *
     * <p>使用默认采样率 16000Hz 进行校验</p>
     */
    public static boolean validateAudioFormat(int bitDepth, int channels) {
        return validateAudioFormat(DEFAULT_SAMPLE_RATE, bitDepth, channels);
    }

    /**
     * 判断文件是否为音频文件
     * 根据文件扩展名判断
     *
     * @param filename 文件名
     * @return 是否为音频文件
     */
    public static boolean isAudioFile(String filename) {
        if (filename == null || filename.isEmpty()) {
            return false;
        }

        String lowerFilename = filename.toLowerCase();
        return lowerFilename.endsWith(".mp3") ||
               lowerFilename.endsWith(".wav") ||
               lowerFilename.endsWith(".pcm") ||
               lowerFilename.endsWith(".m4a") ||
               lowerFilename.endsWith(".aac") ||
               lowerFilename.endsWith(".flac") ||
               lowerFilename.endsWith(".ogg") ||
               lowerFilename.endsWith(".wma") ||
               lowerFilename.endsWith(".amr") ||
               lowerFilename.endsWith(".3gp");
    }

    /**
     * 计算PCM文件时长（秒）
     * 公式：时长（秒） = 文件字节数 / (采样率 × 位深度（字节） × 声道数)
     * 默认参数：采样率16000Hz，位深度16bit（2字节），单声道
     *
     * @param audioFile PCM音频文件
     * @return 音频时长（秒），计算失败返回0
     */
    private static int calculatePcmDurationSeconds(File audioFile) {
        try {
            long fileSizeBytes = audioFile.length();

            // PCM文件默认参数：16000Hz采样率，16bit位深度（2字节），单声道
            int sampleRate = 16000;  // 采样率
            int bitDepthBytes = 2;   // 位深度（字节）：16bit = 2字节
            int channels = 1;        // 声道数：单声道

            // 计算时长：文件字节数 / (采样率 × 位深度字节数 × 声道数)
            double durationSeconds = (double) fileSizeBytes / (sampleRate * bitDepthBytes * channels);

            int duration = (int) Math.round(durationSeconds);
            log.debug("PCM文件 {} 时长计算: 文件大小={}字节, 采样率={}Hz, 位深度={}字节, 声道数={}, 时长={}秒",
                     audioFile.getName(), fileSizeBytes, sampleRate, bitDepthBytes, channels, duration);

            return Math.max(0, duration);
        } catch (Exception e) {
            log.error("计算PCM文件时长失败: {}, 错误: {}", audioFile.getName(), e.getMessage());
            return 0;
        }
    }

    /**
     * 计算PCM文件时长（秒） - MultipartFile版本
     * 公式：时长（秒） = 文件字节数 / (采样率 × 位深度（字节） × 声道数)
     * 默认参数：采样率16000Hz，位深度16bit（2字节），单声道
     *
     * @param audioFile PCM音频文件
     * @return 音频时长（秒），计算失败返回0
     */
    private static int calculatePcmDurationSeconds(MultipartFile audioFile) {
        try {
            long fileSizeBytes = audioFile.getSize();

            // PCM文件默认参数：16000Hz采样率，16bit位深度（2字节），单声道
            int sampleRate = 16000;  // 采样率
            int bitDepthBytes = 2;   // 位深度（字节）：16bit = 2字节
            int channels = 1;        // 声道数：单声道

            // 计算时长：文件字节数 / (采样率 × 位深度字节数 × 声道数)
            double durationSeconds = (double) fileSizeBytes / (sampleRate * bitDepthBytes * channels);

            int duration = (int) Math.round(durationSeconds);
            log.debug("PCM文件 {} 时长计算: 文件大小={}字节, 采样率={}Hz, 位深度={}字节, 声道数={}, 时长={}秒",
                     audioFile.getOriginalFilename(), fileSizeBytes, sampleRate, bitDepthBytes, channels, duration);

            return Math.max(0, duration);
        } catch (Exception e) {
            log.error("计算PCM文件时长失败: {}, 错误: {}", audioFile.getOriginalFilename(), e.getMessage());
            return 0;
        }
    }

    /**
     * 提取音频文件元数据
     *
     * @param audioFile 音频文件
     * @return 元数据对象
     * @throws Exception 解析异常
     */
    private static Metadata extractMetadata(File audioFile) throws Exception {
        AutoDetectParser parser = new AutoDetectParser();
        BodyContentHandler handler = new BodyContentHandler(-1);
        Metadata metadata = new Metadata();
        ParseContext context = new ParseContext();

        try (FileInputStream inputStream = new FileInputStream(audioFile)) {
            parser.parse(inputStream, handler, metadata, context);
        }

        return metadata;
    }
}