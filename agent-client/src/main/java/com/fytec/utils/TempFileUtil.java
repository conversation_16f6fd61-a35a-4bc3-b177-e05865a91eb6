package com.fytec.utils;

import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.Optional;

/**
 * 临时文件工具类
 *
 * <AUTHOR>
 */
@Slf4j
@UtilityClass
public class TempFileUtil {

    /**
     * 创建临时文件
     *
     * @param file 上传文件
     * @return 临时文件
     */
    public static File createTempFile(MultipartFile file) {
        String originalFilename = Optional.ofNullable(file.getOriginalFilename())
            .orElse("audio");
        String suffix = StrUtil.isBlank(FileUtil.getSuffix(originalFilename)) 
            ? "tmp" 
            : FileUtil.getSuffix(originalFilename);

        File tempFile = FileUtil.createTempFile("temp_", "." + suffix, true);
        try {
            FileUtil.writeBytes(file.getBytes(), tempFile);
        } catch (IOException e) {
            throw new ValidateException("文件读取失败");
        }

        log.debug("创建临时文件: {}", tempFile.getAbsolutePath());
        return tempFile;
    }

    /**
     * 清理临时文件
     *
     * @param tempFile 临时文件
     */
    public static void cleanupTempFile(File tempFile) {
        Optional.ofNullable(tempFile)
            .filter(FileUtil::exist)
            .ifPresent(file -> {
                boolean deleted = FileUtil.del(file);
                if (deleted) {
                    log.debug("清理临时文件: {}", file.getAbsolutePath());
                } else {
                    log.warn("清理临时文件失败: {}", file.getAbsolutePath());
                }
            });
    }
}