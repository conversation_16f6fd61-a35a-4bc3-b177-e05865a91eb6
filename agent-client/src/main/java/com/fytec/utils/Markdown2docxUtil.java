package com.fytec.utils;

import cn.hutool.core.exceptions.ValidateException;
import com.vladsch.flexmark.ext.tables.TablesExtension;
import com.vladsch.flexmark.html.HtmlRenderer;
import com.vladsch.flexmark.parser.Parser;
import com.vladsch.flexmark.util.ast.Node;
import com.vladsch.flexmark.util.data.MutableDataSet;
import org.apache.poi.xwpf.usermodel.*;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.TextNode;
import org.jsoup.select.Elements;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTHyperlink;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTR;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTRPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STUnderline;

import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;

public class Markdown2docxUtil {
    public static byte[] buildDocFileBytes(String markdown) {
        try (XWPFDocument xwpfDocument = convertMarkdownToWord(markdown);) {
            // 转为文件
            // 将文档写入字节数组输出流
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            xwpfDocument.write(byteArrayOutputStream);
            return byteArrayOutputStream.toByteArray();
        } catch (IOException e) {
            throw new ValidateException("生成word出错");
        }
    }

    public static String buildDocFile(String markdown) {
        try (XWPFDocument xwpfDocument = convertMarkdownToWord(markdown)) {
            // 转为文件
            // 将文档写入字节数组输出流
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            xwpfDocument.write(byteArrayOutputStream);
            byte[] documentBytes = byteArrayOutputStream.toByteArray();
            return Base64.getEncoder().encodeToString(documentBytes);
        } catch (IOException e) {
            throw new ValidateException("生成word出错");
        }

    }

    public static XWPFDocument convertMarkdownToWord(String markdown) throws IOException {
        // 将 Markdown 转换为 HTML
        MutableDataSet options = new MutableDataSet();
        options.set(Parser.EXTENSIONS, List.of(TablesExtension.create()));
        Parser parser = Parser.builder(options).build();
        HtmlRenderer renderer = HtmlRenderer.builder(options).build();
        Node document = parser.parse(markdown);
        String html = renderer.render(document);

        // 创建 Word 文档
        XWPFDocument wordDocument = new XWPFDocument();

        // 解析 HTML 并添加到 Word 文档
        Document jsoupDoc = Jsoup.parse(html);
        Elements elements = jsoupDoc.body().children();
        for (Element element : elements) {
            processElement(element, wordDocument, 0);
        }
        return wordDocument;
    }

    private static void processElement(Element element, XWPFDocument document, int indentLevel) {
        switch (element.tagName()) {
            case "p":
                XWPFParagraph paragraph = document.createParagraph();
                // 设置首行缩进 2 字符
                setFirstLineIndent(paragraph);
                processInlineElements(element, paragraph);
                break;
            case "h1":
            case "h2":
            case "h3":
            case "h4":
            case "h5":
            case "h6":
                // 格式要求center
                XWPFParagraph headerParagraph = document.createParagraph();
                headerParagraph.setSpacingBetween(1.5);
                headerParagraph.setStyle("Heading" + element.tagName().substring(1));
//                if (element.tagName().equals("h1") || element.tagName().equals("h2")) {
//                    headerParagraph.setAlignment(ParagraphAlignment.CENTER);
//                }
                processInlineElements(element, headerParagraph, true);
                break;
            case "ol":
                processOrderedList(element, document, indentLevel);
                break;
            case "ul":
                processUnorderedList(element, document, indentLevel);
                break;
            case "strong":
                XWPFParagraph strongParagraph = document.createParagraph();
                setFirstLineIndent(strongParagraph);
                XWPFRun strongRun = strongParagraph.createRun();
                strongRun.setText(element.text());
                strongRun.setBold(true);
                break;
            case "table":
                processTable(element, document);
                break;
            case "hr":
                XWPFParagraph hrParagraph = document.createParagraph();
                setFirstLineIndent(hrParagraph);
                XWPFRun hrRun = hrParagraph.createRun();
                hrRun.addBreak();
                break;
            case "a":
                XWPFParagraph linkParagraph = document.createParagraph();
                // 设置首行缩进 2 字符
                setFirstLineIndent(linkParagraph);
                processLinkElement(element, linkParagraph);
                break;
            case "blockquote":
                processBlockquote(element, document, indentLevel);
                break;
            case "code":
                XWPFParagraph codeParagraph = document.createParagraph();
                setFirstLineIndent(codeParagraph);
                XWPFRun codeRun = codeParagraph.createRun();
                codeRun.setText(element.text());
                codeRun.setFontFamily("Courier New");
                break;
            case "pre":
                processCodeBlock(element, document);
                break;
            // 可以根据需要添加更多标签的处理逻辑
            default:
                XWPFParagraph defaultParagraph = document.createParagraph();
                setFirstLineIndent(defaultParagraph);
                processInlineElements(element, defaultParagraph);
        }
    }

    /**
     * 处理内联元素（如段落内的格式化文本、链接等）
     */
    private static void processInlineElements(Element element, XWPFParagraph paragraph) {
        processInlineElements(element, paragraph, false);
    }

    private static void processInlineElements(Element element, XWPFParagraph paragraph, boolean isBold) {
        for (org.jsoup.nodes.Node node : element.childNodes()) {
            if (node instanceof TextNode) {
                XWPFRun run = paragraph.createRun();
                run.setText(((TextNode) node).text());
                if (isBold) {
                    run.setBold(true);
                }
            } else if (node instanceof Element) {
                Element childElement = (Element) node;
                processInlineElement(childElement, paragraph);
            }
        }
    }

    /**
     * 处理内联元素
     */
    private static void processInlineElement(Element element, XWPFParagraph paragraph) {
        XWPFRun run = paragraph.createRun();

        switch (element.tagName()) {
            case "strong":
            case "b":
                run.setText(element.text());
                run.setBold(true);
                break;
            case "em":
            case "i":
                run.setText(element.text());
                run.setItalic(true);
                break;
            case "code":
                run.setText(element.text());
                run.setFontFamily("Courier New");
                break;
            case "a":
                processLinkElement(element, paragraph);
                break;
            default:
                run.setText(element.text());
        }
    }

    private static void processLinkElement(Element element, XWPFParagraph paragraph) {
        String href = element.attr("href");
        String linkText = element.text();

        XWPFRun run = paragraph.createRun();
        if (!href.isEmpty()) {
            try {
                String rId = paragraph.getDocument().getPackagePart()
                        .addExternalRelationship(href, XWPFRelation.HYPERLINK.getRelation()).getId();
                CTHyperlink hyperlink = paragraph.getCTP().addNewHyperlink();
                hyperlink.setId(rId);

                // 直接通过CTR设置文本和样式
                CTR ctr = hyperlink.addNewR();
                ctr.addNewT().setStringValue(linkText);  // 仅在此处设置文本
                CTRPr rPr = ctr.addNewRPr();
                rPr.addNewColor().setVal("0000FF");
                rPr.addNewU().setVal(STUnderline.SINGLE);
            } catch (Exception e) {
                // 如果创建超链接失败，就作为普通文本处理
                run.setColor("000000");
                run.setUnderline(UnderlinePatterns.NONE);
            }
        } else {
            run.setText(linkText);
        }
    }

    private static void processOrderedList(Element olElement, XWPFDocument document, int indentLevel) {
        int num = 1;
        for (Element liElement : olElement.children()) {
            if (liElement.tagName().equals("li")) {
                // 创建列表项段落
                XWPFParagraph listItemParagraph = document.createParagraph();
                setListIndent(listItemParagraph, indentLevel);

                // 添加编号
                XWPFRun numberRun = listItemParagraph.createRun();
                numberRun.setText(num + ". ");
                num++;

                // 处理列表项中的所有内联元素
                processInlineElements(liElement, listItemParagraph);

                // 检查并处理嵌套列表
                Elements nestedLists = liElement.select("> ol, > ul");
                for (Element nestedList : nestedLists) {
                    processElement(nestedList, document, indentLevel + 1);
                }
            }
        }
    }

    private static void processUnorderedList(Element ulElement, XWPFDocument document, int indentLevel) {
        for (Element liElement : ulElement.children()) {
            if (liElement.tagName().equals("li")) {
                // 创建列表项段落
                XWPFParagraph listItemParagraph = document.createParagraph();
                setListIndent(listItemParagraph, indentLevel);

                // 添加项目符号
                XWPFRun bulletRun = listItemParagraph.createRun();
                bulletRun.setText(getBulletForLevel(indentLevel) + " ");

                // 处理列表项中的所有内联元素
                processInlineElements(liElement, listItemParagraph);

                // 检查并处理嵌套列表
                Elements nestedLists = liElement.select("> ol, > ul");
                for (Element nestedList : nestedLists) {
                    processElement(nestedList, document, indentLevel + 1);
                }
            }
        }
    }

    /**
     * 获取元素的直接文本内容（不包括子元素的文本）
     */
    private static String getDirectTextContent(Element element) {
        StringBuilder text = new StringBuilder();
        for (org.jsoup.nodes.Node node : element.childNodes()) {
            if (node instanceof TextNode) {
                text.append(((TextNode) node).text().trim());
            } else if (node instanceof Element) {
                Element childElement = (Element) node;
                // 只处理非列表的子元素
                if (!childElement.tagName().equals("ol") && !childElement.tagName().equals("ul")) {
                    text.append(childElement.text());
                }
            }
        }
        return text.toString().trim();
    }

    /**
     * 根据缩进级别获取项目符号
     */
    private static String getBulletForLevel(int level) {
        String[] bullets = {"•", "◦", "▪", "▫"};
        return bullets[level % bullets.length];
    }

    private static void processTable(Element tableElement, XWPFDocument document) {
        Elements rows = tableElement.select("tr");
        if (rows.isEmpty()) return;

        int rowCount = rows.size();
        int colCount = rows.first().select("td, th").size();
        XWPFTable table = document.createTable(rowCount, colCount);

        for (int i = 0; i < rowCount; i++) {
            Element row = rows.get(i);
            Elements cells = row.select("td, th");
            for (int j = 0; j < Math.min(colCount, cells.size()); j++) {
                Element cell = cells.get(j);
                XWPFTableCell tableCell = table.getRow(i).getCell(j);
                // 清除默认段落
                tableCell.removeParagraph(0);

                XWPFParagraph cellParagraph = tableCell.addParagraph();
                setFirstLineIndent(cellParagraph);

                // 如果是表头，设置粗体
                boolean isHeader = cell.tagName().equals("th");
                processInlineElements(cell, cellParagraph, isHeader);
            }
        }
    }

    private static void processBlockquote(Element blockquoteElement, XWPFDocument document, int indentLevel) {
        for (Element child : blockquoteElement.children()) {
            // 递归处理引用块内的元素
            processElement(child, document, indentLevel);
        }
    }

    private static void processCodeBlock(Element preElement, XWPFDocument document) {
        XWPFParagraph codeParagraph = document.createParagraph();
        setFirstLineIndent(codeParagraph);

        // 设置代码块样式
        codeParagraph.setSpacingBetween(1.0);

        XWPFRun codeRun = codeParagraph.createRun();
        codeRun.setText(preElement.text());
        codeRun.setFontFamily("Courier New");
        codeRun.setFontSize(10);
    }

    private static void setFirstLineIndent(XWPFParagraph paragraph) {
        // 一个字符的宽度约为 200 缇，2 字符就是 400 缇
        paragraph.setFirstLineIndent(400);
        //1.5倍行间距
        paragraph.setSpacingBetween(1.5);
    }

    private static void setListIndent(XWPFParagraph paragraph, int indentLevel) {
        // 基础缩进 + 列表级别缩进
        int baseIndent = 400; // 2 字符
        int levelIndent = indentLevel * 400; // 每级增加 2 字符缩进
        paragraph.setIndentFromLeft(baseIndent + levelIndent);
        paragraph.setSpacingBetween(1.5);
    }

    public static void main(String[] args) {
//        byte[] bytes = buildDocFileBytes("# 猴子“齐天”火星之旅研究报告\n\n## 关键要点\n- 2050 年，因地球航天发展及“火星先锋计划”筹备，对航天知识感兴趣且有技能的猴子“齐天”触发“猴子火星计划”并前往火星。\n- “齐天”在火星有丰富探索行为，采集岩石样本、探索洞穴获奇异矿石，还与火星环境积极互动，保障探测车和自身生存。\n- “齐天”的活动为火星地质、矿物质研究提供数据和线索，其应对火星环境的方法为人类未来火星生存保障研究提供实践经验。\n- 不同群体对“齐天”上火星评价不一，科学家有分歧后部分认可，公众反响热烈，媒体评价信息未获取。\n\n## 概述\n随着地球科技的飞速发展，人类对宇宙的探索愈发深入。2050 年，在“火星先锋计划”筹备之际，一只名为“齐天”的猴子因其对航天知识的兴趣和具备的操控技能，促成了“猴子火星计划”。本报告旨在探讨“齐天”上火星的任务背景、目的、在火星的具体活动、对各方面的影响以及不同群体的评价。\n\n## 详细分析\n### 任务背景与时间\n2050 年，地球科技发展迅速，人类对宇宙探索热情高涨，航天科研基地筹备“火星先锋计划”。猴子“齐天”对航天知识感兴趣且有一定操控技能，引发了科研团队“猴子火星计划”的想法，该计划于 2050 年实施，“齐天”搭乘飞船前往火星。\n\n### 任务目的推测\n虽文档未明确提及任务目的，但推测与人类探索火星的大目标相关，如收集火星数据、测试火星环境适应性等。\n\n### 火星表面活动\n#### 探索行为\n“齐天”到达火星后，先插下写有“齐天到此一游”的小旗并摆超人造型。之后操纵小型探测车采集岩石样本，还进入神秘洞穴探索，挖出散发奇异蓝光的矿石。\n#### 与火星环境的互动\n遇到陡坡探测车打滑时，“齐天”用尾巴缠住岩石拽回探测车；遇到沙尘暴，它和宇航员躲进避难舱，用空气过滤法改造通风系统提高氧气循环效率，煮“火星糊糊”维持生存。\n\n### 对各方面的影响\n#### 航天领域\n“齐天”成功上火星证明了非人类生物在航天任务中的潜力，成为航天基地吉祥物和激励科研人员的“奇兵”，推动航天领域创新发展。\n#### 科学研究\n为火星地质研究提供岩石样本数据，探索洞穴获奇异矿石可能为火星矿物质研究提供新线索，应对火星环境的措施为人类未来火星生存保障研究提供实践经验。\n#### 公众认知\n“齐天”回到地球成为超级明星，其火星之旅激发公众对宇宙探索的兴趣和热情，提升公众对航天事业的认知和理解。\n\n### 不同群体的评价\n#### 科学家视角\n部分科学家起初觉得猴子参与航天任务是胡闹，但张博士研究“齐天”操作数据后决定开展“猴子火星计划”，认可其能力。\n#### 公众视角\n“齐天”的火星之旅激发公众对宇宙探索的兴趣和热情，让更多人关注航天事业。\n#### 媒体视角\n文档未提及媒体对猴子上火星的评价。\n\n### 调查说明\n#### 文献综述与理论框架\n目前关于猴子上火星的研究较少，本研究主要基于“猴子去火星_副本 (1).docx”中的内容，结合人类探索火星的相关理论和目标进行分析。\n#### 研究方法与数据分析\n本研究通过对文档内容的详细梳理和分析，提取关键信息，进行归纳总结。\n#### 批判性讨论\n本研究的局限性在于部分信息如任务目的未明确提及，仅为推测。且关于媒体视角的评价信息缺失，可能影响对该事件全面影响的评估。\n#### 未来研究方向\n未来可进一步明确猴子上火星的具体任务目的，深入研究猴子在火星的行为对科学研究的具体价值，以及加强对不同群体评价的全面收集和分析。\n\n## 关键引用\n- [猴子去火星_副本 (1).docx](rag://doc/3388)");
        byte[] bytes = buildDocFileBytes("根据提供的2024年5月苏州工业园区统计资料，现将核心数据整理如下：\n\n### **2024年5月苏州工业园区经济运行简报**\n\n**统计周期：2024年1-5月累计数据**\n\n#### **一、工业经济**\n\n| 指标 | 单位 | 园区累计值 | 同比增速 |\n| --- | --- | --- | --- |\n| 规模以上工业总产值 | 亿元 | 2706.0 | +6.7% |\n| 苏相合作区工业总产值 | 亿元 | 189.12 | +13.5% |\n\n#### **二、对外贸易**\n\n1.  **进出口总额**\n    *   总额：2717.1亿元（+22.9%）\n    *   进口：1366.6亿元（+23.9%）\n    *   出口：1350.4亿元（+21.9%）\n2.  **实际利用外资**\n    *   当月：3801万美元\n    *   累计：134658万美元（+4.0%）\n    *   历史累计注册外资：415.86亿美元\n\n#### **三、国内贸易与投资**\n\n| 指标 | 单位 | 园区累计值 | 同比增速 |\n| --- | --- | --- | --- |\n| 社会消费品零售总额 | 亿元 | 498.63 | +14.3% |\n| 固定资产投资 | 亿元 | 247.85 | +12.9% |\n| 第三产业投资占比 | \\\\- | 66.2% | +15.6% |\n\n#### **四、重点领域投资**\n\n1.  **制造业**\n    *   投资额：83.19亿元（+8.4%）\n    *   技改投资：19.95亿元（-29.2%）\n2.  **房地产**\n    *   投资额：81.68亿元（+7.4%）\n3.  **基础设施**\n    *   投资额：12.63亿元（+14.6%）\n\n#### **五、社会民生**\n\n| 指标 | 单位 | 园区累计值 |\n| --- | --- | --- |\n| 全社会用电量 | 万kWh | 625965 |\n| 工业用电量占比 | \\\\- | 60.9% |\n| 全区就业人口 | 人 | 919084 |\n| 三产就业人口占比 | \\\\- | 67.3% |\n| 社会保险参保人数 | 人 | 975834 |\n\n#### **六、需关注问题**\n\n1.  **外资项目收缩**：新注册外资项目同比减少3.6%\n2.  **技改投资下滑**：技改投资同比下降29.2%\n3.  **私营资本波动**：私营企业注册资本同比下降76.1%\n\n**数据说明**：\n\n*   部分科技创新指标（如专利授权量、高企产值）为季度统计；\n*   苏相合作区数据单列，显示区域协同发展成效；\n*   完整报告建议参考附件原始统计表。\n\n（注：因原始数据存在表格错位和单位混用情况，部分数值需交叉验证。）");
        try (FileOutputStream fos = new FileOutputStream("/Users/<USER>/Downloads/文档/test.doc")) {
            fos.write(bytes);
        } catch (IOException e) {
            e.printStackTrace();
        }

    }
}