package com.fytec.satoken;

import cn.dev33.satoken.SaManager;
import cn.dev33.satoken.oauth2.data.loader.SaOAuth2DataLoader;
import cn.dev33.satoken.oauth2.data.model.loader.SaClientModel;
import cn.hutool.core.util.StrUtil;
import com.fytec.entity.system.OauthClientDetail;
import com.fytec.mapper.system.OauthClientDetailMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class SaOAuth2DataLoaderImpl implements SaOAuth2DataLoader {
    @Resource
    private OauthClientDetailMapper oauthClientDetailMapper;

    @Override
    public SaClientModel getClientModel(String clientId) {
        OauthClientDetail oauthClientDetail = (OauthClientDetail) SaManager.getSaTokenDao().getObject("fytec:client:" + clientId);
        if (oauthClientDetail == null) {
            oauthClientDetail = oauthClientDetailMapper.selectById(clientId);
            SaManager.getSaTokenDao().setObject("fytec:client:" + clientId, oauthClientDetail, 60 * 60 * 24 * 30);
        }


        if (oauthClientDetail == null) {
            return null;
        }

        if (!StrUtil.equals("A", oauthClientDetail.getStatus())) {
            return null;
        }

        return new SaClientModel()
                .setClientId(oauthClientDetail.getClientId())    // client id
                .setClientSecret(oauthClientDetail.getClientSecret())    // client 秘钥
                .addAllowRedirectUris(oauthClientDetail.getAllowRedirectUri().split(";"))    // 所有允许授权的 url
                .addContractScopes(oauthClientDetail.getScope().split(";"))    // 所有签约的权限
                .addAllowGrantTypes(oauthClientDetail.getGrantTypes().split(";")); // 所有允许的授权模式
    }
}
