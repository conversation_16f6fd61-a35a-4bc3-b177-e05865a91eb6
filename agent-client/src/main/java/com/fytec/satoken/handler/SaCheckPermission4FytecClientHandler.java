package com.fytec.satoken.handler;

import cn.dev33.satoken.annotation.handler.SaAnnotationHandlerInterface;
import cn.dev33.satoken.annotation.handler.SaCheckPermissionHandler;
import com.fytec.satoken.StpClientUserUtil;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

@Component
public class SaCheckPermission4FytecClientHandler implements SaAnnotationHandlerInterface<SaCheckPermission4FytecClient> {
    @Override
    public Class<SaCheckPermission4FytecClient> getHandlerAnnotationClass() {
        return SaCheckPermission4FytecClient.class;
    }

    @Override
    public void checkMethod(SaCheckPermission4FytecClient saCheckPermission4FytecClient, Method method) {
        SaCheckPermissionHandler._checkMethod(StpClientUserUtil.TYPE, saCheckPermission4FytecClient.value(), saCheckPermission4FytecClient.mode(), saCheckPermission4FytecClient.orRole());
    }
}
