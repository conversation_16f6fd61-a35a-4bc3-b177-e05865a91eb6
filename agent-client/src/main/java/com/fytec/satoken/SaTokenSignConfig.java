package com.fytec.satoken;

import cn.dev33.satoken.SaManager;
import cn.dev33.satoken.config.SaSignConfig;
import cn.dev33.satoken.sign.SaSignTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import javax.annotation.PostConstruct;


@Configuration
public class SaTokenSignConfig {

    @Value("${client.clientSecret}") // 假设配置项的键是client.secret
    private String clientSecret;

    @PostConstruct
    public void init() {
        SaSignTemplate saSignTemplate = new SaSignTemplate();
        SaSignConfig saSignConfig = new SaSignConfig();
        saSignConfig.setSecretKey(clientSecret);
        saSignTemplate.setSignConfig(saSignConfig);
        SaManager.setSaSignTemplate(saSignTemplate);
    }
}
