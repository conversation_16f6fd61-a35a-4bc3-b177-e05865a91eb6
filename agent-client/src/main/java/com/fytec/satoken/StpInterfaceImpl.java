package com.fytec.satoken;

import cn.dev33.satoken.SaManager;
import cn.dev33.satoken.stp.StpInterface;
import cn.hutool.core.collection.CollUtil;
import com.fytec.mapper.system.SysMenuMapper;
import com.fytec.mapper.system.SysUserRoleMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

@Component    // 保证此类被 SpringBoot 扫描，完成 Sa-Token 的自定义权限验证扩展
public class StpInterfaceImpl implements StpInterface {
    @Resource
    private SysUserRoleMapper sysUserRoleMapper;
    @Resource
    private SysMenuMapper sysMenuMapper;

    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        List<Long> roleIds = sysUserRoleMapper.selectRoleIdByUserId(loginId);
        List<String> permissions = sysMenuMapper.selectPermissionListByRoles(roleIds);
        //TODO 临时添加
        permissions.add("*:*");
//        permissions.add("knowledge:*");
//        permissions.add("workflow:*");
//        permissions.add("agent:*");
//        permissions.add("resource:*");
//        permissions.add("application-mgmt:detail");
//        permissions.add("embedded:*");
//        permissions.add("model:*");
//        permissions.add("prompt:*");
//        permissions.add("dict:*");
//        permissions.add("application:*");
//        permissions.add("note:*");
//        permissions.add("ai_work:*");
//        permissions.add("wechat:*");
        return permissions;
    }

    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        List<String> roleList = sysUserRoleMapper.selectRoleListByUserId(loginId);
//        List<String> roleList = (List<String>) SaManager.getSaTokenDao().getObject("fytec-client:role:" + loginId);
//        if (CollUtil.isEmpty(roleList)) {
//            // 从数据库查询这个账号id拥有的角色列表，
//            roleList = sysUserRoleMapper.selectRoleListByUserId(loginId);
//            // 查好后，set 到缓存中
//            SaManager.getSaTokenDao().setObject("fytec-client:role:" + loginId, roleList, 60 * 60 * 24 * 30);
//        }
        return roleList;
    }
}
