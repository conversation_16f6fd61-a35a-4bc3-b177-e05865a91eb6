package com.fytec.util;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.util.List;

/**
 * MongoDB分页对象转换工具类
 * 用于MyBatis-Plus的Page和Spring Data的Page之间的互转
 */
public class MongoPageUtil {

    /**
     * 将MyBatis-Plus的Page转换为Spring Data的Pageable
     *
     * @param page MyBatis-Plus分页对象
     * @return Spring Data分页对象
     */
    public static Pageable toPageable(Page<?> page) {
        if (page == null) {
            return PageRequest.of(0, 10);
        }
        // MyBatis-Plus页码从1开始，Spring Data从0开始
        int pageNum = (int) (page.getCurrent() - 1);
        int pageSize = (int) page.getSize();
        return PageRequest.of(Math.max(0, pageNum), Math.max(1, pageSize));
    }

    /**
     * 将MyBatis-Plus的Page转换为Spring Data的Pageable（带排序）
     *
     * @param page MyBatis-Plus分页对象
     * @param sort 排序对象
     * @return Spring Data分页对象
     */
    public static Pageable toPageable(Page<?> page, Sort sort) {
        if (page == null) {
            return PageRequest.of(0, 10, sort);
        }
        // MyBatis-Plus页码从1开始，Spring Data从0开始
        int pageNum = (int) (page.getCurrent() - 1);
        int pageSize = (int) page.getSize();
        return PageRequest.of(Math.max(0, pageNum), Math.max(1, pageSize), sort);
    }

    /**
     * 将Spring Data的Page转换为MyBatis-Plus的Page
     *
     * @param springPage Spring Data分页结果
     * @param <T> 数据类型
     * @return MyBatis-Plus分页对象
     */
    public static <T> Page<T> toMybatisPlusPage(org.springframework.data.domain.Page<T> springPage) {
        if (springPage == null) {
            return new Page<>();
        }
        // Spring Data页码从0开始，MyBatis-Plus从1开始
        Page<T> page = new Page<>(springPage.getNumber() + 1, springPage.getSize());
        page.setRecords(springPage.getContent());
        page.setTotal(springPage.getTotalElements());
        return page;
    }

    /**
     * 将Spring Data的Page数据填充到MyBatis-Plus的Page中
     *
     * @param springPage Spring Data分页结果
     * @param targetPage 目标MyBatis-Plus分页对象
     * @param <T> 数据类型
     * @return 填充后的MyBatis-Plus分页对象
     */
    public static <T> Page<T> fillMybatisPlusPage(org.springframework.data.domain.Page<T> springPage, Page<T> targetPage) {
        if (springPage == null || targetPage == null) {
            return targetPage != null ? targetPage : new Page<>();
        }
        targetPage.setRecords(springPage.getContent());
        targetPage.setTotal(springPage.getTotalElements());
        return targetPage;
    }

    /**
     * 创建默认的Spring Data分页对象
     *
     * @param pageNum 页码（从0开始）
     * @param pageSize 页大小
     * @return Spring Data分页对象
     */
    public static Pageable createPageable(int pageNum, int pageSize) {
        return PageRequest.of(Math.max(0, pageNum), Math.max(1, pageSize));
    }

    /**
     * 创建带排序的Spring Data分页对象
     *
     * @param pageNum 页码（从0开始）
     * @param pageSize 页大小
     * @param sort 排序对象
     * @return Spring Data分页对象
     */
    public static Pageable createPageable(int pageNum, int pageSize, Sort sort) {
        return PageRequest.of(Math.max(0, pageNum), Math.max(1, pageSize), sort);
    }

    /**
     * 创建按字段倒序排列的Spring Data分页对象
     *
     * @param pageNum 页码（从0开始）
     * @param pageSize 页大小
     * @param sortField 排序字段
     * @return Spring Data分页对象
     */
    public static Pageable createPageableDesc(int pageNum, int pageSize, String sortField) {
        Sort sort = Sort.by(Sort.Direction.DESC, sortField);
        return PageRequest.of(Math.max(0, pageNum), Math.max(1, pageSize), sort);
    }

    /**
     * 创建按字段正序排列的Spring Data分页对象
     *
     * @param pageNum 页码（从0开始）
     * @param pageSize 页大小
     * @param sortField 排序字段
     * @return Spring Data分页对象
     */
    public static Pageable createPageableAsc(int pageNum, int pageSize, String sortField) {
        Sort sort = Sort.by(Sort.Direction.ASC, sortField);
        return PageRequest.of(Math.max(0, pageNum), Math.max(1, pageSize), sort);
    }
}