package com.fytec.service.assistant;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fytec.dto.assistant.AssistantDTO;
import com.fytec.dto.assistant.AssistantQueryDTO;
import com.fytec.entity.agent.AgentBasic;
import com.fytec.entity.agent.AgentPublish;
import com.fytec.entity.assistant.Assistant;
import com.fytec.mapper.agent.AgentBasicMapper;
import com.fytec.mapper.agent.AgentPublishMapper;
import com.fytec.mapper.assistant.AssistantMapper;
import com.fytec.satoken.StpClientUserUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Transactional
@Slf4j
@RequiredArgsConstructor
public class AssistantService {

    private final AssistantMapper assistantMapper;
    private final AgentBasicMapper agentBasicMapper;
    private final AgentPublishMapper agentPublishMapper;

    public void addAssistant(AssistantDTO dto) {
        Long assistantCount = assistantMapper.selectCount(
                new LambdaQueryWrapper<Assistant>()
                        .eq(Assistant::getUserId, StpClientUserUtil.getLoginIdAsLong())
                        .eq(Assistant::getAgentId, dto.getAgentId())
        );
        if (assistantCount > 0) {
            throw new RuntimeException("该智能助手已添加");
        }

        Assistant assistant = new Assistant();

        assistant.setUserId(StpClientUserUtil.getLoginIdAsLong());
        assistant.setAgentId(dto.getAgentId());
        assistantMapper.insert(assistant);
    }

    public void removeAssistant(AssistantDTO dto) {
        List<Assistant> assistants = assistantMapper.selectList(
                new LambdaQueryWrapper<Assistant>()
                        .eq(Assistant::getUserId, StpClientUserUtil.getLoginIdAsLong())
                        .eq(Assistant::getAgentId, dto.getAgentId())
        );
        for (Assistant assistant : assistants) {
            assistantMapper.deleteById(assistant);
        }
    }

    public List<Map<String, Object>> queryAssistantList(AssistantQueryDTO dto) {
        List<Map<String, Object>> results = new ArrayList<>();
        List<Assistant> assistants = assistantMapper.selectList(
                new LambdaQueryWrapper<Assistant>()
                        .eq(Assistant::getUserId, StpClientUserUtil.getLoginIdAsLong())
        );
        if (CollUtil.isEmpty(assistants)) {
            return results;
        }

        List<Long> agentIds = assistants.stream().map(Assistant::getAgentId).toList();

        Map<Long, AgentBasic> agentBasicMap = agentBasicMapper.selectByIds(agentIds)
                .stream()
                .collect(Collectors.toMap(AgentBasic::getId, Function.identity()));

        // 3. 批量查询最新的AgentPublish记录
        List<AgentPublish> latestPublishes = agentPublishMapper.selectLatestPublishesByAgentIds(agentIds);
        Map<Long, AgentPublish> publishMap = latestPublishes.stream()
                .collect(Collectors.toMap(AgentPublish::getAgentId, Function.identity()));

        for (Assistant assistant : assistants) {
            Map<String, Object> result = new HashMap<>();
            AgentBasic agentBasic = agentBasicMap.get(assistant.getAgentId());
            AgentPublish publish = publishMap.get(assistant.getAgentId());

            if (agentBasic != null && publish != null) {
                result.put("agentId", assistant.getAgentId());
                result.put("publishAgentId", publish.getId());
                result.put("name", agentBasic.getName());
                result.put("logo", agentBasic.getLogo());
                results.add(result);
            }
        }

        return results;
    }
}
