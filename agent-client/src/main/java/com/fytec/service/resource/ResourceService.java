package com.fytec.service.resource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ValidateException;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.config.milvus.MilvusClient;
import com.fytec.constant.Constants;
import com.fytec.constant.Constants.RESOURCE_TYPE;
import com.fytec.dto.resource.AddResourceDTO;
import com.fytec.dto.resource.QueryResourceDTO;
import com.fytec.dto.resource.ResourceDTO;
import com.fytec.dto.resource.UpdateResourceDTO;
import com.fytec.entity.database.DatabaseBasic;
import com.fytec.entity.knowledge.Knowledge;
import com.fytec.entity.llm.EmbeddedModel;
import com.fytec.entity.plugin.PluginBasic;
import com.fytec.entity.resource.AiResource;
import com.fytec.entity.workflow.WorkflowDevelop;
import com.fytec.mapper.database.DatabaseMapper;
import com.fytec.mapper.knowledge.KnowledgeMapper;
import com.fytec.mapper.model.EmbeddedModelMapper;
import com.fytec.mapper.plugin.PluginMapper;
import com.fytec.mapper.resource.ResourceMapper;
import com.fytec.mapper.workflow.WorkflowDevelopMapper;
import com.fytec.satoken.StpClientUserUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotNull;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Random;

@Service
@Slf4j
@Transactional
@AllArgsConstructor
public class ResourceService {

    private final ResourceMapper resourceMapper;

    private final WorkflowDevelopMapper workflowDevelopMapper;

    private final KnowledgeMapper knowledgeMapper;

    private final MilvusClient milvusClient;

    private final EmbeddedModelMapper embeddedModelMapper;

    private final PluginMapper pluginMapper;

    private final DatabaseMapper databaseMapper;

    public static String generate8DigitCode() {
        // 获取当前时间并格式化为特定格式
        SimpleDateFormat sdf = new SimpleDateFormat("ssSS");
        String timePart = sdf.format(new Date());

        // 生成 4 位随机数字
        Random random = new Random();
        StringBuilder randomPart = new StringBuilder();
        for (int i = 0; i < 4; i++) {
            randomPart.append(random.nextInt(10));
        }

        return "_" + timePart + randomPart;
    }

    public Long addResource(AddResourceDTO dto) {
        AiResource resource = new AiResource();
        BeanUtils.copyProperties(dto, resource);
        resource.setStatus(Constants.PUBLISH_STATUS_0);
        resource.setCode(generate8DigitCode());
        resourceMapper.insert(resource);

        if (RESOURCE_TYPE.knowledge.name().equals(resource.getType())) {
            processAddKnowledge(resource, dto);
        }

        // 插件注册
        if (RESOURCE_TYPE.plugin.name().equals(resource.getType())) {
            processAddPlugin(resource, dto);
        }

        if (RESOURCE_TYPE.database.name().equals(resource.getType())) {
            processAddDatabase(resource, dto);
        }

        return resource.getId();
    }

    private void processAddKnowledge(AiResource resource, AddResourceDTO dto) {
        Knowledge knowledge = new Knowledge();
        knowledge.setResourceId(resource.getId());
        knowledge.setType(dto.getKnowledgeType());
        knowledge.setBaseType(dto.getKnowledgeBaseType());
        knowledge.setEmbeddedId(dto.getEmbeddedId());
        if (StringUtils.isNotBlank(dto.getUserId())) {
            knowledge.setUserId(dto.getUserId());
        } else {
            knowledge.setUserId(StpClientUserUtil.getLoginIdAsString());
        }
        knowledgeMapper.insert(knowledge);
        if (!milvusClient.isExitCollection(getCollectionName(resource.getCode(), resource.getId()))) {
            EmbeddedModel embeddedModel = embeddedModelMapper.selectById(knowledge.getEmbeddedId());
            if (embeddedModel == null) {
                throw new ValidateException("未配置向量模型");
            }
            milvusClient.createCollection(getCollectionName(resource.getCode(), resource.getId()), embeddedModel.getDimension());
        }
    }

    private String getCollectionName(String code, Long id) {
        return code + "_" + id;
    }

    private void processAddPlugin(AiResource resource, AddResourceDTO dto) {
        PluginBasic pluginBasic = new PluginBasic();
        pluginBasic.setResourceId(resource.getId());
        pluginBasic.setType(dto.getPluginType());
        pluginBasic.setStatus(Constants.PUBLISH_STATUS_0);
        //http方式插件
        if (Constants.PLUGIN_TYPE.http.name().equals(dto.getPluginType())) {
            String prefixUrl = dto.getPluginUrl();
            if (prefixUrl.endsWith("/")) {
                prefixUrl = prefixUrl.substring(0, prefixUrl.length() - 1);
            }

            pluginBasic.setUrl(prefixUrl);
            if (CollUtil.isNotEmpty(dto.getPluginHeaders())) {
                pluginBasic.setHeaders(JSON.toJSONString(dto.getPluginHeaders()));
            }
            pluginBasic.setAuthType(dto.getPluginAuthType());
            pluginBasic.setAuthInfo(dto.getPluginAuthInfo());
            pluginMapper.insert(pluginBasic);
        } else if (Constants.PLUGIN_TYPE.mcp.name().equals(dto.getPluginType())) {
            pluginMapper.insert(pluginBasic);
        } else {
            throw new ValidateException("插件类型错误");
        }
    }

    private void processAddDatabase(AiResource resource, AddResourceDTO dto) {
        DatabaseBasic databaseBasic = new DatabaseBasic();
        databaseBasic.setResourceId(resource.getId());
        databaseBasic.setType(dto.getDbType());
        databaseBasic.setConfig(JSON.toJSONString(dto.getDbConfigs()));
        databaseMapper.insert(databaseBasic);
    }

    public Long updateSource(UpdateResourceDTO dto) {
        AiResource resource = getResource(dto.getId());
        if (resource == null) {
            throw new ValidateException("资源不存在");
        }
        dto.setType(resource.getType());
        BeanUtils.copyProperties(dto, resource);
        resourceMapper.updateById(resource);

        if (RESOURCE_TYPE.knowledge.name().equals(resource.getType())) {
            processUpdateKnowledge(resource, dto);
        }

        if (RESOURCE_TYPE.plugin.name().equals(resource.getType())) {
            processUpdatePlugin(resource, dto);
        }

        if (RESOURCE_TYPE.database.name().equals(resource.getType())) {
            processUpdateDatabase(resource, dto);
        }

        return resource.getId();
    }

    private void processUpdateKnowledge(AiResource resource, UpdateResourceDTO dto) {
        knowledgeMapper.updateBaseTypeByResourceId(resource.getId(), dto.getKnowledgeBaseType());
    }

    private void processUpdatePlugin(AiResource resource, UpdateResourceDTO dto) {
        //http方式插件
        if (Constants.PLUGIN_TYPE.http.name().equals(dto.getPluginType())) {
            PluginBasic pluginBasic = pluginMapper.selectOne(
                    new LambdaQueryWrapper<PluginBasic>()
                            .eq(PluginBasic::getResourceId, resource.getId())
            );
            if (pluginBasic == null) {
                throw new ValidateException("资源-插件不存在");
            }
            pluginBasic.setType(dto.getPluginType());

            String prefixUrl = dto.getPluginUrl();
            if (prefixUrl.endsWith("/")) {
                prefixUrl = prefixUrl.substring(0, prefixUrl.length() - 1);
            }
            pluginBasic.setUrl(prefixUrl);
            if (CollUtil.isNotEmpty(dto.getPluginHeaders())) {
                pluginBasic.setHeaders(JSON.toJSONString(dto.getPluginHeaders()));
            }
            pluginBasic.setAuthType(dto.getPluginAuthType());
            pluginBasic.setAuthInfo(dto.getPluginAuthInfo());
            pluginMapper.updateById(pluginBasic);
        }
    }

    private void processUpdateDatabase(AiResource resource, UpdateResourceDTO dto) {
        DatabaseBasic databaseBasic = databaseMapper.selectOne(
                new LambdaQueryWrapper<DatabaseBasic>()
                        .eq(DatabaseBasic::getResourceId, resource.getId())
        );
        if (databaseBasic == null) {
            throw new ValidateException("资源-数据库不存在");
        }

        databaseBasic.setType(dto.getDbType());
        databaseBasic.setConfig(JSON.toJSONString(dto.getDbConfigs()));
        databaseMapper.updateById(databaseBasic);
    }


    private AiResource getResource(@NotNull Long id) {
        return resourceMapper.selectById(id);
    }

    public Page<ResourceDTO> queryPage(QueryResourceDTO dto, Page<ResourceDTO> page) {
        List<ResourceDTO> list = resourceMapper.queryPage(page, dto);
        page.setRecords(list);
        return page;
    }

    public Long copyResource(Long id) {
        AiResource resource = getResource(id);
        if (resource == null) {
            throw new ValidateException("资源不存在");
        }
        AiResource newResource = new AiResource();
        BeanUtils.copyProperties(resource, newResource);
        newResource.setName(newResource.getName() + "_copy");
        newResource.setId(null);
        newResource.setStatus(Constants.PUBLISH_STATUS_0);
        newResource.setCreateTime(LocalDateTime.now());
        newResource.setUpdateTime(LocalDateTime.now());
        newResource.setCode(generate8DigitCode());
        resourceMapper.insert(newResource);

        if (RESOURCE_TYPE.workflow.name().equals(resource.getType())
                || RESOURCE_TYPE.dialogue.name().equals(resource.getType())) {
            WorkflowDevelop develop = workflowDevelopMapper.selectOne(new LambdaQueryWrapper<WorkflowDevelop>()
                    .eq(WorkflowDevelop::getResourceId, resource.getId()));
            if (develop != null) {
                //复制
                WorkflowDevelop newDevelop = new WorkflowDevelop();
                BeanUtils.copyProperties(develop, newDevelop);
                newDevelop.setId(null);
                newDevelop.setResourceId(newResource.getId());
                workflowDevelopMapper.insert(newDevelop);
            }
        }
        return newResource.getId();
    }

    public void deleteResource(Long id) {
        AiResource resource = getResource(id);
        if (resource == null) {
            throw new ValidateException("资源不存在");
        }
        if (RESOURCE_TYPE.knowledge.name().equals(resource.getType())
                && resource.isSystemResource()) {
            throw new ValidateException("系统资源不能删除");
        }
        resourceMapper.deleteById(id);
    }
}
