package com.fytec.service.quartz;

import cn.hutool.core.exceptions.ValidateException;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.constant.Constants;
import com.fytec.dto.QuartzBean;
import com.fytec.entity.quartz.QuartzJob;
import com.fytec.mapper.quartz.QuartzJobMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Service
@Transactional
@Slf4j
public class QuartzService {

    private final QuartzJobMapper quartzJobMapper;

    public QuartzService(QuartzJobMapper quartzJobMapper) {
        this.quartzJobMapper = quartzJobMapper;
    }

    /*
     *创建定时任务
     *@param scheduler 调度器
     *@param quartzBean 定时任务信息类
     */
    public void createScheduleJob(Scheduler scheduler, QuartzBean quartzBean) {
        try {
            String jobId = UUID.randomUUID().toString();
            Class<? extends Job> aClass = (Class<? extends Job>) Class.forName(quartzBean.getJobClass());


            JobDetail build = JobBuilder.newJob(aClass)
                    .withIdentity(jobId)
                    .build();
            //表达式调度构建器
            CronScheduleBuilder cronScheduleBuilder = CronScheduleBuilder.cronSchedule(quartzBean.getCron());
            //按新的cronExpression表达式构建一个新的trigger
            TriggerBuilder<CronTrigger> cronTriggerTriggerBuilder = TriggerBuilder.newTrigger()
                    .withIdentity(jobId)
                    .withSchedule(cronScheduleBuilder);
            if (quartzBean.getParams() != null) {
                JobDataMap jobDataMap = new JobDataMap(quartzBean.getParams());
                cronTriggerTriggerBuilder.usingJobData(jobDataMap);
            }
            CronTrigger trigger = cronTriggerTriggerBuilder.build();
            scheduler.scheduleJob(build, trigger);

            QuartzJob quartzJob = new QuartzJob();
            quartzJob.setName(quartzBean.getName());
            quartzJob.setDescription(quartzBean.getDescription());
            quartzJob.setJobId(jobId);
            quartzJob.setJobClass(quartzBean.getJobClass());
            quartzJob.setCron(quartzBean.getCron());
            quartzJob.setParams(quartzBean.getParams() == null ? null : JSON.toJSONString(quartzBean.getParams()));
            quartzJob.setStatus(Constants.QUARTZ_STATUS.running.name());
            quartzJobMapper.insert(quartzJob);

        } catch (ClassNotFoundException | SchedulerException e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    /**
     * 修改定时任务
     *
     * @param scheduler 调度器
     */
    public void updateScheduleJob(Scheduler scheduler, QuartzBean quartzBean) {
        try {
            QuartzJob quartzJob = quartzJobMapper.selectById(quartzBean.getId());
            if (quartzJob == null) {
                throw new ValidateException("任务不存在");
            }
            quartzJob.setName(quartzBean.getName());
            quartzJob.setDescription(quartzBean.getDescription());
            quartzJob.setJobClass(quartzBean.getJobClass());
            quartzJob.setCron(quartzBean.getCron());
            quartzJob.setParams(quartzBean.getParams() == null ? null : JSON.toJSONString(quartzBean.getParams()));
            quartzJobMapper.updateById(quartzJob);

//            //获取对应的jobKey
//            TriggerKey triggerKey = TriggerKey.triggerKey(quartzJob.getJobId());
//            //获取执行定时任务的执行方式
//            CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(quartzBean.getCron());
//
//            //按新的cronExpression表达式重新构建trigger
//            CronTrigger character= (CronTrigger)scheduler.getTrigger(triggerKey);
//
//            TriggerBuilder<CronTrigger> cronTriggerTriggerBuilder = character.getTriggerBuilder()
//                    .withIdentity(triggerKey)
//                    .withSchedule(scheduleBuilder);
//            if(quartzBean.getParams()!=null) {
//                JobDataMap jobDataMap = new JobDataMap(quartzBean.getParams());
//                cronTriggerTriggerBuilder.usingJobData(jobDataMap);
//            }
//            character = cronTriggerTriggerBuilder.build();
//            //按新的trigger重新设置job执行
//            scheduler.rescheduleJob(triggerKey, character);
            // 获取对应的jobKey
            JobKey jobKey = JobKey.jobKey(quartzJob.getJobId());
            // 删除旧的JobDetail
            scheduler.deleteJob(jobKey);

            Class<? extends Job> aClass = (Class<? extends Job>) Class.forName(quartzBean.getJobClass());
            JobDetail build = JobBuilder.newJob(aClass)
                    .withIdentity(quartzJob.getJobId())
                    .build();
            //表达式调度构建器
            CronScheduleBuilder cronScheduleBuilder = CronScheduleBuilder.cronSchedule(quartzBean.getCron());
            //按新的cronExpression表达式构建一个新的trigger
            TriggerBuilder<CronTrigger> cronTriggerTriggerBuilder = TriggerBuilder.newTrigger()
                    .withIdentity(quartzJob.getJobId())
                    .withSchedule(cronScheduleBuilder);
            if (quartzBean.getParams() != null) {
                JobDataMap jobDataMap = new JobDataMap(quartzBean.getParams());
                cronTriggerTriggerBuilder.usingJobData(jobDataMap);
            }
            CronTrigger trigger = cronTriggerTriggerBuilder.build();
            scheduler.scheduleJob(build, trigger);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /*根据任务恢复定时任务
      @param scheduler 调度器
      任务名称* param jobName
      */
    public void resumeScheduleJob(Scheduler scheduler, Integer id) {
        QuartzJob quartzJob = quartzJobMapper.selectById(id);
        if (quartzJob == null) {
            throw new ValidateException("任务不存在");
        }
        quartzJob.setStatus(Constants.QUARTZ_STATUS.running.name());
        quartzJobMapper.updateById(quartzJob);
        JobKey jobKey = JobKey.jobKey(quartzJob.getJobId());
        try {
            scheduler.resumeJob(jobKey);
        } catch (SchedulerException e) {
            throw new RuntimeException("恢复定时任务失败" + e.getMessage());
        }
    }

    /**
     * 暂停定时任务
     */
    public void pauseScheduleJob(Scheduler scheduler, Integer id) {
        QuartzJob quartzJob = quartzJobMapper.selectById(id);
        if (quartzJob == null) {
            throw new ValidateException("任务不存在");
        }
        quartzJob.setStatus(Constants.QUARTZ_STATUS.pause.name());
        quartzJobMapper.updateById(quartzJob);
        JobKey jobKey = JobKey.jobKey(quartzJob.getJobId());
        try {
            scheduler.pauseJob(jobKey);
        } catch (SchedulerException e) {
            throw new RuntimeException("暂停定时任务失败" + e.getMessage());
        }
    }

    /**
     * 立即执行定时任务
     */
    public void runScheduleJob(Scheduler scheduler, Integer id) {
        QuartzJob quartzJob = quartzJobMapper.selectById(id);
        if (quartzJob == null) {
            throw new ValidateException("任务不存在");
        }
        JobKey jobKey = JobKey.jobKey(quartzJob.getJobId());
        try {
            if (StringUtils.isNotBlank(quartzJob.getParams())) {
                JobDataMap jobDataMap = new JobDataMap(JSON.parseObject(quartzJob.getParams(), Map.class));
                scheduler.triggerJob(jobKey, jobDataMap);
            } else {
                scheduler.triggerJob(jobKey);
            }
            log.info("start scheduler success:{}", quartzJob.getName());
        } catch (SchedulerException e) {
            throw new RuntimeException("立即执行定时任务失败" + e.getMessage());
        }
    }

    /**
     * 删除定时任务
     */
    public void deleteScheduleJob(Scheduler scheduler, Integer id) {
        QuartzJob quartzJob = quartzJobMapper.selectById(id);
        if (quartzJob == null) {
            throw new ValidateException("任务不存在");
        }
        quartzJobMapper.deleteById(id);
        JobKey jobKey = JobKey.jobKey(quartzJob.getJobId());
        try {
            scheduler.deleteJob(jobKey);
        } catch (SchedulerException e) {
            throw new RuntimeException("删除定时任务失败" + e.getMessage());
        }
    }

    /**
     * 获取所有定时任务
     *
     * @return 返回所有定时任务列表
     */
    public Page<QuartzJob> query(String name, Page page) {
        LambdaQueryWrapper<QuartzJob> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(name)) {
            lambdaQueryWrapper.like(QuartzJob::getName, name);
        }
        List<QuartzJob> quartzJobs = quartzJobMapper.selectList(lambdaQueryWrapper);
        page.setRecords(quartzJobs);
        return page;

    }

    /**
     * 获取所有正在运行的定时任务
     *
     * @param scheduler 调度器
     * @return 返回所有正在运行的定时任务列表
     */
    public List<JobDetail> getRunningScheduleJob(Scheduler scheduler) {
        try {
            List<JobExecutionContext> executingJobs = scheduler.getCurrentlyExecutingJobs();
            List<JobDetail> jobDetails = new ArrayList<>();
            for (JobExecutionContext executingJob : executingJobs) {
                JobDetail jobDetail = executingJob.getJobDetail();
                jobDetails.add(jobDetail);
            }
            return jobDetails;
        } catch (SchedulerException e) {
            throw new RuntimeException("获取所有正在运行的定时任务失败" + e.getMessage());
        }
    }


}

