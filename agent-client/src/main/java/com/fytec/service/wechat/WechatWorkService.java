package com.fytec.service.wechat;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.fytec.dto.Node;
import com.fytec.dto.wechat.WechatWorkGroup;
import com.fytec.dto.wechat.WechatWorkUser;
import com.fytec.util.TreeBuilder;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class WechatWorkService {
    private static final String ACCESS_TOKEN_URL = "https://qyapi.weixin.qq.com/cgi-bin/gettoken";
    private static final String USER_INFO_URL = "https://qyapi.weixin.qq.com/cgi-bin/auth/getuserinfo";
    private static final String USER_DETAIL_URL = "https://qyapi.weixin.qq.com/cgi-bin/user/get";
    private static final String DEPT_ID_LIST_URL = "https://qyapi.weixin.qq.com/cgi-bin/department/list";
    private static final String DEPT_USER_ID_LIST_URL = "https://qyapi.weixin.qq.com/cgi-bin/user/simplelist";

    private final String corpId;
    private final String corpSecret;
    private final String agentId;
    private final String redirectUri;
    private final RestTemplate restTemplate;


    // 缓存access token
    private String accessToken;
    private long expiresAt;

    public WechatWorkService(String corpId, String corpSecret, String agentId, String redirectUri) {
        this.corpId = corpId;
        this.corpSecret = corpSecret;
        this.agentId = agentId;
        this.redirectUri = redirectUri;
        this.restTemplate = new RestTemplate();
    }


    // 获取授权链接
    public String getAuthorizationUrl(String state) {
        return UriComponentsBuilder.fromUriString("https://login.work.weixin.qq.com/wwlogin/sso/login")
                .queryParam("login_type", "CorpApp")
                .queryParam("appid", corpId)
                .queryParam("agentid", agentId)
                .queryParam("redirect_uri", redirectUri)
                .queryParam("state", state)
                .toUriString();
    }


    // 获取access token
    private String getAccessToken() {
        if (accessToken != null && System.currentTimeMillis() < expiresAt) {
            return accessToken;
        }

        String url = ACCESS_TOKEN_URL + "?corpid=" + corpId + "&corpsecret=" + corpSecret;
        ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
        Map<String, Object> body = response.getBody();

        if (body != null && body.get("errcode").equals(0)) {
            accessToken = (String) body.get("access_token");
            expiresAt = System.currentTimeMillis() + ((Integer) body.get("expires_in") - 200) * 1000L;
            return accessToken;
        }

        throw new RuntimeException("获取企业微信access_token失败: " + body);
    }

    // 通过code获取用户信息
    public WechatWorkUser getUserInfoByCode(String code) {
        String accessToken = getAccessToken();
        String url = USER_INFO_URL + "?access_token=" + accessToken + "&code=" + code;
        ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
        Map<String, Object> body = response.getBody();

        if (body != null && body.get("errcode").equals(0)) {
            String userId = (String) body.get("UserId");
            return getUserDetail(userId);
        }

        throw new RuntimeException("获取企业微信用户信息失败: " + body);
    }

    // 获取用户详细信息
    private WechatWorkUser getUserDetail(String userId) {
        String accessToken = getAccessToken();
        String url = USER_DETAIL_URL + "?access_token=" + accessToken + "&userid=" + userId;
        ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
        Map<String, Object> body = response.getBody();

        if (body != null && body.get("errcode").equals(0)) {
            WechatWorkUser user = new WechatWorkUser();
            user.setUserId((String) body.get("userid"));
            user.setName((String) body.get("name"));
            user.setMobile((String) body.get("mobile"));
            return user;
        }

        throw new RuntimeException("获取企业微信用户详细信息失败: " + body);
    }


    public Object queryWechatWorkDepartments() {
        String accessToken = getAccessToken();
        String url = DEPT_ID_LIST_URL + "?access_token=" + accessToken;
        ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
        Map<String, Object> body = response.getBody();

        if (body != null && body.get("errcode").equals(0)) {
            JSONArray jsonArray = JSON.parseArray(JSON.toJSONString(body.get("department")));
            List<Node> nodes = new ArrayList<>();
            for (Object o : jsonArray) {
                JSONObject jsonObject = (JSONObject) o;
                WechatWorkGroup node = new WechatWorkGroup();
                node.setId(jsonObject.getLong("id"));
                node.setParentId(jsonObject.getLong("parentid"));
                node.setName(jsonObject.getString("name"));
                nodes.add(node);
            }
            return TreeBuilder.buildListToTree(nodes, 0L);
        }
        return null;
    }

    public Object queryWechatWorkDepartmentUsers(String departmentId) {
        String accessToken = getAccessToken();
        String url = DEPT_USER_ID_LIST_URL + "?access_token=" + accessToken + "&department_id=" + departmentId;;
        ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
        Map<String, Object> body = response.getBody();
        if (body != null && body.get("errcode").equals(0)) {
            JSONArray jsonArray = JSON.parseArray(JSON.toJSONString(body.get("userlist")));
            List<WechatWorkUser> wechatWorkUsers = new ArrayList<>();
            for (Object o : jsonArray) {
                JSONObject jsonObject = (JSONObject) o;
                WechatWorkUser wechatWorkUser = new WechatWorkUser();
                wechatWorkUser.setUserId(jsonObject.getString("userid"));
                wechatWorkUser.setName(jsonObject.getString("name"));
                wechatWorkUsers.add(wechatWorkUser);
            }

            return wechatWorkUsers;
        }
        return null;
    }
}
