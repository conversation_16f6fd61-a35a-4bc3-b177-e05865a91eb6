package com.fytec.service.customer;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.customer.CustomerReqInfoDTO;
import com.fytec.dto.customer.CustomerReqInfoQueryDTO;
import com.fytec.entity.customer.CustomerReqInfo;
import com.fytec.mapper.customer.CustomerReqInfoMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
@Transactional
@RequiredArgsConstructor
public class CustomerReqInfoService {
    private final CustomerReqInfoMapper customerReqInfoMapper;

    public void addCustomerReqInfo(CustomerReqInfoDTO dto) {
        CustomerReqInfo customerReqInfo = new CustomerReqInfo();
        BeanUtil.copyProperties(dto, customerReqInfo);
        customerReqInfo.setStatus(0);
        customerReqInfoMapper.insert(customerReqInfo);
    }

    public void deleteCustomerReqInfo(Long id) {
        customerReqInfoMapper.deleteById(id);
    }

    public Page<CustomerReqInfo> queryCustomerReqInfoPage(CustomerReqInfoQueryDTO dto, Page<CustomerReqInfo> page) {
        LambdaQueryWrapper<CustomerReqInfo> queryWrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isNotBlank(dto.getKeywords())) {
            queryWrapper.and(_queryWrapper -> {
                _queryWrapper.like(CustomerReqInfo::getCustomerCompanyName, dto.getKeywords())
                        .or()
                        .like(CustomerReqInfo::getCustomerName, dto.getKeywords())
                        .or()
                        .eq(CustomerReqInfo::getCustomerTel, dto.getKeywords());
            });
        }
        if (StrUtil.isNotBlank(dto.getStatus())) {
            queryWrapper.eq(CustomerReqInfo::getStatus, dto.getStatus());
        }
        page.setRecords(customerReqInfoMapper.selectList(page, queryWrapper));
        return page;
    }

    public void updateCustomerReqInfoStatus(Long id) {
        CustomerReqInfo customerReqInfo = customerReqInfoMapper.selectById(id);
        if (customerReqInfo == null) {
            throw new ValidateException("客户需求不存在");
        }
        customerReqInfo.setStatus(1);
        customerReqInfoMapper.updateById(customerReqInfo);
    }
}
