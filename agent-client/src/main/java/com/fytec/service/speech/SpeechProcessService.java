package com.fytec.service.speech;

import cn.hutool.core.exceptions.ValidateException;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.bytedance.speech.speechengine.SpeechEngine;
import com.bytedance.speech.speechengine.SpeechEngineDefines;
import com.bytedance.speech.speechengine.SpeechEngineGenerator;
import com.fytec.dto.speech.SpeechStreamDTO;
import com.fytec.util.UUIDGenerator;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.URL;
import java.util.Base64;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicLong;

@Service
@Slf4j
@Transactional
@RequiredArgsConstructor
public class SpeechProcessService {
    @SneakyThrows
    public String processOneSentence(SpeechStreamDTO dto) {
        int ret = SpeechEngineGenerator.prepareEnvironment();
        if (ret != SpeechEngineDefines.ERR_NO_ERROR) {
            throw new ValidateException("Prepare Environment Failed: " + ret);
        }

        final CountDownLatch isFinished = new CountDownLatch(1);
        final AtomicLong startTime = new AtomicLong(0);
        final AtomicLong lastPacketSendTime = new AtomicLong(0);

        // Create engine
        SpeechEngine speechEngine = getSpeechEngine();
        ret = speechEngine.initEngine();
        if (ret != SpeechEngineDefines.ERR_NO_ERROR) {
            speechEngine.destroyEngine();
            throw new ValidateException("Init Engine Failed: " + ret);
        }

        // Set listener
        StringBuilder sb = new StringBuilder();
        speechEngine.setListener((type, bytes, len) -> {
            String data = new String(bytes);
            System.out.println("Get message: " + type + ", data: " + data);
            switch (type) {
                case SpeechEngineDefines.MESSAGE_TYPE_ENGINE_START:
                    System.out.println("Engine start");
                    break;
                case SpeechEngineDefines.MESSAGE_TYPE_ENGINE_STOP:
                    System.out.println("Engine stop");
                    isFinished.countDown();
                    break;
                case SpeechEngineDefines.MESSAGE_TYPE_ENGINE_ERROR:
                    System.out.println("Engine error");
                    break;
                case SpeechEngineDefines.MESSAGE_TYPE_PARTIAL_RESULT:
                    System.out.println("Get partial result process" );
                    break;
                case SpeechEngineDefines.MESSAGE_TYPE_FINAL_RESULT:
                    JSONObject jsonObject = JSON.parseObject(data);
                    JSONArray result = jsonObject.getJSONArray("result");
                    if (result == null) {
                        break;
                    }
                    for (Object o : result) {
                        JSONObject obj = JSON.parseObject(JSON.toJSONString(o));
                        sb.append(obj.getString("text"));
                    }
                    break;
                default:
                    break;
            }
        });

        // Start
        startTime.set(System.currentTimeMillis());
        ret = speechEngine.sendDirective(SpeechEngineDefines.DIRECTIVE_START_ENGINE, "");
        if (ret != SpeechEngineDefines.ERR_NO_ERROR) {
            speechEngine.destroyEngine();
            throw new ValidateException("Start Engine Failed: " + ret);
        }

        try {
            String url = dto.getUrl();
            byte[] bytes;
            if (url.startsWith("data:")) {
                //base64
                String pureBase64 = url.replaceAll("\\s", "");
                String base64Audio = pureBase64.split(",")[1]; // 提取纯Base64部分
                bytes = Base64.getDecoder().decode(base64Audio);

                // Feed each time
                ret = speechEngine.feedAudio(bytes, bytes.length);
                if (ret != SpeechEngineDefines.ERR_NO_ERROR) {
                    speechEngine.destroyEngine();
                    throw new ValidateException("Feed Audio Failed: " + ret);
                }

            } else if (url.startsWith("http:") || url.startsWith("https:")) {
                //http url
                URI uri = new URI(url);
                URL _url = uri.toURL();
                try (InputStream inputStream = _url.openStream()) {
                    bytes = new byte[2048];
                    while (true) {
                        int n = inputStream.read(bytes);
                        if (n == -1) {
                            break;
                        }
                        // Feed each time
                        ret = speechEngine.feedAudio(bytes, n);
                        if (ret != SpeechEngineDefines.ERR_NO_ERROR) {
                            speechEngine.destroyEngine();
                            throw new ValidateException("Feed Audio Failed: " + ret);
                        }
                    }

                }
            } else {
                throw new ValidateException("不支持的url");
            }

            lastPacketSendTime.set(System.currentTimeMillis());
            speechEngine.sendDirective(SpeechEngineDefines.DIRECTIVE_FINISH_TALKING, "");
        } catch (IOException e) {
            speechEngine.destroyEngine();
            throw new ValidateException("Read Audio Failed");
        }

        // Wait for finish
        try {
            isFinished.await();
        } catch (InterruptedException e) {
            log.warn("wait for finish has error");
        }

        // Destroy
        speechEngine.destroyEngine();
        return sb.toString();
    }

    private SpeechEngine getSpeechEngine() {
        SpeechEngine speechEngine = SpeechEngineGenerator.getInstance();
        speechEngine.createEngine();

        // Init engine
        speechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_APP_ID_STRING, "9058373640");
        speechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_APP_TOKEN_STRING, "Bearer;QsNO_mu1boCmfVme_i_DFeIVyA_nfQgw");
        speechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_UID_STRING, UUIDGenerator.getUUID());
        speechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_LOG_LEVEL_STRING, SpeechEngineDefines.LOG_LEVEL_INFO);
        speechEngine.setOptionBoolean(SpeechEngineDefines.PARAMS_KEY_USE_ALOG_BOOL, false);
        speechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_DEBUG_PATH_STRING, "./var/log/speech_sdk");
        speechEngine.setOptionInt(SpeechEngineDefines.PARAMS_KEY_CHANNEL_NUM_INT, 1);
        speechEngine.setOptionInt(SpeechEngineDefines.PARAMS_KEY_SAMPLE_RATE_INT, 16000);
        speechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_RECORDER_TYPE_STRING, SpeechEngineDefines.RECORDER_TYPE_STREAM);
        speechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_ENGINE_NAME_STRING, SpeechEngineDefines.ASR_ENGINE);
        speechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_ASR_ADDRESS_STRING, "wss://speech.bytedance.com");
        speechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_ASR_URI_STRING, "/api/v2/asr");
        speechEngine.setOptionString(SpeechEngineDefines.PARAMS_KEY_ASR_CLUSTER_STRING, "volcengine_input_common");
        speechEngine.setOptionBoolean(SpeechEngineDefines.PARAMS_KEY_ASR_AUTO_STOP_BOOL, false);
        speechEngine.setOptionBoolean(SpeechEngineDefines.PARAMS_KEY_ASR_SHOW_NLU_PUNC_BOOL, true);
        speechEngine.setOptionBoolean(SpeechEngineDefines.PARAMS_KEY_ASR_SHOW_UTTER_BOOL, true);
        return speechEngine;
    }
}
