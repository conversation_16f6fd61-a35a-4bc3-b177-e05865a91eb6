package com.fytec.service.plugin;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fytec.dto.plugin.*;
import com.fytec.entity.plugin.PluginBasic;
import com.fytec.entity.plugin.PluginDev;
import com.fytec.entity.plugin.PluginPublish;
import com.fytec.entity.resource.AiResource;
import com.fytec.mapper.plugin.PluginDevMapper;
import com.fytec.mapper.plugin.PluginMapper;
import com.fytec.mapper.plugin.PluginPublishMapper;
import com.fytec.mapper.resource.ResourceMapper;
import com.fytec.util.UUIDGenerator;
import com.fytec.util.VersionUtils;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;
import com.mashape.unirest.request.GetRequest;
import com.mashape.unirest.request.HttpRequestWithBody;
import io.modelcontextprotocol.client.McpClient;
import io.modelcontextprotocol.client.McpSyncClient;
import io.modelcontextprotocol.client.transport.HttpClientSseClientTransport;
import io.modelcontextprotocol.client.transport.ServerParameters;
import io.modelcontextprotocol.client.transport.StdioClientTransport;
import io.modelcontextprotocol.spec.McpClientTransport;
import io.modelcontextprotocol.spec.McpSchema;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.net.URI;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.fytec.constant.Constants.*;
import static com.fytec.constant.Constants.PLUGIN_AUTH_TYPE.serviceToken;
import static com.fytec.constant.Constants.PLUGIN_MCP_SERVER_TYPE.sse;
import static com.fytec.constant.Constants.PLUGIN_MCP_SERVER_TYPE.stdio;
import static com.fytec.constant.Constants.PLUGIN_TYPE.http;

@Service
@Slf4j
@Transactional
@RequiredArgsConstructor
public class PluginService {
    private final ResourceMapper resourceMapper;
    private final PluginMapper pluginMapper;
    private final PluginDevMapper pluginDevMapper;
    private final PluginPublishMapper pluginPublishMapper;


    public JSONObject getPluginInfo(Long resourceId) {
        JSONObject result = new JSONObject();
        JSONObject metaInfo = new JSONObject();
        AiResource aiResource = resourceMapper.selectById(resourceId);
        if (aiResource == null) {
            throw new ValidateException("插件资源不存在");
        }
        PluginBasic pluginBasic = pluginMapper.selectOne(
                new LambdaQueryWrapper<PluginBasic>()
                        .eq(PluginBasic::getResourceId, resourceId)
                        .last("limit 1")
        );
        if (pluginBasic == null) {
            throw new ValidateException("插件资源不存在");
        }
        metaInfo.put("resourceId", aiResource.getId());
        metaInfo.put("pluginId", pluginBasic.getId());
        metaInfo.put("name", aiResource.getName());
        metaInfo.put("description", aiResource.getDescription());
        metaInfo.put("logo", aiResource.getLogo());
        metaInfo.put("pluginType", pluginBasic.getType());
        metaInfo.put("pluginUrl", pluginBasic.getUrl());
        if (StrUtil.isNotBlank(pluginBasic.getHeaders())) {
            metaInfo.put("pluginHeaders", JSON.parseArray(pluginBasic.getHeaders()));
        } else {
            metaInfo.put("pluginHeaders", new JSONArray());
        }
        metaInfo.put("pluginAuthType", pluginBasic.getAuthType());
        metaInfo.put("pluginAuthInfo", pluginBasic.getAuthInfo());
        metaInfo.put("mcpServerConfig", pluginBasic.getMcpServerConfig());
        result.put("metaInfo", metaInfo);
        return result;
    }

    public void publishPlugin(PluginPublishDTO dto) {
        PluginBasic pluginBasic = pluginMapper.selectById(dto.getId());
        if (pluginBasic == null) {
            throw new ValidateException("插件不存在");
        }

        AiResource aiResource = resourceMapper.selectById(pluginBasic.getResourceId());
        if (aiResource == null) {
            throw new ValidateException("插件不存在");
        }

        String version = VersionUtils.next(pluginBasic.getVersion(), 9);

        List<PluginDev> pluginDevs = pluginDevMapper.selectList(
                new LambdaQueryWrapper<PluginDev>()
                        .eq(PluginDev::getPluginId, dto.getId())
        );

        List<PluginPublish> pluginPublishes = new ArrayList<>();
        for (PluginDev pluginDev : pluginDevs) {
            pluginDev.setStatus(PUBLISH_STATUS_1);

            PluginPublish pluginPublish = new PluginPublish();
            BeanUtils.copyProperties(pluginDev, pluginPublish);
            pluginPublish.setId(null);
            pluginPublish.setToolId(pluginDev.getId());
            pluginPublish.setVersion(version);
            pluginPublish.setPublishTime(LocalDateTime.now());
            pluginPublish.setPubRecord(dto.getPubRecord());
            String functionCallTool;
            if (StrUtil.equals(pluginBasic.getType(), http.name())) {
                functionCallTool = generateChainTool(
                        StrUtil.format("{}_{}_{}", pluginDev.getName(), pluginDev.getId(), version),
                        pluginDev.getDescription(),
                        pluginDev.getRequestParams()
                );
            } else {
                String functionCallToolPrefix = "{\"type\": \"function\",\"function\": {\"name\": \"%s\",\"description\": \"%s\"}, \"parameters\": %s}";
                functionCallTool = functionCallToolPrefix.formatted(
                        StrUtil.format("{}_{}_{}", pluginDev.getName(), pluginDev.getId(), version),
                        pluginDev.getDescription(),
                        pluginDev.getMcpInputSchema());
            }
            pluginPublish.setFunctionCallTool(functionCallTool);
            pluginPublishes.add(pluginPublish);
        }
        pluginDevMapper.updateById(pluginDevs);
        pluginPublishMapper.insert(pluginPublishes);

        pluginBasic.setStatus(PUBLISH_STATUS_1);
        pluginBasic.setVersion(version);
        pluginBasic.setPublishTime(LocalDateTime.now());
        pluginBasic.setPubRecord(dto.getPubRecord());
        pluginMapper.updateById(pluginBasic);

        aiResource.setStatus(PUBLISH_STATUS_1);
        resourceMapper.updateById(aiResource);
    }


    private String generateChainTool(String pluginApiName, String pluginApiNameDescription, String requestParamStr) {
        String chainTool;
        if (StrUtil.isBlank(requestParamStr)) {
            chainTool = "{\"type\": \"function\",\"function\": {\"name\": \"%s\",\"description\": \"%s\"}}";
            chainTool = chainTool.formatted(pluginApiName, pluginApiNameDescription);
        } else {
            chainTool = "{\"type\": \"function\",\"function\": {\"name\": \"%s\",\"description\": \"%s\",\"parameters\": {\"type\": \"object\", %s}}}";
            String parameters = prepareParameters(JSON.parseArray(requestParamStr, PluginApiRequestParamDTO.class));
            chainTool = chainTool.formatted(pluginApiName, pluginApiNameDescription, parameters);
        }

        return chainTool;
    }

    private String prepareParameters(List<PluginApiRequestParamDTO> inputs) {
        String parameters = "\"properties\": {%s},\"required\": [%s]";
        StringBuilder inputProperties = new StringBuilder();
        StringBuilder inputRequiredFields = new StringBuilder();
        for (PluginApiRequestParamDTO input : inputs) {
            String inputType = input.getType();
            String inputName = input.getName();
            String inputDescription = input.getDescription();
            String inputDefaultValue = input.getDefaultValue();

            if (StrUtil.equals(inputType, "object")) {
                String _inputProperties = prepareParameters(input.getChildren());
                inputProperties.append(String.format(
                        "\"%s\": {\"type\": \"%s\", %s},",
                        inputName, inputType.toLowerCase(), _inputProperties
                ));
            } else if (StrUtil.startWith(inputType, "Array")) {
                inputDefaultValue = StrUtil.isBlank(inputDefaultValue) ? "" : ",\"default\": " + inputDefaultValue;
                Pattern pattern = Pattern.compile("Array<([^>]+)>");
                Matcher matcher = pattern.matcher(inputType);
                if (matcher.find()) {
                    String itemType = matcher.group(1);
                    if (StrUtil.equals(itemType, "Object")) {
                        String _inputProperties = prepareParameters(input.getChildren());
                        inputProperties.append(String.format(
                                "\"%s\": {\"type\": \"%s\", \"items\": {\"type\": \"object\", %s}%s},",
                                inputName, "array", _inputProperties, inputDefaultValue
                        ));
                    } else {
                        inputProperties.append(String.format(
                                "\"%s\": {\"type\": \"%s\", \"items\": {\"type\": \"%s\"}%s},",
                                inputName, "array", itemType, inputDefaultValue
                        ));
                    }
                }
            } else {

                inputDefaultValue = StrUtil.isBlank(inputDefaultValue) ? "" : ",\"default\": \"" + inputDefaultValue + "\"";
                inputProperties.append(String.format(
                        "\"%s\": {\"type\": \"%s\", \"description\": \"%s\"%s},",
                        inputName, inputType, inputDescription, inputDefaultValue
                ));
            }

            if (input.isRequired()) {
                inputRequiredFields.append("\"").append(inputName).append("\",");
            }
        }
        if (inputProperties.length() > 1) {
            inputProperties.deleteCharAt(inputProperties.length() - 1);
        }
        if (inputRequiredFields.length() > 1) {
            inputRequiredFields.deleteCharAt(inputRequiredFields.length() - 1);
        }
        parameters = parameters.formatted(inputProperties, inputRequiredFields);
        return parameters;
    }


    public Long createPluginApi(PluginApiDTO dto) {
        PluginBasic pluginBasic = pluginMapper.selectById(dto.getPluginId());
        if (pluginBasic == null) {
            throw new ValidateException("插件不存在");
        }
        PluginDev pluginDev = new PluginDev();
        pluginDev.setPluginId(dto.getPluginId());
        pluginDev.setResourceId(dto.getResourceId());
        pluginDev.setName(dto.getName());
        pluginDev.setDescription(dto.getDescription());
        pluginDev.setPath(StrUtil.format("{}/{}", pluginBasic.getUrl(), dto.getName()));
        pluginDev.setSuffixPath(StrUtil.format("/{}", dto.getName()));
        pluginDev.setMethod(HttpMethod.POST.name());
        pluginDev.setStatus(PUBLISH_STATUS_0);
        pluginDev.setDebugStatus(DEBUG_STATUS_0);
        pluginDev.setEnable(true);
        pluginDevMapper.insert(pluginDev);
        return pluginDev.getId();
    }

    public void updatePluginApiBasic(PluginApiDTO dto) {
        PluginDev pluginDev = pluginDevMapper.selectById(dto.getId());
        if (pluginDev == null) {
            throw new ValidateException("插件api不存在");
        }
        pluginDev.setName(dto.getName());
        pluginDev.setDescription(dto.getDescription());
        pluginDevMapper.updateById(pluginDev);
    }

    public void updatePluginApiExtend(PluginApiDTO dto) {
        PluginDev pluginDev = pluginDevMapper.selectById(dto.getId());
        if (pluginDev == null) {
            throw new ValidateException("插件api不存在");
        }


        PluginBasic pluginBasic = pluginMapper.selectById(pluginDev.getPluginId());
        if (pluginBasic == null) {
            throw new ValidateException("插件不存在");
        }
        pluginDev.setSuffixPath(dto.getPath());
        pluginDev.setPath(StrUtil.format("{}{}", pluginBasic.getUrl(), dto.getPath()));
        pluginDev.setMethod(dto.getMethod());
        pluginDevMapper.updateById(pluginDev);
    }

    public void updatePluginApiRequestParams(PluginApiDTO dto) {
        PluginDev pluginDev = pluginDevMapper.selectById(dto.getId());
        if (pluginDev == null) {
            throw new ValidateException("插件api不存在");
        }
        pluginDev.setRequestParams(JSON.toJSONString(dto.getRequestParams()));
        pluginDevMapper.updateById(pluginDev);

    }

    public void updatePluginApiResponseParams(PluginApiDTO dto) {
        PluginDev pluginDev = pluginDevMapper.selectById(dto.getId());
        if (pluginDev == null) {
            throw new ValidateException("插件api不存在");
        }
        pluginDev.setResponseParams(JSON.toJSONString(dto.getResponseParams()));
        pluginDevMapper.updateById(pluginDev);
    }

    public List<PluginApiListDTO> queryPluginApiList(Long resourceId) {
        AiResource aiResource = resourceMapper.selectById(resourceId);
        if (aiResource == null) {
            throw new ValidateException("插件资源不存在");
        }
        return pluginDevMapper.selectList(
                        new LambdaQueryWrapper<PluginDev>()
                                .eq(PluginDev::getResourceId, resourceId)
                )
                .stream()
                .map(pluginDev -> {
                    PluginApiListDTO pluginApiListDTO = new PluginApiListDTO();
                    BeanUtils.copyProperties(pluginDev, pluginApiListDTO);
                    List<PluginApiRequestParamDTO> requestParams = JSON.parseArray(pluginDev.getRequestParams(), PluginApiRequestParamDTO.class);
                    List<PluginApiResponseParamDTO> responseParams = JSON.parseArray(pluginDev.getRequestParams(), PluginApiResponseParamDTO.class);
                    pluginApiListDTO.setRequestParams(requestParams);
                    pluginApiListDTO.setResponseParams(responseParams);
                    return pluginApiListDTO;
                })
                .toList();
    }

    public PluginApiDTO getPluginApiDetail(Long id) {
        PluginDev pluginDev = pluginDevMapper.selectById(id);
        if (pluginDev == null) {
            throw new ValidateException("插件api不存在");
        }

        PluginBasic pluginBasic = pluginMapper.selectById(pluginDev.getPluginId());

        PluginApiDTO pluginApiDTO = new PluginApiDTO();
        BeanUtils.copyProperties(pluginDev, pluginApiDTO);
        List<PluginApiRequestParamDTO> requestParams = JSON.parseArray(pluginDev.getRequestParams(), PluginApiRequestParamDTO.class);
        List<PluginApiResponseParamDTO> responseParams = JSON.parseArray(pluginDev.getResponseParams(), PluginApiResponseParamDTO.class);
        pluginApiDTO.setRequestParams(requestParams);
        pluginApiDTO.setResponseParams(responseParams);
        pluginApiDTO.setUrl(pluginBasic.getUrl());
        pluginApiDTO.setPath(pluginDev.getSuffixPath());
        return pluginApiDTO;
    }

    public PluginApiDTO getPublishPluginApiDetail(Long id) {
        PluginPublish pluginPublish = pluginPublishMapper.selectById(id);
        if (pluginPublish == null) {
            throw new ValidateException("插件api不存在");
        }

        PluginBasic pluginBasic = pluginMapper.selectById(pluginPublish.getPluginId());

        PluginApiDTO pluginApiDTO = new PluginApiDTO();
        BeanUtils.copyProperties(pluginPublish, pluginApiDTO);
        List<PluginApiRequestParamDTO> requestParams = JSON.parseArray(pluginPublish.getRequestParams(), PluginApiRequestParamDTO.class);
        List<PluginApiResponseParamDTO> responseParams = JSON.parseArray(pluginPublish.getResponseParams(), PluginApiResponseParamDTO.class);
        pluginApiDTO.setRequestParams(requestParams);
        pluginApiDTO.setResponseParams(responseParams);
        pluginApiDTO.setUrl(pluginBasic.getUrl());
        pluginApiDTO.setPath(pluginPublish.getSuffixPath());
        return pluginApiDTO;
    }

    public void enablePluginApi(Long id) {
        PluginDev pluginDev = pluginDevMapper.selectById(id);
        if (pluginDev == null) {
            throw new ValidateException("插件api不存在");
        }
        pluginDev.setEnable(true);
        pluginDevMapper.updateById(pluginDev);
    }

    public void disablePluginApi(Long id) {
        PluginDev pluginDev = pluginDevMapper.selectById(id);
        if (pluginDev == null) {
            throw new ValidateException("插件api不存在");
        }
        pluginDev.setEnable(false);
        pluginDevMapper.updateById(pluginDev);
    }

    public void deletePluginApi(Long id) {
        PluginDev pluginDev = pluginDevMapper.selectById(id);
        if (pluginDev == null) {
            throw new ValidateException("插件api不存在");
        }
        pluginDevMapper.deleteById(id);
    }

    @SneakyThrows
    public Map<String, Object> executePluginApi(PluginExecuteDTO dto) {
        String requestParamStr;
        Long pluginId;
        String httpPath;
        String httpMethod;
        if (dto.isDebug()) {
            PluginDev pluginDev = pluginDevMapper.selectById(dto.getId());
            if (pluginDev == null) {
                throw new ValidateException("开发插件api不存在");
            }
            requestParamStr = pluginDev.getRequestParams();
            pluginId = pluginDev.getPluginId();
            httpPath = pluginDev.getPath();
            httpMethod = pluginDev.getMethod();
        } else {
            PluginPublish pluginPublish = pluginPublishMapper.selectById(dto.getId());
            if (pluginPublish == null) {
                throw new ValidateException("发布插件api不存在");
            }
            requestParamStr = pluginPublish.getRequestParams();
            pluginId = pluginPublish.getPluginId();
            httpPath = pluginPublish.getPath();
            httpMethod = pluginPublish.getMethod();
        }

        PluginBasic pluginBasic = pluginMapper.selectById(pluginId);

        //处理请求参数
        Map<String, Map<String, Object>> requestParams = prepareHttpRequestParams(requestParamStr, dto.getParameters());
        Map<String, Object> queryParams = requestParams.get("queryParams");
        Map<String, Object> bodyParams = requestParams.get("bodyParams");
        Map<String, Object> headerParams = requestParams.get("headerParams");
        Map<String, Object> pathParams = requestParams.get("pathParams");

        //处理请求headers
        Map<String, String> headers = prepareHttpHeaders(pluginBasic.getHeaders(), headerParams);

        //处理请求auth
        String authType = pluginBasic.getAuthType();
        if (StrUtil.equals(authType, serviceToken.name())) {
            JSONObject authInfo = JSON.parseObject(pluginBasic.getAuthInfo());
            String serviceTokenKey = authInfo.getString("serviceTokenKey");
            String serviceTokenValue = authInfo.getString("serviceTokenValue");
            String serviceTokenLocation = authInfo.getString("serviceTokenLocation");
            if (StrUtil.isBlank(serviceTokenLocation)) {
                serviceTokenLocation = "header";
            }
            switch (serviceTokenLocation) {
                case "header":
                    headers.put(serviceTokenKey, serviceTokenValue);
                    break;
                case "query":
                    queryParams.put(serviceTokenKey, serviceTokenValue);
                    break;
            }
        }

        //发送请求
        HttpResponse<String> httpResponse;
        if (StrUtil.equals(httpMethod, HttpMethod.GET.name())) {
            httpResponse = sendHttpGetRequest(Unirest.get(httpPath), queryParams, headers, pathParams);
        } else if (StrUtil.equals(httpMethod, HttpMethod.POST.name())) {
            httpResponse = sendHttpRequestWithBody(Unirest.post(httpPath), queryParams, bodyParams, headers, pathParams);
        } else if (StrUtil.equals(httpMethod, HttpMethod.PUT.name())) {
            httpResponse = sendHttpRequestWithBody(Unirest.put(httpPath), queryParams, bodyParams, headers, pathParams);
        } else if (StrUtil.equals(httpMethod, HttpMethod.DELETE.name())) {
            httpResponse = sendHttpRequestWithBody(Unirest.delete(httpPath), queryParams, bodyParams, headers, pathParams);
        } else {
            throw new ValidateException("不支持的请求方法");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("httpRespCode", httpResponse.getStatus());
        result.put("httpRespBody", httpResponse.getBody());
        if (httpResponse.getStatus() == 200) {
            if (dto.isDebug()) {
                PluginDev pluginDev = pluginDevMapper.selectById(dto.getId());
                pluginDev.setDebugStatus(DEBUG_STATUS_1);
                pluginDevMapper.updateById(pluginDev);
            }
            try {
                JSONObject jsonObject = JSON.parseObject(httpResponse.getBody());
                result.put("httpRespBody", jsonObject);
            } catch (Exception ignored) {
            }
        }

        return result;
    }

    private HttpResponse<String> sendHttpRequestWithBody(HttpRequestWithBody request,
                                                         Map<String, Object> queryParams,
                                                         Map<String, Object> bodyParams,
                                                         Map<String, String> headers,
                                                         Map<String, Object> pathParams) throws UnirestException {

        for (Map.Entry<String, Object> entry : pathParams.entrySet()) {
            request = request.routeParam(entry.getKey(), String.valueOf(entry.getValue()));
        }

        request = request.headers(headers).queryString(queryParams);
        if (CollUtil.isNotEmpty(bodyParams)) {
            return request.body(JSON.toJSONString(bodyParams)).asString();
        }

        return request.headers(headers)
                .queryString(queryParams)
                .asString();
    }


    private HttpResponse<String> sendHttpGetRequest(GetRequest request,
                                                    Map<String, Object> queryParams,
                                                    Map<String, String> headers,
                                                    Map<String, Object> pathParams) throws UnirestException {

        for (Map.Entry<String, Object> entry : pathParams.entrySet()) {
            request = request.routeParam(entry.getKey(), String.valueOf(entry.getValue()));
        }

        return request.headers(headers)
                .queryString(queryParams)
                .asString();
    }

    private Map<String, String> prepareHttpHeaders(String headerStr, Map<String, Object> headerParams) {
        Map<String, String> headerMap = new HashMap<>();

        if (StrUtil.isNotBlank(headerStr)) {
            JSONArray headers = JSON.parseArray(headerStr);
            for (Object obj : headers) {
                JSONObject header = JSON.parseObject(JSON.toJSONString(obj));
                headerMap.put(header.getString("key"), header.getString("value"));
            }
        }

        for (Map.Entry<String, Object> entry : headerParams.entrySet()) {
            headerMap.put(entry.getKey(), String.valueOf(entry.getValue()));
        }
        return headerMap;
    }

    private Map<String, Map<String, Object>> prepareHttpRequestParams(String requestParamStr, String paramStr) {
        Map<String, Object> queryParams = new HashMap<>();
        Map<String, Object> bodyParams = new HashMap<>();
        Map<String, Object> headerParams = new HashMap<>();
        Map<String, Object> pathParams = new LinkedHashMap<>();//path需要按序
        Map<String, Map<String, Object>> result = new HashMap<>();

        JSONObject params = JSON.parseObject(paramStr);
        List<PluginApiRequestParamDTO> requestParams = JSON.parseArray(requestParamStr, PluginApiRequestParamDTO.class);
        if (CollUtil.isNotEmpty(requestParams)) {
            for (PluginApiRequestParamDTO requestParam : requestParams) {
                String name = requestParam.getName();
                boolean required = requestParam.isRequired();
                if (required && !params.containsKey(name)) {
                    throw new ValidateException("参数" + name + "必填");
                }

                Object value = params.get(name);
                String method = requestParam.getMethod();
                switch (method) {
                    case "query":
                        queryParams.put(name, value);
                        break;
                    case "body":
                        bodyParams.put(name, value);
                        break;
                    case "header":
                        headerParams.put(name, value);
                        break;
                    case "path":
                        pathParams.put(name, value);
                        break;
                }
            }
        }

        result.put("queryParams", queryParams);
        result.put("bodyParams", bodyParams);
        result.put("headerParams", headerParams);
        result.put("pathParams", pathParams);
        return result;
    }

    public Page<PluginPublishListDTO> queryPublishPluginList(PluginQueryDTO dto, Page<PluginPublishListDTO> page) {
        List<PluginPublishListDTO> publishPluginList = pluginMapper.queryPublishPluginList(dto, page);
        for (PluginPublishListDTO pluginPublishListDTO : publishPluginList) {
            List<PluginPublish> pluginPublishList = pluginPublishMapper.queryLatestPluginApiList(pluginPublishListDTO);
            pluginPublishListDTO.setPluginApis(
                    pluginPublishList.stream()
                            .map(pluginPublish -> {
                                PluginApiDTO pluginApiDTO = new PluginApiDTO();
                                BeanUtils.copyProperties(pluginPublish, pluginApiDTO);
                                List<PluginApiRequestParamDTO> requestParams = JSON.parseArray(pluginPublish.getRequestParams(), PluginApiRequestParamDTO.class);
                                List<PluginApiResponseParamDTO> responseParams = JSON.parseArray(pluginPublish.getResponseParams(), PluginApiResponseParamDTO.class);
                                pluginApiDTO.setRequestParams(requestParams);
                                pluginApiDTO.setResponseParams(responseParams);
                                return pluginApiDTO;
                            }).toList()
            );
        }
        page.setRecords(publishPluginList);
        return page;
    }

    @SneakyThrows
    public void savePluginMcp(PluginMcpDTO dto) {
        PluginBasic pluginBasic = pluginMapper.selectById(dto.getPluginId());
        if (pluginBasic == null) {
            throw new ValidateException("插件不存在");
        }
        String mcpServerConfig = dto.getMcpServerConfig();

        pluginDevMapper.delete(new LambdaQueryWrapper<PluginDev>()
                .eq(PluginDev::getPluginId, dto.getPluginId())
        );

        ObjectMapper mapper = new ObjectMapper();
        JsonNode root = mapper.readTree(mcpServerConfig);
        Iterator<Map.Entry<String, JsonNode>> servers = root.path("mcpServers").fields();

        try {
            if (servers.hasNext()) {
                Map.Entry<String, JsonNode> entry = servers.next();
                McpClientTransport transport;

                PluginMcpServerConfigDTO config = mapper.treeToValue(entry.getValue(), PluginMcpServerConfigDTO.class);
                if (StrUtil.isNotBlank(config.getUrl())) {
                    pluginBasic.setMcpServerType(sse.name());

                    String url = config.getUrl();
                    URI uri = new URI(url);
                    String baseUrl = StrUtil.format("{}://{}", uri.getScheme(), uri.getAuthority());
                    String sseEndPoint = StrUtil.format("{}?{}", uri.getPath(), uri.getQuery());
                    transport = HttpClientSseClientTransport
                            .builder(baseUrl)
                            .sseEndpoint(sseEndPoint)
                            .build();
                } else if (StrUtil.isNotBlank(config.getCommand())) {
                    pluginBasic.setMcpServerType(stdio.name());

                    ServerParameters params = ServerParameters.builder(config.getCommand())
                            .args(config.getArgs())
                            .env(config.getEnv())
                            .build();
                    transport = new SafeStdioClientTransport(params);
                } else {
                    throw new ValidateException("不支持的MCP Server Type");
                }

                McpSyncClient client = McpClient.sync(transport)
                        .requestTimeout(Duration.ofSeconds(10))
                        .build();

                // Initialize connection
                client.initialize();

                // List available tools
                var tools = client.listTools();
                List<PluginDev> pluginDevList = new ArrayList<>();
                tools.tools().forEach(tool -> {
                    PluginDev pluginDev = new PluginDev();
                    pluginDev.setPluginId(dto.getPluginId());
                    pluginDev.setResourceId(dto.getResourceId());
                    pluginDev.setName(tool.name());
                    pluginDev.setDescription(tool.description());

                    McpSchema.JsonSchema schema = tool.inputSchema();
                    pluginDev.setMcpInputSchema(JSON.toJSONString(schema));
                    try {
                        List<PluginApiRequestParamDTO> dtoList = convertSchemaToDtoList(JSON.toJSONString(schema));
                        pluginDev.setRequestParams(JSON.toJSONString(dtoList));
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                    pluginDev.setStatus(PUBLISH_STATUS_0);
                    pluginDev.setDebugStatus(DEBUG_STATUS_0);
                    pluginDev.setEnable(true);
                    pluginDevList.add(pluginDev);
                });
                pluginDevMapper.insert(pluginDevList);
                client.closeGracefully();
            }

            pluginBasic.setMcpServerConfig(mcpServerConfig);
            pluginMapper.updateById(pluginBasic);
        } catch (Exception e) {
            log.error("MCP Server配置失败", e);
            throw new ValidateException("MCP Server配置失败");
        }
    }

    public List<PluginApiRequestParamDTO> convertSchemaToDtoList(String json) throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        JsonNode parametersNode = mapper.readTree(json);

        List<String> requiredFields = new ArrayList<>();
        if (parametersNode.has("required")) {
            parametersNode.get("required").forEach(r -> requiredFields.add(r.asText()));
        }

        return processProperties(parametersNode, requiredFields);
    }

    private List<PluginApiRequestParamDTO> processProperties(JsonNode parentNode, List<String> parentRequired) {
        List<PluginApiRequestParamDTO> result = new ArrayList<>();
        JsonNode propertiesNode = parentNode.path("properties");

        Iterator<String> fieldNames = propertiesNode.fieldNames();
        while (fieldNames.hasNext()) {
            String fieldName = fieldNames.next();
            JsonNode fieldNode = propertiesNode.get(fieldName);

            PluginApiRequestParamDTO dto = new PluginApiRequestParamDTO();
            dto.setId(UUIDGenerator.getUUID());
            dto.setName(fieldName);
            dto.setType(fieldNode.path("type").asText(null));
            dto.setDescription(fieldNode.path("description").asText(null));
            dto.setRequired(parentRequired != null && parentRequired.contains(fieldName));

            if (fieldNode.has("default")) {
                dto.setDefaultValue(fieldNode.get("default").asText());
            }


            if ("object".equalsIgnoreCase(dto.getType())) {
                // 处理嵌套 object
                List<String> childRequired = new ArrayList<>();
                if (fieldNode.has("required")) {
                    fieldNode.get("required").forEach(r -> childRequired.add(r.asText()));
                }
                dto.setChildren(processProperties(fieldNode, childRequired));
            } else if ("array".equalsIgnoreCase(dto.getType())) {
                // 处理 array
                JsonNode itemsNode = fieldNode.path("items");
                if (itemsNode.isObject()) {
                    String itemsType = itemsNode.path("type").asText();
                    if ("object".equalsIgnoreCase(itemsType)) {
                        List<String> childRequired = new ArrayList<>();
                        if (itemsNode.has("required")) {
                            itemsNode.get("required").forEach(r -> childRequired.add(r.asText()));
                        }
                        dto.setChildren(processProperties(itemsNode, childRequired));
                    } else {
                        dto.setType(StrUtil.format("array<{}>", itemsType));
                    }
                }
            }

            result.add(dto);
        }

        return result;
    }

    @SneakyThrows
    public Object executePluginMcp(PluginExecuteDTO dto) {
        Long pluginId;
        String methodName;
        if (dto.isDebug()) {
            PluginDev pluginDev = pluginDevMapper.selectById(dto.getId());
            if (pluginDev == null) {
                throw new ValidateException("开发插件api不存在");
            }
            pluginId = pluginDev.getPluginId();
            methodName = pluginDev.getName();
        } else {
            PluginPublish pluginPublish = pluginPublishMapper.selectById(dto.getId());
            if (pluginPublish == null) {
                throw new ValidateException("发布插件api不存在");
            }
            pluginId = pluginPublish.getPluginId();
            methodName = pluginPublish.getName();
        }
        PluginBasic pluginBasic = pluginMapper.selectById(pluginId);
        Map<String, Object> params = JSON.parseObject(dto.getParameters(), new TypeReference<>() {
        });

        ObjectMapper mapper = new ObjectMapper();
        JsonNode root = mapper.readTree(pluginBasic.getMcpServerConfig());
        Iterator<Map.Entry<String, JsonNode>> servers = root.path("mcpServers").fields();


        Map<String, Object> result = new HashMap<>();
        if (servers.hasNext()) {
            String mcpServerType = pluginBasic.getMcpServerType();
            Map.Entry<String, JsonNode> entry = servers.next();
            PluginMcpServerConfigDTO config = mapper.treeToValue(entry.getValue(), PluginMcpServerConfigDTO.class);
            McpClientTransport transport;
            if (StrUtil.equals(mcpServerType, sse.name())) {
                String url = config.getUrl();
                URI uri = new URI(url);
                String baseUrl = StrUtil.format("{}://{}", uri.getScheme(), uri.getAuthority());
                String sseEndPoint = StrUtil.format("{}?{}", uri.getPath(), uri.getQuery());
                transport = HttpClientSseClientTransport
                        .builder(baseUrl)
                        .sseEndpoint(sseEndPoint)
                        .build();
            } else if (StrUtil.equals(mcpServerType, stdio.name())) {
                ServerParameters serverParameters = ServerParameters.builder(config.getCommand())
                        .args(config.getArgs())
                        .env(config.getEnv())
                        .build();
                transport = new StdioClientTransport(serverParameters);
            } else {
                throw new ValidateException("不支持的MCP Server Type");
            }

            McpSyncClient client = McpClient.sync(transport)
                    .requestTimeout(Duration.ofSeconds(10))
                    .build();

            // Initialize connection
            client.initialize();

            try {
                var request = new McpSchema.CallToolRequest(methodName, params);
                var callToolResult = client.callTool(request);
                result.put("httpRespBody", callToolResult.content());
                if (callToolResult.isError() == null || !callToolResult.isError()) {
                    if (dto.isDebug()) {
                        PluginDev pluginDev = pluginDevMapper.selectById(dto.getId());
                        pluginDev.setDebugStatus(DEBUG_STATUS_1);
                        pluginDevMapper.updateById(pluginDev);
                    }
                    result.put("httpRespCode", 200);
                } else {
                    result.put("httpRespCode", 400);
                }
            } catch (Exception e) {
                result.put("httpRespCode", 500);
                result.put("httpRespBody", e.getMessage());
            }
            client.closeGracefully();
        }
        return result;
    }
}
