package com.fytec.service.knowledge;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fytec.dto.knowledge.KnowledgeDocBatchDTO;
import com.fytec.dto.knowledge.KnowledgeDocDTO;
import com.fytec.dto.knowledge.KnowledgeDocQueryDTO;
import com.fytec.dto.knowledge.group.KnowledgeGroupDTO;
import com.fytec.dto.knowledge.group.KnowledgeGroupTreeDTO;
import com.fytec.entity.knowledge.KnowledgeGroup;
import com.fytec.entity.knowledge.KnowledgeTeamMember;
import com.fytec.entity.system.SysUser;
import com.fytec.mapper.knowledge.KnowledgeGroupMapper;
import com.fytec.mapper.knowledge.KnowledgeTeamMemberMapper;
import com.fytec.mapper.system.SysUserMapper;
import com.fytec.satoken.StpClientUserUtil;
import com.fytec.utils.TreeBuilder;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.fytec.constant.Constants.DEFAULT_KNOWLEDGE_LOGO;
import static com.fytec.constant.Constants.KNOWLEDGE_TYPE.personal;
import static com.fytec.constant.Constants.KNOWLEDGE_TYPE.team;

@Service
@Slf4j
@Transactional
@RequiredArgsConstructor
public class KnowledgeGroupService {
    private final KnowledgeGroupMapper knowledgeGroupMapper;
    private final KnowledgeService knowledgeService;
    private final SysUserMapper sysUserMapper;
    private final KnowledgeTeamMemberMapper knowledgeTeamMemberMapper;

    @SneakyThrows
    public Long addKnowledgeGroup(KnowledgeGroupDTO dto) {
        KnowledgeGroup group = new KnowledgeGroup();
        BeanUtils.copyProperties(dto, group);
        Long knowledgeId;
        if (group.getParentId() == null) {
            //新建的根目录，需要初始化知识库
            convertStartTimeAndEndTime(dto);
            knowledgeId = knowledgeService.initKnowledge(dto.getName(), dto.getDescription(),
                    dto.getLogo(), dto.getType(), dto.getExpireOption(),
                    dto.getStartTime(), dto.getEndTime(), StpClientUserUtil.getLoginIdAsString());
        } else {
            //新建的子目录，不需要初始化知识库
            KnowledgeGroup groupParent = knowledgeGroupMapper.selectById(dto.getParentId());
            if (groupParent == null) {
                throw new ValidateException("父目录不存在");
            }
            knowledgeId = groupParent.getKnowledgeId();
        }

        group.setKnowledgeId(knowledgeId);
        group.setSystemGroup(false);
        group.setDefaultGroup(false);
        knowledgeGroupMapper.insert(group);

        //初始化默认分组
        if (group.getParentId() == null && group.getType().equals(personal.name())) {
            KnowledgeGroup subGroup = new KnowledgeGroup();
            subGroup.setId(null);
            subGroup.setParentId(group.getId());
            subGroup.setName("默认分组");
            subGroup.setType(group.getType());
            subGroup.setSystemGroup(false);
            subGroup.setDefaultGroup(true);
            subGroup.setKnowledgeId(knowledgeId);
            knowledgeGroupMapper.insert(subGroup);
        }

        if (group.getParentId() == null && group.getType().equals(team.name())) {
            KnowledgeTeamMember knowledgeTeamMember = new KnowledgeTeamMember();
            knowledgeTeamMember.setKnowledgeId(knowledgeId);
            knowledgeTeamMember.setUserId(StpClientUserUtil.getLoginIdAsLong());
            knowledgeTeamMember.setRole("owner");
            knowledgeTeamMemberMapper.insert(knowledgeTeamMember);
        }

        return group.getId();
    }

    private void convertStartTimeAndEndTime(KnowledgeGroupDTO dto) {
        if (StrUtil.isBlank(dto.getExpireOption())) {
            return;
        }
        switch (dto.getExpireOption()) {
            case "forever":
                dto.setStartTime(null);
                dto.setEndTime(null);
                dto.setStartTimeStr(null);
                dto.setEndTimeStr(null);
                break;
            case "custom":
                dto.setStartTime(LocalDate.parse(dto.getStartTimeStr()).atTime(0, 0, 0));
                dto.setEndTime(LocalDate.parse(dto.getEndTimeStr()).atTime(23, 59, 59));
                break;
            case "7":
                dto.setStartTimeStr(LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE));
                dto.setEndTimeStr(LocalDate.now().plusDays(7).format(DateTimeFormatter.ISO_LOCAL_DATE));
                dto.setStartTime(LocalDate.parse(dto.getStartTimeStr()).atTime(0, 0, 0));
                dto.setEndTime(LocalDate.parse(dto.getEndTimeStr()).atTime(23, 59, 59));
                break;
            case "30":
                dto.setStartTimeStr(LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE));
                dto.setEndTimeStr(LocalDate.now().plusDays(30).format(DateTimeFormatter.ISO_LOCAL_DATE));
                dto.setStartTime(LocalDate.parse(dto.getStartTimeStr()).atTime(0, 0, 0));
                dto.setEndTime(LocalDate.parse(dto.getEndTimeStr()).atTime(23, 59, 59));
                break;
        }
    }

    @SneakyThrows
    public void editKnowledgeGroup(KnowledgeGroupDTO dto) {
        KnowledgeGroup group = knowledgeGroupMapper.selectById(dto.getId());
        BeanUtils.copyProperties(dto, group, "id", "systemGroup", "defaultGroup", "knowledgeId");
        knowledgeGroupMapper.updateById(group);

        if (group.getType().equals(team.name()) && group.getParentId() == null) {
            convertStartTimeAndEndTime(dto);
            dto.setStartTime(null);
            dto.setEndTime(null);
            dto.setKnowledgeId(group.getKnowledgeId());

            knowledgeService.updateKnowledgeAfterGroupUpdate(dto);
        }
    }

    public void deleteKnowledgeGroup(Long id) {
        KnowledgeGroup group = knowledgeGroupMapper.selectById(id);
        if (group == null) {
            throw new ValidateException("知识库分组不存在");
        }

        if (group.isSystemGroup()) {
            throw new RuntimeException("系统内置组不允许删除");
        }

        List<KnowledgeGroup> subGroups = knowledgeGroupMapper.selectList(
                new LambdaQueryWrapper<KnowledgeGroup>()
                        .eq(KnowledgeGroup::getParentId, group.getId())
                        .eq(KnowledgeGroup::getDeleted, false)
        );
        if (CollUtil.isNotEmpty(subGroups)) {
            for (KnowledgeGroup subGroup : subGroups) {
                deleteKnowledgeGroup(subGroup.getId());
            }
        }

        KnowledgeDocQueryDTO queryDTO = new KnowledgeDocQueryDTO();
        queryDTO.setKnowledgeId(group.getKnowledgeId());
        queryDTO.setGroupId(group.getId());
        List<Long> knowledgeDocIds = knowledgeService.getKnowledgeDocs(queryDTO)
                .stream().map(KnowledgeDocDTO::getId).toList();

        KnowledgeDocBatchDTO dto = new KnowledgeDocBatchDTO();
        dto.setKnowledgeDocIds(knowledgeDocIds);
        knowledgeService.batchDeleteDoc(dto);
        knowledgeGroupMapper.deleteById(id);
    }

    public List<KnowledgeGroupTreeDTO> queryKnowledgeGroup(String type) {
        initKnowledgeGroup();

        LambdaQueryWrapper<KnowledgeGroup> query = new LambdaQueryWrapper<>();
        query.eq(KnowledgeGroup::getCreateBy, StpClientUserUtil.getLoginIdAsLong());
        query.eq(KnowledgeGroup::getType, type);
        query.orderByAsc(KnowledgeGroup::getCreateTime);
        List<KnowledgeGroupTreeDTO> groups = knowledgeGroupMapper.selectList(query).stream().map(group -> {
            KnowledgeGroupTreeDTO dto = new KnowledgeGroupTreeDTO();
            BeanUtils.copyProperties(group, dto);
            return dto;
        }).toList();
        return (List<KnowledgeGroupTreeDTO>) TreeBuilder.buildListToTree(groups);
    }


    public KnowledgeGroupTreeDTO detailTree(Long id) {
        initKnowledgeGroup();
        LambdaQueryWrapper<KnowledgeGroup> query = new LambdaQueryWrapper<>();
        query.eq(KnowledgeGroup::getCreateBy, StpClientUserUtil.getLoginIdAsLong());
        query.orderByAsc(KnowledgeGroup::getCreateTime);
        List<KnowledgeGroupTreeDTO> groups = knowledgeGroupMapper.selectList(query).stream().map(group -> {
            KnowledgeGroupTreeDTO dto = new KnowledgeGroupTreeDTO();
            BeanUtils.copyProperties(group, dto);
            return dto;
        }).toList();
        for (KnowledgeGroupTreeDTO result : groups) {
            if (result.getId().longValue() == id.longValue()) {
                return getGroupTree(result, groups);
            }
        }
        return new KnowledgeGroupTreeDTO();
    }

    private KnowledgeGroupTreeDTO getGroupTree(KnowledgeGroupTreeDTO result, List<KnowledgeGroupTreeDTO> list) {
        for (KnowledgeGroupTreeDTO knowledgeGroupTreeDTO : list) {
            if (knowledgeGroupTreeDTO.getParentId() == null) {
                continue;
            }
            if (knowledgeGroupTreeDTO.getParentId().longValue() == result.getId().longValue()) {
                if (result.getChildren() == null) {
                    result.setChildren(new ArrayList<>());
                }
                result.getChildren().add(getGroupTree(knowledgeGroupTreeDTO, list));
            }
        }
        return result;
    }


    public Map<String, Long> initKnowledgeGroup() {
        Map<String, Long> result = new HashMap<>();
        KnowledgeGroup rootGroup = findOrCreateGroup(null, "个人知识库", personal.name(), null, false);
        KnowledgeGroup defaultGroup = findOrCreateGroup(rootGroup.getId(), "默认分组", personal.name(), rootGroup.getKnowledgeId(), true);
        result.put("knowledgeId", rootGroup.getKnowledgeId());
        result.put("defaultGroupId", defaultGroup.getId());
        return result;
    }

    @SneakyThrows
    private KnowledgeGroup findOrCreateGroup(Long parentId, String name, String type, Long knowledgeId, boolean isDefault) {
        LambdaQueryWrapper<KnowledgeGroup> query = new LambdaQueryWrapper<KnowledgeGroup>()
                .eq(parentId != null, KnowledgeGroup::getParentId, parentId)
                .isNull(parentId == null, KnowledgeGroup::getParentId)
                .eq(KnowledgeGroup::getType, type)
                .eq(KnowledgeGroup::getCreateBy, StpClientUserUtil.getLoginIdAsLong())
                .eq(KnowledgeGroup::isSystemGroup, true)
                .eq(KnowledgeGroup::getDeleted, false)
                .last("limit 1");
        KnowledgeGroup group = knowledgeGroupMapper.selectOne(query);
        if (group == null) {
            group = new KnowledgeGroup();
            group.setParentId(parentId);
            group.setName(name);
            group.setType(type);
            group.setSystemGroup(true);
            group.setDefaultGroup(isDefault);
            if (parentId == null && knowledgeId == null) {
                //只有根目录才初始化知识库
                knowledgeId = knowledgeService.initKnowledge(name, name, DEFAULT_KNOWLEDGE_LOGO, type,
                        null, null, null, StpClientUserUtil.getLoginIdAsString());
            }
            group.setKnowledgeId(knowledgeId);
            knowledgeGroupMapper.insert(group);
        }
        return group;
    }

    @SneakyThrows
    public Map<String, Object> getKnowledgeGroupDetail(Long id) {
        KnowledgeGroup group = knowledgeGroupMapper.selectById(id);
        SysUser sysUser = sysUserMapper.selectById(group.getCreateBy());

        Map<String, Object> result = knowledgeService.getKnowledgeDetailByGroup(group.getKnowledgeId());
        if (sysUser != null) {
            result.put("createBy", sysUser.getName());
            result.put("creatorId", sysUser.getId());
        }
        result.put("type", group.getType());
        result.put("groupName", group.getName());
        return result;
    }

    public KnowledgeGroup getKnowledgeGroup(Long id) {
        return knowledgeGroupMapper.selectById(id);
    }

    public List<KnowledgeGroup> getChildrenByParentId(Long id) {
        return knowledgeGroupMapper.selectList(
                new LambdaQueryWrapper<KnowledgeGroup>()
                        .eq(KnowledgeGroup::getParentId, id)
        );
    }

    public List<Long> getGroupIdWithChildrenIterative(Long groupId) {
        List<Long> result = new ArrayList<>();
        Set<Long> visited = new HashSet<>();
        Queue<Long> queue = new LinkedList<>();

        queue.offer(groupId);

        while (!queue.isEmpty()) {
            Long currentId = queue.poll();

            if (currentId == null || visited.contains(currentId)) {
                continue;
            }

            // 添加当前节点
            result.add(currentId);
            visited.add(currentId);

            // 查询子节点并加入队列
            List<KnowledgeGroup> children = getChildrenByParentId(currentId);
            queue.addAll(children.stream().map(KnowledgeGroup::getId).toList());
        }

        return result;
    }
}
