package com.fytec.service.knowledge;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fytec.config.ClientProperties;
import com.fytec.config.milvus.MilvusClient;
import com.fytec.constant.Constants;
import com.fytec.dto.knowledge.KnowledgeDocAutoDTO;
import com.fytec.dto.knowledge.KnowledgeDocCallBackDTO;
import com.fytec.dto.knowledge.KnowledgeDocSegmentDTO;
import com.fytec.dto.knowledge.KnowledgeFileDTO;
import com.fytec.dto.llm.EmbeddedCallDTO;
import com.fytec.dto.llm.VectorRecordDTO;
import com.fytec.dto.parse.FileParseDTO;
import com.fytec.embedded.DynamicEmbeddedCallService;
import com.fytec.entity.knowledge.Knowledge;
import com.fytec.entity.knowledge.KnowledgeDoc;
import com.fytec.entity.knowledge.KnowledgeSegment;
import com.fytec.entity.llm.EmbeddedModel;
import com.fytec.entity.resource.AiResource;
import com.fytec.file.FileParseUtils;
import com.fytec.mapper.knowledge.KnowledgeDocMapper;
import com.fytec.mapper.knowledge.KnowledgeMapper;
import com.fytec.mapper.knowledge.KnowledgeSegmentMapper;
import com.fytec.mapper.model.EmbeddedModelMapper;
import com.fytec.mapper.resource.ResourceMapper;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static com.fytec.constant.Constants.KNOWLEDGE_DOC_STATUS.go_to_complete;
import static com.fytec.constant.Constants.KNOWLEDGE_DOC_STATUS.go_to_embedded;

@Service
@Slf4j
@RequiredArgsConstructor
public class KnowledgeWithoutTranService {
    private final KnowledgeMapper knowledgeMapper;

    private final KnowledgeDocMapper knowledgeDocMapper;

    private final KnowledgeSegmentMapper knowledgeSegmentMapper;

    private final ResourceMapper resourceMapper;

    private final MilvusClient milvusClient;

    private final EmbeddedModelMapper embeddedModelMapper;

    private final ClientProperties clientProperties;

    private final KnowledgeService knowledgeService;

    private final DynamicEmbeddedCallService dynamicEmbeddedCallService;


    @Autowired
    @Lazy // 延迟注入避免循环依赖
    private KnowledgeWithoutTranService self;


    private String getCollectionName(String code, Long id) {
        return code + "_" + id;
    }

    @Async
    public void autoProcessDocV2(KnowledgeFileDTO file, KnowledgeDocAutoDTO dto) {
        KnowledgeDoc doc = file.getDoc();
        if (clientProperties.getDoc().isEnabledAdvance()
                && StrUtil.equals("advance", dto.getParseStrategy())) {
            // 高级模式, 需要提交任务到解析流程, 解析流程会回掉处理
            // 文件类型pdf, doc, docx, ppt, pptx
            if (StrUtil.equalsAny(file.getFileType(), "pdf", "doc", "docx", "ppt", "pptx")) {
                updateDocInfoBeforeCallBack(doc, dto);
                FileParseDTO fileParseDTO = knowledgeService.getFileParseDTO(file, doc);
                FileParseUtils.fileParsePerPageAsyncTask(fileParseDTO);
                CompletableFuture.completedFuture(doc.getId());
                return;
            }
        }
        knowledgeService.autoProcessDoc(file, dto);
    }

    private void updateDocInfoBeforeCallBack(KnowledgeDoc doc, KnowledgeDocAutoDTO dto) {
        doc.setDefaultSegment(dto.isDefaultSegment());
        doc.setAutoSegment(dto.isAutoSegment());
        if (dto.isDocEnhancement()) {
            if (StrUtil.equals(dto.getDocEnhancementStrategy(), "title")) {
                // 设置文件标题为概要
                String[] split = doc.getFileName().split("\\.");
                // 去掉最后的文件后缀，保留前面的文本
                String title = split[0];
                doc.setOverview(title);
            } else {
                doc.setOverview(dto.getOverview());
            }
        } else {
            doc.setOverview(null);
        }
        if (dto.isPreview()) {
            doc.setStatus(go_to_complete.name());
        } else {
            doc.setStatus(go_to_embedded.name());
        }
        doc.setUpdateBy(dto.getUserId());
        knowledgeDocMapper.updateById(doc);
    }

    @Transactional
    public void processDocCallBack(KnowledgeDocCallBackDTO dto) {
        log.info("执行解析任务回调");
        KnowledgeDoc doc = knowledgeDocMapper.selectById(dto.getKnowledgeDocId());
        try {
            Map<Integer, String> contentPerPage = parseContent(dto.getKnowledgeDocContent());
            StringBuilder sb = new StringBuilder();
            for (Map.Entry<Integer, String> entry : contentPerPage.entrySet()) {
                sb.append(entry.getValue());
            }
            String fileContent = sb.toString();

            doc.setFileContent(fileContent);
            doc.setFileContentMap(contentPerPage);

            //文档分段
            Constants.KNOWLEDGE_DOC_TYPE docType = Constants.KNOWLEDGE_DOC_TYPE.valueOf(doc.getDocType());
            List<KnowledgeDocSegmentDTO> segments = switch (docType) {
                case Constants.KNOWLEDGE_DOC_TYPE.text, Constants.KNOWLEDGE_DOC_TYPE.image ->
                        knowledgeService.textSegmentationPerPage(doc);
                case Constants.KNOWLEDGE_DOC_TYPE.table -> knowledgeService.tableSegmentation(doc);
            };

            //调用向量服务
            if (StrUtil.equals(doc.getStatus(), Constants.KNOWLEDGE_DOC_STATUS.go_to_complete.name())) {
                doc.setStatus(Constants.KNOWLEDGE_DOC_STATUS.complete.name());
                knowledgeDocMapper.updateById(doc);

                //高级模式先解析完成，不生成向量
                List<KnowledgeSegment> knowledgeSegments = new ArrayList<>();
                int segmentId = 0;
                for (KnowledgeDocSegmentDTO knowledgeDocSegmentDTO : segments) {
                    KnowledgeSegment segment = new KnowledgeSegment();
                    segment.setDocId(doc.getId());
                    segment.setKnowledgeId(doc.getKnowledgeId());
                    segment.setPageNumber(knowledgeDocSegmentDTO.getPageNumber());
                    segment.setText(knowledgeDocSegmentDTO.getContent());
                    segment.setCreateBy(doc.getUpdateBy());
                    segment.setUpdateBy(doc.getUpdateBy());
                    segment.setVectorIndex(segmentId * 10);
                    segment.setOverview(knowledgeDocSegmentDTO.getOverview());
                    knowledgeSegments.add(segment);
                    segmentId++;
                }
                knowledgeSegmentMapper.insert(knowledgeSegments);
            } else {
                doc.setStatus(Constants.KNOWLEDGE_DOC_STATUS.embedded.name());
                knowledgeDocMapper.updateById(doc);

                Knowledge knowledge = knowledgeMapper.selectById(doc.getKnowledgeId());

                AiResource resource = resourceMapper.selectById(knowledge.getResourceId());
                if (resource == null) {
                    throw new RuntimeException("资源不存在");
                }
                EmbeddedModel embeddedModel = embeddedModelMapper.selectById(knowledge.getEmbeddedId());
                if (embeddedModel == null) {
                    throw new RuntimeException("向量模型不存在");
                }

                String collectionName = getCollectionName(resource.getCode(), resource.getId());
                knowledgeService.convertSegmentsToVectors(segments, collectionName, embeddedModel, doc);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            doc.setStatus(Constants.KNOWLEDGE_DOC_STATUS.failed.name());
            doc.setReason("解析失败");
            knowledgeDocMapper.updateById(doc);
        }
    }

    private Map<Integer, String> parseContent(String contentJson) {
        Map<Integer, String> contentPerPage = new LinkedHashMap<>();
        if (StrUtil.isNotBlank(contentJson)) {
            JSONArray jsonArray = JSON.parseArray(contentJson);
            int page = 1;
            StringBuilder sb = new StringBuilder();
            for (Object o : jsonArray) {
                JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(o));
                String type = jsonObject.getString("type");
                StringBuilder content = new StringBuilder();
                if (type.equals("image")) {
                    content = new StringBuilder();
                    content.append("<img src=\"").append(jsonObject.getString("img_path")).append("\">");
                } else if (type.equals("table")) {
                    JSONArray tableCaptions = jsonObject.getJSONArray("table_caption");
                    String tableBody = jsonObject.getString("table_body");
                    JSONArray tableFootnotes = jsonObject.getJSONArray("table_footnote");
                    if (!tableCaptions.isEmpty()) {
                        for (Object tableCaption : tableCaptions) {
                            content.append(JSON.toJSONString(tableCaption));
                        }
                    }
                    content.append(tableBody);
                    if (!tableFootnotes.isEmpty()) {
                        for (Object tableFootnote : tableFootnotes) {
                            content.append(JSON.toJSONString(tableFootnote));
                        }
                    }
                } else {
                    content = new StringBuilder(jsonObject.getString("text"));
                }

                int currentPage = jsonObject.getIntValue("page_idx") + 1;
                if (currentPage == page) {
                    sb.append(content).append("\n");
                } else {
                    contentPerPage.put(page, sb.toString());
                    sb = new StringBuilder();
                    page = currentPage;
                    sb.append(content).append("\n");
                }
            }
            contentPerPage.put(page, sb.toString());
        }
        return contentPerPage;
    }


    @Async
    @SneakyThrows
    public void advancedVectorData(Long docId, String userId) {
        //此处暂时还是用轮询
        boolean isComplete = waitUntilDocComplete(docId);
        if (!isComplete) {
            //30分钟内还未完成分段
            KnowledgeDoc doc = knowledgeDocMapper.selectById(docId);
            if(doc!=null){
                doc.setStatus(Constants.KNOWLEDGE_DOC_STATUS.failed.name());
                doc.setReason("解析文档分段超时");
                doc.setUpdateBy(userId);
                knowledgeDocMapper.updateById(doc);
            }
            return;
        }
        self.processAdvancedVectorDataInTransaction(docId);
    }


    private boolean waitUntilDocComplete(Long docId) throws InterruptedException {
        for (int i = 0; i < 180; i++) {
            KnowledgeDoc doc = knowledgeDocMapper.selectById(docId);
            log.info("向量化文档分段:{}", doc);
            if (doc == null)
                return false;
            if (StrUtil.equals(doc.getStatus(), Constants.KNOWLEDGE_DOC_STATUS.complete.name())) {
                return true;
            }
            Thread.sleep(10000);
        }
        return false;
    }

    @Transactional
    public void processAdvancedVectorDataInTransaction(Long docId) {
        KnowledgeDoc doc = knowledgeDocMapper.selectById(docId);

        Knowledge knowledge = knowledgeMapper.selectById(doc.getKnowledgeId());
        if (knowledge == null) {
            throw new RuntimeException("知识库不存在");
        }
        AiResource resource = resourceMapper.selectById(knowledge.getResourceId());
        if (resource == null) {
            throw new RuntimeException("资源不存在");
        }
        EmbeddedModel embeddedModel = embeddedModelMapper.selectById(knowledge.getEmbeddedId());
        if (embeddedModel == null) {
            throw new RuntimeException("向量模型不存在");
        }
        //文档分段
        List<KnowledgeSegment> segments = knowledgeSegmentMapper.selectList(
                new LambdaQueryWrapper<KnowledgeSegment>()
                        .eq(KnowledgeSegment::getKnowledgeId, knowledge.getId())
                        .eq(KnowledgeSegment::getDocId, doc.getId())
        );

        // 文档向量化，设为可见
        doc.setStatus(Constants.KNOWLEDGE_DOC_STATUS.embedded.name());
        knowledgeDocMapper.updateById(doc);

        //调用向量服务
        advanceSegmentsToVectors(segments,
                getCollectionName(resource.getCode(), resource.getId()),
                embeddedModel,
                doc);
    }

    private void advanceSegmentsToVectors(List<KnowledgeSegment> segments, String collectionName, EmbeddedModel model, KnowledgeDoc doc) {
        if (!milvusClient.isExitCollection(collectionName)) {
            milvusClient.createCollection(collectionName, model.getDimension());
        }
        if (CollUtil.isEmpty(segments)) {
            doc.setStatus(Constants.KNOWLEDGE_DOC_STATUS.failed.name());
            doc.setReason("分段内容为空");
            knowledgeDocMapper.updateById(doc);
            return;
        }

        List<String> contentSegments = new ArrayList<>();
        for (KnowledgeSegment segment : segments) {
            contentSegments.add(segment.getText());
        }

        EmbeddedCallDTO dto = new EmbeddedCallDTO();
        dto.setModelType(model.getCode());
        dto.setSegments(contentSegments);
        dto.setUrl(model.getUrl());
        dto.setMethodName(model.getMethodName());
        List<List<Float>> contentVectors = dynamicEmbeddedCallService.callEmbedded(dto);


        // 向量化可能的overview字段。包括：概要、标签、文件名字
        // 需要去重一下向量化
        Map<String, Integer> overviewSegmentsIndexMap = new LinkedHashMap<>();
        List<String> overviewSegments = new ArrayList<>();
        int index = 0;
        for (KnowledgeSegment segment : segments) {
            if (StringUtils.isNotBlank(segment.getOverview())) {
                if (!overviewSegmentsIndexMap.containsKey(segment.getOverview())) {
                    overviewSegmentsIndexMap.put(segment.getOverview(), index);
                    overviewSegments.add(segment.getOverview());
                    index++;
                }
            }
        }
        List<List<Float>> segmentVectors = null;
        if (CollUtil.isNotEmpty(overviewSegments)) {
            EmbeddedCallDTO dtoOverview = new EmbeddedCallDTO();
            dtoOverview.setModelType(model.getCode());
            dtoOverview.setSegments(overviewSegments);
            dtoOverview.setUrl(model.getUrl());
            dtoOverview.setMethodName(model.getMethodName());
            segmentVectors = dynamicEmbeddedCallService.callEmbedded(dtoOverview);
        }

        List<VectorRecordDTO> vectorRecordDTOS = new ArrayList<>();
        for (int i = 0; i < segments.size(); i++) {
            VectorRecordDTO vectorRecordDTO = new VectorRecordDTO();
            List<Float> vectors = contentVectors.get(i);
            vectorRecordDTO.setVector(vectors);
            vectorRecordDTO.setVectorExt(vectors);
            String overview = segments.get(i).getOverview();
            if (segmentVectors != null && StringUtils.isNotBlank(overview)) {
                if (overviewSegmentsIndexMap.containsKey(overview)) {
                    int overviewIndex = overviewSegmentsIndexMap.get(overview);
                    List<Float> overviewVector = segmentVectors.get(overviewIndex);
                    vectorRecordDTO.setVectorExt(overviewVector);
                }
            }
            vectorRecordDTO.setText(segments.get(i).getText());
            vectorRecordDTO.setUrl(doc.getFileUrl());
            vectorRecordDTO.setDocId(doc.getId());
            vectorRecordDTO.setGroupId(doc.getGroupId());
            vectorRecordDTO.setDoc(doc.getFileName());
            vectorRecordDTO.setCreateBy(doc.getUpdateBy());
            vectorRecordDTO.setPageNumber(segments.get(i).getPageNumber());
            vectorRecordDTO.setSegId(segments.get(i).getId());

            vectorRecordDTOS.add(vectorRecordDTO);
        }
        List<Object> vectorIds = milvusClient.insert(vectorRecordDTOS, collectionName);

        for (int i = 0; i < segments.size(); i++) {
            KnowledgeSegment knowledgeSegment = segments.get(i);
            knowledgeSegment.setVectorId(vectorIds.get(i).toString());
            knowledgeSegmentMapper.updateById(knowledgeSegment);
        }
        doc.setStatus(Constants.KNOWLEDGE_DOC_STATUS.success.name());
        knowledgeDocMapper.updateById(doc);
    }
}
