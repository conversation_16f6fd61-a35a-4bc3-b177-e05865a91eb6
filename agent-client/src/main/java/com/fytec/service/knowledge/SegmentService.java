package com.fytec.service.knowledge;

import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fytec.config.milvus.MilvusClient;
import com.fytec.constant.Constants;
import com.fytec.dto.knowledge.AddKnowledgeSegmentDTO;
import com.fytec.dto.knowledge.SegmentOverviewAutoDTO;
import com.fytec.dto.knowledge.SegmentQueryDTO;
import com.fytec.dto.knowledge.UpdateKnowledgeSegmentDTO;
import com.fytec.dto.llm.*;
import com.fytec.embedded.DynamicEmbeddedCallService;
import com.fytec.entity.agent.AgentConstant;
import com.fytec.entity.knowledge.Knowledge;
import com.fytec.entity.knowledge.KnowledgeDoc;
import com.fytec.entity.knowledge.KnowledgeSegment;
import com.fytec.entity.llm.AiModel;
import com.fytec.entity.llm.EmbeddedModel;
import com.fytec.entity.resource.AiResource;
import com.fytec.mapper.agent.AgentConstantMapper;
import com.fytec.mapper.knowledge.KnowledgeDocMapper;
import com.fytec.mapper.knowledge.KnowledgeMapper;
import com.fytec.mapper.knowledge.KnowledgeSegmentMapper;
import com.fytec.mapper.model.EmbeddedModelMapper;
import com.fytec.mapper.model.ModelMapper;
import com.fytec.mapper.resource.ResourceMapper;
import com.fytec.model.DynamicModelCallService;
import com.fytec.satoken.StpClientUserUtil;
import com.fytec.service.model.ModelService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@Transactional
@AllArgsConstructor
public class SegmentService {

    private final KnowledgeSegmentMapper knowledgeSegmentMapper;

    private final MilvusClient milvusClient;

    private final KnowledgeMapper knowledgeMapper;

    private final KnowledgeDocMapper knowledgeDocMapper;


    private final ResourceMapper resourceMapper;

    private final EmbeddedModelMapper embeddedModelMapper;

    private final DynamicEmbeddedCallService dynamicEmbeddedCallService;

    private final ModelMapper modelMapper;
    private final DynamicModelCallService modelCallService;
    private final AgentConstantMapper constantMapper;


    public List<KnowledgeSegment> list(SegmentQueryDTO dto) {
        LambdaQueryWrapper<KnowledgeSegment> qw = new LambdaQueryWrapper<>();
        if (dto.getDocId() != null) {
            qw.eq(KnowledgeSegment::getDocId, dto.getDocId());
        }
        return knowledgeSegmentMapper.selectList(qw);
    }

    public KnowledgeSegment add(AddKnowledgeSegmentDTO dto) {
        KnowledgeSegment segment = new KnowledgeSegment();
        BeanUtils.copyProperties(dto, segment);
        segment.setType(Constants.KNOWLEDGE_DOC_SOURCE_TYPE.customize.name());
        //向量库新增
        Knowledge knowledge = knowledgeMapper.selectById(dto.getKnowledgeId());
        if (knowledge == null) {
            throw new RuntimeException("知识库不存在");
        }
        AiResource resource = resourceMapper.selectById(knowledge.getResourceId());
        if (resource == null) {
            throw new RuntimeException("资源不存在");
        }
        EmbeddedModel embeddedModel = embeddedModelMapper.selectById(knowledge.getEmbeddedId());
        if (embeddedModel == null) {
            throw new RuntimeException("向量模型不存在");
        }
        // 向量化文本内容
        EmbeddedCallDTO embeddedCallDTO = new EmbeddedCallDTO();
        embeddedCallDTO.setModelType(embeddedModel.getCode());
//        embeddedCallDTO.setSegments(Collections.list(dto.getText(), dto.getTag(), dto.getOverview()));
        LinkedList<String> segs = new LinkedList<>();
        segs.add(dto.getText());
//        if (StringUtils.isNotBlank(dto.getTag())) {
//            segs.add(dto.getTag());
//        }
        if (StringUtils.isNotBlank(dto.getOverview())) {
            segs.add(dto.getOverview());
        }
        embeddedCallDTO.setSegments(segs);
        embeddedCallDTO.setUrl(embeddedModel.getUrl());
        embeddedCallDTO.setMethodName(embeddedModel.getMethodName());
        List<List<Float>> contentVectors = dynamicEmbeddedCallService.callEmbedded(embeddedCallDTO);
        if (contentVectors.isEmpty()) {
            throw new ValidateException("向量模型调用失败");
        }
        //如果标签还存在，需要向量化标签和概述


        List<VectorRecordDTO> vectorRecordDTOS = new ArrayList<>();
        VectorRecordDTO vectorRecordDTO = new VectorRecordDTO();
        int segindex = 0;
        List<Float> vectors = contentVectors.get(segindex);
        // 这个是文本的
        vectorRecordDTO.setVector(vectors);
        vectorRecordDTO.setVectorExt(vectors);
        // 还有可能的标签以及概述的,按照顺序
//        if (StringUtils.isNotBlank(dto.getTag())) {
//            segindex++;
//            vectorRecordDTO.setVectorTag(contentVectors.get(segindex));
//        }
        if (StringUtils.isNotBlank(dto.getOverview())) {
            segindex++;
            vectorRecordDTO.setVectorExt(contentVectors.get(segindex));
        }

        vectorRecordDTO.setText(dto.getText());
        vectorRecordDTO.setDocId(dto.getDocId());
        if (StringUtils.isNotBlank(dto.getOverview())) {
            // 考虑片段的标题
            vectorRecordDTO.setDoc(shortSegmentDocName(dto.getOverview()));

        } else {
            // 如果为空，可以考虑拿分组的名字？
            // 可以支持文件的分组改名吗？
            // 拿内容的前几个
            vectorRecordDTO.setDoc(shortSegmentDocName(dto.getText()));
        }
//        vectorRecordDTO.setTag(StringUtils.isBlank(dto.getTag()) ? null : dto.getTag());
//        vectorRecordDTO.setOverview(StringUtils.isBlank(dto.getOverview()) ? null : dto.getOverview());

        vectorRecordDTO.setCreateBy(StpClientUserUtil.getLoginIdAsString());

//        segment.setVectorId(vectorIds.getFirst().toString());
        knowledgeSegmentMapper.insert(segment);

        vectorRecordDTO.setSegId(segment.getId());
        vectorRecordDTOS.add(vectorRecordDTO);

        List<Object> vectorIds = milvusClient.insert(vectorRecordDTOS, getCollectionName(resource.getCode(), resource.getId()));
        if (vectorIds.size() != 1) {
            throw new RuntimeException("向量库新增失败");
        }
        knowledgeSegmentMapper.updateVectorIdById(vectorIds.getFirst().toString(), segment.getId());


        return segment;
    }


    private String getCollectionName(String code, Long id) {
        return code + "_" + id;
    }

    public static String shortSegmentDocName(String name) {
        if (name.length() >= 10) {
            return name.substring(0, 10) + "...";
        }
        return name;
    }

    public KnowledgeSegment update(UpdateKnowledgeSegmentDTO dto) {
        KnowledgeSegment segment = knowledgeSegmentMapper.selectById(dto.getId());
        if (segment == null) {
            throw new RuntimeException("片段不存在");
        }
        // 先查询片段的来源类型，如果是customer开头的，说明是自定义的，就基于这个来源做处理。
        String TYPE = Constants.KNOWLEDGE_DOC_SOURCE_TYPE.customize.name();
        if (StringUtils.isBlank(segment.getType())) {
            segment.setType(TYPE);
        }
        if (segment.getType().startsWith(TYPE)) {
            TYPE = segment.getType();
        } else {
            // 忽略，就用默认的customer，与其他类别（有文件上传的）区分开
        }

        boolean changeRawText = false;
        if (StringUtils.isNotBlank(dto.getText()) && !dto.getText().equals(segment.getText())) {
            changeRawText = true;
        }
        segment.setText(dto.getText());
        // 如果片段是人工处理,需要考虑更新片段的标题
        // 如果最开始就是上传文件自动分段的，那么不做处理？不过如果不做处理，上传的片段已经和原始文件没关系了，还是需要修改片段类型的
        if (changeRawText) {
            segment.setType(TYPE);
        }
        // 默认用fileName的了
        if (StringUtils.isBlank(segment.getDoc()) && segment.getDocId() != null && segment.getDocId() > 0L) {
            // 如果为空，用正文作为名称，处理一下数据刚好
            KnowledgeDoc knowledgeDoc = knowledgeDocMapper.selectById(segment.getDocId());
            if (StringUtils.isBlank(knowledgeDoc.getFileUrl())) {
                // 说明这个不是自己上传的
                segment.setType(TYPE);
            } else {
                segment.setDoc(knowledgeDoc.getFileName());// 初始化一下
            }
        }
        if (segment.getType().startsWith(Constants.KNOWLEDGE_DOC_SOURCE_TYPE.customize.name())) {
            //取片段自己的标题
            if (StringUtils.isNotBlank(dto.getOverview())) {
                segment.setDoc(shortSegmentDocName(dto.getOverview()));
            } else {
                // 清掉了概述，就拿正文作为名称
                segment.setDoc(shortSegmentDocName(dto.getText()));
            }
        }
        segment.setOverview(dto.getOverview());

        Knowledge knowledge = knowledgeMapper.selectById(segment.getKnowledgeId());
        if (knowledge == null) {
            throw new RuntimeException("知识库不存在");
        }
        AiResource resource = resourceMapper.selectById(knowledge.getResourceId());
        if (resource == null) {
            throw new RuntimeException("资源不存在");
        }
        EmbeddedModel embeddedModel = embeddedModelMapper.selectById(knowledge.getEmbeddedId());
        if (embeddedModel == null) {
            throw new RuntimeException("向量模型不存在");
        }
        List<String> segments = new ArrayList<>();
        segments.add(dto.getText());
        if (StringUtils.isNotBlank(dto.getOverview())) {
            segments.add(dto.getOverview());
        }

        EmbeddedCallDTO embeddedCallDTO = new EmbeddedCallDTO();
        embeddedCallDTO.setModelType(embeddedModel.getCode());
        embeddedCallDTO.setSegments(segments);
        embeddedCallDTO.setUrl(embeddedModel.getUrl());
        embeddedCallDTO.setMethodName(embeddedModel.getMethodName());
        List<List<Float>> contentVectors = dynamicEmbeddedCallService.callEmbedded(embeddedCallDTO);
        if (!(contentVectors.size() == 1
                || (StringUtils.isNotBlank(dto.getOverview()) && contentVectors.size() == 2))) {
            throw new ValidateException("向量模型调用失败");
        }
        List<VectorRecordDTO> vectorRecordDTOS = new ArrayList<>();
        VectorRecordDTO vectorRecordDTO = new VectorRecordDTO();
        String vectorId = segment.getVectorId();
        if (StringUtils.isBlank(vectorId)) {
            throw new ValidateException("向量库向量id为空，无法更新");
        }
        vectorRecordDTO.setId(Long.valueOf(vectorId));
        List<Float> vectors = contentVectors.getFirst();
        vectorRecordDTO.setVector(vectors);
        vectorRecordDTO.setVectorExt(vectors);
        if (StringUtils.isNotBlank(dto.getOverview())) {
            vectorRecordDTO.setVectorExt(contentVectors.get(1));
        }
        vectorRecordDTO.setText(dto.getText());
        vectorRecordDTO.setDocId(segment.getDocId());
        vectorRecordDTO.setDoc(segment.getDoc());
        vectorRecordDTO.setCreateBy(StpClientUserUtil.getLoginIdAsString());
        vectorRecordDTO.setSegId(segment.getId());

        vectorRecordDTOS.add(vectorRecordDTO);
        List<Object> vectorIds = milvusClient.update(vectorRecordDTOS, getCollectionName(resource.getCode(), resource.getId()));
        if (vectorIds.size() != 1) {
            throw new RuntimeException("向量库新增失败");
        }
        segment.setVectorId(vectorIds.getFirst().toString());
        knowledgeSegmentMapper.updateById(segment);
        return segment;
    }

    public KnowledgeSegment detail(Long id) {
        return knowledgeSegmentMapper.selectById(id);
    }

    public void delete(Long id) {
        KnowledgeSegment segment = knowledgeSegmentMapper.selectById(id);
        if (segment == null) {
            throw new RuntimeException("片段不存在");
        }

        Knowledge knowledge = knowledgeMapper.selectById(segment.getKnowledgeId());
        if (knowledge == null) {
            throw new RuntimeException("知识库不存在");
        }
        AiResource resource = resourceMapper.selectById(knowledge.getResourceId());
        if (resource == null) {
            throw new RuntimeException("资源不存在");
        }

        milvusClient.delete(Collections.singletonList(segment.getVectorId()), getCollectionName(resource.getCode(), resource.getId()));
        knowledgeSegmentMapper.deleteById(id);
    }

    public void autoOverviewContent(HttpServletResponse response, SegmentOverviewAutoDTO dto) {
        AiModel model = modelMapper.selectOne(
                new LambdaQueryWrapper<AiModel>()
                        .eq(AiModel::isEnable, true)
                        .eq(AiModel::isDefaulted, true)
                        .last("limit 1")
        );
        if (model == null) {
            throw new ValidateException("模型不存在");
        }

        // 设置模型名称，历史记录中需要
        String url = model.getStreamUrl();

        ModelProcessDTO req = new ModelProcessDTO();
        req.setClientId(IdUtil.nanoId());
        req.setModelType(model.getCode());
        // 先通过常量，如果常量找不到再用这个提示词
        AgentConstant agentConstant = constantMapper.selectOne(new LambdaQueryWrapper<AgentConstant>()
                .eq(AgentConstant::getCode, "DEFAULT_AUTO_OVERVIEW_CONTENT")
                .last("limit 1"));
        if (agentConstant != null && StringUtils.isNotBlank(agentConstant.getValue())) {
            req.setSystemMessage(agentConstant.getValue());
        } else {
            req.setSystemMessage(Constants.DEFAULT_AUTO_OVERVIEW_CONTENT);
        }

        req.setUserMessage(dto.getContent());

        Map<String, Object> paramBody = JSON.parseObject(JSON.toJSONString(req), Map.class);

        List<ModelParamDTO> diversityParams = JSON.parseArray(model.getDiversityParams(), ModelParamDTO.class);
        for (ModelParamDTO diversityParam : diversityParams) {
            paramBody.put(diversityParam.getCode(), diversityParam.getValue());
        }
        List<ModelParamDTO> ioParams = JSON.parseArray(model.getIoParams(), ModelParamDTO.class);
        for (ModelParamDTO ioParam : ioParams) {
            paramBody.put(ioParam.getCode(), ioParam.getValue());
        }
        StringBuffer history = new StringBuffer();
        StringBuffer reasonHistory = new StringBuffer();
        StringBuffer referenceHistory = new StringBuffer();

        ModelCallDTO modelCallDTO = new ModelCallDTO();
        modelCallDTO.setResponse(response);
        modelCallDTO.setMethodName(model.getStreamMethod());
        modelCallDTO.setUrl(url);
        modelCallDTO.setParamBody(paramBody);
        modelCallDTO.setHistory(history);
        modelCallDTO.setReasonHistory(reasonHistory);
        modelCallDTO.setReferenceHistory(referenceHistory);
        modelCallService.callModelStream(modelCallDTO);
    }
}
