package com.fytec.service.knowledge;

import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ValidateException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fytec.constant.Constants;
import com.fytec.dto.knowledge.KnowledgeQueryDTO;
import com.fytec.dto.knowledge.group.KnowledgeGroupTreeDTO;
import com.fytec.dto.knowledge.team.SysWorkUserDTO;
import com.fytec.dto.knowledge.team.TeamKnowledgeMemberDTO;
import com.fytec.dto.knowledge.team.TeamKnowledgeMemberEditRoleDTO;
import com.fytec.dto.knowledge.team.TeamKnowledgeMemberListDTO;
import com.fytec.dto.wechat.WechatWorkUser;
import com.fytec.entity.knowledge.KnowledgeTeamMember;
import com.fytec.entity.system.SysUser;
import com.fytec.entity.system.SysUserRole;
import com.fytec.mapper.knowledge.KnowledgeGroupMapper;
import com.fytec.mapper.knowledge.KnowledgeTeamMemberMapper;
import com.fytec.mapper.system.SysUserMapper;
import com.fytec.mapper.system.SysUserRoleMapper;
import com.fytec.satoken.StpClientUserUtil;
import com.fytec.utils.TreeBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@Transactional
@RequiredArgsConstructor
public class KnowledgeTeamService {
    private final KnowledgeGroupMapper knowledgeGroupMapper;
    private final KnowledgeTeamMemberMapper knowledgeTeamMemberMapper;
    private final SysUserMapper sysUserMapper;
    private final SysUserRoleMapper sysUserRoleMapper;


    public List<KnowledgeGroupTreeDTO> queryKnowledgeTeamCreatedByMine(KnowledgeQueryDTO dto) {
        dto.setUserId(StpClientUserUtil.getLoginIdAsLong());
        dto.setKnowledgeType(Constants.KNOWLEDGE_TYPE.team.name());
        List<KnowledgeGroupTreeDTO> rootGroups = knowledgeGroupMapper.queryKnowledgeRootTeamCreatedByMine(dto);
        List<KnowledgeGroupTreeDTO> groups = knowledgeGroupMapper.queryKnowledgeTeamCreatedByMine(dto);
        List<KnowledgeGroupTreeDTO> result = (List<KnowledgeGroupTreeDTO>) TreeBuilder.buildListToTree(groups);
        return result.stream()
                .filter(itemB -> rootGroups.stream()
                        .anyMatch(itemA -> itemA.getId().longValue() == itemB.getId().longValue()))
                .collect(Collectors.toList());
    }


    public List<KnowledgeGroupTreeDTO> queryKnowledgeTeamJoined(KnowledgeQueryDTO dto) {
        dto.setUserId(StpClientUserUtil.getLoginIdAsLong());
        dto.setKnowledgeType(Constants.KNOWLEDGE_TYPE.team.name());
        List<KnowledgeGroupTreeDTO> groups = knowledgeGroupMapper.queryKnowledgeTeamJoined(dto);
        return (List<KnowledgeGroupTreeDTO>) TreeBuilder.buildListToTree(groups);
    }

    public void knowledgeTeamAddMember(TeamKnowledgeMemberDTO dto) {
        if (CollUtil.isEmpty(dto.getSysWorkUsers())
                && CollUtil.isEmpty(dto.getWechatWorkUsers())) {
            throw new ValidateException("用户ID不能为空");
        }

        if (CollUtil.isNotEmpty(dto.getSysWorkUsers())) {
            for (SysWorkUserDTO workUser : dto.getSysWorkUsers()) {
                KnowledgeTeamMember knowledgeTeamMember = knowledgeTeamMemberMapper.selectOne(
                        new LambdaQueryWrapper<KnowledgeTeamMember>()
                                .eq(KnowledgeTeamMember::getKnowledgeId, dto.getKnowledgeId())
                                .eq(KnowledgeTeamMember::getUserId, workUser.getUserId())
                                .last("LIMIT 1")
                );
                if (knowledgeTeamMember == null) {
                    knowledgeTeamMember = new KnowledgeTeamMember();
                    knowledgeTeamMember.setKnowledgeId(dto.getKnowledgeId());
                    knowledgeTeamMember.setUserId(workUser.getUserId());
                    knowledgeTeamMember.setRole(workUser.getRole());
                    knowledgeTeamMemberMapper.insert(knowledgeTeamMember);
                }
            }
        }

        if (CollUtil.isNotEmpty(dto.getWechatWorkUsers())) {
            for (WechatWorkUser wechatWorkUser : dto.getWechatWorkUsers()) {
                SysUser sysUser = sysUserMapper.selectOne(
                        new LambdaQueryWrapper<SysUser>()
                                .eq(SysUser::getThirdUserId, wechatWorkUser.getUserId())
                                .last("LIMIT 1")
                );
                if (sysUser == null) {
                    sysUser = new SysUser();
                    sysUser.setThirdUserId(wechatWorkUser.getUserId());
                    sysUser.setName(wechatWorkUser.getName());
                    sysUser.setStatus("A");
                    sysUser.setPassword(BCrypt.hashpw("3456@erty"));
                    sysUserMapper.insert(sysUser);

                    SysUserRole sysUserRole = new SysUserRole();
                    sysUserRole.setUserId(sysUser.getId());
                    sysUserRole.setRoleId(2L);//临时用户
                    sysUserRoleMapper.insert(sysUserRole);

                }

                KnowledgeTeamMember knowledgeTeamMember = knowledgeTeamMemberMapper.selectOne(
                        new LambdaQueryWrapper<KnowledgeTeamMember>()
                                .eq(KnowledgeTeamMember::getKnowledgeId, dto.getKnowledgeId())
                                .eq(KnowledgeTeamMember::getUserId, sysUser.getId())
                                .last("LIMIT 1")
                );
                if (knowledgeTeamMember == null) {
                    knowledgeTeamMember = new KnowledgeTeamMember();
                    knowledgeTeamMember.setKnowledgeId(dto.getKnowledgeId());
                    knowledgeTeamMember.setUserId(sysUser.getId());
                    knowledgeTeamMember.setRole(wechatWorkUser.getRole());
                    knowledgeTeamMemberMapper.insert(knowledgeTeamMember);
                }
            }
        }
    }

    public void knowledgeTeamMemberEditRole(TeamKnowledgeMemberEditRoleDTO dto) {
        KnowledgeTeamMember knowledgeTeamMember = knowledgeTeamMemberMapper.selectOne(
                new LambdaQueryWrapper<KnowledgeTeamMember>()
                        .eq(KnowledgeTeamMember::getKnowledgeId, dto.getKnowledgeId())
                        .eq(KnowledgeTeamMember::getUserId, dto.getUserId())
                        .last("LIMIT 1")
        );
        if (knowledgeTeamMember != null) {
            knowledgeTeamMember.setRole(dto.getRole());
            knowledgeTeamMemberMapper.updateById(knowledgeTeamMember);
        }
    }

    public void knowledgeTeamRemoveMember(TeamKnowledgeMemberDTO dto) {
        if (CollUtil.isEmpty(dto.getUserIds())) {
            throw new ValidateException("用户ID不能为空");
        }
        for (Long userId : dto.getUserIds()) {
            LambdaQueryWrapper<KnowledgeTeamMember> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(KnowledgeTeamMember::getKnowledgeId, dto.getKnowledgeId());
            queryWrapper.eq(KnowledgeTeamMember::getUserId, userId);
            knowledgeTeamMemberMapper.delete(queryWrapper);
        }

    }

    public List<TeamKnowledgeMemberListDTO> queryKnowledgeTeamMembers(Long knowledgeId) {
        return knowledgeTeamMemberMapper.queryKnowledgeTeamMembers(knowledgeId);
    }

    public void quitKnowledgeTeamMembers(Long knowledgeId) {
        LambdaQueryWrapper<KnowledgeTeamMember> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnowledgeTeamMember::getKnowledgeId, knowledgeId);
        queryWrapper.eq(KnowledgeTeamMember::getUserId, StpClientUserUtil.getLoginId());
        knowledgeTeamMemberMapper.delete(queryWrapper);
    }
}
