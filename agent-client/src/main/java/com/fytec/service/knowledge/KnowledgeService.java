package com.fytec.service.knowledge;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.config.ClientProperties;
import com.fytec.config.JodConverterProperties;
import com.fytec.config.MinioProperties;
import com.fytec.config.SelfProperties;
import com.fytec.config.milvus.MilvusClient;
import com.fytec.constant.Constants;
import com.fytec.doc.BaiduDocParseService;
import com.fytec.dto.knowledge.*;
import com.fytec.dto.knowledge.deprecated.AddKnowledgeDocDTO;
import com.fytec.dto.knowledge.deprecated.DocSegmentDTO;
import com.fytec.dto.knowledge.deprecated.SegmentConfigDTO;
import com.fytec.dto.knowledge.group.KnowledgeGroupDTO;
import com.fytec.dto.llm.*;
import com.fytec.dto.parse.FileParseDTO;
import com.fytec.embedded.DynamicEmbeddedCallService;
import com.fytec.entity.knowledge.*;
import com.fytec.entity.llm.AiModel;
import com.fytec.entity.llm.EmbeddedModel;
import com.fytec.entity.resource.AiResource;
import com.fytec.file.FileParseUtils;
import com.fytec.mapper.knowledge.*;
import com.fytec.mapper.model.EmbeddedModelMapper;
import com.fytec.mapper.model.ModelMapper;
import com.fytec.mapper.resource.ResourceMapper;
import com.fytec.model.DynamicModelCallService;
import com.fytec.satoken.StpClientUserUtil;
import com.fytec.service.system.SysUserService;
import com.fytec.splitter.RecursiveCharacterTextSplitter;
import com.fytec.util.MinioUtil;
import com.fytec.util.UUIDGenerator;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jodconverter.boot.autoconfigure.JodConverterRemoteProperties;
import org.jodconverter.core.document.DefaultDocumentFormatRegistry;
import org.jodconverter.core.office.OfficeManager;
import org.jodconverter.remote.RemoteConverter;
import org.jodconverter.remote.office.RemoteOfficeManager;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URI;
import java.net.URL;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.fytec.service.resource.ResourceService.generate8DigitCode;

@Service
@Slf4j
@Transactional
@RequiredArgsConstructor
public class KnowledgeService {

    private final ModelMapper modelMapper;

    private final KnowledgeMapper knowledgeMapper;

    private final KnowledgeGroupMapper knowledgeGroupMapper;

    private final KnowledgeDocMapper knowledgeDocMapper;

    private final KnowledgeSegmentMapper knowledgeSegmentMapper;

    private final ResourceMapper resourceMapper;

    private final MilvusClient milvusClient;

    private final EmbeddedModelMapper embeddedModelMapper;

    private final DynamicEmbeddedCallService dynamicEmbeddedCallService;

    private final BaiduDocParseService baiduDocParseService;

    private final DynamicModelCallService modelService;

    private final JodConverterRemoteProperties jodConverterRemoteProperties;

    private final ImageReplacerService imageReplacerService;

    private final JodConverterProperties jodConverterProperties;

    private final ClientProperties clientProperties;
    private final SelfProperties selfProperties;
    private final MinioProperties minioProperties;

    private final MinioUtil minioUtil;
    private final KnowledgeTeamMemberMapper knowledgeTeamMemberMapper;

    private final SysUserService sysUserService;


    @Deprecated
    public Long addDoc(AddKnowledgeDocDTO dto) {
        Knowledge knowledge = knowledgeMapper.selectById(dto.getKnowledgeId());
        if (knowledge == null) {
            throw new RuntimeException("知识库不存在");
        }
        KnowledgeDoc doc = new KnowledgeDoc();
        BeanUtils.copyProperties(dto, doc);
        doc.setVisible(true);
        if (Constants.KNOWLEDGE_DOC_SOURCE_TYPE.upload.name().equalsIgnoreCase(dto.getDocSourceType())) { //文件上传
            doc.setFileType(dto.getFileUrl().substring(dto.getFileUrl().lastIndexOf(".") + 1).toLowerCase());
            if ("pdf".equals(doc.getFileType())
                    || "docx".equals(doc.getFileType())
                    || "doc".equals(doc.getFileType())
                    || "txt".equals(doc.getFileType())
                    || "md".equals(doc.getFileType())
            ) {
                doc.setFileContent(FileParseUtils.fileParse(dto.getFileUrl(), doc.getFileType()));
                doc.setStatus(Constants.KNOWLEDGE_DOC_STATUS.complete.name());
                log.info("调用本地文档解析");
            } else {
                //调用文档解析服务
                String taskId = baiduDocParseService.submitTask(doc.getFileUrl(), doc.getFileName());
                if (StringUtils.isBlank(taskId)) {
                    throw new RuntimeException("文档解析失败");
                }
                doc.setStatus(Constants.KNOWLEDGE_DOC_STATUS.analysis.name());
                doc.setTaskId(taskId);
                log.info("调用百度文档解析服务");
            }
        } else if (Constants.KNOWLEDGE_DOC_SOURCE_TYPE.customize.name().equalsIgnoreCase(dto.getDocSourceType())
                || StringUtils.isNotBlank(dto.getDocSourceType()) && dto.getDocSourceType().toLowerCase().startsWith(Constants.KNOWLEDGE_DOC_SOURCE_TYPE.customize.name())
        ) {
            //自定义编辑
            doc.setStatus(Constants.KNOWLEDGE_DOC_STATUS.complete.name());
            doc.setFileContent(dto.getFileContent());
        }
        knowledgeDocMapper.insert(doc);
        return doc.getId();
    }


    @Deprecated
    public DocSegmentDTO previewDoc(Long docId) {
        DocSegmentDTO dto = new DocSegmentDTO();
        KnowledgeDoc doc = knowledgeDocMapper.selectById(docId);
        if (doc == null) {
            throw new RuntimeException("文档不存在");
        }

        if (Constants.KNOWLEDGE_DOC_STATUS.analysis.name().equals(doc.getStatus())) {
            DocProcessResultDTO task = baiduDocParseService.getTask(doc.getTaskId());
            doc.setStatus(Constants.KNOWLEDGE_DOC_STATUS.complete.name());
            doc.setFileContent("");
            task.getContents().forEach(content -> {
                doc.setFileContent(doc.getFileContent() + content);
            });
        }

        knowledgeDocMapper.updateById(doc);
        //文档分段
        List<KnowledgeDocSegmentDTO> segments = textSegmentationPerPage(doc);
        dto.setSegments(segments.stream().map(KnowledgeDocSegmentDTO::getContent).toList());
        dto.setStatus(doc.getStatus());
        dto.setTaskId(doc.getTaskId());
        dto.setContent(doc.getFileContent());
        return dto;

    }


    @Deprecated
    public void segmentConfig(SegmentConfigDTO dto) {
        KnowledgeDoc doc = knowledgeDocMapper.selectById(dto.getDocId());
        if (doc == null) {
            throw new RuntimeException("文档不存在");
        }
        BeanUtils.copyProperties(dto, doc);
        knowledgeDocMapper.updateById(doc);
        analysisDoc(doc);
    }

    @Deprecated
    private void analysisDoc(KnowledgeDoc doc) {
        if (Constants.KNOWLEDGE_DOC_STATUS.analysis.name().equals(doc.getStatus())) {
            boolean isAanalysis = true;
            while (isAanalysis) {
                DocProcessResultDTO task = baiduDocParseService.getTask(doc.getTaskId());
                if (task != null) {
                    doc.setStatus(Constants.KNOWLEDGE_DOC_STATUS.complete.name());
                    doc.setFileContent("");
                    task.getContents().forEach(content -> {
                        doc.setFileContent(doc.getFileContent() + content);
                    });
                    knowledgeDocMapper.updateById(doc);
                    isAanalysis = false;
                } else {
                    try {
                        Thread.sleep(2000);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        }
    }

    @Deprecated
    public void vectorData(Long docId) {
        KnowledgeDoc doc = knowledgeDocMapper.selectById(docId);
        if (doc == null) {
            throw new RuntimeException("文档不存在");
        }
        Knowledge knowledge = knowledgeMapper.selectById(doc.getKnowledgeId());
        if (knowledge == null) {
            throw new RuntimeException("知识库不存在");
        }
        AiResource resource = resourceMapper.selectById(knowledge.getResourceId());
        if (resource == null) {
            throw new RuntimeException("资源不存在");
        }
        EmbeddedModel embeddedModel = embeddedModelMapper.selectById(knowledge.getEmbeddedId());
        if (embeddedModel == null) {
            throw new RuntimeException("向量模型不存在");
        }
        //文档分段
        List<KnowledgeDocSegmentDTO> segments = textSegmentationPerPage(doc);

        //调用向量服务
        convertSegmentsToVectors(segments,
                getCollectionName(resource.getCode(), resource.getId()),
                embeddedModel,
                doc);
        doc.setStatus(Constants.KNOWLEDGE_DOC_STATUS.embedded.name());
        knowledgeDocMapper.updateById(doc);
    }

    @Deprecated
    public String searchDocStatus(Long docId) {
        KnowledgeDoc doc = knowledgeDocMapper.selectById(docId);
        if (doc == null) {
            throw new RuntimeException("文档不存在");
        }
        Long count = knowledgeSegmentMapper.selectCount(new LambdaQueryWrapper<KnowledgeSegment>()
                .eq(KnowledgeSegment::getDocId, docId));
        if (count > 0) {
            doc.setStatus(Constants.KNOWLEDGE_DOC_STATUS.success.name());
            knowledgeDocMapper.updateById(doc);
        }

        return doc.getStatus();
    }

    public KnowledgeDetailDTO getKnowledgeDetail(Long resourceId) {
        AiResource resource = resourceMapper.selectById(resourceId);
        if (resource == null) {
            throw new RuntimeException("知识库资源不存在");
        }
        Knowledge knowledge = knowledgeMapper.selectOne(new LambdaQueryWrapper<Knowledge>()
                .eq(Knowledge::getResourceId, resourceId)
                .last("limit 1"));
        if (knowledge == null) {
            throw new RuntimeException("知识库不存在");
        }
        KnowledgeDetailDTO dto = new KnowledgeDetailDTO();
        BeanUtils.copyProperties(knowledge, dto);
        dto.setName(resource.getName());
        dto.setDescription(resource.getDescription());
        dto.setLogo(resource.getLogo());
        dto.setKnowledgeType(knowledge.getType());
        dto.setKnowledgeBaseType(knowledge.getBaseType());

        List<KnowledgeDocCountDTO> docCounts = knowledgeDocMapper.selectDocCountByKnowledgeId(knowledge.getId());


        if (docCounts == null || docCounts.isEmpty()) {
            dto.setTotalSize(0);
            dto.setTotalDoc(0);
            dto.setTotalSegment(0);
        } else {
            Set<Long> docIds = new HashSet<>();
            int totalSize = 0;
            for (KnowledgeDocCountDTO docCount : docCounts) {
                if (!docIds.contains(docCount.getId())) {
                    totalSize += docCount.getFileSize();
                }
                docIds.add(docCount.getId());
            }
            dto.setTotalSize(totalSize);
            dto.setTotalDoc(docIds.size());
            dto.setTotalSegment(docCounts.size());
        }
        return dto;
    }

    public List<KnowledgeDocDTO> getKnowledgeDocs(KnowledgeDocQueryDTO dto) {
        List<KnowledgeDocDTO> dtos = new ArrayList<>();

        LambdaQueryWrapper<KnowledgeDoc> lambdaQueryWrapper = new LambdaQueryWrapper<KnowledgeDoc>()
                .eq(KnowledgeDoc::getKnowledgeId, dto.getKnowledgeId())
                .eq(StrUtil.isBlank(dto.getDocIds()), KnowledgeDoc::getVisible, true)
//                .eq(KnowledgeDoc::getCreateBy, dto.getCreateBy())
                .eq(KnowledgeDoc::getDeleted, false)
                .orderByDesc(KnowledgeDoc::getCreateTime);

        if (dto.getGroupId() != null) {
            lambdaQueryWrapper.eq(KnowledgeDoc::getGroupId, dto.getGroupId());
        }
        if (StrUtil.isNotBlank(dto.getKeywords())) {
            lambdaQueryWrapper.like(KnowledgeDoc::getFileName, dto.getKeywords());
        }
        if (dto.isSuccessFilter()) {
            lambdaQueryWrapper.eq(KnowledgeDoc::getStatus, Constants.KNOWLEDGE_DOC_STATUS.success.name());
        }
        // 如果filterContent不为空，先基于片段获取到可能到docId
        if (StringUtils.isNotBlank(dto.getFilterContent())) {
            List<Long> docIds = knowledgeSegmentMapper.selectDocIdsByContent(dto.getKnowledgeId(), dto.getFilterContent());
            HashSet<Long> docIdSet = new HashSet<>();
            if (StrUtil.isNotBlank(dto.getDocIds())) {
                String[] docIdArr = dto.getDocIds().split(",");
                for (String docId : docIdArr) {
                    docIdSet.add(Long.parseLong(docId));
                }
            }
            docIdSet.addAll(docIds);
            if (docIdSet.isEmpty()) {
                return dtos;
            } else {
                lambdaQueryWrapper.in(KnowledgeDoc::getId, docIds);
            }

        } else {
            if (StrUtil.isNotBlank(dto.getDocIds())) {
                String[] docIdArr = dto.getDocIds().split(",");
                List<Long> docIds = new ArrayList<>();
                for (String docId : docIdArr) {
                    docIds.add(Long.parseLong(docId));
                }
                lambdaQueryWrapper.in(KnowledgeDoc::getId, docIds);
            }
        }

        List<KnowledgeDoc> docs = knowledgeDocMapper.selectList(lambdaQueryWrapper);
        docs.forEach(doc -> {
            KnowledgeDocDTO knowledgeDocDTO = new KnowledgeDocDTO();
            BeanUtils.copyProperties(doc, knowledgeDocDTO);
            knowledgeDocDTO.setCreateName(sysUserService.getUserName(knowledgeDocDTO.getCreateBy()));
            String fileContent = knowledgeDocDTO.getFileContent();
            if (StringUtils.isNotBlank(fileContent)) {
                knowledgeDocDTO.setFileContent(fileContent.substring(0, Math.min(fileContent.length(), 200)));
            }
            if (doc.getGroupId() != null) {
                KnowledgeGroup group = knowledgeGroupMapper.selectById(doc.getGroupId());
                if (group != null) {
                    knowledgeDocDTO.setGroupName(group.getName());
                }
            }
            dtos.add(knowledgeDocDTO);
        });
        return dtos;
    }

    public KnowledgeDocDetailDTO getKnowledgeDocDetail(Long docId) {
        KnowledgeDocDetailDTO dto = new KnowledgeDocDetailDTO();
        KnowledgeDoc doc = knowledgeDocMapper.selectById(docId);
        if (doc == null) {
            throw new ValidateException("文档不存在");
        }
        BeanUtils.copyProperties(doc, dto);
        List<KnowledgeSegment> segments = knowledgeSegmentMapper.selectList(new LambdaQueryWrapper<KnowledgeSegment>()
                .eq(KnowledgeSegment::getDocId, docId).orderByAsc(KnowledgeSegment::getVectorIndex));
        dto.setSegments(segments);
        return dto;
    }

    public List<KnowledgeDoc> batchGetKnowledgeDocDetail(KnowledgeDocBatchDTO dto) {
        return getKnowledgeDoc(dto.getKnowledgeDocIds());
    }

    public List<KnowledgeDoc> getKnowledgeDoc(List<Long> docIds) {
        return knowledgeDocMapper.selectByIds(docIds);
    }

    public List<KnowledgeDoc> getKnowledgeDocByKnowledgeIds(List<Long> knowledgeIds) {
        return knowledgeDocMapper.selectList(
                new LambdaQueryWrapper<KnowledgeDoc>()
                        .in(KnowledgeDoc::getKnowledgeId, knowledgeIds)
        );
    }


    public List<KnowledgeDocHistoryDTO> getKnowledgeDocHistory(List<Long> docIds) {
        List<KnowledgeDoc> docs = getKnowledgeDoc(docIds);
        return docs.stream().map(doc -> {
            KnowledgeDocHistoryDTO knowledgeDocHistoryDTO = new KnowledgeDocHistoryDTO();
            BeanUtils.copyProperties(doc, knowledgeDocHistoryDTO);
            return knowledgeDocHistoryDTO;
        }).toList();

    }

    public void deleteDoc(String docIds) {
        String[] docIdArr = docIds.split(",");
        for (String docId : docIdArr) {
            KnowledgeDoc doc = knowledgeDocMapper.selectById(docId);
            if (doc == null) {
                return;
                //throw new ValidateException("文档不存在");
            }
            Knowledge knowledge = knowledgeMapper.selectById(doc.getKnowledgeId());
            if (knowledge == null) {
                return;
                //throw new ValidateException("知识库不存在");
            }
            List<KnowledgeSegment> segments = knowledgeSegmentMapper.selectList(
                    new LambdaQueryWrapper<KnowledgeSegment>()
                            .eq(KnowledgeSegment::getDocId, docId)
                            .eq(KnowledgeSegment::getKnowledgeId, doc.getKnowledgeId())
            );
            knowledgeSegmentMapper.delete(new LambdaQueryWrapper<KnowledgeSegment>()
                    .eq(KnowledgeSegment::getDocId, docId)
                    .eq(KnowledgeSegment::getKnowledgeId, doc.getKnowledgeId()));
            if (CollUtil.isNotEmpty(segments)) {
                AiResource resource = resourceMapper.selectById(knowledge.getResourceId());
                if (resource != null) {
                    String collectionName = getCollectionName(resource.getCode(), resource.getId());
                    List<String> vectorIds = new ArrayList<>();
                    for (KnowledgeSegment segment : segments) {
                        if (StrUtil.isNotBlank(segment.getVectorId())) {
                            vectorIds.add(segment.getVectorId());
                        }
                    }
                    if (CollUtil.isNotEmpty(vectorIds)) {
                        milvusClient.delete(vectorIds, collectionName);
                    }
                }
            }
            knowledgeDocMapper.deleteById(docId);

        }
    }


    public void batchDeleteDoc(KnowledgeDocBatchDTO dto) {
        for (Long docId : dto.getKnowledgeDocIds()) {
            KnowledgeDoc doc = knowledgeDocMapper.selectById(docId);
            if (doc == null) {
                throw new ValidateException("文档不存在");
            }
            Knowledge knowledge = knowledgeMapper.selectById(doc.getKnowledgeId());
            if (knowledge == null) {
                throw new ValidateException("知识库不存在");
            }
            List<KnowledgeSegment> segments = knowledgeSegmentMapper.selectList(
                    new LambdaQueryWrapper<KnowledgeSegment>()
                            .eq(KnowledgeSegment::getDocId, docId)
                            .eq(KnowledgeSegment::getKnowledgeId, doc.getKnowledgeId())
                            .eq(KnowledgeSegment::getDeleted, false)
            );
            knowledgeSegmentMapper.delete(new LambdaQueryWrapper<KnowledgeSegment>()
                    .eq(KnowledgeSegment::getDocId, docId)
                    .eq(KnowledgeSegment::getKnowledgeId, doc.getKnowledgeId()));
            if (CollUtil.isNotEmpty(segments)) {
                AiResource resource = resourceMapper.selectById(knowledge.getResourceId());
                if (resource != null) {
                    String collectionName = getCollectionName(resource.getCode(), resource.getId());
                    List<String> vectorIds = new ArrayList<>();
                    for (KnowledgeSegment segment : segments) {
                        if (StrUtil.isNotBlank(segment.getVectorId())) {
                            vectorIds.add(segment.getVectorId());
                        }
                    }
                    if (CollUtil.isNotEmpty(vectorIds)) {
                        milvusClient.delete(vectorIds, collectionName);
                    }
                }
            }
            knowledgeDocMapper.deleteById(docId);
        }
    }

    public List<VectorResultDTO> docSearchByDto(KnowLedgeDocSearchDTO dto) {
        List<VectorResultDTO> searchResults = new LinkedList<>();
        for (Long knowledgeId : dto.getKnowledgeIds()) {
            Knowledge knowledge = knowledgeMapper.selectById(knowledgeId);
            if (knowledge == null) {
//                throw new RuntimeException("知识库不存在");
                continue;
            }
            AiResource resource = resourceMapper.selectById(knowledge.getResourceId());
            if (resource == null) {
                throw new RuntimeException("资源不存在");
            }
            EmbeddedModel embeddedModel = embeddedModelMapper.selectById(knowledge.getEmbeddedId());
            if (embeddedModel == null) {
                throw new RuntimeException("向量模型不存在");
            }
            EmbeddedCallDTO callDTO = new EmbeddedCallDTO();
            callDTO.setModelType(embeddedModel.getCode());
            callDTO.setUrl(embeddedModel.getUrl());
            List<String> segments = new ArrayList<>();
            if (dto.getSearchHistory() != null && dto.getSearchHistory()) {
                if (dto.getConversationName() != null) {
                    // todo 查询上下文历史信息，传入
                    dto.setHistory(new ArrayList<>());
                }
            }
            if (dto.getHistory() != null && !dto.getHistory().isEmpty()) {
                for (Map<String, String> eachHistory : dto.getHistory()) {
                    String role = eachHistory.get("role");
                    if ("user".equals(role)) {
                        segments.add(eachHistory.get("content"));
                    }
                }
            }
            segments.add(dto.getQuery());
            if (segments.size() > 1) {
                segments.add(StringUtils.join(segments, ";"));
            }

            callDTO.setSegments(segments);
            callDTO.setMethodName(embeddedModel.getMethodName());
            List<List<Float>> vectors = dynamicEmbeddedCallService.callEmbedded(callDTO);
            List<VectorResultDTO> resultDTOS = milvusClient.search(dto, vectors, getCollectionName(resource.getCode(), resource.getId()));

            //  遍历查询出来，然后再做merge
            for (VectorResultDTO resultDTO : resultDTOS) {
                if (dto.getMinScore() != null && dto.getMinScore() > 0.01f) {
                    if (resultDTO.getScore() < dto.getMinScore()) {
                        continue;
                    }
                    resultDTO.setKnowledgeId(knowledgeId);
                    searchResults.add(resultDTO);
                }
            }
        }
        // 根据VectorResultDTO的score进行快排，取出topK个最大分数的元素
        searchResults.sort(Comparator.comparing(VectorResultDTO::getScore).reversed());
        if(dto.getTopK()!=null&&dto.getTopK()>0)
            return searchResults.subList(0, Math.min(dto.getTopK(), searchResults.size()));
        return searchResults;
    }

    public List<VectorResultDTO> docSearchByDtoWithFilterMap(KnowLedgeDocSearchDTO dto) {
        List<VectorResultDTO> searchResults = new LinkedList<>();
        for (Long knowledgeId : dto.getKnowledgeFilterMap().keySet()) {
            Knowledge knowledge = knowledgeMapper.selectById(knowledgeId);
            if (knowledge == null) {
                continue;
            }
            AiResource resource = resourceMapper.selectById(knowledge.getResourceId());
            if (resource == null) {
                throw new RuntimeException("资源不存在");
            }
            EmbeddedModel embeddedModel = embeddedModelMapper.selectById(knowledge.getEmbeddedId());
            if (embeddedModel == null) {
                throw new RuntimeException("向量模型不存在");
            }
            EmbeddedCallDTO callDTO = new EmbeddedCallDTO();
            callDTO.setModelType(embeddedModel.getCode());
            callDTO.setUrl(embeddedModel.getUrl());
            List<String> segments = new ArrayList<>();
            if (dto.getSearchHistory() != null && dto.getSearchHistory()) {
                if (dto.getConversationName() != null) {
                    dto.setHistory(new ArrayList<>());
                }
            }
            if (dto.getHistory() != null && !dto.getHistory().isEmpty()) {
                for (Map<String, String> eachHistory : dto.getHistory()) {
                    String role = eachHistory.get("role");
                    if ("user".equals(role)) {
                        segments.add(eachHistory.get("content"));
                    }
                }
            }
            segments.add(dto.getQuery());
            if (segments.size() > 1) {
                segments.add(StringUtils.join(segments, ";"));
            }

            callDTO.setSegments(segments);
            callDTO.setMethodName(embeddedModel.getMethodName());
            List<List<Float>> vectors = dynamicEmbeddedCallService.callEmbedded(callDTO);

            String filter = dto.getKnowledgeFilterMap().get(knowledgeId);
            dto.setFilter(filter);
            List<VectorResultDTO> resultDTOS = milvusClient.search(dto, vectors, getCollectionName(resource.getCode(), resource.getId()));

            //  遍历查询出来，然后再做merge
            for (VectorResultDTO resultDTO : resultDTOS) {
                if (dto.getMinScore() != null && dto.getMinScore() > 0.01f) {
                    if (resultDTO.getScore() < dto.getMinScore()) {
                        continue;
                    }
                    resultDTO.setKnowledgeId(knowledgeId);
                    searchResults.add(resultDTO);
                }
            }
        }
        // 根据VectorResultDTO的score进行快排，取出topK个最大分数的元素
        searchResults.sort(Comparator.comparing(VectorResultDTO::getScore).reversed());
        return searchResults.subList(0, Math.min(dto.getTopK(), searchResults.size()));
    }


    public Page<KnowledgeDetailDTO> queryKnowledge(KnowledgeQueryDTO dto, Page<KnowledgeDetailDTO> page) {
        List<KnowledgeDetailDTO> knowledgeDTOS = knowledgeMapper.queryKnowledge(dto, page);
        if (CollUtil.isNotEmpty(knowledgeDTOS)) {
            for (KnowledgeDetailDTO knowledgeDTO : knowledgeDTOS) {
                knowledgeDTO.setDocs(knowledgeDocMapper.selectList(new LambdaQueryWrapper<KnowledgeDoc>()
                        .select(KnowledgeDoc::getFileName, KnowledgeDoc::getFileSize)
                        .eq(KnowledgeDoc::getKnowledgeId, knowledgeDTO.getId())));
            }
        }
        page.setRecords(knowledgeDTOS);
        return page;
    }

    public Long initKnowledge(String name, String description, String logo, String knowledgeType,
                              String expireOption, LocalDateTime startTime, LocalDateTime endTime, String userId) {
        AiResource resource = initResource(name, description, logo);
        Knowledge knowledge = initKnowledge(resource, knowledgeType, expireOption, startTime, endTime, userId);
        return knowledge.getId();
    }

    public Long addPersonalKnowledge(String name, String description, String logo, String knowledgeType,
                                     String expireOption, LocalDateTime startTime, LocalDateTime endTime, String userId) {
        AiResource resource = initResource(name, description, logo, false);
        Knowledge knowledge = initKnowledge(resource, knowledgeType, expireOption, startTime, endTime, userId);
        return knowledge.getId();
    }

    private AiResource initResource(String name, String description, String logo, boolean systemResourced) {
        AiResource resource = new AiResource();
        resource.setName(name);
        resource.setDescription(description);
        resource.setLogo(logo);
        resource.setType(Constants.RESOURCE_TYPE.knowledge.name());
        resource.setParentId(null);
        resource.setSystemResource(systemResourced);
        resource.setStatus(Constants.PUBLISH_STATUS_0);
        resource.setCode(generate8DigitCode());
        resourceMapper.insert(resource);
        return resource;
    }

    private AiResource initResource(String name, String description, String logo) {
        return initResource(name, description, logo, true);
    }

    private Knowledge initKnowledge(AiResource resource, String knowledgeType, String expireOption,
                                    LocalDateTime startTime, LocalDateTime endTime, String userId) {
        Knowledge knowledge = knowledgeMapper.selectOne(new LambdaQueryWrapper<Knowledge>()
                .eq(Knowledge::getResourceId, resource.getId())
                .eq(Knowledge::getType, knowledgeType)
                .last("limit 1"));
        if (knowledge == null) {
            knowledge = createKnowledge(resource.getId(), knowledgeType);
            knowledge.setExpireOption(expireOption);
            knowledge.setStartTime(startTime);
            knowledge.setEndTime(endTime);
            knowledge.setUserId(userId);
            knowledgeMapper.insert(knowledge);
            if (!milvusClient.isExitCollection(getCollectionName(resource.getCode(), resource.getId()))) {
                EmbeddedModel embeddedModel = embeddedModelMapper.selectById(knowledge.getEmbeddedId());
                milvusClient.createCollection(getCollectionName(resource.getCode(), resource.getId()), embeddedModel.getDimension());
            }
        }
        return knowledge;
    }

    private Knowledge createKnowledge(Long resourceId, String knowledgeType) {
        Knowledge knowledge = new Knowledge();
        knowledge.setResourceId(resourceId);
        knowledge.setType(knowledgeType);
        EmbeddedModel embeddedModel = embeddedModelMapper.selectOne(new LambdaQueryWrapper<EmbeddedModel>()
                .eq(EmbeddedModel::isDefaulted, true)
                .last("limit 1"));
        if (embeddedModel == null) {
            knowledge.setEmbeddedId(2L);
        } else {
            knowledge.setEmbeddedId(embeddedModel.getId());
        }
        return knowledge;
    }


    public List<KnowledgeDoc> autoProcessData(KnowledgeDocAutoDTO dto) {
        Knowledge knowledge = knowledgeMapper.selectById(dto.getKnowledgeId());
        if (knowledge == null) {
            throw new ValidateException("知识库不存在");
        }

        // 兼容ai work知识库的默认分组
        KnowledgeGroup knowledgeGroup = knowledgeGroupMapper.selectOne(
                new LambdaQueryWrapper<KnowledgeGroup>()
                        .eq(KnowledgeGroup::getKnowledgeId, dto.getKnowledgeId())
                        .eq(KnowledgeGroup::isDefaultGroup, true)
                        .eq(KnowledgeGroup::getCreateBy, StpClientUserUtil.getLoginIdAsLong())
                        .eq(KnowledgeGroup::getDeleted, false)
                        .orderByAsc(KnowledgeGroup::getCreateTime)
                        .last("limit 1")
        );

        AiResource resource = resourceMapper.selectById(knowledge.getResourceId());
        if (resource == null) {
            throw new RuntimeException("资源不存在");
        }
        EmbeddedModel embeddedModel = embeddedModelMapper.selectById(knowledge.getEmbeddedId());
        if (embeddedModel == null) {
            throw new RuntimeException("向量模型不存在");
        }
        dto.setEmbeddedModel(embeddedModel);

        String collectionName = getCollectionName(resource.getCode(), resource.getId());
        dto.setCollectionName(collectionName);

        // 校验文档类型,并根据文档类型设置文件类型
        if (!checkKnowledgeDocType(dto.getFiles())) {
            throw new ValidateException("文档类型不匹配");
        }

        List<KnowledgeDoc> docs = new ArrayList<>();

        for (KnowledgeFileDTO file : dto.getFiles()) {
            KnowledgeDoc doc = new KnowledgeDoc();
            BeanUtils.copyProperties(file, doc);
            doc.setKnowledgeId(dto.getKnowledgeId());
            doc.setNoteId(file.getNoteId());
            doc.setDocSourceType(dto.getDocSourceType());
            if (dto.isPreview()) {
                // 高级模式预览，分段未向量化部可见
                doc.setVisible(false);
            } else {
                doc.setVisible(file.isVisible());
            }
            doc.setGroupId(file.getGroupId() != null ? file.getGroupId() : knowledgeGroup != null ? knowledgeGroup.getId() : null);
            if (!dto.isDefaultSegment()) {
                doc.setSegmentStrategy(dto.getSegmentStrategy());
                doc.setCustomizeChar(dto.getCustomizeChar());
                doc.setMaxLength(dto.getMaxLength());
                doc.setOverlapLength(dto.getOverlapLength());
            }
            doc.setStatus(Constants.KNOWLEDGE_DOC_STATUS.analysis.name());
            doc.setThirdUserId(dto.getThirdUserId());
            doc.setCreateBy(dto.getUserId());
            knowledgeDocMapper.insert(doc);
            file.setDoc(doc);
            docs.add(doc);
        }
        return docs;
    }

    private boolean checkKnowledgeDocType(List<KnowledgeFileDTO> files) {
        for (KnowledgeFileDTO file : files) {
            boolean isEnum = false;
            for (Constants.KNOWLEDGE_DOC_TYPE value : Constants.KNOWLEDGE_DOC_TYPE.values()) {
                if (StrUtil.equals(value.name(), file.getDocType())) {
                    isEnum = true;
                    break;
                }
            }

            if (!isEnum) {
                log.info("{} 文件的doctype不符合,为{}", file.getFileName(), file.getDocType());
                return false;
            }


            String extName = file.getFileName().substring(file.getFileName().lastIndexOf(".") + 1).toLowerCase();
            Constants.KNOWLEDGE_DOC_TYPE doctType = Constants.KNOWLEDGE_DOC_TYPE.valueOf(file.getDocType());
            switch (doctType) {
                case Constants.KNOWLEDGE_DOC_TYPE.text:
                    if (!"pdf".equals(extName) && !"docx".equals(extName) && !"doc".equals(extName) && !"txt".equals(extName) && !"md".equals(extName)) {
                        log.info("{} 文件的extName不符合,为{}", file.getFileName(), extName);
                        return false;
                    }
                    break;
                case Constants.KNOWLEDGE_DOC_TYPE.table:
                    if (!"csv".equals(extName) && !"xls".equals(extName) && !"xlsx".equals(extName)) {
                        return false;
                    }
                    break;
                case Constants.KNOWLEDGE_DOC_TYPE.image:
                    if (!"png".equals(extName) && !"jpeg".equals(extName) && !"jpg".equals(extName)) {
                        return false;
                    }
                    break;
                default:
                    log.info(String.format("%s 文件的doctype不符合,为%s", file.getFileName(), doctType));
                    return false;
            }

            file.setFileType(extName);
            file.setDocTypeEnum(doctType);
        }
        return true;
    }


    @Async
    public CompletableFuture<Long> autoProcessDoc(KnowledgeFileDTO file, KnowledgeDocAutoDTO dto) {
        KnowledgeDoc doc = file.getDoc();
        //这个一步流程不需要更新可见状态，设置为null让mybatis不更新这个字段
        doc.setVisible(null);
        try {
            Map<Integer, String> fileContentMap = new HashMap<>();
            String fileContent;
            if (StrUtil.equals(dto.getDocSourceType(), Constants.KNOWLEDGE_DOC_SOURCE_TYPE.upload.name())) {
                ClientProperties.Image image = clientProperties.getImage();
                String imageProcessType = image == null ? null : image.getProcessType();
                if (jodConverterProperties.isEnabled()) {
                    //启用转pdf服务
                    FileParseDTO fileParseDTO = getFileParseDTO(file, doc);
                    if (!clientProperties.getDoc().isEnabledAdvance()) {
                        // 未启用高级模式，不使用mineru方式解析，直接解析文件内容
                        fileParseDTO.setMode("normal");
                    } else {
                        // 根据策略确定解析方式
                        fileParseDTO.setMode(dto.getParseStrategy());
                    }
                    fileParseDTO.setImageProcessType(imageProcessType);
                    // 高级策略使用minerU解析在V2中单独处理，此方法中一定是一般模式
                    fileParseDTO.setMode("normal");
                    fileContentMap = FileParseUtils.fileParsePerPage(fileParseDTO);
                } else {
                    //此处不用libreoffice预处理成pdf，直接解析文件内容
                    fileContentMap = imageReplacerService.parseDocument(file.getFileUrl(), doc.getFileType());
                }
                StringBuilder sb = new StringBuilder();
                for (Map.Entry<Integer, String> entry : fileContentMap.entrySet()) {
                    sb.append(entry.getValue());
                }
                fileContent = sb.toString();

                // 如果是图片，需要将Base64通过大模型转换为文字
                if (Constants.KNOWLEDGE_DOC_TYPE.image.equals(file.getDocTypeEnum())
                        && StrUtil.equals("vision", imageProcessType)) {
                    fileContent = getImageContent(fileContent);
                    fileContentMap.put(1, fileContent);
                }
            } else {
                // 此处处理纯文本
                // 笔记添加到知识库， 粘帖复制等
                fileContent = file.getFileContent();
                fileContentMap.put(1, fileContent);
            }

            if (StringUtils.isBlank(fileContent)) {
                doc.setStatus(Constants.KNOWLEDGE_DOC_STATUS.failed.name());
                doc.setReason("解析内容为空");
                doc.setUpdateBy(dto.getUserId());
                knowledgeDocMapper.updateById(doc);
                return CompletableFuture.completedFuture(doc.getId());
            }

            doc.setFileContent(fileContent);
            doc.setFileContentMap(fileContentMap);
            doc.setDefaultSegment(dto.isDefaultSegment());
            doc.setAutoSegment(dto.isAutoSegment());
            if (dto.isDocEnhancement()) {
                if (StrUtil.equals(dto.getDocEnhancementStrategy(), "title")) {
                    // 设置文件标题为概要
                    String[] split = doc.getFileName().split("\\.");
                    // 去掉最后的文件后缀，保留前面的文本
                    String title = split[0];
                    doc.setOverview(title);
                } else {
                    doc.setOverview(dto.getOverview());
                }
            } else {
                doc.setOverview(null);
            }
            //文档分段
            List<KnowledgeDocSegmentDTO> segments = switch (file.getDocTypeEnum()) {
                case Constants.KNOWLEDGE_DOC_TYPE.text, Constants.KNOWLEDGE_DOC_TYPE.image ->
                        textSegmentationPerPage(doc);
                case Constants.KNOWLEDGE_DOC_TYPE.table -> tableSegmentation(doc);
            };

            //调用向量服务
            doc.setUpdateBy(dto.getUserId());
            if (dto.isPreview()) {
                doc.setStatus(Constants.KNOWLEDGE_DOC_STATUS.complete.name());
                doc.setUpdateBy(dto.getUserId());
                knowledgeDocMapper.updateById(doc);

                //高级模式先解析完成，不生成向量
                List<KnowledgeSegment> knowledgeSegments = new ArrayList<>();
                int segmentId = 0;
                for (KnowledgeDocSegmentDTO knowledgeDocSegmentDTO : segments) {
                    KnowledgeSegment segment = new KnowledgeSegment();
                    segment.setDocId(doc.getId());
                    segment.setKnowledgeId(doc.getKnowledgeId());
                    segment.setPageNumber(knowledgeDocSegmentDTO.getPageNumber());
                    segment.setText(knowledgeDocSegmentDTO.getContent());
                    segment.setCreateBy(doc.getUpdateBy());
                    segment.setUpdateBy(doc.getUpdateBy());
                    segment.setVectorIndex(segmentId * 10);
                    segment.setOverview(knowledgeDocSegmentDTO.getOverview());
                    knowledgeSegments.add(segment);
                    segmentId++;
                }
                knowledgeSegmentMapper.insert(knowledgeSegments);
            } else {
                doc.setStatus(Constants.KNOWLEDGE_DOC_STATUS.embedded.name());
                doc.setUpdateBy(dto.getUserId());
                knowledgeDocMapper.updateById(doc);
                convertSegmentsToVectors(segments, dto.getCollectionName(), dto.getEmbeddedModel(), doc);
            }

            return CompletableFuture.completedFuture(doc.getId());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            doc.setStatus(Constants.KNOWLEDGE_DOC_STATUS.failed.name());
            doc.setReason("解析失败");
            doc.setUpdateBy(dto.getUserId());
            knowledgeDocMapper.updateById(doc);
            return CompletableFuture.completedFuture(doc.getId());
        }
    }


    public FileParseDTO getFileParseDTO(KnowledgeFileDTO file, KnowledgeDoc doc) {
        FileParseDTO fileParseDTO = new FileParseDTO();
        fileParseDTO.setDocId(doc.getId());
        fileParseDTO.setFileUrl(file.getFileUrl());
        fileParseDTO.setFileType(doc.getFileType());

        if (file.getDocTypeEnum().equals(Constants.KNOWLEDGE_DOC_TYPE.text)) {
            String jodUrl = jodConverterRemoteProperties.getUrl();
            fileParseDTO.setJodUrl(jodUrl);
            // 下面是使用mineru才会使用的参数
            String finalUrl;
            if (StrUtil.equalsAny(doc.getFileType(), "doc", "docx", "ppt", "pptx")) {
                finalUrl = convertToPdfUrl(file.getFileUrl(), jodUrl);
            } else {
                finalUrl = file.getFileUrl();
            }

            fileParseDTO.setBucketName(minioProperties.getBucketName());
            fileParseDTO.setAccessKey(minioProperties.getAccessKey());
            fileParseDTO.setSecretKey(minioProperties.getSecretKey());
            fileParseDTO.setEndpointUrl(minioProperties.getUrl());
            fileParseDTO.setShowUrl(minioProperties.getShowUrl());
            fileParseDTO.setFileName(finalUrl);

            fileParseDTO.setSubmitUrl(clientProperties.getDoc().getSubmitUrl());
            fileParseDTO.setTaskUrl(clientProperties.getDoc().getTaskUrl());
            fileParseDTO.setApiKey(selfProperties.getClient().getClientId());

            fileParseDTO.setSubmitCallBackUrl(clientProperties.getDoc().getSubmitCallBackUrl());
            fileParseDTO.setCallBackUrl(clientProperties.getDoc().getCallBackUrl());
            String clientToken = Base64.encode(selfProperties.getClient().getClientId() + ":" + selfProperties.getClient().getClientSecret());
            fileParseDTO.setCallBackToken(clientToken);
        } else if (file.getDocTypeEnum().equals(Constants.KNOWLEDGE_DOC_TYPE.table)) {
            //TODO 后期根据参数变动headerParam
            fileParseDTO.setHeaderParam("1");
        }
        return fileParseDTO;
    }

    @SneakyThrows
    private String convertToPdfUrl(String fileUrl, String jodUrl) {
        URI uri = new URI(fileUrl);
        URL url = uri.toURL();
        try (InputStream inputStream = url.openStream()) {
            OfficeManager officeManager = RemoteOfficeManager.builder()
                    .urlConnection(jodUrl)
                    .build();
            officeManager.start();
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                RemoteConverter.make(officeManager).convert(inputStream).to(outputStream).as(DefaultDocumentFormatRegistry.PDF).execute();
                byte[] convertedBytes = outputStream.toByteArray();
                String fileName = UUIDGenerator.getUUID() + ".pdf";
                minioUtil.upload(convertedBytes, fileName);
                return minioUtil.getUrl(fileName);
            } catch (Exception e) {
                log.warn("转换pdf失败");
            } finally {
                officeManager.stop();
            }
        }
        return "";
    }

    public List<KnowledgeDocSegmentDTO> textSegmentationPerPage(KnowledgeDoc doc) {
        // 系统分段字符
        String splitChar; //分段字符
        String text; //文档内容
        Map<Integer, String> fileContentMap = doc.getFileContentMap();
        int maxLength; //最大分段长度
        int overlapLengthPercent; //重叠度
        if (doc.getAutoSegment()) {
            // 自动分段
            maxLength = Integer.parseInt(clientProperties.getText().getMaxLength());
            overlapLengthPercent = 20;
            splitChar = null;
        } else {
            maxLength = doc.getMaxLength();
            overlapLengthPercent = doc.getOverlapLength();
            splitChar = switch (doc.getSegmentStrategy()) {
                case "换行" -> "\n";
                case "2个换行" -> "\n\n";
                case "中文句号" -> "。";
                case "中文叹号" -> "！";
                case "英文句号" -> ".";
                case "英文叹号" -> "!";
                case "中文问号" -> "？";
                case "英文问号" -> "?";
                case "自定义" -> StrUtil.isBlank(doc.getCustomizeChar()) ? " " : doc.getCustomizeChar();
                default -> " ";
            };
        }
        // int 转为百分比
        double percent = overlapLengthPercent / 100.0;
        int overlapLength = (int) Math.round(maxLength * percent); //重叠度

        List<String> separators = StrUtil.isEmpty(splitChar) ? null : List.of(splitChar);

        RecursiveCharacterTextSplitter splitter = new RecursiveCharacterTextSplitter(maxLength, overlapLength, separators, String::length);
        List<KnowledgeDocSegmentDTO> segments = new ArrayList<>();
        List<Integer> pages = fileContentMap.keySet().stream().toList();
        for (Integer page : pages) {
            text = fileContentMap.get(page);
            List<String> chunks = splitter.splitText(text);
            for (String chunk : chunks) {
                KnowledgeDocSegmentDTO docSegmentDTO = new KnowledgeDocSegmentDTO();
                docSegmentDTO.setPageNumber(Long.parseLong(page.toString()));
                docSegmentDTO.setContent(chunk);
                docSegmentDTO.setOverview(doc.getOverview());
                segments.add(docSegmentDTO);
            }
        }
        return segments;
    }

    public List<KnowledgeDocSegmentDTO> tableSegmentation(KnowledgeDoc doc) {
        List<KnowledgeDocSegmentDTO> segments = new ArrayList<>();
        Map<Integer, String> fileContentMap = doc.getFileContentMap();
        String text = fileContentMap.get(1);
        int maxLength; //最大分段长度
        if (doc.getAutoSegment()) {
            maxLength = Integer.parseInt(clientProperties.getText().getMaxLength());
        } else {
            maxLength = doc.getMaxLength();
        }
        JSONArray jsonArray = JSON.parseArray(text);
        for (int i = 0; i < jsonArray.size(); i++) {
            KnowledgeDocSegmentDTO segmentDTO = new KnowledgeDocSegmentDTO();
            String line = jsonArray.getString(i);
            if (maxLength != -1 && line.length() > maxLength) {
                System.out.println(line);
                throw new ValidateException("分段长度超过最大值，请调整分段设置或减少内容长度");
            }
            segmentDTO.setContent(line);
            segmentDTO.setOverview(doc.getOverview());
            segments.add(segmentDTO);
        }
        return segments;
    }


    private String getImageContent(String imageBase64) {
        AiModel model = modelMapper.selectOne(
                new LambdaQueryWrapper<AiModel>()
                        .eq(AiModel::getCode, clientProperties.getImage().getVisionModel())
        );
        if (model == null) {
            return "";
        }

        String url = model.getNonStreamUrl();

        ModelProcessDTO req = new ModelProcessDTO();
        req.setModelType(model.getCode());
        req.setSystemMessage(Constants.DEFAULT_IMAGE_PROMPT);
        req.setUserMessage("文字提取");
        req.setImageUrls(CollUtil.newArrayList(imageBase64));
        req.setMaxTokens(12288);
        Map<String, Object> paramBody = JSON.parseObject(JSON.toJSONString(req), Map.class);
        ModelCallDTO callDTO = new ModelCallDTO();
        callDTO.setMethodName(model.getNonStreamMethod());
        callDTO.setUrl(url);
        callDTO.setParamBody(paramBody);
        Map<String, Object> response = modelService.callModelNonStream(callDTO);
        return response.get("data").toString();
    }

    private List<KnowledgeDocSegmentDTO> autoSegmentTextByModel(String text) {
        List<KnowledgeDocSegmentDTO> segments = new ArrayList<>();
        AiModel model = modelMapper.selectOne(
                new LambdaQueryWrapper<AiModel>()
                        .eq(AiModel::getCode, "deep_seek_chat_v3")
        );
        String url = model.getNonStreamUrl();


        ModelProcessDTO req = new ModelProcessDTO();
        req.setModelType(model.getCode());
        req.setSystemMessage(Constants.DEFAULT_AUTO_SEG_PROMPT);
        req.setUserMessage(text);
        req.setMaxTokens(8096);
        Map<String, Object> paramBody = JSON.parseObject(JSON.toJSONString(req), Map.class);
        ModelCallDTO callDTO = new ModelCallDTO();
        callDTO.setMethodName(model.getNonStreamMethod());
        callDTO.setUrl(url);
        callDTO.setParamBody(paramBody);
        Map<String, Object> response = modelService.callModelNonStream(callDTO);
        if (!response.containsKey("status")
                || Integer.parseInt(response.getOrDefault("status", 0).toString()) != 200) {
            throw new RuntimeException("调用大模型服务失败");
        }
        JSONObject result = JSON.parseObject(response.getOrDefault("data", "{}").toString());
        JSONObject data = JSON.parseObject(result.getString("data"));
        JSONArray content = JSON.parseArray(data.getString("content"));
        for (Object o : content) {
            KnowledgeDocSegmentDTO obj = JSON.parseObject(o.toString(), KnowledgeDocSegmentDTO.class);
            segments.add(obj);
        }
        return segments;
    }


    private List<KnowledgeDocSegmentDTO> segmentText(String text, String splitChar, int maxLength, int overlapLength) {
        // 参数校验
        if (StrUtil.isEmpty(text) || StrUtil.isEmpty(splitChar)) {
            throw new ValidateException("文本不能为空");
        }
        if (overlapLength >= maxLength) {
            throw new IllegalArgumentException("分段重叠度必须小于分段最大长度");
        }

        List<KnowledgeDocSegmentDTO> segments = new ArrayList<>();
        int textLength = text.length();
        if (textLength <= maxLength) {
            KnowledgeDocSegmentDTO docSegmentDTO = new KnowledgeDocSegmentDTO();
            docSegmentDTO.setContent(text);
            segments.add(docSegmentDTO);
            return segments;
        }

        int start = 0;
        while (start < textLength) {
            int end = Math.min(start + maxLength, textLength);
            String preSegment = text.substring(start, end);
            // 查找最后一个分隔符的位置
            int lastSplitIndex = preSegment.lastIndexOf(splitChar);
            if (lastSplitIndex != -1) {
                end = start + lastSplitIndex + splitChar.length();
            }
            // 确保 end 不超过文本长度
            end = Math.min(end, textLength);
            String segment = text.substring(start, end);
            KnowledgeDocSegmentDTO docSegmentDTO = new KnowledgeDocSegmentDTO();
            docSegmentDTO.setContent(segment);
            segments.add(docSegmentDTO);

            // 计算下一段的起始位置
            if (segment.isEmpty()) {
                break; // 避免因空字符串导致死循环
            } else if (segment.length() <= overlapLength) {
                start = end;
            } else {
                start = Math.max(end - overlapLength, start + 1); // 确保 start 至少前进 1
            }
        }
        return segments;
    }

    private String getCollectionName(String code, Long id) {
        return code + "_" + id;
    }

    public void convertSegmentsToVectors(List<KnowledgeDocSegmentDTO> segments, String collectionName, EmbeddedModel model, KnowledgeDoc doc) {
        if (!milvusClient.isExitCollection(collectionName)) {
            milvusClient.createCollection(collectionName, model.getDimension());
        }
        if (CollUtil.isEmpty(segments)) {
            doc.setStatus(Constants.KNOWLEDGE_DOC_STATUS.failed.name());
            doc.setReason("分段内容为空");
            knowledgeDocMapper.updateById(doc);
            return;
        }

        List<String> contentSegments = new ArrayList<>();
        for (KnowledgeDocSegmentDTO segment : segments) {
            contentSegments.add(segment.getContent());
//            contentSegments.add(segment.getSummary() + "\n" + StrUtil.join("\n", segment.getQuestions()));
        }

        EmbeddedCallDTO dto = new EmbeddedCallDTO();
        dto.setModelType(model.getCode());
        dto.setSegments(contentSegments);
        dto.setUrl(model.getUrl());
        dto.setMethodName(model.getMethodName());
        List<List<Float>> contentVectors = dynamicEmbeddedCallService.callEmbedded(dto);

        // 向量化可能的overview字段。包括：概要、标签、文件名字
        // 需要去重一下向量化
        Map<String, Integer> overviewSegmentsIndexMap = new LinkedHashMap<>();
        List<String> overviewSegments = new ArrayList<>();
        int index = 0;
        for (KnowledgeDocSegmentDTO segment : segments) {
            if (StringUtils.isNotBlank(segment.getOverview())) {
                if (!overviewSegmentsIndexMap.containsKey(segment.getOverview())) {
                    overviewSegmentsIndexMap.put(segment.getOverview(), index);
                    overviewSegments.add(segment.getOverview());
                    index++;
                }
            }
        }

        List<List<Float>> segmentVectors = null;
        if (CollUtil.isNotEmpty(overviewSegments)) {
            EmbeddedCallDTO dtoOverview = new EmbeddedCallDTO();
            dtoOverview.setModelType(model.getCode());
            dtoOverview.setSegments(overviewSegments);
            dtoOverview.setUrl(model.getUrl());
            dtoOverview.setMethodName(model.getMethodName());
            segmentVectors = dynamicEmbeddedCallService.callEmbedded(dtoOverview);
        }


        List<VectorRecordDTO> vectorRecordDTOS = new ArrayList<>();
        List<KnowledgeSegment> knowledgeSegments = new ArrayList<>();
        for (int i = 0; i < segments.size(); i++) {
            VectorRecordDTO vectorRecordDTO = new VectorRecordDTO();
            List<Float> vectors = contentVectors.get(i);
            vectorRecordDTO.setVector(vectors);
            vectorRecordDTO.setVectorExt(vectors);
            String overview = segments.get(i).getOverview();
            if (segmentVectors != null && StringUtils.isNotBlank(overview)) {
                if (overviewSegmentsIndexMap.containsKey(overview)) {
                    int overviewIndex = overviewSegmentsIndexMap.get(overview);
                    List<Float> overviewVector = segmentVectors.get(overviewIndex);
                    vectorRecordDTO.setVectorExt(overviewVector);
                }
            }
            vectorRecordDTO.setText(segments.get(i).getContent());
            vectorRecordDTO.setUrl(doc.getFileUrl());
            vectorRecordDTO.setDocId(doc.getId());
            vectorRecordDTO.setGroupId(doc.getGroupId());
            vectorRecordDTO.setDoc(doc.getFileName());
            vectorRecordDTO.setCreateBy(doc.getUpdateBy());
            vectorRecordDTO.setPageNumber(segments.get(i).getPageNumber());
            // 这种就不存储片段id了。通过文件上传的没必要
            vectorRecordDTOS.add(vectorRecordDTO);

            KnowledgeSegment segment = new KnowledgeSegment();
            segment.setDocId(doc.getId());
            segment.setDoc(doc.getFileName());

            segment.setKnowledgeId(doc.getKnowledgeId());
            segment.setPageNumber(segments.get(i).getPageNumber());
            segment.setText(segments.get(i).getContent());
            segment.setCreateBy(doc.getUpdateBy());
            segment.setUpdateBy(doc.getUpdateBy());
            knowledgeSegments.add(segment);
        }
        List<Object> vectorIds = milvusClient.insert(vectorRecordDTOS, collectionName);

        for (int i = 0; i < knowledgeSegments.size(); i++) {
            KnowledgeSegment knowledgeSegment = knowledgeSegments.get(i);
            knowledgeSegment.setVectorId(vectorIds.get(i).toString());
            knowledgeSegment.setVectorIndex(i * 10);
        }
        knowledgeSegmentMapper.insertBatch(knowledgeSegments);

        doc.setStatus(Constants.KNOWLEDGE_DOC_STATUS.success.name());
        knowledgeDocMapper.updateById(doc);
    }

    public void updateDocName(KnowledgeDocUpdateDTO dto) {
        KnowledgeDoc doc = knowledgeDocMapper.selectById(dto.getKnowledgeDocId());
        if (doc == null) {
            throw new RuntimeException("文档不存在");
        }
        if (StringUtils.isBlank(dto.getKnowledgeDocName())) {
            throw new RuntimeException("文档名称不能为空");
        }
        if (dto.getKnowledgeDocName().equals(doc.getFileName())) {
            // 忽略
        } else {
            doc.setFileName(dto.getKnowledgeDocName());
            knowledgeDocMapper.updateById(doc);
            // 同步更新一下向量库的分片的doc字段
            knowledgeSegmentMapper.updateFileNameByDocId(doc.getFileName(), doc.getId());
            // todo 理论上还需要把片段重新向量化一下了，更新doc字段，不然查出来的和重排需要用到文件名称，可能有问题 2025-07-18，先这样吧
        }
    }

    @Async
    @SneakyThrows
    public void advancedVectorData(Long docId, String userId) {
        KnowledgeDoc doc = knowledgeDocMapper.selectById(docId);

        boolean isComplete = false;
        int maxRetries = 180; // 最大重试次数
        for (int i = 0; i < maxRetries; i++) {
            log.info("向量化文档分段:{}", doc);
            isComplete = StrUtil.equals(doc.getStatus(), Constants.KNOWLEDGE_DOC_STATUS.complete.name());
            if (isComplete) {
                // 分段完成，继续执行后续操作
                break;
            }
            Thread.sleep(10000);
            doc = knowledgeDocMapper.selectById(docId);
            if (doc == null) {
                // 在向量化过程中，文档被删除
//                return null;
                return;
            }
        }

        if (!isComplete) {
            //60秒内还未完成分段
            doc.setStatus(Constants.KNOWLEDGE_DOC_STATUS.failed.name());
            doc.setReason("解析文档分段超时");
            doc.setUpdateBy(userId);
            knowledgeDocMapper.updateById(doc);
            return;
        }

        Knowledge knowledge = knowledgeMapper.selectById(doc.getKnowledgeId());
        if (knowledge == null) {
            throw new RuntimeException("知识库不存在");
        }
        AiResource resource = resourceMapper.selectById(knowledge.getResourceId());
        if (resource == null) {
            throw new RuntimeException("资源不存在");
        }
        EmbeddedModel embeddedModel = embeddedModelMapper.selectById(knowledge.getEmbeddedId());
        if (embeddedModel == null) {
            throw new RuntimeException("向量模型不存在");
        }
        //文档分段
        List<KnowledgeSegment> segments = knowledgeSegmentMapper.selectList(
                new LambdaQueryWrapper<KnowledgeSegment>()
                        .eq(KnowledgeSegment::getKnowledgeId, knowledge.getId())
                        .eq(KnowledgeSegment::getDocId, doc.getId())
        );

        // 文档向量化，设为可见
        doc.setStatus(Constants.KNOWLEDGE_DOC_STATUS.embedded.name());
        knowledgeDocMapper.updateById(doc);

        //调用向量服务
        advanceSegmentsToVectors(segments,
                getCollectionName(resource.getCode(), resource.getId()),
                embeddedModel,
                doc);
//        return segments;
    }

    private void advanceSegmentsToVectors(List<KnowledgeSegment> segments, String collectionName, EmbeddedModel model, KnowledgeDoc doc) {
        if (!milvusClient.isExitCollection(collectionName)) {
            milvusClient.createCollection(collectionName, model.getDimension());
        }
        if (CollUtil.isEmpty(segments)) {
            doc.setStatus(Constants.KNOWLEDGE_DOC_STATUS.failed.name());
            doc.setReason("分段内容为空");
            knowledgeDocMapper.updateById(doc);
            return;
        }

        List<String> contentSegments = new ArrayList<>();
        for (KnowledgeSegment segment : segments) {
            contentSegments.add(segment.getText());
        }

        EmbeddedCallDTO dto = new EmbeddedCallDTO();
        dto.setModelType(model.getCode());
        dto.setSegments(contentSegments);
        dto.setUrl(model.getUrl());
        dto.setMethodName(model.getMethodName());
        List<List<Float>> contentVectors = dynamicEmbeddedCallService.callEmbedded(dto);


        // 向量化可能的overview字段。包括：概要、标签、文件名字
        // 需要去重一下向量化
        Map<String, Integer> overviewSegmentsIndexMap = new LinkedHashMap<>();
        List<String> overviewSegments = new ArrayList<>();
        int index = 0;
        for (KnowledgeSegment segment : segments) {
            if (StringUtils.isNotBlank(segment.getOverview())) {
                if (!overviewSegmentsIndexMap.containsKey(segment.getOverview())) {
                    overviewSegmentsIndexMap.put(segment.getOverview(), index);
                    overviewSegments.add(segment.getOverview());
                    index++;
                }
            }
        }
        List<List<Float>> segmentVectors = null;
        if (CollUtil.isNotEmpty(overviewSegments)) {
            EmbeddedCallDTO dtoOverview = new EmbeddedCallDTO();
            dtoOverview.setModelType(model.getCode());
            dtoOverview.setSegments(overviewSegments);
            dtoOverview.setUrl(model.getUrl());
            dtoOverview.setMethodName(model.getMethodName());
            segmentVectors = dynamicEmbeddedCallService.callEmbedded(dtoOverview);
        }

        List<VectorRecordDTO> vectorRecordDTOS = new ArrayList<>();
        for (int i = 0; i < segments.size(); i++) {
            VectorRecordDTO vectorRecordDTO = new VectorRecordDTO();
            List<Float> vectors = contentVectors.get(i);
            vectorRecordDTO.setVector(vectors);
            vectorRecordDTO.setVectorExt(vectors);
            String overview = segments.get(i).getOverview();
            if (segmentVectors != null && StringUtils.isNotBlank(overview)) {
                if (overviewSegmentsIndexMap.containsKey(overview)) {
                    int overviewIndex = overviewSegmentsIndexMap.get(overview);
                    List<Float> overviewVector = segmentVectors.get(overviewIndex);
                    vectorRecordDTO.setVectorExt(overviewVector);
                }
            }
            vectorRecordDTO.setText(segments.get(i).getText());
            vectorRecordDTO.setUrl(doc.getFileUrl());
            vectorRecordDTO.setDocId(doc.getId());
            vectorRecordDTO.setGroupId(doc.getGroupId());
            vectorRecordDTO.setDoc(doc.getFileName());
            vectorRecordDTO.setCreateBy(doc.getUpdateBy());
            vectorRecordDTO.setPageNumber(segments.get(i).getPageNumber());
            vectorRecordDTO.setSegId(segments.get(i).getId());

            vectorRecordDTOS.add(vectorRecordDTO);
        }
        List<Object> vectorIds = milvusClient.insert(vectorRecordDTOS, collectionName);

        for (int i = 0; i < segments.size(); i++) {
            KnowledgeSegment knowledgeSegment = segments.get(i);
            knowledgeSegment.setVectorId(vectorIds.get(i).toString());
            knowledgeSegmentMapper.updateById(knowledgeSegment);
        }
        doc.setStatus(Constants.KNOWLEDGE_DOC_STATUS.success.name());
        knowledgeDocMapper.updateById(doc);
    }

    public List<KnowledgeDoc> updateDocVisible(List<Long> docIds) {
        List<KnowledgeDoc> docs = knowledgeDocMapper.selectByIds(docIds);
        for (KnowledgeDoc doc : docs) {
            // 文档向量化，设为可见
            doc.setStatus(null);
            doc.setVisible(true);
            knowledgeDocMapper.updateById(doc);
        }
        return docs;
    }


    public List<Map<String, Object>> aiworkBatchImportFromAnotherKnowledge(AiWorkKnowledgeDocBatchImportDTO dto) {
        Knowledge targetKnowledge = knowledgeMapper.selectById(dto.getTargetKnowledgeId());
        if (targetKnowledge == null) {
            throw new ValidateException("目标知识库不存在");
        }

        AiResource targetResource = resourceMapper.selectById(targetKnowledge.getResourceId());
        if (targetResource == null) {
            throw new ValidateException("目标知识库不存在");
        }

        KnowledgeGroup targetKnowledgeGroup = knowledgeGroupMapper.selectById(dto.getTargetGroupId());
        if (targetKnowledgeGroup == null) {
            throw new ValidateException("目标知识库组不存在");
        }
        Long targetEmbeddedId = targetKnowledge.getEmbeddedId();

        List<Map<String, Object>> result = new ArrayList<>();
        for (Long docId : dto.getDocIds()) {
            Map<String, Object> moveMap = new HashMap<>();
            KnowledgeDoc sourceDoc = knowledgeDocMapper.selectById(docId);
            if (sourceDoc == null) {
                throw new ValidateException("来源文档不存在:{}", docId);
//                log.warn("来源文档不存在:{}", docId);
//                continue;
            }

            if (!StrUtil.equals(sourceDoc.getStatus(), Constants.KNOWLEDGE_DOC_STATUS.success.name())) {
                throw new ValidateException("来源文档未处理完成:{}", docId);
//                log.warn("来源文档未处理完成:{}", docId);
//                continue;
            }

            Knowledge sourceKnowledge = knowledgeMapper.selectById(sourceDoc.getKnowledgeId());
            if (sourceKnowledge == null) {
                throw new ValidateException("来源文档所对应的知识库不存在:{}", docId);
//                log.warn("来源文档所对应的知识库不存在:{}", docId);
//                continue;
            }
            Long sourceEmbeddedId = sourceKnowledge.getEmbeddedId();
            if (!sourceEmbeddedId.equals(targetEmbeddedId)) {
                // 向量模型不一致，不允许导入
                throw new ValidateException("来源文档知识库与目标知识库向量模型不一致，不允许导入:{}", docId);
//                log.warn("来源文档知识库与目标知识库向量模型不一致，不允许导入:{}", docId);
//                continue;
            }

            AiResource sourceResource = resourceMapper.selectById(sourceKnowledge.getResourceId());
            if (sourceResource == null) {
                throw new ValidateException("来源文档所对应的资源不存在:{}", docId);
//                log.warn("来源文档所对应的资源不存在:{}", docId);
//                continue;
            }

            KnowledgeDoc targetDoc = new KnowledgeDoc();
            BeanUtil.copyProperties(sourceDoc, targetDoc);
            targetDoc.setId(null);
            targetDoc.setKnowledgeId(targetKnowledge.getId());
            targetDoc.setGroupId(targetKnowledgeGroup.getId());
            targetDoc.setVisible(true);
            targetDoc.setStatus(Constants.KNOWLEDGE_DOC_STATUS.embedded.name());
            knowledgeDocMapper.insert(targetDoc);
            moveMap.put("sourceCollectionName", getCollectionName(sourceResource.getCode(), sourceResource.getId()));
            moveMap.put("targetCollectionName", getCollectionName(targetResource.getCode(), targetResource.getId()));
            moveMap.put("sourceDoc", sourceDoc);
            moveMap.put("targetDoc", targetDoc);
            moveMap.put("targetDocId", targetDoc.getId());
            result.add(moveMap);
        }

        return result;
    }

    @Async
    public void copySegmentAndVector(Long targetKnowledgeId, String sourceCollectionName, String targetCollectionName,
                                     KnowledgeDoc sourceDoc, KnowledgeDoc targetDoc) {
        List<String> dynamicFields = List.of("vector", "vectorExt", "text", "pageNumber");
        String filter = "docId == " + sourceDoc.getId();
        List<Map<String, Object>> queryList = milvusClient.query(filter, sourceCollectionName, dynamicFields);

        List<VectorRecordDTO> vectorRecordDTOS = new ArrayList<>();
        List<KnowledgeSegment> knowledgeSegments = new ArrayList<>();
        for (Map<String, Object> map : queryList) {
            VectorRecordDTO vectorRecordDTO = new VectorRecordDTO();
            vectorRecordDTO.setVector((List<Float>) map.get("vector"));
            vectorRecordDTO.setVectorExt((List<Float>) map.get("vectorExt"));

            vectorRecordDTO.setText((String) map.get("text"));

//            vectorRecordDTO.setTag((String) map.get("tag"));
//            vectorRecordDTO.setOverview((String) map.get("overview"));

            vectorRecordDTO.setUrl(targetDoc.getFileUrl());
            vectorRecordDTO.setDocId(targetDoc.getId());
            vectorRecordDTO.setGroupId(targetDoc.getGroupId());
            vectorRecordDTO.setDoc(targetDoc.getFileName());
            vectorRecordDTO.setCreateBy(targetDoc.getCreateBy());
            vectorRecordDTO.setPageNumber((Long) map.get("pageNumber"));
            Object segId = map.getOrDefault("segId", null);
            vectorRecordDTO.setSegId(segId == null ? null : (Long) segId);

            vectorRecordDTOS.add(vectorRecordDTO);


            KnowledgeSegment knowledgeSegment = new KnowledgeSegment();
            knowledgeSegment.setDocId(targetDoc.getId());
            knowledgeSegment.setDoc(targetDoc.getFileName());
            knowledgeSegment.setKnowledgeId(targetKnowledgeId);
            knowledgeSegment.setText((String) map.get("text"));
            knowledgeSegment.setPageNumber((Long) map.get("pageNumber"));
            knowledgeSegment.setCreateBy(sourceDoc.getUpdateBy());
            knowledgeSegment.setUpdateBy(sourceDoc.getUpdateBy());
            knowledgeSegments.add(knowledgeSegment);
        }
        List<Object> vectorIds = milvusClient.insert(vectorRecordDTOS, targetCollectionName);

        for (int i = 0; i < knowledgeSegments.size(); i++) {
            KnowledgeSegment knowledgeSegment = knowledgeSegments.get(i);
            knowledgeSegment.setVectorId(vectorIds.get(i).toString());
            knowledgeSegment.setVectorIndex(i * 10);
        }
        knowledgeSegmentMapper.insertBatch(knowledgeSegments);

        targetDoc.setStatus(Constants.KNOWLEDGE_DOC_STATUS.success.name());
        knowledgeDocMapper.updateById(targetDoc);

    }

    public void updateDocGroup(KnowledgeDocMoveDTO dto) {
        List<KnowledgeDoc> docs = knowledgeDocMapper.selectByIds(dto.getKnowledgeDocId());
        for (KnowledgeDoc doc : docs) {
            doc.setGroupId(dto.getKnowledgeGroupId());
            knowledgeDocMapper.updateById(doc);
        }
    }

    public List<KnowledgeAllInOneDTO> queryKnowledgeAllInOne(KnowledgeAllInOneQueryDTO dto) {
        return knowledgeDocMapper.queryKnowledgeAllInOne(dto);
    }

    public void updateKnowledgeAfterGroupUpdate(KnowledgeGroupDTO dto) {
        Knowledge knowledge = knowledgeMapper.selectById(dto.getKnowledgeId());
        knowledge.setExpireOption(dto.getExpireOption());
        if (StrUtil.isNotBlank(dto.getStartTimeStr())) {
            dto.setStartTime(LocalDate.parse(dto.getStartTimeStr()).atTime(0, 0, 0));
            knowledge.setStartTime(dto.getStartTime());
        }
        if (StrUtil.isNotBlank(dto.getEndTimeStr())) {
            dto.setEndTime(LocalDate.parse(dto.getEndTimeStr()).atTime(23, 59, 59));
            knowledge.setEndTime(dto.getEndTime());
        }
        knowledge.setUpdateBy(dto.getUserId());
        knowledgeMapper.updateById(knowledge);

        AiResource resource = resourceMapper.selectById(knowledge.getResourceId());
        resource.setName(dto.getName());
        resource.setDescription(dto.getDescription());
        resource.setLogo(dto.getLogo());
        resource.setUpdateBy(dto.getUserId());
        resourceMapper.updateById(resource);
    }

    public Map<String, Object> getKnowledgeDetailByGroup(Long knowledgeId) {
        Map<String, Object> result = new HashMap<>();
        Knowledge knowledge = knowledgeMapper.selectById(knowledgeId);
        Long resourceId = knowledge.getResourceId();
        AiResource resource = resourceMapper.selectById(resourceId);

        result.put("name", resource.getName());
        result.put("description", resource.getDescription());
        result.put("logo", resource.getLogo());
        result.put("expireOption", knowledge.getExpireOption());
        if (knowledge.getStartTime() != null) {
            result.put("startTime", knowledge.getStartTime().format(DateTimeFormatter.ISO_LOCAL_DATE));
        }
        if (knowledge.getEndTime() != null) {
            result.put("endTime", knowledge.getEndTime().format(DateTimeFormatter.ISO_LOCAL_DATE));
        }
        return result;
    }

    public List<KnowledgeDocDTO> docSearchNameByIds(Set<Long> searchDocNameIds) {
        return knowledgeDocMapper.docSearchNameByIds(searchDocNameIds);
    }

    public KnowledgeDoc searchKnowledgeDocIdByFileName(Long knowledgeId, String filename) {
        KnowledgeDoc knowledgeDoc = knowledgeDocMapper.selectOne(new QueryWrapper<KnowledgeDoc>()
                .eq("knowledge_id", knowledgeId)
                .eq("file_name", filename)
                .last(" limit 1")
        );
        return knowledgeDoc;
    }

    public boolean checkSameSegment(Long knowledgeId, Long docId, String input) {
        Long num = knowledgeSegmentMapper.selectCount(new QueryWrapper<KnowledgeSegment>()
                .eq("knowledge_id", knowledgeId)
                .eq("doc_id", docId)
                .eq("text", input)
                .last(" limit 1")
        );
        return num > 0;
    }

    public List<Map<String, Object>> batchGetKnowledgeDocTree(KnowledgeDocBatchDTO dto) {
        List<Map<String, Object>> result = new ArrayList<>();
        List<KnowledgeDoc> docs = getKnowledgeDoc(dto.getKnowledgeDocIds());
        for (KnowledgeDoc doc : docs) {
            Map<String, Object> map = new HashMap<>();
            map.put("docId", doc.getId());
            map.put("doc", doc.getFileName());
            Knowledge knowledge = knowledgeMapper.selectById(doc.getKnowledgeId());
            AiResource resource = resourceMapper.selectById(knowledge.getResourceId());
            map.put("knowledgeName", resource.getName());
            map.put("knowledgeType", knowledge.getType());
            if (StrUtil.equals(knowledge.getType(), Constants.KNOWLEDGE_TYPE.team.name())) {
                KnowledgeTeamMember knowledgeTeamMember = knowledgeTeamMemberMapper.selectOne(
                        new LambdaQueryWrapper<KnowledgeTeamMember>()
                                .eq(KnowledgeTeamMember::getKnowledgeId, doc.getKnowledgeId())
                                .eq(KnowledgeTeamMember::getUserId, StpClientUserUtil.getLoginIdAsLong())
                                .last("LIMIT 1")
                );
                if (knowledgeTeamMember != null && knowledgeTeamMember.getRole().equals("owner")) {
                    map.put("knowledgeOwner", true);
                } else {
                    map.put("knowledgeOwner", false);
                }
            }

            KnowledgeGroup knowledgeGroup = knowledgeGroupMapper.selectById(doc.getGroupId());
            map.put("groupName", knowledgeGroup.getName());
            result.add(map);
        }
        return result;
    }

    @Async
    public void batchImportFromAnotherKnowledge(KnowledgeDocBatchImportDTO dto) {
        Knowledge targetKnowledge = knowledgeMapper.selectById(dto.getTargetKnowledgeId());
        if (targetKnowledge == null) {
            throw new ValidateException("目标知识库不存在");
        }

        AiResource targetResource = resourceMapper.selectById(targetKnowledge.getResourceId());
        if (targetResource == null) {
            throw new ValidateException("目标知识库不存在");
        }
        Knowledge sourceKnowledge = knowledgeMapper.selectById(dto.getSourceKnowledgeId());
        if (sourceKnowledge == null) {
            throw new ValidateException("来源文档所对应的知识库不存在:{}", dto.getSourceKnowledgeId());
        }

        AiResource sourceResource = resourceMapper.selectById(sourceKnowledge.getResourceId());
        if (sourceResource == null) {
            throw new ValidateException("来源知识库所对应的资源不存在:{}", dto.getSourceKnowledgeId());
        }

        Long targetEmbeddedId = targetKnowledge.getEmbeddedId();
        Long sourceEmbeddedId = sourceKnowledge.getEmbeddedId();
        EmbeddedModel embeddedModel = null;
        if (!sourceEmbeddedId.equals(targetEmbeddedId)) {
            // 向量模型不一致，需要重新向量化
            embeddedModel = embeddedModelMapper.selectById(targetEmbeddedId);
            if (embeddedModel == null) {
                throw new RuntimeException("向量模型不存在");
            }
        }


        List<Map<String, Object>> result = new ArrayList<>();
        List<KnowledgeDoc> copyDocList = new ArrayList<>();
        Set<Long> sourceDocIdSet = new HashSet<>();
        if (dto.getSourceDocIds() != null && !dto.getSourceDocIds().isEmpty()) {
            for (Long docId : dto.getSourceDocIds()) {
                KnowledgeDoc sourceDoc = knowledgeDocMapper.selectById(docId);
                if (sourceDoc == null) {
                    throw new ValidateException("来源文档不存在:{}", docId);
                }

                if (!StrUtil.equals(sourceDoc.getStatus(), Constants.KNOWLEDGE_DOC_STATUS.success.name())) {
                    throw new ValidateException("来源文档未处理完成:{}", docId);
                    // 可以忽略？
                }
                sourceDocIdSet.add(sourceDoc.getId());
                copyDocList.add(sourceDoc);
            }
        } else {
            LambdaQueryWrapper<KnowledgeDoc> lambda = new LambdaQueryWrapper<>();
            lambda = lambda.eq(KnowledgeDoc::getKnowledgeId, sourceKnowledge.getId());
            lambda = lambda.eq(KnowledgeDoc::getStatus, Constants.KNOWLEDGE_DOC_STATUS.success.name());
            List<KnowledgeDoc> sourceDocList = knowledgeDocMapper.selectList(lambda);
            for (KnowledgeDoc sourceDoc : sourceDocList) {
                sourceDocIdSet.add(sourceDoc.getId());
            }
            copyDocList.addAll(sourceDocList);

        }
        for (KnowledgeDoc sourceDoc : copyDocList) {
            KnowledgeDoc targetDoc = new KnowledgeDoc();
            BeanUtil.copyProperties(sourceDoc, targetDoc);
            targetDoc.setId(null);
            targetDoc.setKnowledgeId(targetKnowledge.getId());
            targetDoc.setVisible(true);
            targetDoc.setStatus(Constants.KNOWLEDGE_DOC_STATUS.embedded.name());
            knowledgeDocMapper.insert(targetDoc);

            Map<String, Object> moveMap = new HashMap<>();
            moveMap.put("sourceCollectionName", getCollectionName(sourceResource.getCode(), sourceResource.getId()));
            moveMap.put("targetCollectionName", getCollectionName(targetResource.getCode(), targetResource.getId()));
            moveMap.put("sourceDoc", sourceDoc);
            moveMap.put("targetDoc", targetDoc);
            moveMap.put("targetDocId", targetDoc.getId());
            moveMap.put("sourceDocId", sourceDoc.getId());
            result.add(moveMap);
        }
        List<Long> targetDocIds = new ArrayList<>();
        Map<Long, List<KnowledgeSegment>> mapDoc2Seg = new HashMap<>();
        if (embeddedModel != null) {
            for (Map<String, Object> resultMap : result) {
                Long id = (Long) resultMap.get("sourceDocId");
                mapDoc2Seg.put(id, new LinkedList<>());
            }
            List<KnowledgeSegment> knowledgeSegments = knowledgeSegmentMapper.selectList(new LambdaQueryWrapper<KnowledgeSegment>().in(
                    KnowledgeSegment::getDocId, sourceDocIdSet
            ));
            for (KnowledgeSegment knowledgeSegment : knowledgeSegments) {
                mapDoc2Seg.get(knowledgeSegment.getDocId()).add(knowledgeSegment);
            }
        }


        for (Map<String, Object> resultMap : result) {
            targetDocIds.add((Long) resultMap.get("targetDocId"));
            if (embeddedModel != null) {
                // 查询全部片段
                List<KnowledgeSegment> segList = mapDoc2Seg.get((Long) resultMap.get("sourceDocId"));

                //  查原本数据重新向量化，塞入
                baseCopySegmentAndVector(embeddedModel, segList, dto.getTargetKnowledgeId(),
                        (String) resultMap.get("sourceCollectionName"),
                        (String) resultMap.get("targetCollectionName"),
                        (KnowledgeDoc) resultMap.get("sourceDoc"),
                        (KnowledgeDoc) resultMap.get("targetDoc"));
            } else {
                //相同的向量化模型，直接复用原本的
                copySegmentAndVector(dto.getTargetKnowledgeId(),
                        (String) resultMap.get("sourceCollectionName"),
                        (String) resultMap.get("targetCollectionName"),
                        (KnowledgeDoc) resultMap.get("sourceDoc"),
                        (KnowledgeDoc) resultMap.get("targetDoc"));
            }


        }
//        return targetDocIds;
    }

    /*
        查原本数据重新向量化，塞入
     */
    @Async
    public void baseCopySegmentAndVector(EmbeddedModel embeddedModel, List<KnowledgeSegment> segmentList,
                                         Long targetKnowledgeId, String sourceCollectionName, String targetCollectionName,
                                         KnowledgeDoc sourceDoc, KnowledgeDoc targetDoc) {
//        List<String> dynamicFields = List.of("vector", "vectorExt", "text", "pageNumber");

        //先批量向量化，先处理文本的，再处理概述的，不然忽略，需要对应下面的关系
        List<String> segStrList = new LinkedList<>();
        // 插入和取出逻辑需要保持一样，index才能对应
        for (KnowledgeSegment segment : segmentList) {
            if (StringUtils.isNotBlank(segment.getText())) {
                segStrList.add(segment.getText());
            }
        }
        // 再处理概述的
        for (KnowledgeSegment segment : segmentList) {
            if (StringUtils.isNotBlank(segment.getOverview())) {
                segStrList.add(segment.getOverview());
            }
        }
        if (segStrList.isEmpty()) {
            // 忽略这个文件
            return;
        }
        EmbeddedCallDTO dtoOverview = new EmbeddedCallDTO();
        dtoOverview.setModelType(embeddedModel.getCode());
        dtoOverview.setSegments(segStrList);
        dtoOverview.setUrl(embeddedModel.getUrl());
        dtoOverview.setMethodName(embeddedModel.getMethodName());
        List<List<Float>> segmentVectors = dynamicEmbeddedCallService.callEmbedded(dtoOverview);

        // 处理完后的映射
        Map<Integer, List<Float>> textVectorMap = new HashMap<>();
        Map<Integer, List<Float>> textVectorExtMap = new HashMap<>();

        // 插入和取出逻辑需要保持一样，index才能对应
        int index = 0;
        for (int i = 0; i < segmentList.size(); i++) {
            KnowledgeSegment segment = segmentList.get(i);
            if (StringUtils.isNotBlank(segment.getText())) {
                textVectorMap.put(i, segmentVectors.get(index));
                index += 1;
            }
        }
        for (int i = 0; i < segmentList.size(); i++) {
            KnowledgeSegment segment = segmentList.get(i);
            if (StringUtils.isNotBlank(segment.getOverview())) {
                textVectorExtMap.put(i, segmentVectors.get(index));
                index += 1;
            }
        }
        List<VectorRecordDTO> vectorRecordDTOS = new ArrayList<>();
        List<KnowledgeSegment> knowledgeSegments = new ArrayList<>();
        for (int i = 0; i < segmentList.size(); i++) {
            KnowledgeSegment segment = segmentList.get(i);
            VectorRecordDTO vectorRecordDTO = new VectorRecordDTO();
            vectorRecordDTO.setVector(textVectorMap.get(i));
            vectorRecordDTO.setVectorExt(textVectorExtMap.getOrDefault(i, textVectorMap.get(i)));// 没概述就默认文本

            vectorRecordDTO.setText(segment.getText());

//            vectorRecordDTO.setTag((String) map.get("tag"));
//            vectorRecordDTO.setOverview((String) map.get("overview"));

            vectorRecordDTO.setUrl(targetDoc.getFileUrl());
            vectorRecordDTO.setDocId(targetDoc.getId());
            vectorRecordDTO.setGroupId(targetDoc.getGroupId());
            vectorRecordDTO.setDoc(targetDoc.getFileName());
            vectorRecordDTO.setCreateBy(targetDoc.getCreateBy());
            vectorRecordDTO.setPageNumber(segment.getPageNumber());
            vectorRecordDTO.setSegId(null);

            vectorRecordDTOS.add(vectorRecordDTO);


            KnowledgeSegment knowledgeSegment = new KnowledgeSegment();
            knowledgeSegment.setDocId(targetDoc.getId());
            knowledgeSegment.setDoc(targetDoc.getFileName());
            knowledgeSegment.setKnowledgeId(targetKnowledgeId);
            knowledgeSegment.setText(segment.getText());
            knowledgeSegment.setPageNumber(segment.getPageNumber());
            knowledgeSegment.setCreateBy(sourceDoc.getUpdateBy());
            knowledgeSegment.setUpdateBy(sourceDoc.getUpdateBy());
            knowledgeSegments.add(knowledgeSegment);
        }
        List<Object> vectorIds = milvusClient.insert(vectorRecordDTOS, targetCollectionName);

        for (int i = 0; i < knowledgeSegments.size(); i++) {
            KnowledgeSegment knowledgeSegment = knowledgeSegments.get(i);
            knowledgeSegment.setVectorId(vectorIds.get(i).toString()); //其实可以考虑反向关联的
            knowledgeSegment.setVectorIndex(i * 10);
        }
        knowledgeSegmentMapper.insertBatch(knowledgeSegments);

        targetDoc.setStatus(Constants.KNOWLEDGE_DOC_STATUS.success.name());
        knowledgeDocMapper.updateById(targetDoc);

    }

    public void batchClearFailedDoc(Long knowledgeId) {
        // 挑选出失败的文件，然后处理删除并且把前端也删了
        List<KnowledgeDoc> knowledgeDocs = knowledgeDocMapper.selectList(new LambdaQueryWrapper<KnowledgeDoc>()
                .eq(KnowledgeDoc::getStatus, Constants.KNOWLEDGE_DOC_STATUS.failed.name())
                .eq(KnowledgeDoc::getDeleted, false)
        );
        if (knowledgeDocs.isEmpty()) {
            return;
        }
        List<Long> docIds = new ArrayList<>(knowledgeDocs.size());
        for (KnowledgeDoc knowledgeDoc : knowledgeDocs) {
            docIds.add(knowledgeDoc.getId());
        }
        knowledgeDocMapper.deleteByIds(docIds);
        knowledgeSegmentMapper.delete(new LambdaQueryWrapper<KnowledgeSegment>().in(
                KnowledgeSegment::getDocId, docIds
        ));
    }

    public List<KnowledgeDocDTO> searchDocListByQuery(KnowLedgeDocSearchDTO dto) {
        List<VectorResultDTO> resultDTOS = docSearchByDto(dto);
        // 过滤出 文档id
        Set<Long> docIds = new HashSet<>();
        for (VectorResultDTO resultDTO : resultDTOS) {
            if(resultDTO.getDocId()!=null)
                docIds.add(resultDTO.getDocId());
        }
        if(docIds.isEmpty())
            return null;
        return knowledgeDocMapper.selectDocBaseInfoByIds(docIds,dto.getDocSourceType());

    }
    public List<VectorResultDTO> searchSegmentListByQuery(KnowLedgeDocSearchDTO dto) {
        List<VectorResultDTO> resultDTOS = docSearchByDto(dto);
        // 过滤出 文档id
        return resultDTOS;
    }

}
