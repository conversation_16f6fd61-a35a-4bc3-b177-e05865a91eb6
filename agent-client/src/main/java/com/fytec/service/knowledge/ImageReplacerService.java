package com.fytec.service.knowledge;

import com.fytec.config.UploadProperties;
import com.fytec.file.*;
import com.fytec.util.FileUtil;
import com.fytec.util.MinioUtil;
import com.fytec.util.UUIDGenerator;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.model.PicturesTable;
import org.apache.poi.hwpf.usermodel.Range;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.drawingml.x2006.main.CTBlipFillProperties;
import org.openxmlformats.schemas.drawingml.x2006.picture.CTPicture;
import org.springframework.stereotype.Service;
import org.apache.poi.hwpf.usermodel.*;

import java.io.*;
import java.net.URI;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
@AllArgsConstructor
public class ImageReplacerService {

    private final UploadProperties uploadProperties;

    private final MinioUtil minioUtil;


    @SneakyThrows
    private byte[] switchBytes(String fileUrl) {
        URI uri = new URI(fileUrl);
        URL url = uri.toURL();
        InputStream inputStream = url.openStream();
        return inputStream.readAllBytes();
    }


    @SneakyThrows
    public Map<Integer, String> parseDocument(String fileUrl, String fileType) {
        return switch (fileType) {
            case "txt" -> TxtFileParseUtils.parseUrlPerPage(fileUrl);
            case "pdf" -> PdfFileParseUtils.parseUrlPerPage(fileUrl);
            case "doc" -> replaceDocImage(fileUrl);
            case "docx" -> replaceDocxImage(fileUrl);
            case "md" -> MdFileParseUtils.parseUrlPerPage(fileUrl);
            case "png", "jpg", "jpeg" -> ImageFileParseUtils.parseUrlPerPage(fileUrl);
            // 目前和上面方法一样
            case "xls", "xlsx" -> XlsxFileParseUtils.parseUrlPerPage(fileUrl, "1");
            default -> null;
        };
    }

    private Map<Integer, String> replaceDocImage(String fileUrl) {
        try {
            URI uri = new URI(fileUrl);
            URL url = uri.toURL();
            InputStream inputStream = url.openStream();
            StringBuilder result = new StringBuilder();
            HWPFDocument document = new HWPFDocument(inputStream);
            Range range = document.getRange();
            PicturesTable picturesTable = document.getPicturesTable();
            for (int i = 0; i < range.numParagraphs(); i++) {
                Paragraph paragraph = range.getParagraph(i);
                // 检查段落是否属于表格
                if (paragraph.isInTable()) {
                    Table table = range.getTable(paragraph);
                    for (int rowIndex = 0; rowIndex < table.numRows(); rowIndex++) {
                        TableRow row = table.getRow(rowIndex);
                        for (int cellIndex = 0; cellIndex < row.numCells(); cellIndex++) {
                            TableCell cell = row.getCell(cellIndex);
                            String cellText = cell.text().trim();
                            result.append(cellText).append("\t");

                            for (int j = 0; j < cell.numCharacterRuns(); j++) {
                                CharacterRun run = cell.getCharacterRun(j);
                                if (picturesTable.hasPicture(run)) {
                                    Picture picture = picturesTable.extractPicture(run, false);
                                    if (picture == null) {
                                        System.out.println("无法获取图片数据: " + picture.suggestFullFileName());
                                        continue;
                                    }
                                    byte[] imageBytes = picture.getContent();
                                    String fileName = UUIDGenerator.getUUID() + ".jpg";
                                    // 保存图片到服务器
                                    String imageFilePath = saveFileToServer(imageBytes, fileName);
                                    String img= "<img src=\""+imageFilePath+"\" alt=\""+picture.suggestFullFileName()+"\">";
                                    result.append(img).append("\n");
                                }
                            }

                        }
                        result.append("\n");
                    }
                    i += table.numParagraphs() - 1; // 跳过表格中的其他段落
                } else {
                    // 处理普通段落
                    String text = paragraph.text().trim();
                    result.append(text).append("\n");
                    for (int j = 0; j < paragraph.numCharacterRuns(); j++) {
                        CharacterRun run = paragraph.getCharacterRun(j);
                        if (picturesTable.hasPicture(run)) {
                            Picture picture = picturesTable.extractPicture(run, false);
                            if (picture == null) {
                                System.out.println("无法获取图片数据: " + picture.suggestFullFileName());
                                continue;
                            }
                            byte[] imageBytes = picture.getContent();
                            String fileName = UUIDGenerator.getUUID() + ".jpg";
                            // 保存图片到服务器
                            String imageFilePath = saveFileToServer(imageBytes, fileName);
                            String img= "<img src=\""+imageFilePath+"\" alt=\""+picture.suggestFullFileName()+"\">";
                            result.append(img).append("\n");
                        }
                    }
                }
            }
            Map<Integer, String> map = new HashMap<>();
            map.putIfAbsent(1, result.toString());
            return map;
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private Map<Integer, String>  replaceDocxImage(String fileUrl) {
        try {
            URI uri = new URI(fileUrl);
            URL url = uri.toURL();
            InputStream inputStream = url.openStream();
            StringBuilder text = new StringBuilder();
            XWPFDocument document = new XWPFDocument(inputStream);
            List<IBodyElement> bodyElements = document.getBodyElements();
            for (IBodyElement element : bodyElements) {
                if (element instanceof XWPFParagraph paragraph) {
                    // 处理段落
                    text.append(paragraph.getText()).append("\n");
                    // 检查段落中是否包含图片
                    for (XWPFRun run : paragraph.getRuns()) {
                        if (!run.getEmbeddedPictures().isEmpty()) {
                            for (XWPFPicture picture : run.getEmbeddedPictures()) {
                                // 获取图片的关系 ID
                                CTPicture ctPicture = picture.getCTPicture();
                                CTBlipFillProperties blipFill = ctPicture.getBlipFill();
                                String relationshipId = blipFill.getBlip().getEmbed();

                                // 获取图片数据
                                XWPFPictureData pictureData = picture.getPictureData();
                                if (pictureData == null) {
                                    System.out.println("无法获取图片数据，关系 ID: " + relationshipId);
                                    continue;
                                }
                                byte[] imageBytes = pictureData.getData();
                                String fileName = UUIDGenerator.getUUID() + ".jpg";
                                // 保存图片到服务器

                                String imageFilePath = saveFileToServer(imageBytes, fileName);

//                            run.setText("", 0); // 清空原内容
                                String img= "<img src=\""+imageFilePath+"\" alt=\""+pictureData.getFileName()+"\">";
//                            run.setText(img, 0);
                                text.append(img).append("\n");
                            }
                        }
                    }

                } else if (element instanceof XWPFTable table) {
                    for (XWPFTableRow row : table.getRows()) {
                        for (XWPFTableCell cell : row.getTableCells()) {
                            text.append(cell.getText()).append("\t");
                        }
                        text.append("\n");
                    }
                }
            }
            Map<Integer, String> map = new HashMap<>();
            map.putIfAbsent(1, text.toString());
            return map;
        }catch (Exception e) {
            e.printStackTrace();
        }
        return null;

    }

    private String saveFileToServer(byte[] fileBytes,String fileName) {
        if("minio".equalsIgnoreCase(uploadProperties.getStorage())){
            minioUtil.upload(fileBytes, fileName);
            return minioUtil.getUrl(fileName);
        }else{
            String path = uploadProperties.getLocalServerPath();
            return uploadProperties.getFileServerPath() + FileUtil.saveFile(path, fileBytes, fileName, uploadProperties.getFileGroup());
        }
    }

}