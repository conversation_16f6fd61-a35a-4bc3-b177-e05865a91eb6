package com.fytec.service.sso;

import cn.dev33.satoken.secure.BCrypt;
import cn.dev33.satoken.stp.SaTokenInfo;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fytec.config.SsoProperties;
import com.fytec.dto.SSOLoginDTO;
import com.fytec.entity.system.SysRole;
import com.fytec.entity.system.SysUser;
import com.fytec.entity.system.SysUserRole;
import com.fytec.mapper.system.SysRoleMapper;
import com.fytec.mapper.system.SysUserMapper;
import com.fytec.mapper.system.SysUserRoleMapper;
import com.fytec.satoken.StpClientUserUtil;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class SSOLoginService {

    private final SysUserMapper sysUserMapper;
    private final SysUserRoleMapper sysUserRoleMapper;
    private final SysRoleMapper sysRoleMapper;
    private final SsoProperties ssoProperties;

    //    @SneakyThrows
    public SaTokenInfo login4SipEduSSO(SSOLoginDTO loginDTO) {
        String appId = loginDTO.getAppId();
        String appSecret = loginDTO.getAppSecret();
        String apiToken = "";
        long timestamp = Instant.now().getEpochSecond();
        String sign = DigestUtil.md5Hex(appId + appSecret + timestamp + apiToken);

        Map<String, String> headers = new HashMap<>();
        headers.put("appId", appId);
        headers.put("timestamp", String.valueOf(timestamp));
        headers.put("apiToken", apiToken);
        headers.put("sign", sign);
        headers.put(HttpHeaders.CONTENT_TYPE, "application/json");

        Map<String, Object> body = new HashMap<>();
        body.put("accessToken", loginDTO.getAccessToken());

        String url = StrUtil.format("http://{}/portal/app-api/sys/oauth/user_info", ssoProperties.getAuthHost());
        HttpResponse<String> response = null;
        try {
            response = Unirest.post(url)
                    .headers(headers)
                    .body(JSON.toJSONString(body))
                    .asString();
        } catch (UnirestException e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
//        log.info("login status:" + response.getStatus());
        if (response.getStatus() != 200) {
            throw new ValidateException("单点登录失败");
        }
//        log.info("login body:" + response.getBody());

        JSONObject jsonObject = JSON.parseObject(response.getBody());
//        if (jsonObject.containsKey("retCode")) {
//            String retCode = jsonObject.getString("retCode");
//            if (!StrUtil.equals("000000", retCode)) {
//                throw new ValidateException(jsonObject.getString("retDesc"));
//            }
//        } else {
//            // 登录问题
////            {"code":"100027","message":"请求接口未订阅,认证失败"}
//            if ("100027".equals(jsonObject.getString("code"))) {
//                throw new ValidateException(jsonObject.getString("message"));
//            }
//            throw new ValidateException("单点登录失败");
//        }
        if (jsonObject.containsKey("code")) {
            String retCode = jsonObject.getString("code");
            if (!StrUtil.equals("0", retCode)) {
                throw new ValidateException(jsonObject.containsKey("message") ?
                        jsonObject.getString("message") : jsonObject.getString("msg"));
            }
        } else {
            throw new ValidateException("单点登录失败");
        }

//        log.info("login success:" + loginDTO.getAccessToken());

        JSONObject data = jsonObject.getJSONObject("data");
        String name = data.getString("truename");
        String mobile = data.getString("mobile");
        String thirdUserId = data.getString("id");
        String loginName = data.getString("username");
//        String email = data.getString("email");

        Long userId = createOrFetchByThirdUserId(thirdUserId, data.getString("truename"),
                data.getString("username"), data.getString("mobile"));
        StpClientUserUtil.login(userId);
        return StpClientUserUtil.getTokenInfo();
    }

    @SneakyThrows
    public void logout4SipEduSSO(SSOLoginDTO loginDTO) {
        String appId = loginDTO.getAppId();
        String appSecret = loginDTO.getAppSecret();
        String apiToken = "";
        long timestamp = Instant.now().getEpochSecond();
        String sign = DigestUtil.md5Hex(appId + appSecret + timestamp + apiToken);

        Map<String, String> headers = new HashMap<>();
        headers.put("appId", appId);
        headers.put("timestamp", String.valueOf(timestamp));
        headers.put("apiToken", apiToken);
        headers.put("sign", sign);
        headers.put(HttpHeaders.CONTENT_TYPE, "application/json");

        Map<String, Object> body = new HashMap<>();
        body.put("accessToken", loginDTO.getAccessToken());

        String url = StrUtil.format("http://{}/aep/oauth/remove_token", ssoProperties.getAuthHost());
        HttpResponse<String> response = Unirest.post(url)
                .headers(headers)
                .body(JSON.toJSONString(body))
                .asString();
        if (response.getStatus() != 200) {
            throw new ValidateException("单点登录登出失败");
        }
    }

    @SneakyThrows
    public void login4WuJiangEduSSO(HttpServletRequest request, HttpServletResponse response) {
        String accessToken = request.getParameter("access_token");
        if (StrUtil.isBlank(accessToken)) {
            String code = request.getParameter("code");
            if (StrUtil.isNotBlank(code)) {
                String url = StrUtil.format("http://{}/oauth/oauth/token?grant_type=authorization_code&client_id={}&client_secret={}&redirect_uri={}&code={}&scope=app",
                        ssoProperties.getAuthHost(), ssoProperties.getClientId(),
                        ssoProperties.getClientSecret(), ssoProperties.getRedirectUri(), code);
                HttpResponse<String> res = Unirest.get(url).asString();
                JSONObject jsonObject = JSON.parseObject(res.getBody());
                JSONObject user = jsonObject.getJSONObject("user");
                String thirdUserId = user.getString("id");

                Long userId = createOrFetchByThirdUserId(thirdUserId, user.getString("name"),
                        user.getString("username"), user.getString("mobile"));
                StpClientUserUtil.login(userId);
                SaTokenInfo saTokenInfo = StpClientUserUtil.getTokenInfo();
                response.sendRedirect(ssoProperties.getFinalRedirectUri() + "?token=" + saTokenInfo.getTokenValue());
            } else {
                String url = StrUtil.format("http://{}/oauth/oauth/authorize?response_type=code&client_id={}&redirect_uri={}&scope=app",
                        ssoProperties.getAuthHost(), ssoProperties.getClientId(), ssoProperties.getRedirectUri());
                response.sendRedirect(url);
            }
        }
    }


    private Long createOrFetchByThirdUserId(String thirdUserId, String name,
                                            String loginName, String tel) {
        SysUser sysUser = sysUserMapper.selectOne(
                new LambdaQueryWrapper<SysUser>()
                        .eq(SysUser::getThirdUserId, thirdUserId)
                        .last("LIMIT 1")
        );

        if (sysUser == null) {
            sysUser = new SysUser();
            sysUser.setName(name);
            sysUser.setLoginName(loginName);
            sysUser.setTel(tel);
            sysUser.setThirdUserId(thirdUserId);
            sysUser.setPassword(BCrypt.hashpw("3456@erty"));
            sysUser.setStatus("A");
            sysUser.setIsSys("N");
            sysUserMapper.insert(sysUser);
        }

        List<String> roleList = sysUserRoleMapper.selectRoleListByUserId(sysUser.getId());
        if (CollUtil.isEmpty(roleList)) {
            SysRole sysRole = sysRoleMapper.selectOne(
                    new LambdaQueryWrapper<SysRole>()
                            .eq(SysRole::getRole, "member")
                            .last("LIMIT 1")
            );

            if (sysRole != null) {
                SysUserRole sysUserRole = new SysUserRole();
                sysUserRole.setUserId(sysUser.getId());
                sysUserRole.setRoleId(sysRole.getId());//普通成员
                sysUserRoleMapper.insert(sysUserRole);
            } else {
                throw new ValidateException("sso登录角色不存在,请联系管理员！");
            }
        }
        return sysUser.getId();
    }
}
