package com.fytec.service.note;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.constant.Constants;
import com.fytec.dto.knowledge.KnowledgeDocAutoDTO;
import com.fytec.dto.knowledge.KnowledgeFileDTO;
import com.fytec.dto.note.NoteDTO;
import com.fytec.entity.note.Note;
import com.fytec.mapper.note.NoteMapper;
import com.fytec.satoken.StpClientUserUtil;
import com.fytec.service.knowledge.KnowledgeGroupService;
import com.fytec.service.knowledge.KnowledgeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
@Slf4j
@Transactional
@RequiredArgsConstructor
public class NoteService {
    private final NoteMapper noteMapper;
    private final KnowledgeService knowledgeService;
    private final KnowledgeGroupService knowledgeGroupService;

    public Long saveNote(NoteDTO dto) {
        Note note;
        boolean insert = false;
        if (ObjUtil.isEmpty(dto.getId())) {
            note = new Note();
            insert = true;
        } else {
            note = noteMapper.selectById(dto.getId());
            String content = note.getContent();
            String appContent = dto.getAppendContent();
            if (StrUtil.isNotBlank(appContent)) {
                content = StrUtil.format("{}\n\n{}", content, appContent);
                dto.setContent(content);
            }
        }
        BeanUtils.copyProperties(dto, note);
        String title = note.getTitle();

        if (StrUtil.isBlank(note.getTitle())) {
            String now = LocalDate.now().format(DateTimeFormatter.ofPattern("MM.dd"));
            String docName = StrUtil.format("笔记{}", now);
            note.setTitle(docName);
            note.setDocName(docName);
        } else {
            note.setTitle(title.substring(0, Math.min(title.length(), 100)));
            note.setDocName(note.getTitle());

        }

        if (insert) {
            noteMapper.insert(note);
        } else {
            noteMapper.updateById(note);
        }
        return note.getId();
    }

    public void deleteNote(Long id) {
        Note note = noteMapper.selectById(id);
        if (note == null) {
            throw new ValidateException("笔记不存在");
        }
        Long knowledgeDocId = note.getKnowledgeDocId();
        if (knowledgeDocId != null) {
            knowledgeService.deleteDoc(knowledgeDocId+"");
        }
        noteMapper.deleteById(id);
    }

    public Note getNoteDetail(Long id) {
        return noteMapper.selectById(id);

    }

    public List<Map<String, Object>> queryNoteList(NoteDTO dto) {
        LambdaQueryWrapper<Note> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(Note::getCreateBy, StpClientUserUtil.getLoginIdAsLong())
                .orderByDesc(Note::getCreateTime);
        if (StrUtil.isNotBlank(dto.getDocName())) {
            queryWrapper.like(Note::getDocName, dto.getDocName());
        }
        List<Note> Notes = noteMapper.selectList(queryWrapper);
        Map<String, List<Note>> groupMap = groupByDates(Notes);

        List<Map<String, Object>> results = new ArrayList<>();
        for (String key : groupMap.keySet()) {
            Map<String, Object> result = new HashMap<>();
            List<Note> notes = groupMap.get(key);
            result.put("notesDate", key);
            result.put("notes", notes);
            results.add(result);
        }
        return results;
    }

    private Map<String, List<Note>> groupByDates(List<Note> notes) {
        Map<String, List<Note>> groupMap = new LinkedHashMap<>();
        groupMap.put("今天", new ArrayList<>());
        groupMap.put("昨天", new ArrayList<>());
        groupMap.put("7天内", new ArrayList<>());
        groupMap.put("更久", new ArrayList<>());

        LocalDate today = LocalDate.now();
        LocalDate yesterday = today.minusDays(1);
        LocalDate sevenDaysAgo = today.minusDays(7);

        for (Note note : notes) {
            LocalDate date = note.getCreateTime().toLocalDate();
            if (date.isEqual(today)) {
                groupMap.get("今天").add(note);
            } else if (date.isEqual(yesterday)) {
                groupMap.get("昨天").add(note);
            } else if (date.isAfter(sevenDaysAgo)) {
                groupMap.get("7天内").add(note);
            } else {
                groupMap.get("更久").add(note);
            }
        }
        return groupMap;
    }

    public KnowledgeDocAutoDTO addKnowledge(Long id) {
        Note note = getNoteDetail(id);

        Map<String, Long> initResult = knowledgeGroupService.initKnowledgeGroup();
        KnowledgeDocAutoDTO dto = new KnowledgeDocAutoDTO();
        dto.setKnowledgeId(initResult.get("knowledgeId"));
        dto.setDocSourceType(Constants.KNOWLEDGE_DOC_SOURCE_TYPE.note.name());
        KnowledgeFileDTO knowledgeFile = new KnowledgeFileDTO();
        knowledgeFile.setDocType(Constants.KNOWLEDGE_DOC_TYPE.text.name());
        knowledgeFile.setFileName(StrUtil.format("{}.md", note.getDocName()));
        String content = Jsoup.parse(note.getContent()).text();
        long size = content.getBytes(StandardCharsets.UTF_8).length;
        knowledgeFile.setFileContent(content);
        knowledgeFile.setFileSize(size);
        knowledgeFile.setNoteId(note.getId());
        knowledgeFile.setGroupId(initResult.get("defaultGroupId"));
        dto.setFiles(CollUtil.newArrayList(knowledgeFile));
        dto.setUserId(StpClientUserUtil.getLoginIdAsString());
        knowledgeService.autoProcessData(dto);
        return dto;
    }

    public void updateNoteKnowledgeId(Long id, long knowledgeDocId) {
        Note note = getNoteDetail(id);
        note.setKnowledgeDocId(knowledgeDocId);
        noteMapper.updateById(note);
    }

    public void removeKnowledge(Long id) {
        Note note = getNoteDetail(id);
        note.setKnowledgeDocId(null);
        noteMapper.updateById(note);
        knowledgeService.deleteDoc(note.getKnowledgeDocId()+"");
    }

    public Page<Note> queryNotePage(NoteDTO dto, Page<Note> page) {
        LambdaQueryWrapper<Note> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(Note::getCreateBy, StpClientUserUtil.getLoginIdAsLong())
                .orderByDesc(Note::getCreateTime);
        if (StrUtil.isNotBlank(dto.getDocName())) {
            queryWrapper.like(Note::getDocName, dto.getDocName());
        }
        page = noteMapper.selectPage(page, queryWrapper);
        return page;
    }
}
