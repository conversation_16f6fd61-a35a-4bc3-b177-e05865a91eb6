package com.fytec.service.upload;

import com.fytec.config.UploadProperties;
import com.fytec.dto.UploadResultDTO;
import com.fytec.util.FileUtil;
import com.fytec.util.UUIDGenerator;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.validation.ValidationException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import static com.fytec.controller.upload.FileUploadController.fileType;

@Service
@RequiredArgsConstructor
public class FileUploadService {

    private final UploadProperties uploadProperties;

    @SneakyThrows
    public List<UploadResultDTO> uploadFile(HttpServletRequest request) {
        List<UploadResultDTO> result = new ArrayList<>();

        MultipartHttpServletRequest mRequest = (MultipartHttpServletRequest) request;
        Iterator<String> iter = mRequest.getFileNames();
        while (iter.hasNext()) {
            MultipartFile file = mRequest.getFile(iter.next());
            String originalFileName = file.getOriginalFilename();

            //获取文件类型
            String extName = "";
            int lastDotIndex = originalFileName.lastIndexOf(".");
            if (lastDotIndex != -1 && lastDotIndex != 0) {
                extName = originalFileName.substring(lastDotIndex + 1).toLowerCase();
            }
            if (!fileType.contains(extName)) {
                throw new ValidationException("文件类型格式不正确");
            }

            byte[] bytes = file.getBytes();
            String path = uploadProperties.getLocalServerPath();
            String uniqueFileName = UUIDGenerator.getUUID() + FileUtil.getFileEndfix(originalFileName);
            String url = FileUtil.saveFile(path, bytes, uniqueFileName, uploadProperties.getFileGroup());

            UploadResultDTO uploadResult = new UploadResultDTO();
            uploadResult.setFileName(originalFileName);
            uploadResult.setUrl(uploadProperties.getFileServerPath() + url);
            uploadResult.setType(extName);
            uploadResult.setSize(file.getSize());
            result.add(uploadResult);
        }
        if (result.isEmpty()) {
            throw new ValidationException("文件为空");
        }
        return result;
    }
}
