package com.fytec.service.external;

import com.fasterxml.jackson.databind.JsonNode;
import com.fytec.dto.external.IatRequestDTO;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import java.util.function.Function;

/**
 * 流式语音识别服务接口
 * 支持多平台语音识别服务，便于后续扩展和替换
 *
 * <AUTHOR>
 */
public interface StreamIatService {

    /**
     * 识别音频文件（流式处理）
     *
     * @param file 音频文件
     * @param request IAT请求参数DTO
     * @param emitter SSE发射器
     * @return 会话ID
     */
    String recognizeAudioFile(MultipartFile file, IatRequestDTO request, SseEmitter emitter);

    /**
     * 识别音频文件（Controller层专用）
     *
     * @param file 音频文件
     * @param request IAT请求参数DTO
     * @return SSE发射器
     */
    SseEmitter recognizeAudioFile(MultipartFile file, IatRequestDTO request);

    /**
     * 同步语音识别
     *
     * @param file    音频文件
     * @param request IAT请求参数DTO
     * @return 识别结果列表
     */
    Object recognizeAudioFileSync(MultipartFile file, IatRequestDTO request);

    /**
     * 获取服务提供商名称
     *
     * @return 提供商名称
     */
    String getProviderName();

    /**
     * 检查服务是否可用
     *
     * @return 是否可用
     */
    boolean isAvailable();

}