package com.fytec.service.external;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.entity.external.ExternalUseLog;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 外部服务使用日志服务接口
 */
public interface ExternalUseLogService {
    
    /**
     * 保存外部服务使用日志
     * 
     * @param externalUseLog 外部服务使用日志实体
     */
    void saveExternalUseLog(ExternalUseLog externalUseLog);
    
    /**
     * 根据客户端名称统计调用次数
     * 
     * @param clientName 客户端名称
     * @param serviceType 服务类型
     * @return 调用次数
     */
    Long countByClientAndService(String clientName, String serviceType);
    
    /**
     * 分页查询外部服务使用日志
     * 按创建时间倒序排列
     *
     * @param serviceType 服务类型（可选）
     * @param page 分页参数
     * @return 分页结果
     */
    Page<ExternalUseLog> queryExternalUseLogPage(String serviceType, Page<ExternalUseLog> page);
    
    /**
     * 根据调用时间范围查询外部服务使用日志
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 查询结果列表
     */
    List<ExternalUseLog> findByCallTimeBetween(LocalDateTime startTime, LocalDateTime endTime);
}
