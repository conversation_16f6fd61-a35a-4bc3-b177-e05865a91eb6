package com.fytec.service.external;

import com.fasterxml.jackson.databind.JsonNode;
import com.fytec.dto.external.TtsRequestDTO;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import java.util.function.Function;

/**
 * 流式语音合成服务接口
 * 支持多平台语音合成服务，便于后续扩展和替换
 *
 * <AUTHOR>
 */
public interface StreamTtsService {

    /**
     * 文本转语音（使用完整参数）
     *
     * @param request TTS请求参数DTO
     * @param emitter SSE发射器
     * @return 会话ID
     */
    String synthesizeText(TtsRequestDTO request, SseEmitter emitter);

    /**
     * 文本转语音（Controller层专用，使用完整参数）
     *
     * @param request TTS请求参数DTO
     * @return SSE发射器
     */
    SseEmitter synthesizeText(TtsRequestDTO request);

    /**
     * 同步文本转语音（使用完整参数）
     *
     * @param request TTS请求参数DTO
     * @return 合成结果
     */
    Object synthesizeTextSync(TtsRequestDTO request);

    /**
     * 文本转语音并返回字节数组
     *
     * @param request TTS请求参数DTO
     * @return 音频字节数组
     */
    byte[] synthesizeTextToFileBytes(TtsRequestDTO request);


    /**
     * 获取服务提供商名称
     *
     * @return 提供商名称
     */
    String getProviderName();

    /**
     * 检查服务是否可用
     *
     * @return 是否可用
     */
    boolean isAvailable();
}
