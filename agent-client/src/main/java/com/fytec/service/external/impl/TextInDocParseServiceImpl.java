package com.fytec.service.external.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.fytec.config.TextInProperties;
import com.fytec.dto.external.TextInParseRequestDTO;
import com.fytec.service.external.DocParseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * TextIn文档解析服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TextInDocParseServiceImpl implements DocParseService<TextInParseRequestDTO> {

    private final TextInProperties textInProperties;

    @Override
    public Object parseDocument(MultipartFile file, TextInParseRequestDTO params) {
        if (file == null) {
            throw new IllegalArgumentException("文件不能为空");
        }

        try {
            byte[] fileContent = file.getBytes();
            return executeParseRequest(fileContent, params, "application/octet-stream");
        } catch (IOException e) {
            log.error("读取文件内容失败", e);
            throw new RuntimeException("读取文件内容失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Object parseDocumentByUrl(String url, TextInParseRequestDTO params) {
        if (StrUtil.isBlank(url)) {
            throw new IllegalArgumentException("文件URL不能为空");
        }

        return executeParseRequest(url.getBytes(), params, "text/plain");
    }

    @Override
    public String getProviderName() {
        return "TextIn";
    }

    @Override
    public boolean isAvailable() {
        return Optional.ofNullable(textInProperties.getEnabled()).orElse(false) &&
               StrUtil.isNotBlank(textInProperties.getAppId()) &&
               StrUtil.isNotBlank(textInProperties.getSecretCode());
    }

    /**
     * 执行解析请求
     *
     * @param content     文件内容或URL
     * @param requestDTO  请求参数
     * @param contentType 内容类型
     * @return 解析结果
     */
    private Object executeParseRequest(byte[] content, TextInParseRequestDTO requestDTO, String contentType) {
        if (!isAvailable()) {
            throw new RuntimeException("TextIn服务不可用，请检查配置");
        }

        String baseUrl = textInProperties.getBaseUrl() + textInProperties.getParseApi();
        Map<String, Object> params = buildRequestParams(requestDTO);

        try {
            // 构建带参数的URL
            String finalUrl = baseUrl;
            if (!params.isEmpty()) {
                List<String> paramList = params.entrySet().stream()
                        .filter(entry -> entry.getValue() != null)
                        .map(entry -> entry.getKey() + "=" + entry.getValue())
                        .collect(Collectors.toList());
                if (!paramList.isEmpty()) {
                    finalUrl = baseUrl + "?" + String.join("&", paramList);
                }
            }

            try (HttpResponse response = HttpUtil.createPost(finalUrl)
                    .header("x-ti-app-id", textInProperties.getAppId())
                    .header("x-ti-secret-code", textInProperties.getSecretCode())
                    .header("Content-Type", contentType)
                    .timeout(textInProperties.getConnectTimeout())
                    .setReadTimeout(textInProperties.getReadTimeout())
                    .body(content)
                    .execute()) {

                if (!response.isOk()) {
                    log.error("TextIn API调用失败，状态码: {}, 响应: {}", response.getStatus(), response.body());
                    throw new RuntimeException("TextIn API调用失败: " + response.getStatus());
                }

                String responseBody = response.body();
                log.debug("TextIn API响应: {}", responseBody);

                return JSONUtil.parse(responseBody);
            }

        } catch (Exception e) {
            log.error("调用TextIn API失败", e);
            throw new RuntimeException("调用TextIn API失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建请求参数
     *
     * @param requestDTO 请求DTO
     * @return 请求参数Map
     */
    private Map<String, Object> buildRequestParams(TextInParseRequestDTO requestDTO) {
        Map<String, Object> params = new HashMap<>();
        
        // 只传递用户明确设置的参数，让API接口自己处理默认值
        Optional.ofNullable(requestDTO.getPdfPwd()).ifPresent(v -> params.put("pdf_pwd", v));
        Optional.ofNullable(requestDTO.getPageStart()).ifPresent(v -> params.put("page_start", v));
        Optional.ofNullable(requestDTO.getPageCount()).ifPresent(v -> params.put("page_count", v));
        Optional.ofNullable(requestDTO.getParseMode()).ifPresent(v -> params.put("parse_mode", v));
        Optional.ofNullable(requestDTO.getDpi()).ifPresent(v -> params.put("dpi", v));
        Optional.ofNullable(requestDTO.getApplyDocumentTree()).ifPresent(v -> params.put("apply_document_tree", v));
        Optional.ofNullable(requestDTO.getTableFlavor()).ifPresent(v -> params.put("table_flavor", v));
        Optional.ofNullable(requestDTO.getGetImage()).ifPresent(v -> params.put("get_image", v));
        Optional.ofNullable(requestDTO.getImageOutputType()).ifPresent(v -> params.put("image_output_type", v));
        Optional.ofNullable(requestDTO.getApplyImageAnalysis()).ifPresent(v -> params.put("apply_image_analysis", v));
        Optional.ofNullable(requestDTO.getParatextMode()).ifPresent(v -> params.put("paratext_mode", v));
        Optional.ofNullable(requestDTO.getFormulaLevel()).ifPresent(v -> params.put("formula_level", v));
        Optional.ofNullable(requestDTO.getApplyMerge()).ifPresent(v -> params.put("apply_merge", v));
        Optional.ofNullable(requestDTO.getMarkdownDetails()).ifPresent(v -> params.put("markdown_details", v));
        Optional.ofNullable(requestDTO.getPageDetails()).ifPresent(v -> params.put("page_details", v));
        Optional.ofNullable(requestDTO.getRawOcr()).ifPresent(v -> params.put("raw_ocr", v));
        Optional.ofNullable(requestDTO.getCharDetails()).ifPresent(v -> params.put("char_details", v));
        Optional.ofNullable(requestDTO.getCatalogDetails()).ifPresent(v -> params.put("catalog_details", v));
        Optional.ofNullable(requestDTO.getGetExcel()).ifPresent(v -> params.put("get_excel", v));
        Optional.ofNullable(requestDTO.getCropImage()).ifPresent(v -> params.put("crop_image", v));
        Optional.ofNullable(requestDTO.getRemoveWatermark()).ifPresent(v -> params.put("remove_watermark", v));
        Optional.ofNullable(requestDTO.getApplyChart()).ifPresent(v -> params.put("apply_chart", v));
        
        return params;
    }
}