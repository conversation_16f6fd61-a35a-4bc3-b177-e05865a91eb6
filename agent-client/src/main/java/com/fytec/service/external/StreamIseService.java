package com.fytec.service.external;

import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import java.util.function.Function;

/**
 * 流式语音评测服务接口
 * 支持多平台语音评测服务，便于后续扩展和替换
 *
 * <AUTHOR>
 */
public interface StreamIseService {

    /**
     * 评测音频文件（流式处理）
     *
     * @param text 评测文本
     * @param file 音频文件
     * @param emitter SSE发射器
     * @param timeoutMs 超时时间（毫秒）
     * @return 会话ID
     */
    String evaluateAudioFile(String text, MultipartFile file, SseEmitter emitter, Long timeoutMs);

    /**
     * 评测音频文件（默认超时）
     *
     * @param text 评测文本
     * @param file 音频文件
     * @param emitter SSE发射器
     * @return 会话ID
     */
    default String evaluateAudioFile(String text, MultipartFile file, SseEmitter emitter) {
        return evaluateAudioFile(text, file, emitter, 600000L);
    }

    /**
     * 评测音频文件（Controller层专用）
     *
     * @param text 评测文本
     * @param file 音频文件
     * @param timeout 超时时间（毫秒）
     * @return SSE发射器
     */
    SseEmitter evaluateAudioFile(String text, MultipartFile file, Long timeout);

    /**
     * 同步语音评测
     *
     * @param text    评测文本
     * @param file    音频文件
     * @param timeout 超时时间（毫秒）
     * @return 评测结果列表
     */
    Object evaluateAudioFileSync(String text, MultipartFile file, Long timeout);

    /**
     * 同步语音评测（支持自定义响应处理器）
     *
     * @param text      评测文本
     * @param file      音频文件
     * @param timeout   超时时间（毫秒）
     * @param processor 响应处理器
     * @return 评测结果列表
     */
    default Object evaluateAudioFileSync(String text, MultipartFile file, Long timeout, 
                                        Function<JsonNode, Object> processor) {
        return evaluateAudioFileSync(text, file, timeout);
    }

    /**
     * 获取服务提供商名称
     *
     * @return 提供商名称
     */
    String getProviderName();

    /**
     * 检查服务是否可用
     *
     * @return 是否可用
     */
    boolean isAvailable();
}