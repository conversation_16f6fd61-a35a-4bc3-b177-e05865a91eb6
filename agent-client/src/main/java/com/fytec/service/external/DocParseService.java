package com.fytec.service.external;

import org.springframework.web.multipart.MultipartFile;

/**
 * 文档解析服务接口
 * 支持多平台接入的通用文档解析服务
 *
 * @param <T> 解析参数类型
 * <AUTHOR>
 */
public interface DocParseService<T> {

    /**
     * 解析文档（本地文件）
     *
     * @param file   文件
     * @param params 解析参数
     * @return 解析结果
     */
    Object parseDocument(MultipartFile file, T params);

    /**
     * 解析文档（URL文件）
     *
     * @param url    文件URL
     * @param params 解析参数
     * @return 解析结果
     */
    Object parseDocumentByUrl(String url, T params);

    /**
     * 获取服务提供商名称
     *
     * @return 服务提供商名称
     */
    String getProviderName();

    /**
     * 检查服务是否可用
     *
     * @return 是否可用
     */
    boolean isAvailable();
}