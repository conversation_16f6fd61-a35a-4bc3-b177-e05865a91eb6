package com.fytec.service.external.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fytec.client.external.XfyunIatWebSocketClient;
import com.fytec.config.XfyunIatProperties;
import com.fytec.dto.external.IatRequestDTO;
import com.fytec.service.external.StreamIatService;
import com.fytec.utils.SseUtil;
import com.fytec.utils.TempFileUtil;
import com.fytec.utils.external.XfyunAudioUtil;
import com.fytec.utils.external.processor.ResponseProcessorFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.File;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * 讯飞语音识别服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class XfyunStreamIatServiceImpl implements StreamIatService {

    private final XfyunIatProperties xfyunIatProperties;

    @Override
    public String recognizeAudioFile(MultipartFile file, IatRequestDTO request, SseEmitter emitter) {
        try {
            String sessionId = executeRecognition(file, request,
                response -> SseUtil.sendMessage(
                        emitter, ResponseProcessorFactory.getXfyunIatProcessor().process(response)),
                error -> SseUtil.completeWithError(emitter, error),
                () -> SseUtil.complete(emitter)
            );
            
            log.info("开始语音识别，会话ID: {}", sessionId);
            return sessionId;
        } catch (Exception e) {
            log.error("语音识别失败: {}", e.getMessage(), e);
            SseUtil.completeWithError(emitter, e);
            return null;
        }
    }

    @Override
    public SseEmitter recognizeAudioFile(MultipartFile file, IatRequestDTO request) {
        SseEmitter emitter = new SseEmitter(request.getTimeout());

        try {
            String sessionId = recognizeAudioFile(file, request, emitter);
            log.info("开始音频文件识别，会话ID: {}, 文件名: {}", sessionId, file.getOriginalFilename());
            
            // 设置超时和错误处理
            emitter.onTimeout(() -> {
                log.warn("音频识别超时，会话ID: {}", sessionId);
            });
            
            emitter.onError(throwable -> {
                log.error("音频识别错误，会话ID: {}, 错误: {}", sessionId, throwable.getMessage());
            });
            
            emitter.onCompletion(() -> {
                log.info("音频识别完成，会话ID: {}", sessionId);
            });
            
        } catch (Exception e) {
            log.error("启动音频文件识别失败: {}", e.getMessage());
            emitter.completeWithError(e);
        }
        
        return emitter;
    }

    @Override
    public Object recognizeAudioFileSync(MultipartFile file, IatRequestDTO request) {
        try {
            final List<Object> resultList = new CopyOnWriteArrayList<>();
            final CompletableFuture<List<Object>> resultFuture = new CompletableFuture<>();

            String sessionId = executeRecognition(file, request,
                response -> {
                    Object processedData = ResponseProcessorFactory.getXfyunIatProcessor().process(response);
                    resultList.add(processedData);
                },
                resultFuture::completeExceptionally,
                () -> resultFuture.complete(resultList)
            );

            log.info("开始语音识别（同步），会话ID: {}", sessionId);
            return resultFuture.get(request.getTimeout(), TimeUnit.MILLISECONDS);

        } catch (Exception e) {
            log.error("语音识别失败: {}", e.getMessage(), e);
            throw new RuntimeException("语音识别失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String getProviderName() {
        return "讯飞语音识别";
    }

    @Override
    public boolean isAvailable() {
        return StrUtil.isAllNotBlank(
            xfyunIatProperties.getAppId(),
            xfyunIatProperties.getApiSecret(),
            xfyunIatProperties.getApiKey(),
            xfyunIatProperties.getHostUrl()
        );
    }

    /**
     * 执行语音识别的通用方法
     */
    private String executeRecognition(MultipartFile file, IatRequestDTO request,
                                     Consumer<JsonNode> onResult, Consumer<Throwable> onError,
                                     Runnable onComplete) {
        try {
            final String sessionId = IdUtil.fastSimpleUUID();
            final File tmpFile = TempFileUtil.createTempFile(file);
            final String fileSuffix = FileUtil.getSuffix(tmpFile.getName());

            // 如果请求中没有指定encoding，则根据文件后缀自动判断
            if (StrUtil.isBlank(request.getEncoding())) {
                request.setEncoding(XfyunAudioUtil.getEncoding(fileSuffix));
            }

            // 创建WebSocket客户端（使用完整参数）
            XfyunIatWebSocketClient client = new XfyunIatWebSocketClient(
                xfyunIatProperties.getHostUrl(),
                xfyunIatProperties.getAppId(),
                xfyunIatProperties.getApiSecret(),
                xfyunIatProperties.getApiKey(),
                request,
                onResult,
                error -> {
                    log.error("语音识别错误，会话ID: {}, 错误信息: {}", sessionId, error.getMessage(), error);
                    TempFileUtil.cleanupTempFile(tmpFile);
                    onError.accept(error);
                },
                () -> {
                    log.info("语音识别完成，会话ID: {}", sessionId);
                    TempFileUtil.cleanupTempFile(tmpFile);
                    onComplete.run();
                }
            );
            
            // 连接WebSocket
            client.connect();
            
            // 等待WebSocket连接建立后发送音频文件
            client.getWebSocketConnectedFuture()
                .orTimeout(5, TimeUnit.SECONDS)
                .thenRunAsync(() -> {
                    log.debug("WebSocket连接已建立，开始发送音频文件，会话ID: {}", sessionId);
                    client.sendAudioFileAsync(tmpFile);
                })
                .exceptionally(throwable -> {
                    log.error("WebSocket连接失败或超时，会话ID: {}, 错误: {}", sessionId, throwable.getMessage());
                    TempFileUtil.cleanupTempFile(tmpFile);
                    onError.accept(new RuntimeException("WebSocket连接失败: " + throwable.getMessage()));
                    return null;
                });
            
            return sessionId;
        } catch (Exception e) {
            log.error("执行语音识别失败: {}", e.getMessage(), e);
            throw new RuntimeException("执行语音识别失败: " + e.getMessage(), e);
        }
    }

}