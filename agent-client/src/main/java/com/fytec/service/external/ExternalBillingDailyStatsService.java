package com.fytec.service.external;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fytec.dto.external.ExternalBillingDailyStatsQueryDTO;
import com.fytec.dto.external.ExternalBillingDailyStatsResultDTO;
import com.fytec.entity.external.ExternalBillingDailyStats;

import java.time.LocalDate;

public interface ExternalBillingDailyStatsService extends IService<ExternalBillingDailyStats> {

    /**
     * 生成指定日期的每日统计数据
     * 根据传入的日期，查询 ExternalUseLog 中对应日期范围内的数据，
     * 按照不同的 servicetype 对数据进行分组统计，并将统计结果插入到数据库中。
     * 如果传入日期已存在统计数据，则执行更新操作（upsert 逻辑）。
     *
     * @param statsDate 统计日期
     */
    void generateDailyStats(LocalDate statsDate);

    /**
     * 分页查询外部计费每日统计数据
     * 根据指定的日期范围查询统计数据，按 serviceCode 分组并汇总各项指标
     *
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    Page<ExternalBillingDailyStatsResultDTO> queryBillingStatsPage(Page<ExternalBillingDailyStatsResultDTO> page, ExternalBillingDailyStatsQueryDTO queryDTO);
}
