package com.fytec.service.external.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fytec.client.external.XfyunTtsWebSocketClient;
import com.fytec.config.XfyunStreamTtsProperties;
import com.fytec.dto.external.TtsRequestDTO;

import com.fytec.service.external.StreamTtsService;
import com.fytec.utils.SseUtil;
import com.fytec.utils.external.processor.ResponseProcessorFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * 讯飞语音合成服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class XfyunStreamTtsServiceImpl implements StreamTtsService {

    private final XfyunStreamTtsProperties xfyunStreamTtsProperties;

    @Override
    public String synthesizeText(TtsRequestDTO request, SseEmitter emitter) {
        return executeTextSynthesis(request,
            response -> SseUtil.sendMessage(
                    emitter, ResponseProcessorFactory.getXfyunTtsProcessor().process(response)),
            error -> SseUtil.completeWithError(emitter, error),
            () -> SseUtil.complete(emitter)
        );
    }

    @Override
    public SseEmitter synthesizeText(TtsRequestDTO request) {
        SseEmitter emitter = new SseEmitter(request.getTimeout());
        synthesizeText(request, emitter);
        return emitter;
    }

    @Override
    public Object synthesizeTextSync(TtsRequestDTO request) {
        try {
            final List<Object> resultList = new CopyOnWriteArrayList<>();
            final CompletableFuture<List<Object>> resultFuture = new CompletableFuture<>();

            String sessionId = executeTextSynthesis(request,
                response -> {
                    // 收集所有响应数据
                    Object processedData = ResponseProcessorFactory.getXfyunTtsProcessor().process(response);
                    resultList.add(processedData);
                },
                resultFuture::completeExceptionally,
                () -> resultFuture.complete(resultList)
            );

            log.info("开始语音合成（同步），会话ID: {}, 文本长度: {}", sessionId, request.getText().length());
            return resultFuture.get(request.getTimeout(), TimeUnit.MILLISECONDS);

        } catch (Exception e) {
            log.error("同步语音合成失败: {}", e.getMessage(), e);
            throw new RuntimeException("同步语音合成失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String getProviderName() {
        return "讯飞语音合成";
    }

    @Override
    public boolean isAvailable() {
        return xfyunStreamTtsProperties.getEnabled() != null && xfyunStreamTtsProperties.getEnabled()
                && StrUtil.isNotBlank(xfyunStreamTtsProperties.getAppId())
                && StrUtil.isNotBlank(xfyunStreamTtsProperties.getApiKey())
                && StrUtil.isNotBlank(xfyunStreamTtsProperties.getApiSecret())
                && StrUtil.isNotBlank(xfyunStreamTtsProperties.getHostUrl());
    }

    @Override
    public byte[] synthesizeTextToFileBytes(TtsRequestDTO request) {
        // 参数验证
        validateTtsRequest(request);
        // 生成会话ID
        String sessionId = IdUtil.fastSimpleUUID();
        CompletableFuture<byte[]> future = new CompletableFuture<>();
        // 使用ByteArrayOutputStream收集音频数据
        try(ByteArrayOutputStream audioBuffer = new ByteArrayOutputStream()) {
            log.info("开始字节数组合成，会话ID: {}", sessionId);
            // 创建WebSocket客户端，使用专门的音频数据处理器
            XfyunTtsWebSocketClient client = createWebSocketClient(request, sessionId,
                response -> processAudioResponse(response, audioBuffer, future, sessionId),
                future::completeExceptionally,
                () -> completeAudioCollection(audioBuffer, future)
            );
            // 连接WebSocket
            client.connect();
            // 等待合成完成
            return future.get(request.getTimeout(), TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error("字节数组合成失败: {}", e.getMessage(), e);
            throw new RuntimeException("字节数组合成失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证TTS请求参数
     *
     * @param request TTS请求参数DTO
     */
    private void validateTtsRequest(TtsRequestDTO request) {
        // 参数验证
        if (request == null || StrUtil.isBlank(request.getText())) {
            throw new IllegalArgumentException("合成文本不能为空");
        }

        // 检查文本长度（讯飞TTS限制8000字节）
        byte[] textBytes = request.getText().getBytes(StandardCharsets.UTF_8);
        if (textBytes.length > 8000) {
            throw new IllegalArgumentException("文本长度超过限制（8000字节），当前长度: " + textBytes.length);
        }
    }

    /**
     * 创建WebSocket客户端
     *
     * @param request TTS请求参数DTO
     * @param sessionId 会话ID
     * @param onResult 结果回调
     * @param onError 错误回调
     * @param onComplete 完成回调
     * @return WebSocket客户端
     */
    private XfyunTtsWebSocketClient createWebSocketClient(TtsRequestDTO request, String sessionId,
                                                         Consumer<JsonNode> onResult,
                                                         Consumer<Throwable> onError,
                                                         Runnable onComplete) {
        return new XfyunTtsWebSocketClient(
            xfyunStreamTtsProperties.getHostUrl(),
            xfyunStreamTtsProperties.getAppId(),
            xfyunStreamTtsProperties.getApiSecret(),
            xfyunStreamTtsProperties.getApiKey(),
            request,
            onResult,
            error -> {
                log.error("语音合成错误，会话ID: {}, 错误信息: {}", sessionId, error.getMessage(), error);
                onError.accept(error);
            },
            () -> {
                log.info("语音合成完成，会话ID: {}", sessionId);
                onComplete.run();
            }
        );
    }

    /**
     * 执行文本语音合成（使用完整参数）
     *
     * @param request TTS请求参数DTO
     * @param onResult 结果回调
     * @param onError 错误回调
     * @param onComplete 完成回调
     * @return 会话ID
     */
    private String executeTextSynthesis(TtsRequestDTO request,
                                       Consumer<JsonNode> onResult,
                                       Consumer<Throwable> onError,
                                       Runnable onComplete) {
        // 参数验证
        validateTtsRequest(request);

        // 生成会话ID
        String sessionId = IdUtil.fastSimpleUUID();

        try {
            log.debug("开始执行语音合成，会话ID: {}, 文本: {}, 发音人: {}", sessionId, request.getText(), request.getVcn());

            // 创建WebSocket客户端
            XfyunTtsWebSocketClient client = createWebSocketClient(request, sessionId, onResult, onError, onComplete);

            // 连接WebSocket
            client.connect();

            // 等待WebSocket连接建立
            client.getWebSocketConnectedFuture()
                .orTimeout(5, TimeUnit.SECONDS)
                .exceptionally(throwable -> {
                    log.error("WebSocket连接失败或超时，会话ID: {}, 错误: {}", sessionId, throwable.getMessage());
                    onError.accept(new RuntimeException("WebSocket连接失败: " + throwable.getMessage()));
                    return null;
                });

            return sessionId;
        } catch (Exception e) {
            log.error("执行语音合成失败: {}", e.getMessage(), e);
            throw new RuntimeException("执行语音合成失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理音频响应数据
     *
     * @param response 响应数据
     * @param audioBuffer 音频缓冲区
     * @param future 异步结果
     * @param sessionId 会话ID
     */
    private void processAudioResponse(JsonNode response, ByteArrayOutputStream audioBuffer,
                                     CompletableFuture<byte[]> future, String sessionId) {
        JsonNode dataNode = response.path("data");
        if (dataNode.isMissingNode() || dataNode.isNull()) {
            return;
        }

        // 处理音频数据
        processAudioData(dataNode, audioBuffer, future);

        // 检查是否合成完成
        checkSynthesisCompletion(dataNode, audioBuffer, future, sessionId);
    }

    /**
     * 处理音频数据片段
     *
     * @param dataNode 数据节点
     * @param audioBuffer 音频缓冲区
     * @param future 异步结果
     */
    private void processAudioData(JsonNode dataNode, ByteArrayOutputStream audioBuffer,
                                 CompletableFuture<byte[]> future) {
        JsonNode audioNode = dataNode.path("audio");
        if (audioNode.isMissingNode() || audioNode.isNull()) {
            return;
        }

        String audioData = audioNode.asText();
        if (StrUtil.isBlank(audioData)) {
            return;
        }

        try {
            // 解码Base64音频数据并写入缓冲区
            byte[] audioBytes = Base64.getDecoder().decode(audioData);
            audioBuffer.write(audioBytes);
            log.debug("收集音频数据: {} 字节", audioBytes.length);
        } catch (Exception e) {
            log.error("处理音频数据失败", e);
            future.completeExceptionally(e);
        }
    }

    /**
     * 检查合成是否完成
     *
     * @param dataNode 数据节点
     * @param audioBuffer 音频缓冲区
     * @param future 异步结果
     * @param sessionId 会话ID
     */
    private void checkSynthesisCompletion(JsonNode dataNode, ByteArrayOutputStream audioBuffer,
                                         CompletableFuture<byte[]> future, String sessionId) {
        JsonNode statusNode = dataNode.path("status");
        if (statusNode.isMissingNode() || statusNode.asInt() != 2) {
            return;
        }

        byte[] finalAudioData = audioBuffer.toByteArray();
        log.info("字节数组合成完成，会话ID: {}, 数据大小: {} 字节", sessionId, finalAudioData.length);
        future.complete(finalAudioData);
    }

    /**
     * 完成音频数据收集
     *
     * @param audioBuffer 音频缓冲区
     * @param future 异步结果
     */
    private void completeAudioCollection(ByteArrayOutputStream audioBuffer, CompletableFuture<byte[]> future) {
        if (!future.isDone()) {
            byte[] finalAudioData = audioBuffer.toByteArray();
            future.complete(finalAudioData);
        }
    }

}
