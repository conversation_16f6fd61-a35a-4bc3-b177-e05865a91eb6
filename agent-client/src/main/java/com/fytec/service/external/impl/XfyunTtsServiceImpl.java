package com.fytec.service.external.impl;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fytec.config.XfyunTtsProperties;
import com.fytec.constant.ExternalConstants;
import com.fytec.dto.external.*;
import com.fytec.service.external.XfyunTtsService;
import com.fytec.utils.external.XfyunSignUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Base64;

/**
 * 讯飞长文本语音合成服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class XfyunTtsServiceImpl implements XfyunTtsService {

    private final XfyunTtsProperties xfyunTtsProperties;
    private final ObjectMapper objectMapper;

    @Override
    public Object createTask(XfyunTtsCreateTaskPublicRequestDTO request) {
        if (!isAvailable()) {
            throw new IllegalStateException("讯飞TTS服务不可用，请检查配置");
        }

        try {
            // 构建带鉴权的URL
            String authUrl = XfyunSignUtil.buildAuthUrl(
                    xfyunTtsProperties.getCreateTaskUrl(),
                    xfyunTtsProperties.getAppId(),
                    xfyunTtsProperties.getApiSecret(),
                    xfyunTtsProperties.getApiKey(),
                    "POST"
            );

            // 在转换为JSON前赋予header值
            processCreateTaskRequestDTO(request);
            
            // 使用Jackson将DTO转换为JSON，确保@JsonProperty注解生效
            String requestBody;
            try {
                requestBody = objectMapper.writeValueAsString(request);
            } catch (JsonProcessingException e) {
                log.error("序列化请求对象失败", e);
                throw new RuntimeException("序列化请求对象失败: " + e.getMessage(), e);
            }
            log.debug("创建TTS任务请求: {}", requestBody);

            // 发送HTTP请求
            try (HttpResponse response = HttpUtil.createPost(authUrl)
                    .header("Content-Type", "application/json")
                    .timeout(xfyunTtsProperties.getConnectTimeout())
                    .setReadTimeout(xfyunTtsProperties.getReadTimeout())
                    .body(requestBody)
                    .execute()) {

                if (!response.isOk()) {
                    log.error("创建TTS任务失败，状态码: {}, 响应: {}", response.getStatus(), response.body());
                    throw new RuntimeException("创建TTS任务失败: " + response.getStatus());
                }

                String responseBody = response.body();
                log.debug("创建TTS任务响应: {}", responseBody);

                JSONObject responseObj = JSONUtil.parseObj(responseBody);
                
                // 检查响应中的错误码
                JSONObject header = responseObj.getJSONObject("header");
                if (header != null && header.getInt("code", 0) != 0) {
                    String errorMsg = header.getStr("message", "未知错误");
                    log.error("创建TTS任务业务失败，错误码: {}, 错误信息: {}", header.getInt("code"), errorMsg);
                    throw new RuntimeException("创建TTS任务失败: " + errorMsg);
                }

                return responseObj;
            }

        } catch (Exception e) {
            log.error("调用创建TTS任务API失败", e);
            throw new RuntimeException("调用创建TTS任务API失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Object queryTask(XfyunTtsQueryTaskPublicRequestDTO request) {
        if (!isAvailable()) {
            throw new IllegalStateException("讯飞TTS服务不可用，请检查配置");
        }

        try {
            // 构建带鉴权的URL
            String authUrl = XfyunSignUtil.buildAuthUrl(
                    xfyunTtsProperties.getQueryTaskUrl(),
                    xfyunTtsProperties.getAppId(),
                    xfyunTtsProperties.getApiSecret(),
                    xfyunTtsProperties.getApiKey(),
                    "POST"
            );

            // 在转换为JSON前赋予header值
            ensureAndSetQueryHeader(request);
            
            // 使用Jackson将DTO转换为JSON，确保@JsonProperty注解生效
            String requestBody;
            try {
                requestBody = objectMapper.writeValueAsString(request);
            } catch (JsonProcessingException e) {
                log.error("序列化请求对象失败", e);
                throw new RuntimeException("序列化请求对象失败: " + e.getMessage(), e);
            }
            log.debug("查询TTS任务请求: {}", requestBody);

            // 发送HTTP请求
            try (HttpResponse response = HttpUtil.createPost(authUrl)
                    .header("Content-Type", "application/json")
                    .timeout(xfyunTtsProperties.getConnectTimeout())
                    .setReadTimeout(xfyunTtsProperties.getReadTimeout())
                    .body(requestBody)
                    .execute()) {

                if (!response.isOk()) {
                    log.error("查询TTS任务失败，状态码: {}, 响应: {}", response.getStatus(), response.body());
                    throw new RuntimeException("查询TTS任务失败: " + response.getStatus());
                }

                String responseBody = response.body();
                log.debug("查询TTS任务响应: {}", responseBody);

                JSONObject responseObj = JSONUtil.parseObj(responseBody);
                
                // 检查响应中的错误码
                JSONObject header = responseObj.getJSONObject("header");
                if (header != null && header.getInt("code", 0) != 0) {
                    String errorMsg = header.getStr("message", "未知错误");
                    log.error("查询TTS任务业务失败，错误码: {}, 错误信息: {}", header.getInt("code"), errorMsg);
                    throw new RuntimeException("查询TTS任务失败: " + errorMsg);
                }

                return responseObj;
            }

        } catch (Exception e) {
            log.error("调用查询TTS任务API失败", e);
            throw new RuntimeException("调用查询TTS任务API失败: " + e.getMessage(), e);
        }
    }



    /**
     * 确保并设置创建任务请求头信息，为没有值的字段赋值默认值，同时对text字段进行base64编码
     */
    private void processCreateTaskRequestDTO(XfyunTtsCreateTaskPublicRequestDTO request) {
        if (request.getHeader() == null) {
            XfyunTtsCreateTaskPublicRequestDTO.Header header = new XfyunTtsCreateTaskPublicRequestDTO.Header();
            request.setHeader(header);
        }
        request.getHeader().setAppId(xfyunTtsProperties.getAppId());
        
        // 为没有值的字段设置默认值
        setCreateTaskDefaultValues(request);
        
        // 对text字段进行base64编码
        if (request.getPayload() != null && request.getPayload().getText() != null && request.getPayload().getText().getText() != null) {
            XfyunTtsCreateTaskPublicRequestDTO.Text text = request.getPayload().getText();
            String originalText = text.getText();
            String encodedText = Base64.getEncoder().encodeToString(originalText.getBytes());
            text.setText(encodedText);
            log.debug("文本已进行base64编码，原文本长度: {}, 编码后长度: {}", originalText.length(), encodedText.length());
        }
    }
    
    /**
     * 为创建任务请求设置默认值
     */
    private void setCreateTaskDefaultValues(XfyunTtsCreateTaskPublicRequestDTO request) {
        // 确保parameter结构存在
        if (request.getParameter() == null) {
            XfyunTtsCreateTaskPublicRequestDTO.Parameter parameter = new XfyunTtsCreateTaskPublicRequestDTO.Parameter();
            request.setParameter(parameter);
        }
        
        XfyunTtsCreateTaskPublicRequestDTO.Dts dts;
        if (request.getParameter().getDts() == null) {
            dts = new XfyunTtsCreateTaskPublicRequestDTO.Dts();
            request.getParameter().setDts(dts);
        } else {
            dts = request.getParameter().getDts();
        }
        
        // 设置TTS参数默认值
        if (dts.getVcn() == null) {
            dts.setVcn((String) ExternalConstants.XfyunTtsCreateDefaultParams.VCN.getDefaultValue());
        }

        if (dts.getLanguage() == null) {
            dts.setLanguage((String) ExternalConstants.XfyunTtsCreateDefaultParams.LANGUAGE.getDefaultValue());
        }
        
        if (dts.getSpeed() == null) {
            dts.setSpeed((Integer) ExternalConstants.XfyunTtsCreateDefaultParams.SPEED.getDefaultValue());
        }
        
        if (dts.getVolume() == null) {
            dts.setVolume((Integer) ExternalConstants.XfyunTtsCreateDefaultParams.VOLUME.getDefaultValue());
        }
        
        if (dts.getPitch() == null) {
            dts.setPitch((Integer) ExternalConstants.XfyunTtsCreateDefaultParams.PITCH.getDefaultValue());
        }
        
        if (dts.getRam() == null) {
            dts.setRam((Integer) ExternalConstants.XfyunTtsCreateDefaultParams.RAM.getDefaultValue());
        }
        
        if (dts.getRhy() == null) {
            dts.setRhy((Integer) ExternalConstants.XfyunTtsCreateDefaultParams.RHY.getDefaultValue());
        }
        
        // 设置音频格式默认值
        XfyunTtsCreateTaskPublicRequestDTO.Audio audio;
        if (dts.getAudio() == null) {
            audio = new XfyunTtsCreateTaskPublicRequestDTO.Audio();
            dts.setAudio(audio);
        } else {
            audio = dts.getAudio();
        }
        
        if (audio.getEncoding() == null) {
            audio.setEncoding((String) ExternalConstants.XfyunTtsCreateDefaultParams.AUDIO_ENCODING.getDefaultValue());
        }
        
        if (audio.getSampleRate() == null) {
            audio.setSampleRate((Integer) ExternalConstants.XfyunTtsCreateDefaultParams.SAMPLE_RATE.getDefaultValue());
        }
        
        // 设置拼音标注默认值
        XfyunTtsCreateTaskPublicRequestDTO.Pybuf pybuf;
        if (dts.getPybuf() == null) {
            pybuf = new XfyunTtsCreateTaskPublicRequestDTO.Pybuf();
            dts.setPybuf(pybuf);
        } else {
            pybuf = dts.getPybuf();
        }
        
        if (pybuf.getEncoding() == null) {
            pybuf.setEncoding((String) ExternalConstants.XfyunTtsCreateDefaultParams.PYBUF_ENCODING.getDefaultValue());
        }
        
        if (pybuf.getCompress() == null) {
            pybuf.setCompress((String) ExternalConstants.XfyunTtsCreateDefaultParams.PYBUF_COMPRESS.getDefaultValue());
        }
        
        if (pybuf.getFormat() == null) {
            pybuf.setFormat((String) ExternalConstants.XfyunTtsCreateDefaultParams.PYBUF_FORMAT.getDefaultValue());
        }
        
        // 设置文本格式默认值
        if (request.getPayload() == null) {
            XfyunTtsCreateTaskPublicRequestDTO.Payload payload = new XfyunTtsCreateTaskPublicRequestDTO.Payload();
            request.setPayload(payload);
        }
        
        XfyunTtsCreateTaskPublicRequestDTO.Text text;
        if (request.getPayload().getText() == null) {
            text = new XfyunTtsCreateTaskPublicRequestDTO.Text();
            request.getPayload().setText(text);
        } else {
            text = request.getPayload().getText();
        }
        
        if (text.getEncoding() == null) {
            text.setEncoding((String) ExternalConstants.XfyunTtsCreateDefaultParams.TEXT_ENCODING.getDefaultValue());
        }
        
        if (text.getCompress() == null) {
            text.setCompress((String) ExternalConstants.XfyunTtsCreateDefaultParams.TEXT_COMPRESS.getDefaultValue());
        }
        
        if (text.getFormat() == null) {
            text.setFormat((String) ExternalConstants.XfyunTtsCreateDefaultParams.TEXT_FORMAT.getDefaultValue());
        }
        
        log.debug("创建任务请求默认值设置完成，语言={}, 语速={}, 音量={}, 音调={}, 音频编码={}, 采样率={}", 
                dts.getLanguage(), dts.getSpeed(), dts.getVolume(), dts.getPitch(), 
                dts.getAudio().getEncoding(), dts.getAudio().getSampleRate());
    }

    /**
     * 确保并设置查询任务请求头信息
     */
    /**
     * 确保并设置查询任务请求头信息，为没有值的字段赋值默认值
     */
    private void ensureAndSetQueryHeader(XfyunTtsQueryTaskPublicRequestDTO request) {
        if (request.getHeader() == null) {
            XfyunTtsQueryTaskPublicRequestDTO.Header header = new XfyunTtsQueryTaskPublicRequestDTO.Header();
            request.setHeader(header);
        }
        
        // 设置必需的appId
        request.getHeader().setAppId(xfyunTtsProperties.getAppId());
    }

    /**
     * 检查服务是否可用
     *
     * @return 是否可用
     */
    private boolean isAvailable() {
        Boolean enabled = xfyunTtsProperties.getEnabled();
        return (enabled != null ? enabled : false) &&
               xfyunTtsProperties.getAppId() != null &&
               xfyunTtsProperties.getApiKey() != null &&
               xfyunTtsProperties.getApiSecret() != null;
    }

    /**
     * 获取服务提供商名称
     *
     * @return 服务提供商名称
     */
    public String getProviderName() {
        return "讯飞长文本语音合成";
    }
}