package com.fytec.service.external.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fytec.client.external.XfyunIseWebSocketClient;
import com.fytec.config.XfyunIseProperties;
import com.fytec.service.external.StreamIseService;
import com.fytec.utils.SseUtil;
import com.fytec.utils.TempFileUtil;
import com.fytec.utils.external.XfyunAudioUtil;
import com.fytec.utils.external.processor.ResponseProcessorFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.File;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * 讯飞语音评测服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class XfyunStreamIseServiceImpl implements StreamIseService {

    private final XfyunIseProperties xfyunIseProperties;

    @Override
    public String evaluateAudioFile(String text, MultipartFile file, SseEmitter emitter, Long timeoutMs) {
        try {
            String sessionId = executeEvaluation(text, file, timeoutMs,
                response -> {
                    Object processedData = ResponseProcessorFactory.getXfyunIseProcessor().process(response);
                    SseUtil.sendMessage(emitter, processedData);
                },
                error -> SseUtil.completeWithError(emitter, error),
                () -> SseUtil.complete(emitter)
            );
            
            log.info("开始语音评测，会话ID: {}, 评测文本: {}", sessionId, text);
            return sessionId;
        } catch (Exception e) {
            log.error("语音评测失败: {}", e.getMessage(), e);
            SseUtil.completeWithError(emitter, e);
            return null;
        }
    }

    @Override
    public String getProviderName() {
        return "讯飞语音评测";
    }

    @Override
    public boolean isAvailable() {
        return StrUtil.isAllNotBlank(
            xfyunIseProperties.getAppId(), 
            xfyunIseProperties.getApiSecret(), 
            xfyunIseProperties.getApiKey(), 
            xfyunIseProperties.getHostUrl()
        );
    }

    @Override
    public SseEmitter evaluateAudioFile(String text, MultipartFile file, Long timeout) {
        SseEmitter emitter = new SseEmitter(timeout);
        
        try {
            String sessionId = evaluateAudioFile(text, file, emitter, timeout);
            log.info("开始音频文件评测，会话ID: {}, 文件名: {}, 评测文本: {}", sessionId, file.getOriginalFilename(), text);
            
            // 设置超时和错误处理
            emitter.onTimeout(() -> log.warn("音频评测超时，会话ID: {}", sessionId));
            
            emitter.onError(throwable -> log.error("音频评测错误，会话ID: {}, 错误: {}", sessionId, throwable.getMessage()));
            
            emitter.onCompletion(() -> log.info("音频评测完成，会话ID: {}", sessionId));
            
        } catch (Exception e) {
            log.error("启动音频文件评测失败: {}", e.getMessage());
            emitter.completeWithError(e);
        }
        
        return emitter;
    }

    @Override
    public Object evaluateAudioFileSync(String text, MultipartFile file, Long timeout) {
        return evaluateAudioFileSync(text, file, timeout,
                response -> ResponseProcessorFactory.getXfyunIseProcessor().process(response));
    }

    /**
     * 同步语音评测（支持自定义响应处理器）
     *
     * @param text      评测文本
     * @param file      音频文件
     * @param timeout   超时时间
     * @param processor 响应处理器
     * @return 评测结果
     */
    @Override
    public Object evaluateAudioFileSync(String text, MultipartFile file, Long timeout, 
                                       Function<JsonNode, Object> processor) {
        try {
            final List<Object> resultList = new CopyOnWriteArrayList<>();
            final CompletableFuture<List<Object>> resultFuture = new CompletableFuture<>();
            
            String sessionId = executeEvaluation(text, file, timeout,
                response -> {
                    Object processedData = processor.apply(response);
                    resultList.add(processedData);
                },
                resultFuture::completeExceptionally,
                () -> resultFuture.complete(resultList)
            );
            
            log.info("开始语音评测（同步），会话ID: {}, 评测文本: {}", sessionId, text);
            return resultFuture.get(timeout, TimeUnit.MILLISECONDS);
            
        } catch (Exception e) {
            log.error("语音评测失败: {}", e.getMessage(), e);
            throw new RuntimeException("语音评测失败: " + e.getMessage(), e);
        }
    }


    /**
     * 执行语音评测的通用方法
     */
    private String executeEvaluation(String text, MultipartFile file, Long timeout,
                                     Consumer<JsonNode> onResult, Consumer<Throwable> onError,
                                     Runnable onComplete) {
        final String sessionId = IdUtil.fastSimpleUUID();
        final File tmpFile = TempFileUtil.createTempFile(file);
        final String fileSuffix = FileUtil.getSuffix(tmpFile.getName());
        
        // 创建WebSocket客户端
        XfyunIseWebSocketClient client = new XfyunIseWebSocketClient(
            xfyunIseProperties.getHostUrl(),
            xfyunIseProperties.getAppId(),
            xfyunIseProperties.getApiSecret(),
            xfyunIseProperties.getApiKey(),
            text,
            XfyunAudioUtil.getEncoding(fileSuffix),
            onResult,
            error -> {
                log.error("语音评测错误，会话ID: {}, 错误信息: {}", sessionId, error.getMessage(), error);
                TempFileUtil.cleanupTempFile(tmpFile);
                onError.accept(error);
            },
            () -> {
                log.info("语音评测完成，会话ID: {}", sessionId);
                TempFileUtil.cleanupTempFile(tmpFile);
                onComplete.run();
            }
        );
        
        // 连接WebSocket
        client.connect();
        
        // 等待WebSocket连接建立后发送音频文件
        client.getWebSocketConnectedFuture()
            .orTimeout(5, TimeUnit.SECONDS)
            .thenRunAsync(() -> {
                log.debug("WebSocket连接已建立，开始发送音频文件，会话ID: {}", sessionId);
                client.sendAudioFile(tmpFile);
            })
            .exceptionally(throwable -> {
                log.error("WebSocket连接失败或超时，会话ID: {}, 错误: {}", sessionId, throwable.getMessage());
                TempFileUtil.cleanupTempFile(tmpFile);
                onError.accept(new RuntimeException("WebSocket连接失败: " + throwable.getMessage()));
                return null;
            });
        
        return sessionId;
    }
}