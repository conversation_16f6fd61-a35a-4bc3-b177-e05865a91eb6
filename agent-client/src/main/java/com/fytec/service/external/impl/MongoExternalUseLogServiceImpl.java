package com.fytec.service.external.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.entity.external.ExternalUseLog;
import com.fytec.mapper.external.ExternalUseLogRepo;
import com.fytec.service.external.ExternalUseLogService;
import com.fytec.util.MongoPageUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * MongoDB外部服务使用日志服务实现类
 */
@Service
@AllArgsConstructor
@Slf4j
public class MongoExternalUseLogServiceImpl implements ExternalUseLogService {

    private final ExternalUseLogRepo externalUseLogRepo;

    @Override
    public void saveExternalUseLog(ExternalUseLog externalUseLog) {
        externalUseLog.prePersist();
        // 保存到MongoDB
        externalUseLogRepo.save(externalUseLog);
    }

    @Override
    public Long countByClientAndService(String clientName, String serviceType) {
        // 根据参数组合选择合适的查询方法
        if (StrUtil.isNotBlank(clientName) && StrUtil.isNotBlank(serviceType)) {
            return externalUseLogRepo.countByClientIdAndServiceType(clientName, serviceType);
        } else if (StrUtil.isNotBlank(clientName)) {
            return externalUseLogRepo.countByClientId(clientName);
        } else if (StrUtil.isNotBlank(serviceType)) {
            return externalUseLogRepo.countByServiceType(serviceType);
        } else {
            return externalUseLogRepo.count();
        }
    }

    @Override
    public Page<ExternalUseLog> queryExternalUseLogPage(String serviceType, Page<ExternalUseLog> page) {
        // 使用MongoPageUtil转换分页对象
        Pageable pageable = MongoPageUtil.toPageable(page);

        // 根据查询条件选择合适的查询方法
        org.springframework.data.domain.Page<ExternalUseLog> springPage = StrUtil.isNotBlank(serviceType)
            ? externalUseLogRepo.findByServiceTypeOrderByCreateTimeDesc(serviceType, pageable)
            : externalUseLogRepo.findAllByOrderByCreateTimeDesc(pageable);

        // 使用MongoPageUtil填充结果
        MongoPageUtil.fillMybatisPlusPage(springPage, page);
        return page;
    }

    @Override
    public List<ExternalUseLog> findByCallTimeBetween(LocalDateTime startTime, LocalDateTime endTime) {
        return externalUseLogRepo.findByCallTimeBetween(startTime, endTime);
    }
}
