package com.fytec.service.flow;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.config.FlowProperties;
import com.fytec.config.SelfProperties;
import com.fytec.constant.Constants;
import com.fytec.dto.flow.*;
import com.fytec.entity.history.AiExecuteHistory;
import com.fytec.entity.resource.AiResource;
import com.fytec.entity.workflow.WorkflowDevelop;
import com.fytec.entity.workflow.WorkflowPublish;
import com.fytec.mapper.history.AiExecuteHistoryMapper;
import com.fytec.mapper.resource.ResourceMapper;
import com.fytec.mapper.workflow.WorkflowDevelopMapper;
import com.fytec.mapper.workflow.WorkflowPublishMapper;
import com.fytec.satoken.StpClientUserUtil;
import com.fytec.util.UUIDGenerator;
import com.fytec.util.VersionUtils;
import com.fytec.utils.AirFlowUtil;
import com.fytec.utils.LangGraphUtil;
import com.fytec.utils.PrefectFlowUtil;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;
import io.minio.GetObjectArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHeaders;
import org.apache.rocketmq.client.apis.ClientConfiguration;
import org.apache.rocketmq.client.apis.ClientServiceProvider;
import org.apache.rocketmq.client.apis.consumer.FilterExpression;
import org.apache.rocketmq.client.apis.consumer.FilterExpressionType;
import org.apache.rocketmq.client.apis.consumer.SimpleConsumer;
import org.apache.rocketmq.client.apis.message.MessageId;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.fytec.util.HttpUtil.*;
import static com.fytec.utils.AirFlowUtil.*;

@Service
@Slf4j
@Transactional
@RequiredArgsConstructor
public class WorkflowService {

    private static final Map<String, SseEmitter> SSE_CACHE = new ConcurrentHashMap<>();
    private final WorkflowDevelopMapper workflowDevelopMapper;
    private final WorkflowPublishMapper workflowPublishMapper;
    private final AiExecuteHistoryMapper aiExecuteHistoryMapper;
    private final ResourceMapper resourceMapper;
    private final FlowProperties flowProperties;
    private final AirFlowUtil airFlowUtil;
    private final PrefectFlowUtil prefectFlowUtil;
    private final LangGraphUtil langGraphUtil;
    private final MinioClient minioClient;
    private final SelfProperties selfProperties;

    @Value("${rocketmq.topic}")
    private String topic;

    @Value("${rocketmq.endpoint}")
    private String endpoints;

    public WorkflowDevelop getWorkflowDevelop(Long resourceId) {
        return workflowDevelopMapper.selectOne(new LambdaQueryWrapper<WorkflowDevelop>()
                .eq(WorkflowDevelop::getResourceId, resourceId));
    }

    public LocalDateTime developWorkflow(DevelopWorkflowDTO dto) {
        WorkflowDevelop develop = getWorkflowDevelop(dto.getResourceId());
        AiResource aiResource = resourceMapper.selectById(dto.getResourceId());

        String flowType = flowProperties.getType();
        Map<String, Object> nodeMap;
        if (StrUtil.equals(flowType, "airflow")) {
            nodeMap = airFlowUtil.convertToAirFlow(dto.getConfigJson(),
                    StrUtil.format("{}_{}", aiResource.getName(), aiResource.getId()),
                    aiResource.getDescription());
        } else if (StrUtil.equalsAny(flowType, "prefect", "langGraph")) {
            nodeMap = airFlowUtil.parseFlow(dto.getConfigJson(),
                    StrUtil.format("{}_{}", aiResource.getName(), aiResource.getId()),
                    aiResource.getDescription());
        } else {
            throw new ValidateException("不支持的流程类型");
        }

        if (develop == null) {
            develop = new WorkflowDevelop();
            develop.setResourceId(dto.getResourceId());
            develop.setConfigJson(dto.getConfigJson());
            develop.setNodeConfigMapping(JSON.toJSONString(nodeMap.get(NODE_CONFIG_MAPPING_KEY)));
            develop.setNodeEdgesMapping(JSON.toJSONString(nodeMap.get(NODE_EDGES_MAPPING_KEY)));
            workflowDevelopMapper.insert(develop);
        } else {
            develop.setConfigJson(dto.getConfigJson());
            develop.setNodeConfigMapping(JSON.toJSONString(nodeMap.get(NODE_CONFIG_MAPPING_KEY)));
            develop.setNodeEdgesMapping(JSON.toJSONString(nodeMap.get(NODE_EDGES_MAPPING_KEY)));
            workflowDevelopMapper.updateById(develop);
        }
        return LocalDateTime.now();
    }

    public WorkflowDevelopDetailDTO getDetail(Long resourceId) {
        AiResource resource = resourceMapper.selectById(resourceId);
        if (resource != null) {
            WorkflowDevelopDetailDTO develop = new WorkflowDevelopDetailDTO();
            develop.setName(resource.getName());
            develop.setDescription(resource.getDescription());
            develop.setLogo(resource.getLogo());
            develop.setType(resource.getType());
            develop.setResourceId(resource.getId());

            WorkflowDevelop workflowDevelop = getWorkflowDevelop(resourceId);
            if (workflowDevelop != null) {
                BeanUtils.copyProperties(workflowDevelop, develop);
            }
            return develop;
        }
        return null;
    }

    private WorkflowPublish getLatestWorkflowPublish(Long resourceId) {
        return workflowPublishMapper.selectOne(new LambdaQueryWrapper<WorkflowPublish>()
                .eq(WorkflowPublish::getResourceId, resourceId)
                .orderByDesc(WorkflowPublish::getPublishTime)
                .last("limit 1"));

    }

    public void publishDevelop(PublishWorkflowDTO dto) {
        WorkflowPublish publish = getLatestWorkflowPublish(dto.getResourceId());
        String latestVersion = null;
        if (publish != null) {
            latestVersion = publish.getVersion();
        }

        String nextVersion = VersionUtils.next(latestVersion, 9);

        AiResource aiResource = resourceMapper.selectById(dto.getResourceId());
        aiResource.setStatus(Constants.PUBLISH_STATUS_1);
        resourceMapper.updateById(aiResource);

        String flowType = flowProperties.getType();
        Map<String, Object> nodeMap;
        if (StrUtil.equals(flowType, "airflow")) {
            nodeMap = airFlowUtil.convertToAirFlow(dto.getConfigJson(),
                    StrUtil.format("{}_{}_{}", aiResource.getName(), aiResource.getId(), nextVersion),
                    aiResource.getDescription());
        } else if (StrUtil.equals(flowType, "prefect")) {
            nodeMap = prefectFlowUtil.convertToPrefectFlow(dto.getConfigJson(), dto.getConfigTreeJson(),
                    StrUtil.format("{}_{}_{}", aiResource.getName(), aiResource.getId(), nextVersion),
                    aiResource.getDescription());
        } else if (StrUtil.equals(flowType, "langGraph")) {
            nodeMap = langGraphUtil.convertToLangGraph(dto.getConfigJson(),
                    StrUtil.format("{}_{}_{}", aiResource.getName(), aiResource.getId(), nextVersion),
                    aiResource.getDescription());
        } else {
            throw new ValidateException("不支持的流程类型");
        }

        WorkflowPublish newPublish = new WorkflowPublish();
        newPublish.setResourceId(dto.getResourceId());
        newPublish.setConfigJson(dto.getConfigJson());
        newPublish.setConfigTreeJson(dto.getConfigTreeJson());
        newPublish.setNodeConfigMapping(JSON.toJSONString(nodeMap.get(NODE_CONFIG_MAPPING_KEY)));
        newPublish.setNodeEdgesMapping(JSON.toJSONString(nodeMap.get(NODE_EDGES_MAPPING_KEY)));
        newPublish.setFunctionCallTool(nodeMap.get(FUNCTION_CALL_TOOL).toString());
        newPublish.setVersion(nextVersion);
        newPublish.setPubRecord(dto.getPubRecord());
        newPublish.setPublishTime(LocalDateTime.now());
        workflowPublishMapper.insert(newPublish);
    }

    @SneakyThrows
    public String executeWorkflow(WorkflowExecuteDTO dto) {
        Map<String, Object> flowTriggerConfig;
        if (dto.getParams() == null) {
            log.warn("{} : executeWorkflow params为空", dto.getId());
            flowTriggerConfig = new HashMap<>();
        } else {
            log.info("{} : executeWorkflow params: {}", dto.getId(), dto.getParams());
            flowTriggerConfig = new HashMap<>(dto.getParams());
        }
        String flowName = prepareFlowRunParam(dto, flowTriggerConfig);

        String flowType = flowProperties.getType();
        if (StrUtil.equals(flowType, "airflow")) {
            String authorization = flowProperties.getAirflow().getBasicAuth();
            unpauseWorkflow(flowName, authorization);
            String dagRunId = runWorkflow(flowName, flowTriggerConfig, authorization);
            return waitForWorkflowCompletion(flowName, dagRunId, authorization);
        } else {
            String condaPath = flowProperties.getCondaEnv().getCondaPath();
            String envName = flowProperties.getCondaEnv().getEnvName();
            Path path = Paths.get(flowProperties.getDag().getPath(), "%s.py".formatted(flowName));

            Map<String, Object> config = new HashMap<>();
            config.put("conf", flowTriggerConfig);

            long startTime = System.currentTimeMillis();
            Path pythonPath = Paths.get(condaPath, envName, "bin", "python");

            byte[] fileBytes = JSON.toJSONString(config).getBytes(StandardCharsets.UTF_8);
            ByteArrayInputStream inputStream = new ByteArrayInputStream(fileBytes);
            String runId = UUIDGenerator.getUUID();
            String fileName = "%s.txt".formatted(runId);
            String contentType = URLConnection.guessContentTypeFromName(fileName);
            String bucketName = "prefect-variables";
            minioClient.putObject(PutObjectArgs.builder()
                    .bucket(bucketName)
                    .object(fileName)
                    .stream(inputStream, fileBytes.length, -1)
                    .contentType(contentType)
                    .build());

            String[] cmd = {
                    pythonPath.toString(), path.toString(),
                    "--runId", runId,
                    "--file", fileName
            };
            Process process = new ProcessBuilder(cmd).start();
            if (dto.isDebug()
                    && StrUtil.equals(flowType, "langGraph")) {
                // langgraph debug模式下运行时返回runId， 结果通过runId查询
                return runId;
            }

            int exitCode = process.waitFor();
            long endTime = System.currentTimeMillis();
            System.out.printf("execute workflow cost: %d ms%n", endTime - startTime);

            if (exitCode == 0) {
                String endResult = "%s_end_result".formatted(runId);
                InputStream stream = minioClient.getObject(GetObjectArgs.builder()
                        .bucket(bucketName)
                        .object(endResult).build());
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(stream));
                StringBuilder result = new StringBuilder();
                String line;
                while ((line = bufferedReader.readLine()) != null) {
                    result.append(line);
                }
                return result.toString();
            } else {
                log.error("execute workflow  error exitCode:{}",exitCode);
            }
            return "";
        }

    }

    private void unpauseWorkflow(String flowName, String authorization) throws UnirestException {
        HttpResponse<String> response = sendPatchRequest(flowProperties.getAirflow().getUnpauseUrl().formatted(flowName), "{\"is_paused\": false}", authorization);
        if (response.getStatus() != 200) {
            throw new ValidateException("unpause workflow failed, status: %d".formatted(response.getStatus()));
        } else {
            log.info("pause workflow success, flowName: %s".formatted(flowName));
        }
    }

    private String runWorkflow(String flowName, Map<String, Object> config, String authorization) throws UnirestException {
        String body = "{\"conf\": %s}".formatted(JSON.toJSONString(config));
        HttpResponse<String> response = sendPostRequest(flowProperties.getAirflow().getRunUrl().formatted(flowName), body, authorization);
        if (response.getStatus() != 200) {
            throw new ValidateException("run workflow failed, status: %d".formatted(response.getStatus()));
        } else {
            log.info("run workflow success, flowName: %s".formatted(flowName));
        }
        JSONObject jsonObject = JSON.parseObject(response.getBody());
        return jsonObject.getString("dag_run_id");
    }

    private String waitForWorkflowCompletion(String flowName, String dagRunId, String authorization) throws UnirestException {
        dagRunId = URLEncoder.encode(dagRunId, StandardCharsets.UTF_8);
        int maxRetries = 300; // 最大重试次数
        for (int i = 0; i < maxRetries; i++) {
            HttpResponse<String> response = sendGetRequest(flowProperties.getAirflow().getStateUrl().formatted(flowName, dagRunId), authorization);
            if (response.getStatus() != 200) {
                continue;
            }
            JSONObject jsonObject = JSON.parseObject(response.getBody());
            String state = jsonObject.getString("state");
            log.info("query workflow status, flowName: %s,status: %s".formatted(flowName, state));

            if ("success".equals(state)) {
                return getXcomValue(flowName, dagRunId, authorization);
            }
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new ValidateException("Thread was interrupted", e);
            }
        }
        throw new ValidateException("Workflow did not complete within the allowed time");
    }

    private String getXcomValue(String flowName, String dagRunId, String authorization) throws UnirestException {
        HttpResponse<String> response = sendGetRequest(flowProperties.getAirflow().getXcomEntryUrl().formatted(flowName, dagRunId, "90001", "end_result"), authorization);
        if (response.getStatus() != 200) {
            throw new ValidateException("get workflow xcom failed, status: %d".formatted(response.getStatus()));
        }
        JSONObject jsonObject = JSON.parseObject(response.getBody());
        return jsonObject.getString("value");
    }

    public Page<WorkflowDTO> queryWorkflow(QueryWorkflowDTO dto, Page<WorkflowDTO> page) {
        List<WorkflowDTO> list = workflowPublishMapper.queryWorkflow(dto, page);
        page.setRecords(list);
        return page;
    }

    @SneakyThrows
    @Async
    public void executeChatFlow(WorkflowExecuteDTO dto) {
        Map<String, Object> flowTriggerConfig = new HashMap<>(dto.getParams());
        flowTriggerConfig.put("stream", true);
        String flowName = prepareFlowRunParam(dto, flowTriggerConfig);

        String condaPath = flowProperties.getCondaEnv().getCondaPath();
        String envName = flowProperties.getCondaEnv().getEnvName();
        Path path = Paths.get(flowProperties.getDag().getPath(), "%s.py".formatted(flowName));

        Map<String, Object> config = new HashMap<>();
        config.put("conf", flowTriggerConfig);

        Path pythonPath = Paths.get(condaPath, envName, "bin", "python");
        byte[] fileBytes = JSON.toJSONString(config).getBytes(StandardCharsets.UTF_8);
        ByteArrayInputStream inputStream = new ByteArrayInputStream(fileBytes);
        String runId = dto.getRunId();
        String fileName = "%s.txt".formatted(runId);
        String contentType = URLConnection.guessContentTypeFromName(fileName);
        String bucketName = "prefect-variables";
        minioClient.putObject(PutObjectArgs.builder()
                .bucket(bucketName)
                .object(fileName)
                .stream(inputStream, fileBytes.length, -1)
                .contentType(contentType)
                .build());

        String[] cmd = {
                pythonPath.toString(), path.toString(),
                "--runId", runId,
                "--file", fileName
        };
        new ProcessBuilder(cmd).start();
    }

    private String prepareFlowRunParam(WorkflowExecuteDTO dto,
                                       Map<String, Object> flowTriggerConfig) {
        Long resourceId = dto.getResourceId();
        AiResource aiResource = resourceMapper.selectById(dto.getResourceId());

        String nodeConfigMapping;
        String nodeEdgesMapping;
        String flowName;
        if (dto.isDebug()) {
            String flowType = flowProperties.getType();
            flowName = StrUtil.format("{}_{}", aiResource.getName(), aiResource.getId());
            if (StrUtil.equals(flowType, "prefect")) {
                Map<String, Object> nodeMap = prefectFlowUtil.convertToPrefectFlow(dto.getConfigJson(), dto.getConfigTreeJson(),
                        flowName, aiResource.getDescription());
                nodeConfigMapping = JSON.toJSONString(nodeMap.get(NODE_CONFIG_MAPPING_KEY));
                nodeEdgesMapping = JSON.toJSONString(nodeMap.get(NODE_EDGES_MAPPING_KEY));
            } else if (StrUtil.equals(flowType, "langGraph")) {
                Map<String, Object> nodeMap = langGraphUtil.convertToLangGraph(dto.getConfigJson(), flowName, aiResource.getDescription());
                nodeConfigMapping = JSON.toJSONString(nodeMap.get(NODE_CONFIG_MAPPING_KEY));
                nodeEdgesMapping = JSON.toJSONString(nodeMap.get(NODE_EDGES_MAPPING_KEY));
            } else {
                WorkflowDevelop develop = getWorkflowDevelop(resourceId);
                nodeConfigMapping = develop.getNodeConfigMapping();
                nodeEdgesMapping = develop.getNodeEdgesMapping();
            }
        } else {
            WorkflowPublish publish;
            if (dto.getId() != null) {
                publish = workflowPublishMapper.selectById(dto.getId());
            } else {
                publish = getLatestWorkflowPublish(resourceId);
            }
            if (publish == null) {
                throw new ValidateException("workflow not found");
            }
            nodeConfigMapping = publish.getNodeConfigMapping();
            nodeEdgesMapping = publish.getNodeEdgesMapping();
            flowName = StrUtil.format("{}_{}_{}", aiResource.getName(), aiResource.getId(), publish.getVersion());
        }

        if (StrUtil.isEmpty(nodeConfigMapping)) {
            throw new ValidateException("workflow config is empty");
        }
        flowTriggerConfig.put("nodeConfigMapping", JSON.parse(nodeConfigMapping));
        flowTriggerConfig.put("nodeEdgesMapping", JSON.parse(nodeEdgesMapping));

        String clientToken = Base64.encode(selfProperties.getClient().getClientId() + ":" + selfProperties.getClient().getClientSecret());
        flowTriggerConfig.put("clientToken", clientToken);
        flowTriggerConfig.put("clientUrlPrefix", flowProperties.getApi().getPrefix().getClientUrl());
        flowTriggerConfig.put("serverUrlPrefix", flowProperties.getApi().getPrefix().getServerUrl());
        flowTriggerConfig.put("thirdUserId", StpClientUserUtil.getLoginIdAsString());
        return flowName;
    }

    public SseEmitter getConn(String clientId) {
        final SseEmitter sseEmitter = SSE_CACHE.get(clientId);

        if (sseEmitter != null) {
            return sseEmitter;
        } else {
            // 设置连接超时时间，需要配合配置项 spring.mvc.async.request-timeout: 600000 一起使用
            final SseEmitter emitter = new SseEmitter(600000L);
            // 注册超时回调，超时后触发
            emitter.onTimeout(() -> {
                log.info("连接已超时，正准备关闭，clientId = {}", clientId);
                SSE_CACHE.remove(clientId);
            });
            // 注册完成回调，调用 emitter.complete() 触发
            emitter.onCompletion(() -> {
                log.info("连接已关闭，正准备释放，clientId = {}", clientId);
                SSE_CACHE.remove(clientId);
                log.info("连接已释放，clientId = {}", clientId);
            });
            // 注册异常回调，调用 emitter.completeWithError() 触发
            emitter.onError(throwable -> {
                log.error("连接已异常，正准备关闭，clientId = {}", clientId, throwable);
                SSE_CACHE.remove(clientId);
            });

            SSE_CACHE.put(clientId, emitter);
            return emitter;
        }
    }

    public void listenExecuteChatFlow(ClientServiceProvider provider,
                                      String clientId, String runId) {
        try {
            log.info("listenExecuteChatFlow run id {}", runId);
            final SseEmitter sseEmitter = SSE_CACHE.get(clientId);
            ClientConfiguration clientConfiguration = ClientConfiguration.newBuilder()
                    .setEndpoints(endpoints)
                    .build();
            Duration awaitDuration = Duration.ofSeconds(30);
            FilterExpression filterExpression = new FilterExpression(runId, FilterExpressionType.TAG);
            // In most case, you don't need to create too many consumers, singleton pattern is recommended.
            SimpleConsumer consumer = provider.newSimpleConsumerBuilder()
                    .setClientConfiguration(clientConfiguration)
                    // Set the consumer group name.
                    .setConsumerGroup(runId)
                    // set await duration for long-polling.
                    .setAwaitDuration(awaitDuration)
                    // Set the subscription for the consumer.
                    .setSubscriptionExpressions(Collections.singletonMap(topic, filterExpression))
                    .build();
            // Max message num for each long polling.
            int maxMessageNum = 16;
            // Set message invisible duration after it is received.
            Duration invisibleDuration = Duration.ofSeconds(15);
            // Receive message, multi-threading is more recommended.
            do {
                final List<MessageView> messages = consumer.receive(maxMessageNum, invisibleDuration);
                log.info("Received {} message(s)", messages.size());
                for (MessageView message : messages) {
                    final MessageId messageId = message.getMessageId();
                    try {
                        consumer.ack(message);
                        log.info("Message is acknowledged successfully, messageId={}", messageId);
                        String content = StandardCharsets.UTF_8.decode(message.getBody()).toString();
                        if (content.equals("[DONE]")) {
                            sseEmitter.send(
                                    SseEmitter.event().name("done").data("")
                            );
                            sseEmitter.complete();
                            consumer.close();
                            return;
                        }
                        sseEmitter.send(
                                SseEmitter.event().name("message").data(content)
                        );

                        JSONObject jsonObject = JSON.parseObject(content);
                        if (jsonObject.containsKey("type") && "history".equals(jsonObject.getString("type"))) {
                            saveExecuteHistory(jsonObject);
                        }

                    } catch (Throwable t) {
                        log.error("Message is failed to be acknowledged, messageId={}", messageId, t);
                    }
                }
            } while (true);
        } catch (Throwable t) {
            log.error("Failed to receive message", t);
        }
    }

    @SneakyThrows
    public JSONArray queryDevelopWorkflowHistory(String threadId) {
        Map<String, Object> params = new HashMap<>();
        params.put("thread_id", threadId);
        HttpResponse<String> response = Unirest.post("http://localhost:10000/api/agent/inner/workflow/history")
                .header(HttpHeaders.CONTENT_TYPE, "application/json")
                .body(JSON.toJSONString(params))
                .asString();
        if (response.getStatus() != 200) {
            throw new ValidateException("获取历史记录失败");
        }
        System.out.println(response.getBody());
        return JSON.parseArray(response.getBody());
    }

    private void saveExecuteHistory(JSONObject jsonObject) {
        AiExecuteHistory history = new AiExecuteHistory();
        history.setConversationId(jsonObject.getString("conversation_name"));
        history.setThirdUserId(jsonObject.getString("third_user_id"));
        history.setUserInput(jsonObject.getString("user_input"));
        history.setContent(jsonObject.getString("content"));
        aiExecuteHistoryMapper.insert(history);
    }
}
