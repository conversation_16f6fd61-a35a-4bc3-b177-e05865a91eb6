package com.fytec.service.flow;

import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fytec.constant.AgentFlowConstants;
import com.fytec.controller.open.agent.flow.protocol.AgentHandlerError;
import com.fytec.controller.open.agent.flow.protocol.IAgentHandler;
import com.fytec.controller.open.agent.flow.protocol.IHandlerContext;
import com.fytec.controller.open.agent.flow.utils.SqlQueryUtils;
import com.fytec.controller.open.agent.flow.utils.dataquery.dto.Output;
import com.fytec.controller.open.agent.flow.utils.functioncall.FCall;
import com.fytec.controller.open.agent.flow.utils.functioncall.IFunctionCallService;
import com.fytec.dto.llm.ModelCallDTO;
import com.fytec.dto.llm.ModelParamDTO;
import com.fytec.dto.llm.ModelProcessDTO;
import com.fytec.entity.llm.AiModel;
import com.fytec.mapper.model.ModelMapper;
import com.fytec.mapper.prompt.PromptContent4UserMapper;
import com.fytec.model.DynamicModelCallService;
import com.fytec.utils.AiFormatJsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class FunctionCallService implements IFunctionCallService {

    @Value("${agent.utils.echart.format}")
    private Long formatAgentId;

    private final ModelMapper modelMapper;
    private final PromptContent4UserMapper promptContentMapper;

    private final DynamicModelCallService modelService;



    public ModelCallDTO autoFunctionCallContext(String sysPrompt, String userInput) {
        AiModel model = modelMapper.selectOne(
                new LambdaQueryWrapper<AiModel>()
                        .eq(AiModel::isEnable, true)
                        .eq(AiModel::isDefaulted, true)
                        .last("limit 1")
        );
        if (model == null) {
            throw new ValidateException("模型不存在");
        }

        // 设置模型名称，历史记录中需要
        String url = model.getStreamUrl();

        ModelProcessDTO req = new ModelProcessDTO();
        req.setClientId(IdUtil.nanoId());
        req.setModelType(model.getCode());
        req.setSystemMessage(sysPrompt);
        req.setUserMessage(userInput);

        Map<String, Object> paramBody = JSON.parseObject(JSON.toJSONString(req), Map.class);

        List<ModelParamDTO> diversityParams = JSON.parseArray(model.getDiversityParams(), ModelParamDTO.class);
        for (ModelParamDTO diversityParam : diversityParams) {
            paramBody.put(diversityParam.getCode(), diversityParam.getValue());
        }
        List<ModelParamDTO> ioParams = JSON.parseArray(model.getIoParams(), ModelParamDTO.class);
        for (ModelParamDTO ioParam : ioParams) {
            paramBody.put(ioParam.getCode(), ioParam.getValue());
        }
        StringBuffer history = new StringBuffer();
        StringBuffer reasonHistory = new StringBuffer();
        StringBuffer referenceHistory = new StringBuffer();

        ModelCallDTO modelCallDTO = new ModelCallDTO();
        modelCallDTO.setMethodName(model.getNonStreamMethod());
        modelCallDTO.setUrl(url);
        modelCallDTO.setParamBody(paramBody);
        modelCallDTO.setHistory(history);
        modelCallDTO.setReasonHistory(reasonHistory);
        modelCallDTO.setReferenceHistory(referenceHistory);
        Map<String, Object> stringObjectMap = modelService.callModelNonStream(modelCallDTO);
        return modelCallDTO;
    }


    @Override

    public void checkAndBuildEchartFormat(String body, IHandlerContext context, IAgentHandler currentNode) {
        String echartType = SqlQueryUtils.checkHasEchartType(context.getRawInput());
        log.info("checkAndBuildEchartFormat,input:{},type:{}",context.getRawInput(),echartType);
        long startTime= System.currentTimeMillis();
        if(StringUtils.isNotBlank(echartType)){
            // todo 解读作为最后一个节点，这边把可能的图表存储下来,需要生成图表
            // todo 将数据完全根据智能体生成，数据结构也需要处理一下
            // 变成output的格式，这种核心就是放echart模板数据
            StringBuilder input = new StringBuilder();
            input.append("【已知信息】\n");
            input.append(body);
            input.append("【用户问题】:\n");
            input.append(context.getRawInput());
            String resultJson = null;
            try {
                resultJson = context.getAgentFlowService().execAgentNodeById(
                        AgentFlowConstants.AGENT_RUN_TYPE.format,
                        this.formatAgentId, context, input.toString());
                if(StringUtils.isNotBlank(resultJson)){
                    log.error("checkAndBuildEchartFormat format json:" + resultJson);

                    resultJson = AiFormatJsonUtil.formatJson(resultJson);
                    Map resultMap = JSON.parseObject(resultJson,Map.class);
                    Output object = new Output();
                    object.setConfigs(resultMap);
                    currentNode.printExtraInfo(context,System.currentTimeMillis() - startTime, AgentFlowConstants.AgentExtraInfoProtocol.echart.name(),
                            JSON.toJSONString(object));
                }else{
                    log.error("checkAndBuildEchartFormat null");
                }
            } catch (AgentHandlerError e) {
//                        e.printStackTrace();
                log.error("getEduResourceInfo format chart error:" + e.getErrorMsg());
                currentNode.printExtraInfo(context,System.currentTimeMillis() - startTime,  AgentFlowConstants.AgentExtraInfoProtocol.error.name(),
                        e.getErrorMsg());
            } catch (JSONException e){
                log.error("getEduResourceInfo format chart error:" + e.getMessage());
                currentNode.printExtraInfo(context, System.currentTimeMillis() - startTime, AgentFlowConstants.AgentExtraInfoProtocol.error.name(),
                        StringUtils.isBlank(resultJson)?e.getMessage():
                                (String.format("json 格式化错误 %s，原始json:", e.getMessage())+resultJson));
            }
        }

    }

}
