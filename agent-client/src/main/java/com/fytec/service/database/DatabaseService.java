package com.fytec.service.database;


import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.constant.Constants;
import com.fytec.dto.database.ConnectionTestDTO;
import com.fytec.dto.database.DatabaseQueryDTO;
import com.fytec.dto.database.ExecuteSqlDTO;
import com.fytec.entity.database.DatabaseBasic;
import com.fytec.entity.resource.AiResource;
import com.fytec.mapper.database.DatabaseMapper;
import com.fytec.mapper.resource.ResourceMapper;
import com.fytec.utils.RSAUtil;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.sql.DataSource;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class DatabaseService {
    private final ResourceMapper resourceMapper;
    private final DatabaseMapper databaseMapper;

    public JSONObject getDatabaseInfo(Long resourceId, Long dbId) {
        JSONObject result = new JSONObject();
        AiResource aiResource;
        DatabaseBasic databaseBasic;
        if (ObjectUtil.isNotEmpty(resourceId)) {
            aiResource = resourceMapper.selectById(resourceId);
            if (aiResource == null) {
                throw new ValidateException("数据库资源不存在");
            }
            databaseBasic = databaseMapper.selectOne(
                    new LambdaQueryWrapper<DatabaseBasic>()
                            .eq(DatabaseBasic::getResourceId, resourceId)
                            .last("limit 1")
            );
            if (databaseBasic == null) {
                throw new ValidateException("数据库资源不存在");
            }
        } else if (ObjectUtil.isNotEmpty(dbId)) {
            databaseBasic = databaseMapper.selectById(dbId);
            if (databaseBasic == null) {
                throw new ValidateException("数据库资源不存在");
            }
            aiResource = resourceMapper.selectById(databaseBasic.getResourceId());
            if (aiResource == null) {
                throw new ValidateException("数据库资源不存在");
            }
        } else {
            throw new ValidateException("参数错误");
        }

        result.put("resourceId", aiResource.getId());
        result.put("dbId", databaseBasic.getId());
        result.put("name", aiResource.getName());
        result.put("description", aiResource.getDescription());
        result.put("logo", aiResource.getLogo());
        result.put("dbType", databaseBasic.getType());
        result.put("dbConfigs", JSON.parseObject(databaseBasic.getConfig()));
        return result;
    }

    public boolean testConn(ConnectionTestDTO dto) {
        String jdbcUrl = getJdbcUrl(dto.getHost(), dto.getPort(), dto.getSchema(), dto.getDbType());
        DataSource dataSource = init(jdbcUrl, dto.getUsername(), dto.getPassword());
        try {
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);

            // 示例：查询数据库版本
            jdbcTemplate.queryForObject("SELECT VERSION()", String.class);
            return true;
        } catch (Exception e) {
            return false;
        } finally {
            if (dataSource instanceof AutoCloseable) {
                try {
                    ((AutoCloseable) dataSource).close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private String getJdbcUrl(String host, String port, String schema, String dbType) {
        Constants.DB_TYPE _dbType = Constants.DB_TYPE.valueOf(dbType);
        return switch (_dbType) {
            case mysql ->
                    "jdbc:mysql://" + host + ":" + port + "/" + schema + "?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=GMT%2b8&allowPublicKeyRetrieval=true";
            case oracle -> "jdbc:oracle:thin:@" + host + ":" + port + ":" + schema;
            default -> throw new ValidateException("系统暂不支持数据源类型：[" + dbType + "]");
        };
    }

    private DataSource init(String jdbcUrl, String username, String password) {
        password = RSAUtil.decryptByPrivateKey(password);

        HikariConfig config = new HikariConfig();
        config.setJdbcUrl(jdbcUrl);
        config.setUsername(username);
        config.setPassword(password);
        // 配置其他连接池参数...
        // 最小空闲连接
        config.setMinimumIdle(5);
        // 最大连接数
        config.setMaximumPoolSize(10);
        // 自动提交从池中返回的连接
        config.setAutoCommit(true);
        // 空闲连接超时时间
        config.setIdleTimeout(TimeUnit.MINUTES.toMillis(10));
        // 连接最大存活时间
        config.setMaxLifetime(TimeUnit.MINUTES.toMillis(30));
        // 连接超时时间
        config.setConnectionTimeout(TimeUnit.SECONDS.toMillis(30));
        return new HikariDataSource(config);
    }

    public JSONArray getTables(String dbId) {
        DatabaseBasic databaseBasic = databaseMapper.selectById(dbId);
        if (databaseBasic == null) {
            throw new ValidateException("数据库资源不存在");
        }

        JSONArray result = new JSONArray();

        JSONObject dbConfig = JSON.parseObject(databaseBasic.getConfig());
        String schema = dbConfig.getString("schema");
        DataSource dataSource = initDataSource(dbConfig, databaseBasic.getType());
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
        ResultSet tables;
        try {
            if (jdbcTemplate.getDataSource() == null) {
                throw new ValidateException("JdbcTemplate has no DataSource configured!");
            }
            DatabaseMetaData dbMetaData = jdbcTemplate.getDataSource().getConnection().getMetaData();
            String[] types = {"TABLE"};
            tables = dbMetaData.getTables(schema, null, null, types);
            while (tables.next()) {
                JSONObject tableMeta = new JSONObject();
                String tableName = tables.getString("TABLE_NAME");
                tableMeta.put("tableDesc", tables.getString("REMARKS"));
                tableMeta.put("tableName", tableName);
                tableMeta.put("tableType", tables.getString("TABLE_TYPE"));

                // 获取表的列信息
                ResultSet columns = dbMetaData.getColumns(schema, null, tableName, null);
                JSONArray columnMetaArray = new JSONArray();
                while (columns.next()) {
                    JSONObject columnMeta = new JSONObject();
                    columnMeta.put("columnName", columns.getString("COLUMN_NAME"));
                    columnMeta.put("typeName", columns.getString("TYPE_NAME"));
                    columnMeta.put("columnDesc", columns.getString("REMARKS"));
                    columnMeta.put("columnType", columns.getInt("DATA_TYPE"));
                    columnMeta.put("defaultVal", columns.getString("COLUMN_DEF"));
                    columnMeta.put("isNullAble", columns.getString("IS_NULLABLE"));
                    columnMeta.put("columnSize", columns.getString("COLUMN_SIZE"));
                    columnMeta.put("decimalDigits", columns.getInt("DECIMAL_DIGITS"));
                    columnMeta.put("isAutoIncrement", columns.getString("IS_AUTOINCREMENT"));
                    columnMeta.put("ordinalPosition", columns.getInt("ORDINAL_POSITION"));
                    columnMetaArray.add(columnMeta);
                }
                tableMeta.put("columns", columnMetaArray);
                result.add(tableMeta);
            }
        } catch (Exception e) {
            log.error("获取数据库表信息失败", e);
        }
        return result;
    }

    private DataSource initDataSource(JSONObject dbConfig, String dbType) {
        String jdbcUrl = getJdbcUrl(dbConfig.getString("host"),
                dbConfig.getString("port"),
                dbConfig.getString("schema"),
                dbType);
        return init(jdbcUrl, dbConfig.getString("username"), dbConfig.getString("password"));
    }

    public JSONObject executeSql(ExecuteSqlDTO executeSqlDTO) {
        DatabaseBasic databaseBasic = databaseMapper.selectById(executeSqlDTO.getDbId());
        if (databaseBasic == null) {
            throw new ValidateException("数据库资源不存在");
        }
        JSONObject dbConfig = JSON.parseObject(databaseBasic.getConfig());
        DataSource dataSource = initDataSource(dbConfig, databaseBasic.getType());

        try {
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);

            JSONObject result = new JSONObject();
            if (executeSqlDTO.getSql().toLowerCase().startsWith("select")) {
                List<Map<String, Object>> queryList = jdbcTemplate.queryForList(executeSqlDTO.getSql());
                result.put("outputList", queryList);
                result.put("rowNum", queryList.size());
            } else {
                int updateCount = jdbcTemplate.update(executeSqlDTO.getSql());
                result.put("outputList", null);
                result.put("rowNum", updateCount);
            }
            return result;
        } catch (Exception e) {
            log.error("执行SQL失败", e);
            throw new ValidateException("执行SQL失败");
        } finally {
            if (dataSource instanceof AutoCloseable) {
                try {
                    ((AutoCloseable) dataSource).close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

    }

    public Page<JSONObject> queryDatabase(DatabaseQueryDTO dto, Page<JSONObject> page) {
        List<JSONObject> databases = databaseMapper.queryDatabase(dto, page);
//        if (CollUtil.isNotEmpty(knowledgeDTOS)) {
//            for (KnowledgeDetailDTO knowledgeDTO : knowledgeDTOS) {
//                knowledgeDTO.setDocs(knowledgeDocMapper.selectList(new LambdaQueryWrapper<KnowledgeDoc>()
//                        .select(KnowledgeDoc::getFileName, KnowledgeDoc::getFileSize)
//                        .eq(KnowledgeDoc::getKnowledgeId, knowledgeDTO.getId())));
//            }
//        }
        page.setRecords(databases);
        return page;
    }
}
