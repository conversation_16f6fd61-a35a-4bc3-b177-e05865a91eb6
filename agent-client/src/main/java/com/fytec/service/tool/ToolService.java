package com.fytec.service.tool;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fytec.config.ClientProperties;
import com.fytec.dto.llm.Txt2ImgCallDTO;
import com.fytec.dto.response.Txt2ImgDTO;
import com.fytec.dto.tool.InvoiceOcrDTO;
import com.fytec.dto.tool.Txt2ImgExecuteDTO;
import com.fytec.entity.llm.AiModel;
import com.fytec.mapper.model.ModelMapper;
import com.fytec.model.DynamicModelCallService;
import com.fytec.token.ClientTokenService;
import com.fytec.util.MinioUtil;
import com.fytec.util.UUIDGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Service
@Slf4j
@Transactional
@RequiredArgsConstructor
public class ToolService {
    private final WebClient webClient = WebClient.create();

    private final ModelMapper modelMapper;
    private final DynamicModelCallService modelService;
    private final MinioUtil minioUtil;

    private final ClientProperties clientProperties;
    private final ClientTokenService clientTokenService;

    public List<Txt2ImgDTO> generateImageByText(Txt2ImgExecuteDTO dto) {
        AiModel model;
        if (StrUtil.isNotBlank(dto.getModelId())) {
            model = modelMapper.selectById(dto.getModelId());
        } else {
            model = modelMapper.selectOne(
                    new LambdaQueryWrapper<AiModel>()
                            .eq(AiModel::isEnable, true)
                            .eq(AiModel::getType, "txt2img")
                            .orderByDesc(AiModel::getCreateTime)
                            .last("limit 1")
            );
        }
        if (model == null) {
            throw new ValidateException("模型不存在");
        }

        Txt2ImgCallDTO txt2ImgCallDTO = new Txt2ImgCallDTO();
        txt2ImgCallDTO.setMethodName(model.getNonStreamMethod());
        txt2ImgCallDTO.setModelCode(model.getCode());
        txt2ImgCallDTO.setUrl(model.getNonStreamUrl());
        txt2ImgCallDTO.setInputs(dto.getInputs());
        List<Txt2ImgDTO> txt2ImgDTOList = modelService.callModelTxt2Img(txt2ImgCallDTO);

        if (CollUtil.isNotEmpty(txt2ImgDTOList)) {
            for (Txt2ImgDTO txt2ImgDTO : txt2ImgDTOList) {
                if (StrUtil.isNotBlank(txt2ImgDTO.getB64Json())) {
                    // 上传到我们的文件服务器上，图片
                    String fileName = "t2i_" + UUIDGenerator.getUUID() + ".png";
                    //  上传附件
                    byte[] decode = Base64.getDecoder().decode(txt2ImgDTO.getB64Json());
                    minioUtil.upload(decode, fileName);
                    String url = minioUtil.getUrl(fileName);
                    txt2ImgDTO.setUrl(url);
                }
            }
        }
        return txt2ImgDTOList;
    }

    @Async
    public CompletableFuture<Map<String, Object>> invoiceOcr(InvoiceOcrDTO dto) {
        Map<String, Object> invoiceInfo = new HashMap<>();
        invoiceInfo.put("id", dto.getId());
        invoiceInfo.put("invoiceNo", "");
        invoiceInfo.put("totalPriceAndTax", "");

        Map<String, String> params = new HashMap<>();
        params.put("fileUrl", dto.getUrl());

        Mono<String> mono = webClient.post()
                .uri(clientProperties.getOcrConfig().getOrcVatInvoiceUrl())
                .contentType(MediaType.APPLICATION_JSON)
                .headers(httpHeaders -> {
                    httpHeaders.setBearerAuth(clientTokenService.getToken());
                })
                .body(BodyInserters.fromValue(params))
                .retrieve()
                .bodyToMono(String.class);
        String result = mono.block();
        log.info("发票识别结果:{}", result);
        if (StrUtil.isBlank(result)) {
            return CompletableFuture.completedFuture(invoiceInfo);
        }

        JSONObject jsonObject = JSON.parseObject(result);
        if (jsonObject.containsKey("error")) {
            return CompletableFuture.completedFuture(invoiceInfo);
        }

        if (StrUtil.isNotBlank(jsonObject.getString("InvoiceCode"))) {
            invoiceInfo.put("invoiceNo", jsonObject.getString("InvoiceCode") + jsonObject.getString("InvoiceNum"));
        } else {
            invoiceInfo.put("invoiceNo", jsonObject.getString("InvoiceNum"));
        }
        invoiceInfo.put("totalPriceAndTax", jsonObject.get("InvoiceAmountAndTax"));
        return CompletableFuture.completedFuture(invoiceInfo);
    }
}
