package com.fytec.service;

import cn.dev33.satoken.secure.BCrypt;
import cn.dev33.satoken.stp.SaTokenInfo;
import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fytec.dto.LoginDTO;
import com.fytec.dto.wechat.WechatWorkUser;
import com.fytec.entity.system.SysUser;
import com.fytec.entity.system.SysUserRole;
import com.fytec.mapper.system.SysUserMapper;
import com.fytec.mapper.system.SysUserRoleMapper;
import com.fytec.satoken.StpClientUserUtil;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;

@Service
@RequiredArgsConstructor
public class LoginService {
    private final SysUserMapper sysUserMapper;
    private final SysUserRoleMapper sysUserRoleMapper;

    public SaTokenInfo generateLoginAccessToken(LoginDTO loginDTO) {
        SysUser sysUser = sysUserMapper.selectOne(
                new LambdaQueryWrapper<SysUser>()
                        .eq(SysUser::getLoginName, loginDTO.getName())
        );
        if (sysUser == null || !BCrypt.checkpw(loginDTO.getPassword(), sysUser.getPassword())) {
            throw new ValidateException("用户不存在或密码不正确");
        }
        StpClientUserUtil.login(sysUser.getId());
        return StpClientUserUtil.getTokenInfo();
    }


    @SneakyThrows
    public SaTokenInfo generateLoginAccessTokenByOa(LoginDTO loginDTO) {
        SysUser sysUser = sysUserMapper.selectOne(
                new LambdaQueryWrapper<SysUser>()
                        .eq(SysUser::getLoginName, loginDTO.getName())
        );
        if (sysUser == null) {
            throw new ValidateException("OA登录失败,用户不存在或密码不正确");
        }

        String OA_LOGIN_URL = "https://fykj.fengyuntec.com/api/valid/nameandpassword";
        HttpResponse<String> response = Unirest.post(OA_LOGIN_URL)
                .header("Content-type", "application/x-www-form-urlencoded")
                .field("userName", loginDTO.getName())
                .field("password", loginDTO.getPassword())
                .asString();
        if (response.getStatus() != 200) {
            throw new ValidateException("OA登录失败,用户不存在或密码不正确");
        }
        JSONObject jsonObject = JSON.parseObject(response.getBody());
        String code = jsonObject.getString("code");
        if (!StrUtil.equals("0", code)) {
            throw new ValidateException("OA登录失败,用户不存在或密码不正确");
        }
        StpClientUserUtil.login(sysUser.getId());
        return StpClientUserUtil.getTokenInfo();
    }


    public SaTokenInfo generateWechatWorkLoginAccessToken(WechatWorkUser user) {
        SysUser sysUser = sysUserMapper.selectOne(
                new LambdaQueryWrapper<SysUser>()
                        .eq(SysUser::getThirdUserId, user.getUserId())
        );

        if (sysUser == null) {
            sysUser = new SysUser();
            sysUser.setName(user.getName());
            sysUser.setThirdUserId(user.getUserId());
            sysUser.setTel(user.getMobile());
            sysUser.setPassword(BCrypt.hashpw("3456@erty"));
            sysUser.setStatus("A");
            sysUser.setIsSys("N");
            sysUserMapper.insert(sysUser);

            SysUserRole sysUserRole = new SysUserRole();
            sysUserRole.setUserId(sysUser.getId());
            sysUserRole.setRoleId(2L);//普通成员
            sysUserRoleMapper.insert(sysUserRole);
        }
        StpClientUserUtil.login(sysUser.getId());
        return StpClientUserUtil.getTokenInfo();
    }
}
