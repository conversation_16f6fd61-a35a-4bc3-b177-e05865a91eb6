package com.fytec.service.agent;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.agent.AddConstantDTO;
import com.fytec.dto.agent.AgentExecuteDTO;
import com.fytec.dto.agent.UpdateConstantDTO;
import com.fytec.entity.agent.AgentConstant;
import com.fytec.mapper.agent.AgentConstantMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.fytec.constant.Constants.DEFAULT_REFER_PROMPT;

@Service
@Transactional
@Slf4j
@AllArgsConstructor
public class ConstantService {

    private final AgentConstantMapper constantMapper;

    public boolean exist(String code, Long id) {
        LambdaQueryWrapper<AgentConstant> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentConstant::getCode, code);
        if (id != null) {
            queryWrapper.ne(AgentConstant::getId, id);
        }
        return constantMapper.selectCount(queryWrapper) > 0;
    }

    public Long add(AddConstantDTO dto) {
        if (exist(dto.getCode(), null)) {
            throw new RuntimeException("常量code已存在");
        }
        AgentConstant constant = new AgentConstant();
        BeanUtils.copyProperties(dto, constant);
        constantMapper.insert(constant);
        return constant.getId();
    }

    public Long update(UpdateConstantDTO dto) {
        if (exist(dto.getCode(), dto.getId())) {
            throw new RuntimeException("常量code已存在");
        }
        AgentConstant constant = constantMapper.selectById(dto.getId());
        BeanUtils.copyProperties(dto, constant);
        constantMapper.updateById(constant);
        return constant.getId();
    }

    public void delete(@NotNull(message = "ID不能为空") Long id) {
        constantMapper.deleteById(id);
    }

    public Page<AgentConstant> queryPage(String name, Page page) {
        LambdaQueryWrapper<AgentConstant> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(name)) {
            queryWrapper.or()
                    .like(AgentConstant::getCode, name)
                    .like(AgentConstant::getName, name);
        }
        return constantMapper.selectPage(page, queryWrapper);
    }

    public List<AgentConstant> queryList(String name) {
        LambdaQueryWrapper<AgentConstant> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(AgentConstant.class, i -> !i.getProperty().equals("value"));
        if (StringUtils.isNotBlank(name)) {
            //根据code或名称查询
            queryWrapper.or()
                    .like(AgentConstant::getCode, name)
                    .like(AgentConstant::getName, name);
        }
        return constantMapper.selectList(queryWrapper);
    }


    public String convertPromptConstant(String prompt) {
        if (StringUtils.isBlank(prompt)) {
            return prompt;
        }
        //识别并替换prompt字符串中的"${XX}"格式的内容为常量值
        Pattern pattern = Pattern.compile("\\$\\{([^}]+)}");
        Matcher matcher = pattern.matcher(prompt);
        while (matcher.find()) {
            String constantCode = matcher.group(1);
            System.out.println(constantCode);
            // 从数据库中查询常量值
            AgentConstant agentConstant = constantMapper.selectOne(new LambdaQueryWrapper<AgentConstant>()
                    .eq(AgentConstant::getCode, constantCode).last("LIMIT 1")
            );
            if (agentConstant != null) {
                prompt = prompt.replace("${" + constantCode + "}", agentConstant.getValue());
            }
        }
        return prompt;
    }

    public String addReferPrompt(String sysPrompt) {
        //避免历史有问题，兼容一下
        if(!sysPrompt.contains("## 标注规则")){
            sysPrompt = sysPrompt + DEFAULT_REFER_PROMPT;
        }
        return sysPrompt;
    }

}
