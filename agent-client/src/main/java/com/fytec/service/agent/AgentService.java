package com.fytec.service.agent;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fytec.config.ClientProperties;
import com.fytec.config.milvus.MilvusClient;
import com.fytec.constant.AgentFlowConstants;
import com.fytec.constant.Constants;
import com.fytec.constant.enums.ReRankerModelEnum;
import com.fytec.dto.agent.*;
import com.fytec.dto.application.ApplicationAgentDTO;
import com.fytec.dto.flow.WorkflowDTO;
import com.fytec.dto.knowledge.*;
import com.fytec.dto.llm.*;
import com.fytec.dto.model.ModelDTO;
import com.fytec.dto.plugin.PluginDTO;
import com.fytec.dto.voice.VoiceDTO;
import com.fytec.entity.agent.*;
import com.fytec.entity.application.ApplicationBasic;
import com.fytec.entity.knowledge.KnowledgeDoc;
import com.fytec.entity.llm.AiModel;
import com.fytec.entity.plugin.PluginPublish;
import com.fytec.entity.workflow.WorkflowPublish;
import com.fytec.mapper.agent.*;
import com.fytec.mapper.application.ApplicationBasicMapper;
import com.fytec.mapper.model.ModelMapper;
import com.fytec.mapper.plugin.PluginPublishMapper;
import com.fytec.mapper.workflow.WorkflowPublishMapper;
import com.fytec.model.DynamicModelCallService;
import com.fytec.reranker.DynamicReRankerCallService;
import com.fytec.satoken.StpClientUserUtil;
import com.fytec.service.knowledge.KnowledgeGroupService;
import com.fytec.service.knowledge.KnowledgeService;
import com.fytec.token.ClientTokenService;
import com.fytec.util.VersionUtils;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import com.volcengine.ark.runtime.model.completion.chat.ChatFunction;
import com.volcengine.ark.runtime.model.completion.chat.ChatTool;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
@Transactional
@Slf4j
@AllArgsConstructor
public class AgentService {
    private final AgentBasicMapper agentBasicMapper;

    private final AgentDevelopMapper agentDevelopMapper;

    private final DynamicModelCallService modelService;

    private final ModelMapper modelMapper;

    private final AgentDevelopHistoryMapper agentDevelopHistoryMapper;

    private final AgentPublishMapper agentPublishMapper;

    private final AgentPublishHistoryMapper agentPublishHistoryMapper;

    private final KnowledgeService knowledgeService;

    private final WorkflowPublishMapper workflowPublishMapper;

    private final PluginPublishMapper pluginPublishMapper;

    private final ClientProperties clientProperties;

    private final DynamicReRankerCallService reRankerCallService;

    private final ConstantService constantService;

    private final ApplicationBasicMapper applicationBasicMapper;

    private final AgentSftService agentSftService;

    private final ClientTokenService clientTokenService;

    private final KnowledgeGroupService knowledgeGroupService;

    public Long addAgent(AddAgentDTO dto) {
        AgentBasic agentBasic = new AgentBasic();
        BeanUtils.copyProperties(dto, agentBasic);
        agentBasic.setStatus(Constants.PUBLISH_STATUS_0);
        agentBasicMapper.insert(agentBasic);

        //保存开发版信息
        AgentDevelop develop = new AgentDevelop();
        develop.setAgentId(agentBasic.getId());
        develop.setType(StrUtil.isNotBlank(dto.getType()) ? dto.getType() : Constants.AGENT_TYPE.llm.name());
        develop.setModel(JSON.toJSONString(getDefaultModel()));
        develop.setKnowledge(JSON.toJSONString(getDefaultKnowledge()));
        develop.setPositiveKnowledge(JSON.toJSONString(getDefaultKnowledge()));
        develop.setHistoryKnowledge(JSON.toJSONString(getDefaultKnowledge()));
        develop.setNegativeKnowledge(JSON.toJSONString(getDefaultKnowledge()));
//        develop.setParams(JSON.toJSONString(getDefaultParams()));
        develop.setTips(JSON.toJSONString(new ArrayList<String>()));

        agentDevelopMapper.insert(develop);
        return agentBasic.getId();
    }

    private KnowledgeConfigDTO getDefaultKnowledge() {
        KnowledgeConfigDTO configDTO = new KnowledgeConfigDTO();
        configDTO.setTopK(5);
        configDTO.setStrategy("混合");
        configDTO.setMinScore(0.5f);
        configDTO.setRerank(false);
        return configDTO;
    }

    private AgentParamsDto getDefaultParams() {
        AgentParamsDto dto = new AgentParamsDto();
//        dto.setBlockButPrintThink(false);
        return dto;
    }

    public Long updateAgent(UpdateAgentDTO dto) {
        AgentBasic agentBasic = agentBasicMapper.selectById(dto.getId());
        if (agentBasic == null) {
            throw new ValidateException("智能体不存在");
        }
        List<String> roleList = StpClientUserUtil.getRoleList();
        if (roleList.contains("admin")) {

        } else {
            if (!StrUtil.equals(agentBasic.getCreateBy(), StpClientUserUtil.getLoginIdAsString())) {
                throw new ValidateException("非本人创建智能体不可编辑");
            }
        }

        BeanUtils.copyProperties(dto, agentBasic);
        agentBasicMapper.updateById(agentBasic);
        return agentBasic.getId();
    }

    private ModelDTO getDefaultModel() {
        AiModel model = modelMapper.selectOne(new LambdaQueryWrapper<AiModel>().eq(AiModel::isDefaulted, true));
        if (model == null) {
            return null;
        }
        ModelDTO modelDTO = new ModelDTO();
        BeanUtils.copyProperties(model, modelDTO);
        modelDTO.setDiversityParams(JSON.parseArray(model.getDiversityParams(), ModelParamDTO.class));
        modelDTO.setIoParams(JSON.parseArray(model.getIoParams(), ModelParamDTO.class));
        return modelDTO;

    }

    public LocalDateTime developAgent(DevelopAgentDTO dto) {
        AgentBasic agentBasic = agentBasicMapper.selectById(dto.getAgentId());
        if (agentBasic == null) {
            throw new ValidateException("智能体不存在");
        }
        AgentDevelop develop = agentDevelopMapper.selectOne(new LambdaQueryWrapper<AgentDevelop>()
                .eq(AgentDevelop::getAgentId, dto.getAgentId()));
        if (develop == null) {
            throw new ValidateException("智能体不存在!");
        }
        BeanUtils.copyProperties(dto, develop);
        develop.setModel(JSON.toJSONString(dto.getModel()));
        develop.setPrompt(dto.getPrompt());
        if (dto.getPlugins() != null) {
            develop.setPlugins(JSON.toJSONString(dto.getPlugins()));
        }
        // 更新时清空工作流，空数组需要更新
        if (dto.getWorkflows() != null) {
            develop.setWorkflows(JSON.toJSONString(dto.getWorkflows()));
        }
        if (dto.getTips() != null) {
            develop.setTips(JSON.toJSONString(dto.getTips()));
        }
        if (ObjectUtil.isNotEmpty(dto.getKnowledge())) {
            develop.setKnowledge(JSON.toJSONString(dto.getKnowledge()));
        }
        if (ObjectUtil.isNotEmpty(dto.getPositiveKnowledge())) {
            develop.setPositiveKnowledge(JSON.toJSONString(dto.getPositiveKnowledge()));
        }
        if (ObjectUtil.isNotEmpty(dto.getHistoryKnowledge())) {
            develop.setHistoryKnowledge(JSON.toJSONString(dto.getHistoryKnowledge()));
        }
        if (ObjectUtil.isNotEmpty(dto.getParams())) {
//            develop.setParams(JSON.toJSONString(dto.getParams()));
        }

        if (ObjectUtil.isNotEmpty(dto.getNegativeKnowledge())) {
            develop.setNegativeKnowledge(JSON.toJSONString(dto.getNegativeKnowledge()));
        }
        if (ObjectUtil.isNotEmpty(dto.getSuggestions())) {
            develop.setSuggestions(JSON.toJSONString(dto.getSuggestions()));
        }
        if (ObjectUtil.isNotEmpty(dto.getVoice())) {
            develop.setVoice(JSON.toJSONString(dto.getVoice()));
        }
        agentDevelopMapper.updateById(develop);
        return LocalDateTime.now();
    }

    public Page<AgentBasicDTO> developAgentPage(AgentQueryDTO dto, Page<AgentBasicDTO> page) {
        if (dto.getApplicationId() != null) {
            dto.setAgentIds(getAgentIdsByApplicationId(dto.getApplicationId()));
        }

        List<AgentBasicDTO> list = agentBasicMapper.developAgentPage(page, dto);
        page.setRecords(list);
        return page;
    }

    public void debug(HttpServletResponse response, AgentDebugDTO dto) {
        AgentDevelop develop = agentDevelopMapper.selectOne(new LambdaQueryWrapper<AgentDevelop>()
                .eq(AgentDevelop::getAgentId, dto.getAgentId()));
        if (develop == null) {
            throw new ValidateException("智能体不存在!");
        }
        if (StringUtils.isBlank(develop.getModel())) {
            throw new ValidateException("智能体未配置模型");
        }
        ModelDTO modelDTO = JSON.parseObject(develop.getModel(), ModelDTO.class);
        AiModel model = modelMapper.selectById(modelDTO.getId());
        if (model == null) {
            throw new ValidateException("模型不存在");
        }

        String url = model.getStreamUrl();

        ModelProcessDTO req = new ModelProcessDTO();
        req.setClientId(UUID.randomUUID().toString());
        req.setModelType(model.getCode());
        req.setSystemMessage(StringUtils.isNotBlank(develop.getPrompt()) ? constantService.convertPromptConstant(develop.getPrompt()) : Constants.DEFAULT_PROMPT);
        req.setUserMessage(dto.getUserInput());

        List<ChatToolDTO> tools = new ArrayList<>();
        //查询工作流工具---start
        addWorkflowTool(tools, develop.getWorkflows());
        //查询工作流工具---end

        //查询技能工具---start
        addPluginTool(tools, develop.getPlugins());
        //查询技能工具---end
        req.setTools(tools);

        req.setMultiTurn(true);
        //查询历史---start
        int historySize = 3;
        for (ModelParamDTO ioParam : modelDTO.getIoParams()) {
            if (ioParam.getCode().equals("historySize")) {
                if (ioParam.getValue() != null)
                    historySize = (int) ioParam.getValue();
            }
        }
        List<AgentDevelopHistory> agentHistories = null;
        if (historySize > 0) {
            agentHistories = agentDevelopHistoryMapper.selectList(new LambdaQueryWrapper<AgentDevelopHistory>()
                    .eq(AgentDevelopHistory::getDebugId, StpClientUserUtil.getLoginIdAsLong())
                    .eq(AgentDevelopHistory::getAgentId, develop.getAgentId())
                    .last("LIMIT " + historySize)
                    .orderByDesc(AgentDevelopHistory::getCreateTime)
            );
        }

        List<Map<String, String>> histories = new ArrayList<>();
        List<KnowledgeDocHistoryDTO> knowledgeDocInfos = new ArrayList<>();
        if (CollUtil.isNotEmpty(agentHistories)) {
            for (int i = agentHistories.size() - 1; i >= 0; i--) {
                AgentDevelopHistory agentHistory = agentHistories.get(i);
                Map<String, String> map = new HashMap<>();
                map.put(agentHistory.getUserInput(), agentHistory.getAnswer());
                histories.add(map);

                // 历史中的附件记录
                if (StrUtil.isNotBlank(agentHistory.getKnowledgeDocInfo())) {
                    knowledgeDocInfos.addAll(JSON.parseArray(agentHistory.getKnowledgeDocInfo(), KnowledgeDocHistoryDTO.class));
                }
            }
        }
        req.setHistories(histories);
        //查询历史---end

        // 处理多模态模型需要传入图片url
        boolean isMultimodal = StrUtil.equals(Constants.MODEL_TYPE.multimodal.name(), model.getType());
        List<String> imageUrls = new ArrayList<>();

        //查询知识库---start
        // 历史中的附件处理，知识库id为键，docId列表为值
        Set<Long> knowledgeDocIds = new HashSet<>();
        Map<Long, Set<Long>> hisKnowledgeDocMap = new HashMap<>();
        if (CollUtil.isNotEmpty(knowledgeDocInfos)) {
            for (KnowledgeDocHistoryDTO knowledgeDocHistoryDTO : knowledgeDocInfos) {
                if (isMultimodal) {
                    if (StrUtil.equalsAny(knowledgeDocHistoryDTO.getFileType(), "png", "jpeg", "jpg")) {
                        imageUrls.add(knowledgeDocHistoryDTO.getFileUrl());
                        log.debug("多模态模型过滤知识库图片片段:{}", knowledgeDocHistoryDTO.getId());
                        continue;
                    }
                }

                hisKnowledgeDocMap.computeIfAbsent(
                        knowledgeDocHistoryDTO.getKnowledgeId(),
                        k -> new HashSet<>()
                ).add(knowledgeDocHistoryDTO.getId());
                knowledgeDocIds.add(knowledgeDocHistoryDTO.getId());
            }
        }
        // 对话中新添加上传附件的，添加到知识库id对应的docId列表中
        if (CollUtil.isNotEmpty(dto.getFileDocIds())) {
            // 有文档的，使用知识库id为键，docId List为值
            List<KnowledgeDoc> docs = knowledgeService.getKnowledgeDoc(dto.getFileDocIds());
            for (KnowledgeDoc doc : docs) {
                if (isMultimodal) {
                    if (StrUtil.equalsAny(doc.getFileType(), "png", "jpeg", "jpg")) {
                        imageUrls.add(doc.getFileUrl());
                        log.debug("多模态模型过滤知识库图片片段:{}", doc.getId());
                        continue;
                    }
                }

                hisKnowledgeDocMap.computeIfAbsent(
                        doc.getKnowledgeId(),
                        k -> new HashSet<>()
                ).add(doc.getId());
                knowledgeDocIds.add(doc.getId());
            }
        }


        List<VectorResultDTO> docsList;
        if (CollUtil.isNotEmpty(hisKnowledgeDocMap)) {
            // 使用智能体外的知识库
            AgentExecuteDTO executeDTO = new AgentExecuteDTO();
            executeDTO.setUserInput(dto.getUserInput());
            executeDTO.setKnowledgeDocIds(knowledgeDocIds);
            executeDTO.setKnowledgeDocMap(hisKnowledgeDocMap);
            docsList = addKnowledge(req, executeDTO);
        } else {
            //对于智能体中配置的知识库
            docsList = addKnowledge(req, dto.getUserInput(), develop.getKnowledge(), develop.getPositiveKnowledge());
        }
        //查询知识库---end

        // 多模态模型需要传入图片url
        log.info("多模态图片数量: {}", imageUrls.size());
        req.setImageUrls(imageUrls);

        Map<String, Object> paramBody = JSON.parseObject(JSON.toJSONString(req), Map.class);
        for (ModelParamDTO diversityParam : modelDTO.getDiversityParams()) {
            paramBody.put(diversityParam.getCode(), diversityParam.getValue());
        }
        for (ModelParamDTO ioParam : modelDTO.getIoParams()) {
            paramBody.put(ioParam.getCode(), ioParam.getValue());
        }
        StringBuffer history = new StringBuffer();
        StringBuffer reasonHistory = new StringBuffer();
        StringBuffer referenceHistory = new StringBuffer();

        ModelCallDTO modelCallDTO = new ModelCallDTO();
        modelCallDTO.setMethodName(model.getStreamMethod());
        modelCallDTO.setResponse(response);
        modelCallDTO.setUrl(url);
        modelCallDTO.setParamBody(paramBody);
        modelCallDTO.setHistory(history);
        modelCallDTO.setReasonHistory(reasonHistory);
        modelCallDTO.setReferenceHistory(referenceHistory);
        modelCallDTO.setDocsList(docsList);
        modelService.callModelStream(modelCallDTO);
        //记录历史
        saveDebugHistory(develop.getAgentId(), dto,
                history, reasonHistory, referenceHistory);
    }

    private List<VectorResultDTO> addKnowledge(ModelProcessDTO req, String userInput, String knowledgeConfig, String positiveKnowledgeConfig) {
        if (StringUtils.isNotBlank(knowledgeConfig) || StringUtils.isNotBlank(positiveKnowledgeConfig)) {
            List<KnowledgeConfigDTO> configs = new LinkedList<>();
            if (StringUtils.isNotBlank(knowledgeConfig)) {
                configs.add(JSON.parseObject(knowledgeConfig, KnowledgeConfigDTO.class));
            }
            // 标注、正样本知识库，如果需要查询，自行添加到文本/知识库中吧
//            if (StringUtils.isNotBlank(positiveKnowledgeConfig)) {
//                configs.add(JSON.parseObject(positiveKnowledgeConfig, KnowledgeConfigDTO.class));
//            }
            String rerankerType = null;
            Integer minDocLen = 0;

            int topK = 10;

            KnowLedgeDocSearchDTO knowLedgeDocSearchDTO = new KnowLedgeDocSearchDTO();
            List<VectorResultDTO> allDocList = new LinkedList<>();
            for (KnowledgeConfigDTO configDTO : configs) {
                // 转List<Long>
                if (StringUtils.isNotBlank(configDTO.getRerankerType())
                        && configDTO.getRerank() != null
                        && configDTO.getRerank()) {
                    rerankerType = configDTO.getRerankerType();
                }
                if (configDTO.getMinDocLen() != null && minDocLen < configDTO.getMinDocLen()) {
                    minDocLen = configDTO.getMinDocLen();
                }
                topK = configDTO.getTopK();
                if (CollUtil.isNotEmpty(configDTO.getKnowledgeList())) {
                    String baseType = null;

                    boolean notHasSameConfig = false;
                    Float minScore = null;
                    for (KnowledgeDTO knowledgeDTO : configDTO.getKnowledgeList()) {
                        if (knowledgeDTO.getWeight() != null && knowledgeDTO.getWeight() != 0 && knowledgeDTO.getWeight() != 1) {
                            notHasSameConfig = true;
                            break;
                        }
                        // 或者有阈值不同的情况时，就需要分开处理了
                        if (minScore == null) {
                            minScore = knowledgeDTO.getMinScore();
                        } else {
                            if (knowledgeDTO.getMinScore() != minScore) {
                                notHasSameConfig = true;
                                break;
                            }
                        }
                    }
                    if (!notHasSameConfig) {
                        List<Long> knowledgeIdsList = new ArrayList<>();

                        for (KnowledgeDTO knowledgeDTO : configDTO.getKnowledgeList()) {
                            // 有权重的话
                            knowledgeIdsList.add(knowledgeDTO.getId());
                            baseType = knowledgeDTO.getBaseType();
                        }
                        knowLedgeDocSearchDTO.setKnowledgeIds(knowledgeIdsList);
                        knowLedgeDocSearchDTO.setQuery(userInput.trim());
                        knowLedgeDocSearchDTO.setTopK(configDTO.getTopK() == 0 ? 5 : configDTO.getTopK());
                        knowLedgeDocSearchDTO.setMinScore(configDTO.getMinScore());
                        List<VectorResultDTO> knowledgeDocs = knowledgeService.docSearchByDto(knowLedgeDocSearchDTO);
                        allDocList.addAll(knowledgeDocs);
                    } else {
                        //必须分开处理了
                        for (KnowledgeDTO knowledgeDTO : configDTO.getKnowledgeList()) {
                            // 有权重的话
                            List<Long> knowledgeIdsList = new ArrayList<>();
                            knowledgeIdsList.add(knowledgeDTO.getId());
                            baseType = knowledgeDTO.getBaseType();
                            knowLedgeDocSearchDTO.setKnowledgeIds(knowledgeIdsList);
                            knowLedgeDocSearchDTO.setQuery(userInput.trim());
                            knowLedgeDocSearchDTO.setTopK(knowledgeDTO.getTopK() == 0 ? 5 : knowledgeDTO.getTopK());
                            knowLedgeDocSearchDTO.setMinScore(knowledgeDTO.getMinScore());
                            List<VectorResultDTO> knowledgeDocs = knowledgeService.docSearchByDto(knowLedgeDocSearchDTO);
                            for (VectorResultDTO knowledgeDoc : knowledgeDocs) {
                                knowledgeDoc.setScore(
                                        ((knowledgeDTO.getWeight() == null || knowledgeDTO.getWeight() == 1) ? 1 : knowledgeDTO.getWeight()) * knowledgeDoc.getScore());
                            }
                            // todo 还需要判断是否有最小文档数，，比如表结构，都查不出来肯定是有问题的 20250411 ，暂时应该不会有这个情况

                            allDocList.addAll(knowledgeDocs);
                        }
                    }

                }
            }
            String prefix = "## 参考内容：\n";
//            if (Constants.KNOWLEDGE_BASE_TYPE.positive.name().equals(baseType)) {
//                prefix = "[历史正确记录可供参考]";
//            }
//            boolean needReranker = false;

            // 内容去重
            List<VectorResultDTO> allDocListFilerSame = new LinkedList<>();
            if (!allDocList.isEmpty()) {
                Set<String> textSet = new HashSet<>();
                for (VectorResultDTO vectorResultDTO : allDocList) {
                    String text = vectorResultDTO.getText();
                    if (textSet.contains(text)) {
                        continue;
                    }
                    textSet.add(text);
                    allDocListFilerSame.add(vectorResultDTO);
                }
            }
            allDocList = allDocListFilerSame;
            // todo 向量化中的doc可能改过名字不准确，这里从知识库中查找准确的文档片段。不然重排的话，会涉及名称不准确？

            if (StringUtils.isNotBlank(rerankerType)) {
                if (allDocList.size() > topK) {
                    log.info("重排:{}", rerankerType);
                    // 调用阿里接口，重排后获取（重排需要加上标题，而且为了避免总文档超出，需要进行filter，不能大于3w，单挑不能超过4000词
                    ReRankerModelEnum rType = ReRankerModelEnum.valueOf(rerankerType);
                    allDocList = reRankerCallService.filterKnowledgeDocsByReRanker(rType, userInput.trim(), topK, allDocList);
                }
            } else if (allDocList.size() > topK) {
                // 排序剔除
                allDocList.sort(Comparator.comparingDouble(VectorResultDTO::getScore));
                allDocList = allDocList.stream().limit(topK).toList();
            }
            if (minDocLen > 0 & minDocLen > allDocList.size()) {
                // 不满足最小文档数，需要“无事发生"
                throw new ValidateException("未查询到相关数据，请换一个问题");
            }

            // 有些doc发现没有name，需要处理.片段那种不需要处理，但是upload的分段的会存在片段问题
            // 20250613老文件问题，查询有docId但是没有doc的文件名字
            Map<Long, String> docNames = new HashMap<>();
            for (VectorResultDTO vectorResultDTO : allDocList) {
                if (StringUtils.isNotBlank(vectorResultDTO.getDoc())) {
                    docNames.put(vectorResultDTO.getDocId(), vectorResultDTO.getDoc());
                }
            }
            Set<Long> searchDocNameIds = new HashSet<>();
            for (VectorResultDTO vectorResultDTO : allDocList) {
                if (StringUtils.isBlank(vectorResultDTO.getDoc())) {
                    if (docNames.containsKey(vectorResultDTO.getDocId())) {
                        vectorResultDTO.setDoc(docNames.get(vectorResultDTO.getDocId()));
                    } else {
                        searchDocNameIds.add(vectorResultDTO.getDocId());
                    }
                }
            }
            // 过滤查询
            if (!searchDocNameIds.isEmpty()) {
                List<KnowledgeDocDTO> docDTOS = knowledgeService.docSearchNameByIds(searchDocNameIds);
                Map<Long, String> leftDocNames = new HashMap<>();
                // 补充描述
                for (KnowledgeDocDTO docDTO : docDTOS) {
                    leftDocNames.put(docDTO.getId(), docDTO.getFileName());
                }
                for (VectorResultDTO vectorResultDTO : allDocList) {
                    if (StringUtils.isBlank(vectorResultDTO.getDoc())) {
                        vectorResultDTO.setDoc(leftDocNames.get(vectorResultDTO.getDocId()));
                    }
                }
            }


            if (CollUtil.isNotEmpty(allDocList)) {
                StringBuilder knowledge = new StringBuilder();
                knowledge.append(prefix);
                int index = req.getIndex();
                for (VectorResultDTO knowledgeDoc : allDocList) {
                    index++;

                    knowledgeDoc.setIndex(index);
                    String text = knowledgeDoc.getText();
                    Map<String, Object> knowledgeDocInfo = new HashMap<>();
                    knowledgeDocInfo.put("序号", index);
                    String doc = knowledgeDoc.getDoc();
                    if (StringUtils.isBlank(doc)) {
//                        knowledgeDocInfo.put("文档名", knowledgeDoc.getMetadata().get("overview"));
//                        todo 可以把概述作为文档名字吧
                    } else {
                        knowledgeDocInfo.put("标题", doc);
                    }
                    knowledgeDocInfo.put("内容", text);
                    knowledge.append(JSON.toJSONString(knowledgeDocInfo));
                }
                knowledge.append("## 我的问题是：\n");
                req.setUserMessage(knowledge + req.getUserMessage());
                req.setIndex(index);
            }
            return allDocList;
        }
        return null;
    }

    @SneakyThrows
    private void addWorkflowTool(List<ChatToolDTO> tools, String workflows) {
        if (StringUtils.isNotBlank(workflows)) {
            List<WorkflowDTO> workflowDTOList = JSON.parseArray(workflows, WorkflowDTO.class);
            if (CollUtil.isNotEmpty(workflowDTOList)) {
                for (WorkflowDTO workflowDTO : workflowDTOList) {
                    WorkflowPublish workflowPublish = workflowPublishMapper.selectOne(new LambdaQueryWrapper<WorkflowPublish>()
                            .eq(WorkflowPublish::getResourceId, workflowDTO.getResourceId())
                            .eq(WorkflowPublish::getId, workflowDTO.getId())
                    );
                    if (workflowPublish == null || StringUtils.isBlank(workflowPublish.getFunctionCallTool())) {
                        continue;
                    }
                    ObjectMapper objectMapper = new ObjectMapper();
                    JSONObject jsonObject = JSON.parseObject(workflowPublish.getFunctionCallTool(), JSONObject.class);
                    ChatTool chatTool = new ChatTool();
                    chatTool.setType(jsonObject.get("type").toString());
                    ChatFunction function = new ChatFunction();
                    JSONObject functionObj = JSON.parseObject(jsonObject.get("function").toString());
                    function.setName(functionObj.get("name").toString());
                    function.setDescription(functionObj.get("description").toString());
                    JsonNode parameters = objectMapper.readTree(functionObj.get("parameters").toString());
                    function.setParameters(parameters);
                    chatTool.setFunction(function);
                    ChatToolDTO chatToolDTO = new ChatToolDTO();
                    chatToolDTO.setId(workflowPublish.getId());
                    chatToolDTO.setVersion(workflowPublish.getVersion());
                    chatToolDTO.setResourceId(workflowPublish.getResourceId());
                    chatToolDTO.setClientId(clientProperties.getClientId());
                    chatToolDTO.setType(Constants.RESOURCE_TYPE.workflow.name());
                    chatToolDTO.setChatTool(chatTool);
                    tools.add(chatToolDTO);
                }
            }
        }
    }

    @SneakyThrows
    private void addPluginTool(List<ChatToolDTO> tools, String plugins) {
        if (StringUtils.isNotBlank(plugins)) {
            List<PluginDTO> pluginDTOList = JSON.parseArray(plugins, PluginDTO.class);
            if (CollUtil.isNotEmpty(pluginDTOList)) {
                for (PluginDTO pluginDTO : pluginDTOList) {
                    PluginPublish pluginPublish = pluginPublishMapper.selectById(pluginDTO.getId());
                    if (pluginPublish == null || StringUtils.isBlank(pluginPublish.getFunctionCallTool())) {
                        continue;
                    }
                    ObjectMapper objectMapper = new ObjectMapper();
                    JSONObject jsonObject = JSON.parseObject(pluginPublish.getFunctionCallTool());
                    ChatTool chatTool = new ChatTool();
                    chatTool.setType(jsonObject.getString("type"));
                    ChatFunction function = new ChatFunction();
                    JSONObject functionObj = JSON.parseObject(jsonObject.getString("function"));
                    function.setName(functionObj.getString("name"));
                    function.setDescription(functionObj.getString("description"));
                    if (StrUtil.isNotBlank(functionObj.getString("parameters"))) {
                        JsonNode parameters = objectMapper.readTree(functionObj.getString("parameters"));
                        function.setParameters(parameters);
                    }
                    chatTool.setFunction(function);
                    ChatToolDTO chatToolDTO = new ChatToolDTO();
                    chatToolDTO.setId(pluginPublish.getId());
                    chatToolDTO.setVersion(pluginPublish.getVersion());
                    chatToolDTO.setResourceId(pluginPublish.getResourceId());
                    chatToolDTO.setClientId(clientProperties.getClientId());
                    chatToolDTO.setType(Constants.RESOURCE_TYPE.plugin.name());
                    chatToolDTO.setChatTool(chatTool);
                    tools.add(chatToolDTO);
                }
            }
        }
    }

    private void saveDebugHistory(Long agentId, AgentDebugDTO dto,
                                  StringBuffer history,
                                  StringBuffer reasonHistory, StringBuffer referenceHistory) {
        Long messageId = dto.getMessageId();
        if (messageId == null) {
            messageId = agentDevelopHistoryMapper.getMaxMessageId(agentId, StpClientUserUtil.getLoginIdAsLong());
            messageId = messageId + 1;
        }

        AgentDevelopHistory agentDevelopHistory = new AgentDevelopHistory();
        agentDevelopHistory.setAgentId(agentId);
        agentDevelopHistory.setMessageId(messageId);
        agentDevelopHistory.setUserInput(dto.getUserInput());
        agentDevelopHistory.setAnswer(history.toString());
        if (reasonHistory != null && !reasonHistory.isEmpty()) {
            agentDevelopHistory.setReasoning(reasonHistory.toString());
        }
        if (referenceHistory != null && !referenceHistory.isEmpty()) {
            agentDevelopHistory.setReference(referenceHistory.toString());
        }

        if (ObjectUtil.isNotEmpty(dto.getFileDocIds())) {
            List<KnowledgeDocHistoryDTO> knowledgeDocHistoryDTOs = knowledgeService.getKnowledgeDocHistory(dto.getFileDocIds());
            agentDevelopHistory.setKnowledgeDocInfo(JSON.toJSONString(knowledgeDocHistoryDTOs));
        }

        agentDevelopHistory.setDebugId(StpClientUserUtil.getLoginIdAsLong());
        agentDevelopHistoryMapper.insert(agentDevelopHistory);
    }


    public void deleteAgent(Long id) {
        AgentBasic agentBasic = agentBasicMapper.selectById(id);
        if (agentBasic == null) {
            throw new ValidateException("智能体不存在");
        }
        if (!StrUtil.equals(agentBasic.getCreateBy(), StpClientUserUtil.getLoginIdAsString())) {
            throw new ValidateException("非本人创建智能体不可删除");
        }
        agentBasicMapper.deleteById(id);
    }

    public Long copyAgent(Long id) {
        AgentBasic agentBasic = agentBasicMapper.selectById(id);

        if (agentBasic == null) {
            throw new ValidateException("智能体不存在");
        }
        AgentDevelop develop = agentDevelopMapper.selectOne(new LambdaQueryWrapper<AgentDevelop>()
                .eq(AgentDevelop::getAgentId, id));
        if (develop == null) {
            throw new ValidateException("智能体不存在!");
        }

        AgentBasic newAgentBasic = new AgentBasic();
        BeanUtils.copyProperties(agentBasic, newAgentBasic);
        newAgentBasic.setName(agentBasic.getName() + "_copy");
        newAgentBasic.setId(null);
        newAgentBasic.setStatus(Constants.PUBLISH_STATUS_0);
        newAgentBasic.setCreateBy(StpClientUserUtil.getLoginIdAsString());
        newAgentBasic.setUpdateBy(StpClientUserUtil.getLoginIdAsString());
        newAgentBasic.setCreateTime(LocalDateTime.now());
        newAgentBasic.setUpdateTime(LocalDateTime.now());
        agentBasicMapper.insert(newAgentBasic);

        AgentDevelop newDevelop = new AgentDevelop();
        BeanUtils.copyProperties(develop, newDevelop);
        newDevelop.setId(null);
        newDevelop.setAgentId(newAgentBasic.getId());
        agentDevelopMapper.insert(newDevelop);
        return newAgentBasic.getId();
    }

    public DevelopAgentDetailDTO developAgentDetail(Long agentId) {
        AgentBasic agentBasic = agentBasicMapper.selectById(agentId);
        if (agentBasic == null) {
            return null;
        }
        AgentDevelop develop = agentDevelopMapper.selectOne(new LambdaQueryWrapper<AgentDevelop>()
                .eq(AgentDevelop::getAgentId, agentId));
        if (develop == null) {
            return null;
        }
        DevelopAgentDetailDTO dto = new DevelopAgentDetailDTO();
        BeanUtils.copyProperties(develop, dto);

        dto.setKnowledge(JSON.parseObject(develop.getKnowledge(), KnowledgeConfigDTO.class));
        if (StringUtils.isBlank(develop.getPositiveKnowledge())) {
            dto.setPositiveKnowledge(getDefaultKnowledge());
        } else {
            dto.setPositiveKnowledge(JSON.parseObject(develop.getPositiveKnowledge(), KnowledgeConfigDTO.class));
        }
        if (StringUtils.isBlank(develop.getHistoryKnowledge())) {
            dto.setHistoryKnowledge(getDefaultKnowledge());
        } else {
            dto.setHistoryKnowledge(JSON.parseObject(develop.getHistoryKnowledge(), KnowledgeConfigDTO.class));
        }
        if (StringUtils.isBlank(develop.getNegativeKnowledge())) {
            dto.setNegativeKnowledge(getDefaultKnowledge());
        } else {
            dto.setNegativeKnowledge(JSON.parseObject(develop.getNegativeKnowledge(), KnowledgeConfigDTO.class));
        }
//        if (StringUtils.isBlank(develop.getParams())) {
//            dto.setParams(getDefaultParams());
//        } else {
//            dto.setParams(JSON.parseObject(develop.getParams(), AgentParamsDto.class));
//        }

        dto.setPlugins(JSON.parseArray(develop.getPlugins(), PluginDTO.class));
        dto.setWorkflows(JSON.parseArray(develop.getWorkflows(), WorkflowDTO.class));
        dto.setTips(JSON.parseArray(develop.getTips(), String.class));
        dto.setModel(JSON.parseObject(develop.getModel(), ModelDTO.class));
        dto.setSuggestions(JSON.parseObject(develop.getSuggestions(), SuggestionsDTO.class));
        dto.setVoice(JSON.parseObject(develop.getVoice(), VoiceDTO.class));
        dto.setName(agentBasic.getName());
        dto.setDescription(agentBasic.getDescription());
        dto.setLogo(agentBasic.getLogo());
        dto.setTags(agentBasic.getTags());
        return dto;
    }

    public PublishAgentDetailDTO publishAgentDetail(Long agentPublishId) {
        return publishAgentDetail(agentPublishId, null);
    }


    public PublishAgentDetailDTO publishAgentDetail(Long agentPublishId, Long agentId) {
        AgentPublish publish;
        if (agentId != null) {
            publish = getAgentPublishByAgentId(String.valueOf(agentId));
        } else {
            publish = agentPublishMapper.selectOne(
                    new LambdaQueryWrapper<AgentPublish>()
                            .eq(AgentPublish::getId, agentPublishId)
            );
        }
        if (publish == null) {
            return null;
        }

        AgentBasic agentBasic = agentBasicMapper.selectById(publish.getAgentId());
        if (agentBasic == null) {
            return null;
        }

        PublishAgentDetailDTO dto = new PublishAgentDetailDTO();
        BeanUtils.copyProperties(publish, dto);
        dto.setAgentPublishId(publish.getId());
        dto.setName(agentBasic.getName());
        dto.setDescription(agentBasic.getDescription());
        dto.setLogo(agentBasic.getLogo());
        if (StringUtils.isNotBlank(publish.getTips()))
            dto.setTips(JSON.parseArray(publish.getTips(), String.class));

        dto.setTags(agentBasic.getTags());
        if (StringUtils.isNotBlank(publish.getTips()))
            dto.setTips(JSON.parseArray(publish.getTips(), String.class));

        List<AgentPublish> agentPublishes = agentPublishMapper.selectList(new LambdaQueryWrapper<AgentPublish>()
                .eq(AgentPublish::getAgentId, publish.getAgentId())
                .orderByDesc(AgentPublish::getPublishTime)
        );
        List<AgentPublishRecordDTO> recordDTOS = new ArrayList<>();
        if (agentPublishes != null && !agentPublishes.isEmpty()) {
            agentPublishes.forEach(agentPublish -> {
                AgentPublishRecordDTO recordDTO = new AgentPublishRecordDTO();
                BeanUtils.copyProperties(agentPublish, recordDTO);
                recordDTO.setAgentPublishId(agentPublish.getId());
                recordDTO.setPublishRecord(agentPublish.getPubRecord());
                recordDTOS.add(recordDTO);
            });
            dto.setPublishRecords(recordDTOS);
        }
        return dto;
    }

    public List<Map<String, Object>> findDevelopHistory(Page<AgentDevelopHistory> page, Long agentId, Long messageId) {
        LambdaQueryWrapper<AgentDevelopHistory> query = new LambdaQueryWrapper<AgentDevelopHistory>()
                .eq(AgentDevelopHistory::getAgentId, agentId)
                .eq(AgentDevelopHistory::getDebugId, StpClientUserUtil.getLoginIdAsLong())
                .orderByDesc(AgentDevelopHistory::getCreateTime);
        if (messageId != null) {
            query.lt(AgentDevelopHistory::getMessageId, messageId);
        }
        page = agentDevelopHistoryMapper.selectPage(page, query);

        Map<Long, List<AgentDevelopHistoryDTO>> map = new HashMap<>();
        for (AgentDevelopHistory history : page.getRecords()) {
            AgentDevelopHistoryDTO agentDevelopHistoryDTO = new AgentDevelopHistoryDTO();
            BeanUtils.copyProperties(history, agentDevelopHistoryDTO);
            agentDevelopHistoryDTO.setKnowledgeDocInfo(JSON.parseArray(history.getKnowledgeDocInfo(), KnowledgeDocHistoryDTO.class));
            map.computeIfAbsent(history.getMessageId(), k -> new ArrayList<>()).add(agentDevelopHistoryDTO);
        }


        List<Map<String, Object>> results = new ArrayList<>();
        for (Long key : map.keySet()) {
            Map<String, Object> result = new HashMap<>();
            List<AgentDevelopHistoryDTO> histories = map.get(key);
            result.put("question", histories.getFirst().getUserInput());
            result.put("answer", histories);
            result.putIfAbsent("createTime", histories.getFirst().getCreateTime());
            results.add(result);
        }
        results.sort((o1, o2) -> ((LocalDateTime) o2.get("createTime")).compareTo((LocalDateTime) o1.get("createTime")));
        return results;
    }

    public void clearDevelopHistory(@NotNull(message = "智能体ID不能为空") Long agentId) {
        agentDevelopHistoryMapper.delete(new LambdaQueryWrapper<AgentDevelopHistory>()
                .eq(AgentDevelopHistory::getAgentId, agentId)
                .eq(AgentDevelopHistory::getDebugId, StpClientUserUtil.getLoginIdAsLong()));
    }

    public void publishAgent(PublishAgentDTO dto) {
        AgentBasic agentBasic = agentBasicMapper.selectById(dto.getAgentId());
        if (agentBasic == null) {
            throw new ValidateException("智能体不存在");
        }
        agentBasic.setOpen(dto.isOpen());
        agentBasic.setStatus(Constants.PUBLISH_STATUS_1);
        agentBasicMapper.updateById(agentBasic);

        AgentPublish publish = agentPublishMapper.selectOne(
                new LambdaQueryWrapper<AgentPublish>()
                        .eq(AgentPublish::getAgentId, dto.getAgentId())
                        .orderByDesc(AgentPublish::getCreateTime)
                        .last("limit 1")
        );
        String latestVersion = null;
        if (publish != null) {
            latestVersion = publish.getVersion();
        }
        AgentPublish newPublish = new AgentPublish();
        BeanUtils.copyProperties(dto, newPublish);
        newPublish.setVersion(VersionUtils.next(latestVersion, 9));
        newPublish.setPublishTime(LocalDateTime.now());
        newPublish.setModel(JSON.toJSONString(dto.getModel()));
        newPublish.setPrompt(dto.getPrompt());
        newPublish.setPubRecord(dto.getPublishRecord());
        if (CollUtil.isNotEmpty(dto.getPlugins())) {
            newPublish.setPlugins(JSON.toJSONString(dto.getPlugins()));
        }
        if (CollUtil.isNotEmpty(dto.getWorkflows())) {
            newPublish.setWorkflows(JSON.toJSONString(dto.getWorkflows()));
        }
        if (ObjectUtil.isNotEmpty(dto.getKnowledge())) {
            newPublish.setKnowledge(JSON.toJSONString(dto.getKnowledge()));
        }
        if (ObjectUtil.isNotEmpty(dto.getTips())) {
            newPublish.setTips(JSON.toJSONString(dto.getTips()));
        }
        if (ObjectUtil.isNotEmpty(dto.getPositiveKnowledge())) {
            newPublish.setPositiveKnowledge(JSON.toJSONString(dto.getPositiveKnowledge()));
        }
        if (ObjectUtil.isNotEmpty(dto.getHistoryKnowledge())) {
            newPublish.setHistoryKnowledge(JSON.toJSONString(dto.getHistoryKnowledge()));
        }
        if (ObjectUtil.isNotEmpty(dto.getNegativeKnowledge())) {
            newPublish.setNegativeKnowledge(JSON.toJSONString(dto.getNegativeKnowledge()));
        }
        if (ObjectUtil.isNotEmpty(dto.getSuggestions())) {
            newPublish.setSuggestions(JSON.toJSONString(dto.getSuggestions()));
        }
        if (ObjectUtil.isNotEmpty(dto.getVoice())) {
            newPublish.setVoice(JSON.toJSONString(dto.getVoice()));
        }
        agentPublishMapper.insert(newPublish);
    }

    @SneakyThrows
    public AgentPublishHistoryDTO executePublishedAgent(HttpServletResponse response, AgentExecuteDTO dto) {
        AgentPublish agentPublish = agentPublishMapper.selectById(dto.getAgentPublishId());
        if (agentPublish == null) {
            throw new ValidateException("智能体不存在!");
        }
        if (StringUtils.isBlank(agentPublish.getModel())) {
            throw new ValidateException("智能体未配置模型");
        }

        //开放前端可以选择模型，此处优先使用前端选择的模型
        AiModel model;
        ModelDTO modelDTO = JSON.parseObject(agentPublish.getModel(), ModelDTO.class);
        if (ObjectUtil.isNotEmpty(dto.getModelId())) {
            model = modelMapper.selectById(dto.getModelId());
        } else {
            model = modelMapper.selectById(modelDTO.getId());
        }
        if (model == null) {
            throw new ValidateException("模型不存在");
        }
        // 非管理员，未开放模型不能使用
        //if (!StpClientUserUtil.hasRole("admin")) {
        //    if (!model.isOpen()) {
        //        throw new ValidateException("该模型未开放");
        //    }
        //}
        // 设置模型名称，历史记录中需要
        dto.setModelName(model.getName());
        dto.setModelId(model.getId());
        String url = model.getStreamUrl();

        ModelProcessDTO req = new ModelProcessDTO();
        req.setClientId(IdUtil.nanoId());
        req.setModelType(model.getCode());

        if (StringUtils.isNotBlank(dto.getSysPrompt())) {
            req.setSystemMessage(dto.getSysPrompt());
        } else {
            String[] sysPromptRefer = {agentPublish.getPrompt()};
            if (dto.getConstants() != null && StringUtils.isNotBlank(agentPublish.getPrompt())) {
                dto.getConstants().forEach((k, v) -> {
                    if (sysPromptRefer[0].contains("{{" + k + "}}")) {
                        sysPromptRefer[0] = sysPromptRefer[0].replace("{{" + k + "}}", v);
                    }
                });
            }
            req.setSystemMessage(StringUtils.isNotBlank(sysPromptRefer[0]) ? constantService.convertPromptConstant(sysPromptRefer[0]) : Constants.DEFAULT_PROMPT);
        }

        if (dto.isEnableCurrentDate()) {
            String currentDate = "---\nCURRENT_TIME: " + ZonedDateTime.now().format(DateTimeFormatter.ofPattern("EEE MMM dd yyyy HH:mm:ss Z", Locale.CHINESE)) + "\n---\n";
            req.setSystemMessage(currentDate + "\n\n" + req.getSystemMessage());
        }


        req.setUserMessage(dto.getUserInput() + "\n\n" + (StrUtil.isNotBlank(dto.getAppendContent()) ? dto.getAppendContent() : ""));
        //修改为百度搜索，不需要穿参，使用模型对话
        //req.setEnablePluginSearch(dto.isEnablePluginSearch());

        //兼容知识库和联网同时可用的情况，index要共享
        req.setIndex(0);
        JSONArray webSearchResult = null;
        if (dto.isEnablePluginSearch()) {
            webSearchResult = addWebSearchResult(req);
        }

        if (dto.isEnableFunctionCall()) {
            List<ChatToolDTO> tools = new ArrayList<>();
            //查询工作流工具---start
            addWorkflowTool(tools, agentPublish.getWorkflows());
            //查询工作流工具---end

            //查询技能工具---start
            addPluginTool(tools, agentPublish.getPlugins());
            //查询技能工具---end
            req.setTools(tools);
        }

        //查询历史---start
        req.setMultiTurn(true);
        int historySize = dto.getHistorySize();
        for (ModelParamDTO ioParam : modelDTO.getIoParams()) {
            if (ioParam.getCode().equals("historySize")) {
                historySize = (int) ioParam.getValue();
            }
        }
        List<AgentPublishHistory> agentHistories = null;
        if (historySize > 0) {
            LambdaQueryWrapper<AgentPublishHistory> lambdaQueryWrapper = new LambdaQueryWrapper<AgentPublishHistory>()
                    .eq(AgentPublishHistory::getConversationId, dto.getConversationId());
            if (dto.getAgentId() != null) {
                lambdaQueryWrapper = lambdaQueryWrapper
                        .eq(AgentPublishHistory::getAgentId, dto.getAgentId());
            }
//            else {
//                lambdaQueryWrapper = lambdaQueryWrapper
//                        .eq(AgentPublishHistory::getAgentPublishId, agentPublish.getId());
//            }
            if (StringUtils.isNotBlank(dto.getThirdUserId())) {
                lambdaQueryWrapper.eq(AgentPublishHistory::getThirdUserId, dto.getThirdUserId());
            }
            if (StpClientUserUtil.getLoginIdAsLong() != 0) {
                lambdaQueryWrapper.eq(AgentPublishHistory::getUserId, StpClientUserUtil.getLoginIdAsLong());
            }
            if (StringUtils.isNotBlank(StpClientUserUtil.getClientId())) {
                lambdaQueryWrapper.eq(AgentPublishHistory::getClientId, StpClientUserUtil.getClientId());
            }
            lambdaQueryWrapper = lambdaQueryWrapper.last("LIMIT " + historySize)
                    .orderByDesc(AgentPublishHistory::getCreateTime);
            agentHistories = agentPublishHistoryMapper.selectList(lambdaQueryWrapper);
        }

        List<Map<String, String>> histories = new ArrayList<>();
        List<KnowledgeDocHistoryDTO> knowledgeDocInfos = new ArrayList<>();
        if (CollUtil.isNotEmpty(agentHistories)) {
            for (int i = agentHistories.size() - 1; i >= 0; i--) {
                AgentPublishHistory agentHistory = agentHistories.get(i);
                Map<String, String> map = new HashMap<>();
                map.put(agentHistory.getUserInput(), agentHistory.getAnswer());
                histories.add(map);

                // 历史中的附件记录
                if (StrUtil.isNotBlank(agentHistory.getKnowledgeDocInfo())) {
                    knowledgeDocInfos.addAll(JSON.parseArray(agentHistory.getKnowledgeDocInfo(), KnowledgeDocHistoryDTO.class));
                }
            }
        }
        log.info("历史记录：{}", histories.size());
        req.setHistories(histories);
        //查询历史---end

        // 处理多模态模型需要传入图片url
        boolean isMultimodal = StrUtil.equals(Constants.MODEL_TYPE.multimodal.name(), model.getType());
        List<String> imageUrls = new ArrayList<>();

        //查询知识库---start
        // 历史中的附件处理，知识库id为键，docId列表为值
        Set<Long> knowledgeDocIds = new HashSet<>();
        Map<Long, Set<Long>> hisKnowledgeDocMap = new HashMap<>();
        if (CollUtil.isNotEmpty(knowledgeDocInfos)) {
            for (KnowledgeDocHistoryDTO knowledgeDocHistoryDTO : knowledgeDocInfos) {
                if (isMultimodal) {
                    if (StrUtil.equalsAny(knowledgeDocHistoryDTO.getFileType(), "png", "jpeg", "jpg")) {
                        imageUrls.add(knowledgeDocHistoryDTO.getFileUrl());
                        log.debug("多模态模型过滤知识库图片片段:{}", knowledgeDocHistoryDTO.getId());
                        continue;
                    }
                }

                hisKnowledgeDocMap.computeIfAbsent(
                        knowledgeDocHistoryDTO.getKnowledgeId(),
                        k -> new HashSet<>()
                ).add(knowledgeDocHistoryDTO.getId());
                knowledgeDocIds.add(knowledgeDocHistoryDTO.getId());
            }
        }
        // 对话中新添加上传附件的，添加到知识库id对应的docId列表中
        if (CollUtil.isNotEmpty(dto.getFileDocIds())) {
            // 有文档的，使用知识库id为键，docId List为值
            List<KnowledgeDoc> docs = knowledgeService.getKnowledgeDoc(dto.getFileDocIds());
            for (KnowledgeDoc doc : docs) {
                if (isMultimodal) {
                    if (StrUtil.equalsAny(doc.getFileType(), "png", "jpeg", "jpg")) {
                        imageUrls.add(doc.getFileUrl());
                        log.debug("多模态模型过滤知识库图片片段:{}", doc.getId());
                        continue;
                    }
                }

                hisKnowledgeDocMap.computeIfAbsent(
                        doc.getKnowledgeId(),
                        k -> new HashSet<>()
                ).add(doc.getId());
                knowledgeDocIds.add(doc.getId());
            }
        }
        log.info("知识库文档: {}", hisKnowledgeDocMap);

        // 对话中选择了知识库或组
        if (CollUtil.isNotEmpty(dto.getKnowledgeIds())) {
            //首页对话或者知识库对话选择了知识库或者组
            List<KnowledgeDoc> docs = knowledgeService.getKnowledgeDocByKnowledgeIds(dto.getKnowledgeIds());
            for (KnowledgeDoc doc : docs) {
                knowledgeDocIds.add(doc.getId());
            }
            for (Long knowledgeId : dto.getKnowledgeIds()) {
                //值不存在，则添加一个空，如果有值测不处理
                hisKnowledgeDocMap.putIfAbsent(knowledgeId, new HashSet<>());
            }
        }


        List<VectorResultDTO> resultKnowledgeList;
        if (CollUtil.isNotEmpty(hisKnowledgeDocMap)) {
            // 使用智能体外的知识库
            dto.setKnowledgeDocIds(knowledgeDocIds);
            dto.setKnowledgeDocMap(hisKnowledgeDocMap);
            resultKnowledgeList = addKnowledge(req, dto);
        } else {
            //对于智能体中配置的知识库
            resultKnowledgeList = addKnowledge(req, dto.getUserInput(), agentPublish.getKnowledge(), agentPublish.getPositiveKnowledge());
        }
        //查询知识库---end

        // 多模态模型需要传入图片url
        log.info("多模态图片数量: {}", imageUrls.size());
        req.setImageUrls(imageUrls);

        //上下文标注
        if (dto.isEnableCitation() &&
                (CollUtil.isNotEmpty(resultKnowledgeList) || webSearchResult != null)) {
            req.setSystemMessage(constantService.addReferPrompt(req.getSystemMessage()));
        }


        Map<String, Object> paramBody = JSON.parseObject(JSON.toJSONString(req), Map.class);
        for (ModelParamDTO diversityParam : modelDTO.getDiversityParams()) {
            paramBody.put(diversityParam.getCode(), diversityParam.getValue());
        }
        for (ModelParamDTO ioParam : modelDTO.getIoParams()) {
            paramBody.put(ioParam.getCode(), ioParam.getValue());
        }
        StringBuffer history = new StringBuffer();
        StringBuffer reasonHistory = new StringBuffer();
        StringBuffer referenceHistory = new StringBuffer();

        ModelCallDTO modelCallDTO = new ModelCallDTO();
        modelCallDTO.setMethodName(model.getStreamMethod());
        modelCallDTO.setResponse(response);
        modelCallDTO.setUrl(url);
        modelCallDTO.setParamBody(paramBody);
        modelCallDTO.setHistory(history);
        modelCallDTO.setReasonHistory(reasonHistory);
        modelCallDTO.setReferenceHistory(referenceHistory);
        modelCallDTO.setEnableCitation(dto.isEnableCitation());
        modelCallDTO.setResponseHandlerProxy(dto.getResponseHandlerProxy());
        if (CollUtil.isNotEmpty(resultKnowledgeList)) {
            // 记录下来，流返回给前端
            modelCallDTO.setDocsList(resultKnowledgeList);
        }
        if (CollUtil.isNotEmpty(webSearchResult)) {
            modelCallDTO.setWebSearchResult(webSearchResult);
        }
        modelService.callModelStream(modelCallDTO);

        //记录历史
        AgentPublishHistory agentHistory = savePublishHistory(modelCallDTO, dto, agentPublish.getId(), history, reasonHistory, referenceHistory);
        AgentPublishHistoryDTO historyDTO = new AgentPublishHistoryDTO();
        BeanUtils.copyProperties(agentHistory, historyDTO);
        historyDTO.setKnowledgeDocInfo(JSON.parseArray(agentHistory.getKnowledgeDocInfo(), KnowledgeDocHistoryDTO.class));
        return historyDTO;
    }


    /**
     * 调用非流式接口
     *
     * @param dto
     * @return
     */
    public AgentPublishHistoryDTO executePublishedAgent(AgentExecuteDTO dto) {
        AgentPublish agentPublish = agentPublishMapper.selectById(dto.getAgentPublishId());
        if (agentPublish == null) {
            throw new ValidateException("智能体不存在!");
        }
        if (StringUtils.isBlank(agentPublish.getModel())) {
            throw new ValidateException("智能体未配置模型");
        }
        ModelDTO modelDTO = JSON.parseObject(agentPublish.getModel(), ModelDTO.class);
        AiModel model = modelMapper.selectById(modelDTO.getId());
        if (model == null) {
            throw new ValidateException("模型不存在");
        }
        // 设置模型名称，历史记录中需要
        dto.setModelName(model.getName());
        String url = model.getNonStreamUrl();

        ModelProcessDTO req = new ModelProcessDTO();
        req.setClientId(IdUtil.nanoId());
        req.setModelType(model.getCode());

        String[] sysPromptRefer = {agentPublish.getPrompt()};
        if (dto.getConstants() != null && StringUtils.isNotBlank(agentPublish.getPrompt())) {
            dto.getConstants().forEach((k, v) -> {
                if (sysPromptRefer[0].contains("{{" + k + "}}")) {
                    sysPromptRefer[0] = sysPromptRefer[0].replace("{{" + k + "}}", v);
                }
            });
        }
        req.setSystemMessage(StringUtils.isNotBlank(sysPromptRefer[0]) ? constantService.convertPromptConstant(sysPromptRefer[0]) : Constants.DEFAULT_PROMPT);

        req.setUserMessage(dto.getUserInput() + "\n\n" + (StrUtil.isNotBlank(dto.getAppendContent()) ? dto.getAppendContent() : ""));
        req.setEnablePluginSearch(dto.isEnablePluginSearch());
        req.setImageUrls(dto.getImageUrls());

        //查询知识库---start
        List<VectorResultDTO> resultKnowledgeList = null;
        if (ObjectUtil.isNotEmpty(dto.getKnowledgeIds())) {
            resultKnowledgeList = addKnowledge(req, dto);

        } else {
            //对于智能体中配置的知识库
            resultKnowledgeList = addKnowledge(req, dto.getUserInput(), agentPublish.getKnowledge(), agentPublish.getPositiveKnowledge());
        }
        //查询知识库---end
        if (dto.isEnableCitation() && resultKnowledgeList != null && resultKnowledgeList.size() > 0) {
            req.setSystemMessage(constantService.addReferPrompt(req.getSystemMessage()));
        }

        if (dto.isEnableFunctionCall()) {
            List<ChatToolDTO> tools = new ArrayList<>();
            //查询工作流工具---start
            addWorkflowTool(tools, agentPublish.getWorkflows());
            //查询工作流工具---end
            //查询技能工具---start
            //查询技能工具---end
            req.setTools(tools);
        }

        //查询历史---start
        req.setMultiTurn(true);
        int historySize = dto.getHistorySize();
        for (ModelParamDTO ioParam : modelDTO.getIoParams()) {
            if (ioParam.getCode().equals("historySize")) {
                historySize = (int) ioParam.getValue();
            }
        }
        List<AgentPublishHistory> agentHistories = null;
        if (historySize > 0) {
            LambdaQueryWrapper<AgentPublishHistory> lambdaQueryWrapper = new LambdaQueryWrapper<AgentPublishHistory>()
                    .eq(AgentPublishHistory::getConversationId, dto.getConversationId());
            if (dto.getAgentId() != null) {
                lambdaQueryWrapper = lambdaQueryWrapper
                        .eq(AgentPublishHistory::getAgentId, dto.getAgentId());
            } else {
                lambdaQueryWrapper = lambdaQueryWrapper
                        .eq(AgentPublishHistory::getAgentPublishId, agentPublish.getId());
            }

            if (StringUtils.isNotBlank(dto.getThirdUserId())) {
                lambdaQueryWrapper.eq(AgentPublishHistory::getThirdUserId, dto.getThirdUserId());

            } else {
                if (StpClientUserUtil.getLoginIdAsLong() != 0) {
                    lambdaQueryWrapper.eq(AgentPublishHistory::getUserId, StpClientUserUtil.getLoginIdAsLong());
                }
            }

            if (StringUtils.isNotBlank(StpClientUserUtil.getClientId())) {
                lambdaQueryWrapper.eq(AgentPublishHistory::getClientId, StpClientUserUtil.getClientId());
            }
            lambdaQueryWrapper = lambdaQueryWrapper.last("LIMIT " + historySize)
                    .orderByDesc(AgentPublishHistory::getCreateTime);
            agentHistories = agentPublishHistoryMapper.selectList(lambdaQueryWrapper);
        }
//        if (StringUtils.isNotBlank(dto.getThirdUserId())) {
//            log.info(String.format("getThirdUserId\t: %s \tagentHistories len: %s", dto.getThirdUserId(), agentHistories.size()));
//        }
        List<Map<String, String>> histories = new ArrayList<>();
        if (CollUtil.isNotEmpty(agentHistories)) {
            for (int i = agentHistories.size() - 1; i >= 0; i--) {
                AgentPublishHistory agentHistory = agentHistories.get(i);
                Map<String, String> map = new HashMap<>();
                map.put(agentHistory.getUserInput(), agentHistory.getAnswer());
                histories.add(map);
            }
        }
        req.setHistories(histories);
        //查询历史---end

        Map<String, Object> paramBody = JSON.parseObject(JSON.toJSONString(req), Map.class);
        for (ModelParamDTO diversityParam : modelDTO.getDiversityParams()) {
            paramBody.put(diversityParam.getCode(), diversityParam.getValue());
        }
        for (ModelParamDTO ioParam : modelDTO.getIoParams()) {
            paramBody.put(ioParam.getCode(), ioParam.getValue());
        }
        StringBuffer historyStr = new StringBuffer();
        StringBuffer reasonHistory = new StringBuffer();
        StringBuffer referenceHistory = new StringBuffer();

        ModelCallDTO modelCallDTO = new ModelCallDTO();
        modelCallDTO.setMethodName(model.getNonStreamMethod());
        modelCallDTO.setUrl(url);
        modelCallDTO.setParamBody(paramBody);
        modelCallDTO.setHistory(historyStr);
        modelCallDTO.setReasonHistory(reasonHistory);
        modelCallDTO.setReferenceHistory(referenceHistory);
        if (CollUtil.isNotEmpty(resultKnowledgeList)) {
            // 记录下来，流返回给前端
            modelCallDTO.setDocsList(resultKnowledgeList);
        }
        Map<String, Object> result = modelService.callModelNonStream(modelCallDTO);

        //记录历史
        AgentPublishHistory history = savePublishHistory(modelCallDTO, dto, agentPublish.getId(), historyStr, reasonHistory, referenceHistory);
        AgentPublishHistoryDTO historyDTO = new AgentPublishHistoryDTO();
        BeanUtils.copyProperties(history, historyDTO);
        historyDTO.setKnowledgeDocInfo(JSON.parseArray(history.getKnowledgeDocInfo(), KnowledgeDocHistoryDTO.class));

        return historyDTO;
    }

    @SneakyThrows
    private JSONArray addWebSearchResult(ModelProcessDTO req) {
        String clientToken = clientTokenService.getToken4Python();
        Map<String, Object> body = new HashMap<>();
        body.put("query", req.getUserMessage());
        HttpResponse<String> searchResponse = Unirest.post(clientProperties.getDeer().getUrlPrefix() + "/api/search_tool/baidu")
                .header("Authorization", StrUtil.format("Bearer {}", clientToken))
                .header("Content-Type", "application/json")
                .body(JSON.toJSONString(body))
                .asString();
        if (searchResponse.getStatus() != 200) {
            throw new ValidateException("调用搜索插件失败");
        }
        JSONObject result = JSON.parseObject(searchResponse.getBody());
        JSONArray searchResult = result.getJSONArray("web_results");

        if (CollUtil.isNotEmpty(searchResult)) {
            StringBuilder searchContent = new StringBuilder();
            searchContent.append("## 参考内容：\n");
            int index = req.getIndex();
            for (Object object : searchResult) {
                JSONObject jsonObject = (JSONObject) object;
                Map<String, Object> webContentInfo = new HashMap<>();
                index = jsonObject.getInteger("index");
                webContentInfo.put("序号", index);
                webContentInfo.put("标题", jsonObject.get("title"));
                webContentInfo.put("内容", jsonObject.get("summary"));
                searchContent.append(JSON.toJSONString(webContentInfo));

            }
            searchContent.append("## 我的问题是：\n");
            req.setUserMessage(searchContent + req.getUserMessage());
            req.setIndex(index);
        }

        return searchResult;
    }

    private List<VectorResultDTO> addKnowledge(ModelProcessDTO req, AgentExecuteDTO dto) {
        int filterDocNum = -1;
        if (CollUtil.isNotEmpty(dto.getKnowledgeDocIds())) {
            filterDocNum = dto.getKnowledgeDocIds().size();
        }

        Map<Long, String> knowledgefilterMap = dto.getKnowledgefilterMap();
        Map<Long, Set<Long>> knowledgeDocMap = dto.getKnowledgeDocMap();
        for (Long knowledgeId : knowledgeDocMap.keySet()) {
            Set<Long> docIds = knowledgeDocMap.get(knowledgeId);
            if (CollUtil.isNotEmpty(docIds)) {
                String docIdFilter = StrUtil.format("{} in [{}]", MilvusClient.DOC_ID, StrUtil.join(",", docIds));
                knowledgefilterMap.merge(knowledgeId, docIdFilter, (oldValue, newValue) -> oldValue + " || " + newValue);
            } else {
                //值不存在，则添加一个空字符串，确保不漏掉knowledgeId
                knowledgefilterMap.putIfAbsent(knowledgeId, "");
            }
        }

        int topK = 20;
        KnowLedgeDocSearchDTO knowLedgeDocSearchDTO = new KnowLedgeDocSearchDTO();
        knowLedgeDocSearchDTO.setKnowledgeFilterMap(knowledgefilterMap);
        knowLedgeDocSearchDTO.setQuery(dto.getUserInput().trim());
        knowLedgeDocSearchDTO.setTopK(topK);
        knowLedgeDocSearchDTO.setMinScore(0.5f);
        boolean needReranker = false;
        if (filterDocNum != 1) {
            // 多文档解析，上重排
            knowLedgeDocSearchDTO.setTopK(100);
            needReranker = true;
        }

        List<VectorResultDTO> knowledgeDocs = knowledgeService.docSearchByDtoWithFilterMap(knowLedgeDocSearchDTO);

        // 内容去重
        List<VectorResultDTO> allDocListFilerSame = new LinkedList<>();
        if (knowledgeDocs != null && !knowledgeDocs.isEmpty()) {
            Set<String> textSet = new HashSet<>();
            for (VectorResultDTO vectorResultDTO : knowledgeDocs) {
                String text = vectorResultDTO.getText();
                if (textSet.contains(text)) {
                    continue;
                }
                textSet.add(text);
                allDocListFilerSame.add(vectorResultDTO);
            }
        }

        knowledgeDocs = allDocListFilerSame;

        if (needReranker) {
            if (knowledgeDocs.size() > topK) {
                //  调用阿里接口 todo 根据配置切换不同的类型
                //  重排后获取（重排需要加上标题，而且为了避免总文档超出，需要进行filter，不能大于3w，单挑不能超过4000词
                knowledgeDocs = reRankerCallService.filterKnowledgeDocsByReRanker(
                        ReRankerModelEnum.valueOf(clientProperties.getText().getRerankModel()),
                        dto.getUserInput().trim(), topK, knowledgeDocs);
            }
        }
        if (CollUtil.isNotEmpty(knowledgeDocs)) {
            StringBuilder knowledge = new StringBuilder();
            knowledge.append("## 参考内容：\n");
            int index = req.getIndex();
            for (VectorResultDTO knowledgeDoc : knowledgeDocs) {
                index++;
                knowledgeDoc.setIndex(index);

                String text = knowledgeDoc.getText();
                Map<String, Object> knowledgeDocInfo = new HashMap<>();
                knowledgeDocInfo.put("序号", index);
                knowledgeDocInfo.put("标题", knowledgeDoc.getDoc());
                knowledgeDocInfo.put("内容", text);
                knowledge.append(JSON.toJSONString(knowledgeDocInfo));
            }
            knowledge.append("## 我的问题是：\n");
            req.setUserMessage(knowledge + req.getUserMessage());
            req.setIndex(index);

        }
        return knowledgeDocs;
    }

    private AgentPublishHistory savePublishHistory(ModelCallDTO modelCallDTO, AgentExecuteDTO dto, Long agentPublishId, StringBuffer history, StringBuffer reasonHistory, StringBuffer referenceHistory) {
        Long messageId = dto.getMessageId();
        if (messageId == null) {
            if (StringUtils.isNotBlank(dto.getThirdUserId())) {
                messageId = agentPublishHistoryMapper.getMaxMessageIdByThirdUserId(agentPublishId, dto.getThirdUserId());
            } else {
                messageId = agentPublishHistoryMapper.getMaxMessageId(agentPublishId, StpClientUserUtil.getLoginIdAsLong());
            }
            messageId = messageId + 1;
        }

        AgentPublishHistory agentPublishHistory = new AgentPublishHistory();
        agentPublishHistory.setConversationId(dto.getConversationId());
        agentPublishHistory.setAgentPublishId(agentPublishId);
        agentPublishHistory.setThirdUserId(dto.getThirdUserId());
        agentPublishHistory.setObjectId(dto.getObjectId());
        agentPublishHistory.setRawInput(dto.getRawInput());

        agentPublishHistory.setAgentId(dto.getAgentId());
        agentPublishHistory.setUserId(StpClientUserUtil.getLoginIdAsLong());
        agentPublishHistory.setClientId(StpClientUserUtil.getClientId());
        agentPublishHistory.setMessageId(messageId);
        agentPublishHistory.setUserInput(dto.getUserInput());
        agentPublishHistory.setRawInput(dto.getRawInput());
        agentPublishHistory.setType(dto.getAgentRunType());
        Long modelId = dto.getModelId();
        if (modelId != null)
            agentPublishHistory.setModelName(modelId.toString());
        agentPublishHistory.setFlowId(dto.getFlowId());
        if (ObjectUtil.isNotEmpty(dto.getFileDocIds())) {
            List<KnowledgeDocHistoryDTO> knowledgeDocHistoryDTOs = knowledgeService.getKnowledgeDocHistory(dto.getFileDocIds());
            agentPublishHistory.setKnowledgeDocInfo(JSON.toJSONString(knowledgeDocHistoryDTOs));
        }
        agentPublishHistory.setAnswer(history.toString());
        if (reasonHistory != null && !reasonHistory.isEmpty()) {
            agentPublishHistory.setReasoning(reasonHistory.toString());
        }
        if (referenceHistory != null && !referenceHistory.isEmpty()) {
            agentPublishHistory.setReference(referenceHistory.toString());
        }
        if (modelCallDTO.getEndTime() != null && modelCallDTO.getStartTime() != null) {
            agentPublishHistory.setRuntime(modelCallDTO.getEndTime() - modelCallDTO.getStartTime());
        }
        agentPublishHistory.setStatus(AgentFlowConstants.AGENT_RUN_STATUS.success.name());
        // 说明失败了，获取不到endTime
        if (modelCallDTO.getEndTime() == null && modelCallDTO.getStartTime() != null) {
            agentPublishHistory.setStatus(AgentFlowConstants.AGENT_RUN_STATUS.timeout.name());
        }


        agentPublishHistoryMapper.insert(agentPublishHistory);
        // 如果智能体配置了历史仓库，需要存进去
        Long historyKnowledgeId = dto.getHistoryKnowledgeId();
        if (historyKnowledgeId != null && historyKnowledgeId > 0) {
            agentSftService.logKnowledgeHistory(historyKnowledgeId, modelCallDTO, dto, agentPublishHistory.getRawInput(), agentPublishHistory.getUserInput());
        }
        return agentPublishHistory;
    }

    public List<Map<String, Object>> queryPublishedAgentHistory(Page<AgentPublishHistory> page,
                                                                String conversationId,
                                                                Long agentPublishId,
                                                                Long messageId,
                                                                String userId) {
        LambdaQueryWrapper<AgentPublishHistory> query = new LambdaQueryWrapper<AgentPublishHistory>()
                .eq(AgentPublishHistory::getConversationId, conversationId)
                .and(
                        i -> i.eq(AgentPublishHistory::getUserId, userId).or()
                                .eq(AgentPublishHistory::getThirdUserId, userId)
                )

                .orderByDesc(AgentPublishHistory::getCreateTime);
        if (agentPublishId != null) {
            query.eq(AgentPublishHistory::getAgentPublishId, agentPublishId);
        }
        if (messageId != null) {
            query.lt(AgentPublishHistory::getMessageId, messageId);
        }
        String clientId = StpClientUserUtil.getClientId();
        if (StringUtils.isNotBlank(clientId)) {
            query.eq(AgentPublishHistory::getClientId, clientId);
        }
        page = agentPublishHistoryMapper.selectPage(page, query);

        Map<Long, List<AgentPublishHistoryDTO>> map = new HashMap<>();
        for (AgentPublishHistory history : page.getRecords()) {
            AgentPublishHistoryDTO agentPublishHistoryDTO = new AgentPublishHistoryDTO();
            BeanUtils.copyProperties(history, agentPublishHistoryDTO);
            agentPublishHistoryDTO.setKnowledgeDocInfo(JSON.parseArray(history.getKnowledgeDocInfo(), KnowledgeDocHistoryDTO.class));
            map.computeIfAbsent(history.getMessageId(), k -> new ArrayList<>()).add(agentPublishHistoryDTO);
        }

        List<Map<String, Object>> results = new ArrayList<>();
        for (Long key : map.keySet()) {
            Map<String, Object> result = new HashMap<>();
            List<AgentPublishHistoryDTO> histories = map.get(key);
            result.putIfAbsent("question", histories.getFirst().getUserInput());
            result.putIfAbsent("answer", histories);
            result.putIfAbsent("createTime", histories.getFirst().getCreateTime());
            if (CollUtil.isNotEmpty(histories)) {
                AgentPublishHistoryDTO history = histories.getFirst();
                if (StrUtil.isNotBlank(history.getExtraInfo())) {
                    JSONObject extraInfo = JSON.parseObject(history.getExtraInfo());
                    if (extraInfo.containsKey("deep_research") && extraInfo.getBoolean("deep_research")) {
                        String filePath = StrUtil.format("{}/{}/{}.log", clientProperties.getDeer().getFilePath(),
                                extraInfo.getString("deep_research_id"), extraInfo.getString("deep_research_id"));
                        result.put("deep_research_log", filePath);
                    }
                }
            }

            results.add(result);
        }

        results.sort((o1, o2) -> ((LocalDateTime) o2.get("createTime")).compareTo((LocalDateTime) o1.get("createTime")));
        return results;
    }

    public void clearPublishHistory(String conversationId, Long agentPublishId, String userId) {
        String finalUserId = StrUtil.isBlank(userId) ? StpClientUserUtil.getLoginIdAsString() : userId;
        LambdaQueryWrapper<AgentPublishHistory> query = new LambdaQueryWrapper<AgentPublishHistory>()
                .eq(AgentPublishHistory::getConversationId, conversationId)
                .and(
                        i -> i.eq(AgentPublishHistory::getUserId, finalUserId).or()
                                .eq(AgentPublishHistory::getThirdUserId, finalUserId)
                );
        if (agentPublishId != null) {
            query.eq(AgentPublishHistory::getAgentPublishId, agentPublishId);
        }
        agentPublishHistoryMapper.delete(query);
    }

    public Page<ApplicationAgentDTO> queryPublishedAgentList(AgentQueryDTO dto, Page<ApplicationAgentDTO> page) {
        if (dto.getApplicationId() != null) {
            dto.setAgentIds(getAgentIdsByApplicationId(dto.getApplicationId()));
        }

        List<ApplicationAgentDTO> applicationAgentDTOS = agentBasicMapper.queryPublishedAgentList(page, dto);
        page.setRecords(applicationAgentDTOS);
        return page;
    }

    public List<ApplicationAgentDTO> queryPublishedAgentListByTags(AgentQueryDTO dto) {
        if (dto.getApplicationId() != null) {
            dto.setAgentIds(getAgentIdsByApplicationId(dto.getApplicationId()));
        }

        return agentBasicMapper.queryPublishedAgentListByTags(dto);
    }


    private List<Long> getAgentIdsByApplicationId(Long applicationId) {
        ApplicationBasic applicationBasic = applicationBasicMapper.selectById(applicationId);
        String agents = applicationBasic.getAgents();
        JSONArray agentArray = JSON.parseArray(agents);
        List<Long> agentIds = new ArrayList<>();
        if (agentArray != null) {
            for (Object o : agentArray) {
                JSONObject agent = JSON.parseObject(JSON.toJSONString(o));
                JSONArray agentInfos = agent.getJSONArray("agentInfos");
                for (Object agentInfo_o : agentInfos) {
                    JSONObject agentInfo = JSON.parseObject(JSON.toJSONString(agentInfo_o));
                    agentIds.add(agentInfo.getLong("agentId"));
                }
            }
        }
        return agentIds;
    }


    public AgentPublish getAgentPublishByAgentId(String agentId) {
        // 最新版本
        return agentPublishMapper.selectOne(new LambdaQueryWrapper<AgentPublish>()
                .eq(AgentPublish::getAgentId, agentId)
                .orderByDesc(AgentPublish::getPublishTime)
                .last("limit 1"));
    }


}
