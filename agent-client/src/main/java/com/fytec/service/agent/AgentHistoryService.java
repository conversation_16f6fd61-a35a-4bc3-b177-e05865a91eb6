package com.fytec.service.agent;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.exceptions.ValidateException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.config.ClientProperties;
import com.fytec.constant.AgentFlowConstants;
import com.fytec.dto.agent.*;
import com.fytec.dto.agent.feedback.AgentHistoryFeedbackDTO;
import com.fytec.dto.token.OauthClientDetailDTO;
import com.fytec.entity.agent.AgentBasic;
import com.fytec.entity.agent.AgentHistoryFeedback;
import com.fytec.entity.agent.AgentPublishHistory;
import com.fytec.entity.system.OauthClientDetail;
import com.fytec.mapper.agent.*;
import com.fytec.mapper.application.ApplicationBasicMapper;
import com.fytec.mapper.model.ModelMapper;
import com.fytec.mapper.plugin.PluginPublishMapper;
import com.fytec.mapper.system.OauthClientDetailMapper;
import com.fytec.mapper.workflow.WorkflowPublishMapper;
import com.fytec.model.DynamicModelCallService;
import com.fytec.reranker.DynamicReRankerCallService;
import com.fytec.service.knowledge.KnowledgeService;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;

@Service
@Transactional
@Slf4j
@AllArgsConstructor
public class AgentHistoryService {
    private final AgentBasicMapper agentBasicMapper;

    private final AgentDevelopMapper agentDevelopMapper;

    private final DynamicModelCallService modelService;

    private final ModelMapper modelMapper;

    private final AgentDevelopHistoryMapper agentDevelopHistoryMapper;

    private final AgentPublishMapper agentPublishMapper;

    private final AgentPublishHistoryMapper agentPublishHistoryMapper;

    private final KnowledgeService knowledgeService;

    private final WorkflowPublishMapper workflowPublishMapper;

    private final PluginPublishMapper pluginPublishMapper;

    private final ClientProperties clientProperties;

    private final DynamicReRankerCallService reRankerCallService;

    private final ConstantService constantService;

    private final ApplicationBasicMapper applicationBasicMapper;

    private final AgentSftService agentSftService;
    private OauthClientDetailMapper oauthClientDetailMapper;
    private AgentHistoryFeedbackMapper feedbackMapper;

    public Page<AgentFlowHistoryDTO> queryFlowAgentHistory(Page<AgentPublishHistory> page,
                                                           AgentFlowHistoryQueryDTO dto) {

        if (StringUtils.isNotBlank(dto.getStartTimeStr())) {
            dto.setStartTime(LocalDate.parse(dto.getStartTimeStr()).atTime(0, 0, 0));
        }
        if (StringUtils.isNotBlank(dto.getEndTimeStr())) {
            dto.setEndTime(LocalDate.parse(dto.getEndTimeStr()).atTime(23, 59, 59));
        }

        // 响应时间
        if (dto.getRuntimeStart() == null) {
            dto.setRuntimeStart(0L);
        }
        //查询历史，查询最后一条，就是是最后的节点
        LambdaQueryWrapper<AgentPublishHistory> query = new LambdaQueryWrapper<AgentPublishHistory>()
                .eq(AgentPublishHistory::getType, AgentFlowConstants.AGENT_RUN_TYPE.last.name())
                .isNotNull(AgentPublishHistory::getAgentId)
//                .and(
//                        i -> i.eq(AgentPublishHistory::getUserId, userId).or()
//                                .eq(AgentPublishHistory::getThirdUserId, userId)
//                )

                .orderByDesc(AgentPublishHistory::getCreateTime);

        if (dto.getAgentId() != null) {
            query.eq(AgentPublishHistory::getAgentId, dto.getAgentId());
        }
        if (StringUtils.isNotBlank(dto.getClientId())) {
            query.eq(AgentPublishHistory::getClientId, dto.getClientId());
        }
        if (StringUtils.isNotBlank(dto.getThirdUserId())) {
            query.eq(AgentPublishHistory::getThirdUserId, dto.getClientId());
        }
        if (StringUtils.isNotBlank(dto.getStatus())) {
            query.eq(AgentPublishHistory::getStatus, dto.getStatus());
        }
        if (dto.getRuntimeEnd() != null) {
            query.lt(AgentPublishHistory::getRuntime, dto.getRuntimeEnd());
        }
        if (dto.getRuntimeStart() != null) {
            query.gt(AgentPublishHistory::getRuntime, dto.getRuntimeStart());
        }
        if (dto.getStartTime() != null) {
            query.gt(AgentPublishHistory::getCreateTime, dto.getStartTime());
        }
        if (dto.getEndTime() != null) {
            query.lt(AgentPublishHistory::getCreateTime, dto.getEndTime());
        }

        page = agentPublishHistoryMapper.selectPage(page, query);
        Set<Long> ids = new HashSet<>();
        List<Long> historyIds = new LinkedList<>();
        Set<String> clientIds = new HashSet<>();
        List<AgentFlowHistoryDTO> results = new ArrayList<>();
        for (AgentPublishHistory history : page.getRecords()) {
            AgentFlowHistoryDTO agentPublishHistoryDTO = new AgentFlowHistoryDTO();
            BeanUtils.copyProperties(history, agentPublishHistoryDTO);
            results.add(agentPublishHistoryDTO);
            ids.add(history.getAgentId());
            historyIds.add(history.getId());
            if (StringUtils.isNotBlank(history.getClientId()))
                clientIds.add(history.getClientId());
        }
        // 设置一下客户端名称
        Map<String, String> clientNamesMap = new HashMap<>();
        if (!clientIds.isEmpty()) {
            List<OauthClientDetail> oauthClientDetails = oauthClientDetailMapper.selectClientNamesByClientIds(clientIds);
            for (OauthClientDetail oauthClientDetail : oauthClientDetails) {
                clientNamesMap.put(oauthClientDetail.getClientId(), oauthClientDetail.getName());
            }
        }
        Page<AgentFlowHistoryDTO> resultPage = new Page<AgentFlowHistoryDTO>();
        BeanUtils.copyProperties(page, resultPage);

        // 查询智能体名称 agentName
        // 检索一下，反馈记录里是否包含这些东西
        List<AgentHistoryFeedback> agentHistoryFeedbacks = null;
        Map<Long, Integer> feedbackNumMap = new HashMap<>();
        if(!historyIds.isEmpty()){
            agentHistoryFeedbacks = feedbackMapper.selectList(
                    new LambdaQueryWrapper<AgentHistoryFeedback>()
                            .in(AgentHistoryFeedback::getHistoryId, historyIds)
            );
            for (AgentHistoryFeedback agentHistoryFeedback : agentHistoryFeedbacks) {
                feedbackNumMap.put(agentHistoryFeedback.getHistoryId(),
                        feedbackNumMap.getOrDefault(agentHistoryFeedback.getHistoryId(), 0) + 1);
            }
        }



        if (!ids.isEmpty()) {
            List<AgentBasic> agentBasics = agentBasicMapper.selectByIds(ids);
            Map<Long, String> agentNameMaps = new HashMap<>();
            agentBasics.forEach(t -> {
                agentNameMaps.put(t.getId(), t.getName());
            });
            for (AgentFlowHistoryDTO historyDTO : results) {
                historyDTO.setClientName(clientNamesMap.getOrDefault(historyDTO.getClientId(), "内部系统"));
                historyDTO.setFeedbackNum(feedbackNumMap.getOrDefault(historyDTO.getId(), 0));

                historyDTO.setAgentName(agentNameMaps.getOrDefault(historyDTO.getAgentId(), ""));
                for (AgentFlowConstants.AGENT_RUN_STATUS value : AgentFlowConstants.AGENT_RUN_STATUS.values()) {
                    if (value.name().equals(historyDTO.getStatus())) {
                        historyDTO.setStatusName(value.getDesc());
                        break;
                    }
                }
                for (AgentFlowConstants.AGENT_RUN_TYPE value : AgentFlowConstants.AGENT_RUN_TYPE.values()) {
                    if (value.name().equals(historyDTO.getType())) {
                        historyDTO.setTypeName(value.getDesc());
                        break;
                    }
                }
            }

        }


        resultPage.setRecords(results);

        return resultPage;
    }

    // 根据历史详情，获取到这个流程的各种信息
    public List<AgentFlowHistoryDTO> queryFlowAgentHistoryDetailList(Long historyId) {
        AgentPublishHistory history = agentPublishHistoryMapper.selectById(historyId);
        if (history == null) {
            throw new ValidateException("历史记录不存在");
        }
        List<AgentFlowHistoryDTO> results = new ArrayList<>(1);
        if (StringUtils.isBlank(history.getFlowId())) {
            AgentFlowHistoryDTO e = new AgentFlowHistoryDTO();
            BeanUtils.copyProperties(history, e);
//            e.setType(AgentFlowConstants.AGENT_RUN_TYPE.first);
            results.add(e);
            return results;
        }
        // 根据流程id获取详情
        LambdaQueryWrapper<AgentPublishHistory> query = new LambdaQueryWrapper<AgentPublishHistory>()
                .eq(AgentPublishHistory::getFlowId, history.getFlowId())
                .orderByAsc(AgentPublishHistory::getMessageId, AgentPublishHistory::getCreateTime);

        List<AgentPublishHistory> historyList = agentPublishHistoryMapper.selectList(query);
        Set<Long> ids = new HashSet<>();
        // typeName和statusName也要添加一下
        for (AgentPublishHistory agentPublishHistory : historyList) {
            AgentFlowHistoryDTO historyDTO = new AgentFlowHistoryDTO();
            BeanUtils.copyProperties(agentPublishHistory, historyDTO);
            for (AgentFlowConstants.AGENT_RUN_STATUS value : AgentFlowConstants.AGENT_RUN_STATUS.values()) {
                if (value.name().equals(historyDTO.getStatus())) {
                    historyDTO.setStatusName(value.getDesc());
                    break;
                }
            }
            for (AgentFlowConstants.AGENT_RUN_TYPE value : AgentFlowConstants.AGENT_RUN_TYPE.values()) {
                if (value.name().equals(historyDTO.getType())) {
                    historyDTO.setTypeName(value.getDesc());
                    break;
                }
            }
            results.add(historyDTO);
            ids.add(historyDTO.getAgentId());
        }
        if (!ids.isEmpty()) {
            List<AgentBasic> agentBasics = agentBasicMapper.selectByIds(ids);
            Map<Long, String> agentNameMaps = new HashMap<>();
            agentBasics.forEach(t -> {
                agentNameMaps.put(t.getId(), t.getName());
            });
            for (AgentFlowHistoryDTO result : results) {
                result.setAgentName(agentNameMaps.getOrDefault(result.getAgentId(), ""));
            }
        }
        return results;
    }

}
