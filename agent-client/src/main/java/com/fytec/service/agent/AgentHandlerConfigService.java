package com.fytec.service.agent;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fytec.dto.agent.AgentHandlerConfigQueryDTO;
import com.fytec.entity.agent.AgentHandlerConfig;
import com.fytec.util.QueryWrapperUtils;
import io.milvus.v2.service.BaseService;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * Copyright (C), 2025-2099
 * 智能体处理器配置Service
 * <AUTHOR> lix
 * @date :   2025/6/9 18:02
 * Version: V1.0.0
 */

public interface AgentHandlerConfigService extends IService<AgentHandlerConfig> {
    /**
     * 根据分页信息和查询条件返回AgentHandlerConfig列表
     *
     * @param page 分页信息
     * @param queryDTO 查询条件
     * @return AgentHandlerConfig列表
     */
    default List<AgentHandlerConfig> list(IPage<AgentHandlerConfig> page, AgentHandlerConfigQueryDTO queryDTO) {
        QueryWrapper<AgentHandlerConfig> queryWrapper = QueryWrapperUtils.buildQueryWrapper(queryDTO);
        return this.list(page, queryWrapper);
    }

    /**
     * 根据传入的查询条件DTO，获取符合条件的AgentHandlerConfig列表
     *
     * @param queryDTO 查询条件DTO，用于构建查询条件
     * @return 符合条件的AgentHandlerConfig列表
     */
    default List<AgentHandlerConfig> list(AgentHandlerConfigQueryDTO queryDTO) {
        QueryWrapper<AgentHandlerConfig> queryWrapper = QueryWrapperUtils.buildQueryWrapper(queryDTO);
        return this.list(queryWrapper);
    }
}
