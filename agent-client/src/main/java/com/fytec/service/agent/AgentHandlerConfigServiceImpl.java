package com.fytec.service.agent;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fytec.dto.agent.AgentHandlerConfigQueryDTO;
import com.fytec.entity.agent.AgentHandlerConfig;
import com.fytec.mapper.agent.AgentHandlerConfigMapper;
import com.fytec.util.QueryWrapperUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Copyright (C), 2025-2099
 * 智能体处理器配置Service
 * <AUTHOR> lix
 * @date :   2025/6/9 18:03
 * Version: V1.0.0
 */
@Service
public class AgentHandlerConfigServiceImpl extends ServiceImpl<AgentHandlerConfigMapper, AgentHandlerConfig> implements AgentHandlerConfigService {

}
