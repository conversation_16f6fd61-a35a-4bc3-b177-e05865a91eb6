package com.fytec.service.agent;

import cn.hutool.core.exceptions.ValidateException;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.constant.AgentFlowConstants;
import com.fytec.dto.agent.AgentExecuteDTO;
import com.fytec.dto.agent.feedback.AgentFeedbackHandleDTO;
import com.fytec.dto.agent.feedback.AgentFeedbackQueryDTO;
import com.fytec.dto.agent.feedback.AgentHistoryFeedbackDTO;
import com.fytec.dto.llm.ModelCallDTO;
import com.fytec.entity.agent.*;
import com.fytec.entity.knowledge.KnowledgeSegment;
import com.fytec.mapper.agent.*;
import com.fytec.satoken.StpClientUserUtil;
import com.fytec.constant.Constants;
import com.fytec.dto.agent.AgentPublishSftDto;
import com.fytec.dto.agent.AgentSftDto;
import com.fytec.dto.knowledge.*;
import com.fytec.entity.knowledge.KnowledgeDoc;
import com.fytec.mapper.knowledge.KnowledgeDocMapper;
import com.fytec.service.knowledge.KnowledgeService;
import com.fytec.service.knowledge.KnowledgeWithoutTranService;
import com.mchange.lang.LongUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@Transactional
@Slf4j
@AllArgsConstructor
public class AgentSftService {
    @Autowired
    private AgentDevelopMapper agentDevelopMapper;
    @Autowired
    private AgentPublishMapper agentPublishMapper;
    @Autowired
    private KnowledgeDocMapper knowledgeDocMapper;
    @Autowired
    private KnowledgeService knowledgeService;
    @Autowired
    private KnowledgeWithoutTranService knowledgeWithoutTranService;
    @Autowired
    private AgentPublishHistoryMapper historyMapper;
    @Autowired
    private AgentHistoryFeedbackMapper feedbackMapper;
    @Autowired
    private AgentBasicMapper agentBasicMapper;

    private static final String _version = "v1";
    private static final String user_source_third = "third";
    private static final String user_source_inner = "inner";

    /**
     * 记录这次回答为正确的
     * 1. 根据智能体id直接获取到相关的知识库
     * 2. 根据agentId获取到 绑定的知识库类型，根据这个类型，进行解析支撑，把内容丢上去
     *
     * @param dto
     */
    public void sftLogKnowledgePositive(AgentSftDto dto) {
        dto.setType(Constants.KNOWLEDGE_BASE_TYPE.positive.name());

        // 内部的
        AgentDevelop develop = agentDevelopMapper.selectOne(new LambdaQueryWrapper<AgentDevelop>()
                .eq(AgentDevelop::getAgentId, dto.getAgentId()));
        if (develop == null) {
            throw new ValidateException("智能体不存在!");
        }
        String positiveKnowledge = develop.getPositiveKnowledge();
        if (StringUtils.isNotBlank(positiveKnowledge)) {
            addSegToKnowledge(dto, positiveKnowledge, dto.getThirdUserId(), dto.getRawInput(), dto.getUserInput(), dto.getAnswer(), dto.getContent());


            //  上传片段，先上传文档
//            KnowledgeDoc doc = new KnowledgeDoc();
//            doc.setKnowledgeId(knowledgeDTO.getId());
//            doc.setFileName(dto.getUserInput());
//            doc.setFileContent(seg.toString());
//            doc.setStatus(Constants.KNOWLEDGE_DOC_STATUS.complete.name());
//            knowledgeDocMapper.insert(doc);
//
//            AddKnowledgeSegmentDTO addKnowledgeSegmentDTO = new AddKnowledgeSegmentDTO();
//            addKnowledgeSegmentDTO.setKnowledgeId(knowledgeDTO.getId());
//            addKnowledgeSegmentDTO.set


        } else {
            throw new ValidateException("未配置标注知识库");
        }
    }

    public void sftLogKnowledgeNegative(AgentSftDto dto) {
        dto.setType(Constants.KNOWLEDGE_BASE_TYPE.negative.name());
        // 内部的
        if (StringUtils.isBlank(dto.getContent())) {
            throw new ValidateException("错误原因未填写");
        }
        AgentDevelop develop = agentDevelopMapper.selectOne(new LambdaQueryWrapper<AgentDevelop>()
                .eq(AgentDevelop::getAgentId, dto.getAgentId()));
        if (develop == null) {
            throw new ValidateException("智能体不存在!");
        }
        String negativeKnowledge = develop.getNegativeKnowledge();
        if (StringUtils.isNotBlank(negativeKnowledge)) {
            // todo 需要check 不能重复插入？
            addSegToKnowledge(dto, negativeKnowledge, dto.getThirdUserId(), dto.getRawInput(), dto.getUserInput(), dto.getAnswer(), dto.getContent());
        } else {
            throw new ValidateException("未配置负样本知识库");
        }
    }

    // todo ,直接改写为把数据插入到片段，向量知识库中。不需要再走文件解析
    private void tmp_addSegToKnowledge(String knowledgeConfigStr, String thirdUserId, String rawInput, String userInput, String answer, String ps) {

    }

    private List<Long> addSegToKnowledge(AgentSftDto sftDto, String knowledgeConfigStr, String thirdUserId, String rawInput,
                                         String userInput, String answer, String ps) {
        KnowledgeConfigDTO knowledgeConfigDTO = JSON.parseObject(knowledgeConfigStr, KnowledgeConfigDTO.class);
        // 上传
        List<KnowledgeDTO> knowledgeList = knowledgeConfigDTO.getKnowledgeList();
        if (knowledgeList == null || knowledgeList.isEmpty()) {
            throw new ValidateException("未配置样本知识库");
        }
        KnowledgeDTO knowledgeDTO = knowledgeList.get(0);
        StringBuffer seg = new StringBuffer();
        if (StringUtils.isNotBlank(rawInput) && !rawInput.equals(userInput)) {
            seg.append(String.format("用户原始输入:%s\n用户改写输入:%s%s", rawInput, userInput, StringUtils.isBlank(answer) ? "" : ("\n回答结果:" + answer)));
        } else {
            seg.append(String.format("用户输入:%s%s", userInput, StringUtils.isBlank(answer) ? "" : ("\n回答结果:" + answer)));
        }
        if (StringUtils.isNotBlank(ps)) {
            seg.append("\n用户备注:");
            seg.append(ps);
        }
        KnowledgeDocAutoDTO docAutoDTO = new KnowledgeDocAutoDTO();
        docAutoDTO.setDocSourceType(Constants.KNOWLEDGE_DOC_SOURCE_TYPE.customize_sft.name());
        // 增强
        docAutoDTO.setOverview(rawInput);
        docAutoDTO.setDocEnhancement(true);

        KnowledgeFileDTO fileDTO = new KnowledgeFileDTO();
//        String filename = userInput;
//        if (StringUtils.isNotBlank(ps)) {
//            filename = ps;
//        }
//        fileDTO.setFileName(filename.length() > 10 ? (filename.substring(0, 9) + ".txt") : (filename) + ".txt");
        String filename = userInput;
        // 文件名字是来源+用户id格式+版本
        if (StringUtils.isNotBlank(thirdUserId)) {
            filename = String.format("%s_%s_%s.txt", _version, user_source_third, thirdUserId);
        } else {
            filename = String.format("%s_%s_%s.txt", _version, user_source_inner, StpClientUserUtil.getLoginIdAsString());
        }
        fileDTO.setFileName(filename);
        fileDTO.setFileContent(seg.toString());
        fileDTO.setDocType(Constants.KNOWLEDGE_DOC_TYPE.text.name());

        docAutoDTO.setDefaultSegment(false);
        docAutoDTO.setPreview(true);
        docAutoDTO.setKnowledgeId(knowledgeDTO.getId());
//        docAutoDTO.setOverview(StringUtils.isNotBlank(rawInput) ? rawInput : userInput);
        docAutoDTO.setOverview(userInput);
        docAutoDTO.setDocEnhancement(true);

        if (StringUtils.isNotBlank(thirdUserId)) {
            docAutoDTO.setThirdUserId(thirdUserId);
        } else {
            docAutoDTO.setUserId(StpClientUserUtil.getLoginIdAsString());
        }
        LinkedList<KnowledgeFileDTO> files = new LinkedList<>();
        files.add(fileDTO);
        docAutoDTO.setFiles(files);
        // todo 看看能不能设置到一个知识库中，同一个人的反馈，不要每次都加文本
        List<KnowledgeDoc> docs = knowledgeService.autoProcessData(docAutoDTO);
        //Async方法获取不到用户信息，需要当成参数往后传递
        docAutoDTO.setUserId(StpClientUserUtil.getLoginIdAsString());

        for (KnowledgeFileDTO file : docAutoDTO.getFiles()) {
            knowledgeService.autoProcessDoc(file, docAutoDTO);
        }

        List<Long> docIds = new ArrayList<>();
        for (KnowledgeDoc doc : docs) {
            docIds.add(doc.getId());
        }
        List<KnowledgeDoc> knowledgeDocs = knowledgeService.updateDocVisible(docIds);
        for (KnowledgeDoc doc : knowledgeDocs) {
            knowledgeWithoutTranService.advancedVectorData(doc.getId(), StpClientUserUtil.getLoginIdAsString());

//            List<KnowledgeSegment> knowledgeSegments =
//            for (KnowledgeSegment knowledgeSegment : knowledgeSegments) {
//                segIds.add(knowledgeSegment.getId());
//            }
        }
        if (AgentFlowConstants.AGENT_FEEDBACK_TYPE.negative.name().equals(sftDto.getType())
                && AgentFlowConstants.AGENT_FEEDBACK_SOURCE.user.name().equals(sftDto.getSource())
        ) {
            // 记录下来到反馈表
            AgentHistoryFeedback historyFeedback = new AgentHistoryFeedback();
            historyFeedback.setKnowledgeId(knowledgeDTO.getId());
            historyFeedback.setType(sftDto.getType());
            historyFeedback.setAgentId(sftDto.getAgentId());
            historyFeedback.setAnswer(answer);
            historyFeedback.setUserRemark(ps);
            historyFeedback.setRawInput(rawInput);
            historyFeedback.setInput(userInput);
            historyFeedback.setStatus(AgentFlowConstants.AGENT_FEEDBACK_STATUS.uncomplete.name());
            historyFeedback.setDocIds(StringUtils.join(docIds, ","));
            historyFeedback.setFlowId(sftDto.getFlowId());
            historyFeedback.setThirdUserId(sftDto.getThirdUserId());
            historyFeedback.setClientId(StpClientUserUtil.getClientId());
            historyFeedback.setHistoryId(sftDto.getHistoryId());
            // 如果历史id，流程id不为空，查找最后一个节点作为节点
            if(historyFeedback.getHistoryId()==null && StringUtils.isNotBlank(historyFeedback.getFlowId())){
                LambdaQueryWrapper<AgentPublishHistory> query = new LambdaQueryWrapper<AgentPublishHistory>()
                        .eq(AgentPublishHistory::getFlowId, historyFeedback.getFlowId())
                        .eq(AgentPublishHistory::getType, AgentFlowConstants.AGENT_RUN_TYPE.last.name())
                        .last(" limit 1")
                        ;
                AgentPublishHistory history = historyMapper.selectOne(query);
                if(history!=null){
                    historyFeedback.setHistoryId(history.getId());
                }
            }

            feedbackMapper.insert(historyFeedback);
        }
        return docIds;
    }
    //外部的


    public void openStfLogKnowledgePositive(AgentSftDto dto) {

        if (Constants.KNOWLEDGE_BASE_TYPE.positive.name().equals(dto.getType())) {
            sftLogKnowledgePositive(dto);
        } else if (Constants.KNOWLEDGE_BASE_TYPE.negative.name().equals(dto.getType())) {
            sftLogKnowledgeNegative(dto);
        } else {
            throw new ValidateException("标注类型错误");
        }
    }

    public void sftPublishLogKnowledgePositive(AgentPublishSftDto dto) {
        // 根据发布的智能体id获取到id
        AgentPublishHistory history = historyMapper.selectById(dto.getHistoryId());
        if (history == null) {
            throw new ValidateException("历史记录不存在!");
        }
        dto.setAgentId(history.getAgentId());
        dto.setRawInput(history.getRawInput());
        dto.setUserInput(history.getUserInput());
        dto.setAnswer(history.getAnswer());
        dto.setFlowId(history.getFlowId());
        dto.setThirdUserId(history.getThirdUserId());
        dto.setSource(AgentFlowConstants.AGENT_FEEDBACK_SOURCE.user.name());
        dto.setHistoryId(history.getId());

        //判断有没有重复反馈
        if (checkSameTypeFeedBack(history.getId(), AgentFlowConstants.AGENT_FEEDBACK_TYPE.positive.name())) {
            throw new ValidateException("感谢您的优化建议");
        }
        //判断有没有相似反馈
        long feedBackId = checkSimliaryFeedBack(history.getAgentId(), AgentFlowConstants.AGENT_FEEDBACK_TYPE.positive.name(),
                history.getRawInput(), history.getUserInput(), history.getAnswer());
        if (feedBackId > 0) {
//            throw new ValidateException("已存在相似的反馈，感谢您的反馈");
            AgentHistoryFeedback agentHistoryFeedback = feedbackMapper.selectById(feedBackId);
            agentHistoryFeedback.setUserRemark(dto.getContent());
            feedbackMapper.updateById(agentHistoryFeedback);
        }else{
            AgentPublish agentPublish = agentPublishMapper.selectById(history.getAgentPublishId());
            if (agentPublish == null) {
                throw new ValidateException("发布的智能体不存在!");
            }
            String positiveKnowledge = agentPublish.getPositiveKnowledge();
            if (StringUtils.isNotBlank(positiveKnowledge)) {
                addSegToKnowledge(dto, positiveKnowledge, null, history.getRawInput(), history.getUserInput(), history.getAnswer(), dto.getContent());
            } else {
                throw new ValidateException("未配置标注知识库");
            }
        }


    }

    public void sftPublishLogKnowledgeNegative(AgentPublishSftDto dto) {
        // 根据发布的智能体id获取到id
        if (StringUtils.isBlank(dto.getContent())) {
            throw new ValidateException("错误原因未填写");
        }
        AgentPublishHistory history = historyMapper.selectById(dto.getHistoryId());
        if (history == null) {
            throw new ValidateException("历史记录不存在!");
        }
        dto.setAgentId(history.getAgentId());
        dto.setRawInput(history.getRawInput());
        dto.setUserInput(history.getUserInput());
        dto.setAnswer(history.getAnswer());
        dto.setFlowId(history.getFlowId());
        dto.setThirdUserId(history.getThirdUserId());
        dto.setSource(AgentFlowConstants.AGENT_FEEDBACK_SOURCE.user.name());
        dto.setHistoryId(history.getId());

        //判断有没有重复反馈
        if (checkSameTypeFeedBack(history.getId(), AgentFlowConstants.AGENT_FEEDBACK_TYPE.negative.name())) {
            throw new ValidateException("问题已反馈，正在处理中...");
        }
        //判断有没有相似反馈
        long feedBackId = checkSimliaryFeedBack(history.getAgentId(), AgentFlowConstants.AGENT_FEEDBACK_TYPE.negative.name(),
                history.getRawInput(), history.getUserInput(), history.getAnswer());
        if (feedBackId > 0) {
//            throw new ValidateException("已存在相似的反馈，感谢您的反馈");
            // 只更新用户描述，其他不变
            AgentHistoryFeedback agentHistoryFeedback = feedbackMapper.selectById(feedBackId);
            agentHistoryFeedback.setUserRemark(dto.getContent());
            feedbackMapper.updateById(agentHistoryFeedback);
        }else {
            AgentPublish agentPublish = agentPublishMapper.selectById(history.getAgentPublishId());
            if (agentPublish == null) {
                throw new ValidateException("发布的智能体不存在!");
            }

            String negativeKnowledge = agentPublish.getNegativeKnowledge();
            if (StringUtils.isNotBlank(negativeKnowledge)) {
                addSegToKnowledge(dto, negativeKnowledge, null, history.getRawInput(), history.getUserInput(), history.getAnswer(), dto.getContent());


            } else {
                throw new ValidateException("未配置负样本知识库");
            }
        }


    }

    private long checkSimliaryFeedBack(Long agentId, String type,
                                          String rawInput, String userInput, String answer) {
        //todo 判断 相同智能体id 是否已经有类似的反馈问题（改写、输入、输出都一样）
        Long l = feedbackMapper.selectCount(new LambdaQueryWrapper<AgentHistoryFeedback>()
                .eq(AgentHistoryFeedback::getAgentId, agentId)
                .eq(AgentHistoryFeedback::getStatus, AgentFlowConstants.AGENT_FEEDBACK_STATUS.uncomplete.name())
                .eq(AgentHistoryFeedback::getRawInput, rawInput)
                .eq(AgentHistoryFeedback::getInput, userInput)
                .eq(AgentHistoryFeedback::getType, type)
                .eq(AgentHistoryFeedback::getAnswer, answer)
                .last(" limit 1"));

        return l;
    }

    private boolean checkSameTypeFeedBack(Long historyId, String type) {
        //  判断是否同一条记录有一样的反馈了,未处理的
        Long l = feedbackMapper.selectCount(new LambdaQueryWrapper<AgentHistoryFeedback>()
                .eq(AgentHistoryFeedback::getHistoryId, historyId)
                .eq(AgentHistoryFeedback::getType, type)
                .eq(AgentHistoryFeedback::getStatus, AgentFlowConstants.AGENT_FEEDBACK_STATUS.uncomplete.name())
                .last(" limit 1"));

        return l > 0;
    }

    public void logKnowledgeHistory(Long knowledgeId, ModelCallDTO callDTO, AgentExecuteDTO executeDTO
            , String rawInput, String userInput) {
        //  需要提前过滤一下，重复的
        //  可能单独加个历史记录表？.因为可能需要绑定：知识库id、用户id、输入，3个为联合唯一的
        // 最好相同用户的相同版本，用一个历史比较好，考虑一下？
        String filename;
        // 文件名字是来源+用户id格式+版本
        if (StringUtils.isNotBlank(executeDTO.getThirdUserId())) {
            filename = String.format("%s_%s_%s.txt", _version, user_source_third, executeDTO.getThirdUserId());
        } else {
            filename = String.format("%s_%s_%s.txt", _version, user_source_inner, StpClientUserUtil.getLoginIdAsString());
        }
        KnowledgeDoc knowledgeDoc = knowledgeService.searchKnowledgeDocIdByFileName(knowledgeId, filename);

        String input;
        if (userInput.equals(rawInput)) {
            input = String.format("用户输入:%s", userInput);
        } else {
            input = String.format("用户原始输入:%s\n用户改写输入:%s", rawInput, userInput);
        }
        //  判断是否有相同片段，有的话忽略，没有的话，加入到历史记录里
        boolean isHasSameSet = false;
        if (knowledgeDoc != null)
            isHasSameSet = knowledgeService.checkSameSegment(knowledgeId, knowledgeDoc.getId(), input);
        if (!isHasSameSet)
            addSegLogToKnowledge(knowledgeId, executeDTO, input, rawInput);
    }

    private void addSegLogToKnowledge(Long knowledgeId, AgentExecuteDTO executeDTO,
                                      String input, String rawInput) {

//        StringBuffer seg = new StringBuffer();
//        if (StringUtils.isNotBlank(rawInput) && !rawInput.equals(userInput)) {
//            seg.append(String.format("用户原始输入:%s\n用户改写输入:%s%s", rawInput, userInput, StringUtils.isBlank(answer) ? "" : ("\n回答结果:" + answer)));
//        } else {
//            seg.append(String.format("用户输入:%s%s", userInput, StringUtils.isBlank(answer) ? "" : ("\n回答结果:" + answer)));
//        }

//        seg.append(input);

        KnowledgeDocAutoDTO docAutoDTO = new KnowledgeDocAutoDTO();
        docAutoDTO.setDocSourceType(Constants.KNOWLEDGE_DOC_SOURCE_TYPE.customize_history.name());

        KnowledgeFileDTO fileDTO = new KnowledgeFileDTO();
        String filename;
        // 文件名字是来源+用户id格式+版本
        if (StringUtils.isNotBlank(executeDTO.getThirdUserId())) {
            filename = String.format("%s_%s_%s.txt", _version, user_source_third, executeDTO.getThirdUserId());
        } else {
            filename = String.format("%s_%s_%s.txt", _version, user_source_inner, StpClientUserUtil.getLoginIdAsString());
        }
        fileDTO.setFileName(filename);
//        fileDTO.setFileName(filename.length() > 10 ? (filename.substring(0, 9) + ".txt") : (filename) + ".txt");

        fileDTO.setFileContent(input);
        fileDTO.setDocType(Constants.KNOWLEDGE_DOC_TYPE.text.name());

        docAutoDTO.setDefaultSegment(false);
        docAutoDTO.setPreview(true);
        docAutoDTO.setKnowledgeId(knowledgeId);
//        docAutoDTO.setOverview(StringUtils.isNotBlank(rawInput) ? rawInput : userInput);
        docAutoDTO.setOverview(rawInput);
        docAutoDTO.setDocEnhancement(true);
        if (StringUtils.isNotBlank(executeDTO.getThirdUserId())) {
            docAutoDTO.setThirdUserId(executeDTO.getThirdUserId());
        } else {
            docAutoDTO.setUserId(StpClientUserUtil.getLoginIdAsString());
        }

        //todo 存入向量库时，应该考虑实际创建者（自己用户、还是第三方；以及他们的id，作为过滤条件，实现定制化开发）
        // 简单起见，假设第三方用户id的id是唯一的，自己加好前缀，这样避免冲突
        LinkedList<KnowledgeFileDTO> files = new LinkedList<>();
        files.add(fileDTO);
        docAutoDTO.setFiles(files);
        List<KnowledgeDoc> docs = knowledgeService.autoProcessData(docAutoDTO);
        //Async方法获取不到用户信息，需要当成参数往后传递
        docAutoDTO.setUserId(StpClientUserUtil.getLoginIdAsString());

        for (KnowledgeFileDTO file : docAutoDTO.getFiles()) {
            knowledgeService.autoProcessDoc(file, docAutoDTO);
        }

        List<Long> docIds = new ArrayList<>();
        for (KnowledgeDoc doc : docs) {
            docIds.add(doc.getId());
        }
        List<KnowledgeDoc> knowledgeDocs = knowledgeService.updateDocVisible(docIds);
        for (KnowledgeDoc doc : knowledgeDocs) {
            knowledgeWithoutTranService.advancedVectorData(doc.getId(), StpClientUserUtil.getLoginIdAsString());
        }
    }

    public Page<AgentHistoryFeedbackDTO> queryAgentFeedbackPage(Page<AgentHistoryFeedback> page, AgentFeedbackQueryDTO dto) {
        LambdaQueryWrapper<AgentHistoryFeedback> query = new LambdaQueryWrapper<AgentHistoryFeedback>()
                .isNotNull(AgentHistoryFeedback::getAgentId)
                .orderByDesc(AgentHistoryFeedback::getCreateTime);

        if (dto.getAgentId() != null && dto.getAgentId() > 0) {
            query = query.eq(AgentHistoryFeedback::getAgentId, dto.getAgentId());
        }
        if (dto.getHistoryId() != null && dto.getHistoryId() > 0) {
            query = query.eq(AgentHistoryFeedback::getHistoryId, dto.getHistoryId());
        }
        if (StringUtils.isNotBlank(dto.getType())) {
            query = query.eq(AgentHistoryFeedback::getType, dto.getAgentId());

        }
        if (StringUtils.isNotBlank(dto.getStatus())) {
            query = query.eq(AgentHistoryFeedback::getStatus, dto.getStatus());

        }
        Page<AgentHistoryFeedback> feedbackPage = feedbackMapper.selectPage(page, query);
        Page<AgentHistoryFeedbackDTO> resultPage = new Page<AgentHistoryFeedbackDTO>();
        BeanUtils.copyProperties(feedbackPage, resultPage);

        List<AgentHistoryFeedbackDTO> dtoList = new ArrayList<>(feedbackPage.getRecords().size());

        Set<Long> ids = new HashSet<>();
        for (AgentHistoryFeedback record : feedbackPage.getRecords()) {
            AgentHistoryFeedbackDTO e = new AgentHistoryFeedbackDTO();
            BeanUtils.copyProperties(record, e);
            dtoList.add(e);
            ids.add(record.getAgentId());
            // 处理一下老的忘记加历史id的问题.根据流程id补全一下
            if(StringUtils.isNotBlank(e.getFlowId())&&e.getHistoryId()==null){
                LambdaQueryWrapper<AgentPublishHistory> queryHistory = new LambdaQueryWrapper<AgentPublishHistory>()
                        .eq(AgentPublishHistory::getFlowId, e.getFlowId())
                        .last(" limit 1");
                AgentPublishHistory history = historyMapper.selectOne(queryHistory);
                e.setHistoryId(history.getId());
            }
        }
        // 查询智能体名称 agentName
        if (!ids.isEmpty()) {
            List<AgentBasic> agentBasics = agentBasicMapper.selectByIds(ids);
            Map<Long, String> agentNameMaps = new HashMap<>();
            agentBasics.forEach(t -> {
                agentNameMaps.put(t.getId(), t.getName());
            });
            for (AgentHistoryFeedbackDTO agentHistoryFeedbackDTO : dtoList) {
                agentHistoryFeedbackDTO.setAgentName(agentNameMaps.getOrDefault(agentHistoryFeedbackDTO.getAgentId(), ""));
            }
        }



        resultPage.setRecords(dtoList);


        return resultPage;
    }

    public AgentHistoryFeedback handleFeedback(AgentFeedbackHandleDTO dto) {

        AgentHistoryFeedback feedback = feedbackMapper.selectById(dto.getFeedbackId());
        if (feedback == null) {
            // 结果不存在
            throw new ValidateException("反馈不存在");
        }
        if (AgentFlowConstants.AGENT_FEEDBACK_STATUS.uncomplete.name().equals(dto.getStatus())) {
            // 说明只修改状态了的
            feedback.setStatus(dto.getStatus());
            feedbackMapper.updateById(feedback);
            return null;
        }
        if (AgentFlowConstants.AGENT_FEEDBACK_STATUS.complete.name().equals(feedback.getStatus())) {
            throw new ValidateException("已处理不要重复处理");
        }
        if(StringUtils.isNotBlank(dto.getStatus())){
            feedback.setStatus(dto.getStatus());
        }else {
            feedback.setStatus(AgentFlowConstants.AGENT_FEEDBACK_STATUS.complete.name());
        }

        feedback.setRemark(dto.getRemark());
        feedback.setResult(dto.getResult());

        feedbackMapper.updateById(feedback);
        return feedback;
    }

    public void handleFeedbackAsPositive(AgentFeedbackHandleDTO dto) {
        AgentHistoryFeedback feedback = handleFeedback(dto);
        if (feedback != null) {
            // 继续处理
            if (StringUtils.isNotBlank(dto.getContent())) {
                // 修改正文。作为正样本
                // 查询看看有没有正样本
                Long agentId = feedback.getAgentId();
                // 得到最新发布的智能体id？
                AgentBasic agentBasic = agentBasicMapper.selectById(agentId);
                if (agentBasic == null) {
                    throw new ValidateException("智能体不存在");
                }
                AgentDevelop develop = agentDevelopMapper.selectOne(new LambdaQueryWrapper<AgentDevelop>()
                        .eq(AgentDevelop::getAgentId, agentId));
                if (develop == null) {
                    throw new ValidateException("智能体不存在");
                }
                String positiveKnowledge = develop.getPositiveKnowledge();
                if (StringUtils.isBlank(positiveKnowledge)) {
                    throw new ValidateException(String.format("智能体:{%s} 未设置正样本库，无法设置为正样本", agentBasic.getName()));
                }
                // todo 解析正样本库的配置，并把这次记录插入进去设置好

            }
        }
    }

    public AgentHistoryFeedbackDTO getFeedbackDetail(Long feedbackId) {
        AgentHistoryFeedback feedback = feedbackMapper.selectById(feedbackId);
        if(feedback==null){
            throw new ValidateException("反馈不存在");
        }
        AgentHistoryFeedbackDTO e = new AgentHistoryFeedbackDTO();
        BeanUtils.copyProperties(feedback, e);

        return e;
    }
}
