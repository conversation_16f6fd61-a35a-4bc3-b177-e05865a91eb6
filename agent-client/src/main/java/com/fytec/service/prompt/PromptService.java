package com.fytec.service.prompt;


import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.constant.Constants;
import com.fytec.dto.llm.*;
import com.fytec.dto.prompt.*;
import com.fytec.entity.llm.AiModel;
import com.fytec.entity.prompt.PromptContent4User;
import com.fytec.mapper.model.ModelMapper;
import com.fytec.mapper.prompt.PromptContent4UserMapper;
import com.fytec.model.DynamicModelCallService;
import com.fytec.satoken.StpClientUserUtil;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Service
@Slf4j
@Transactional
@RequiredArgsConstructor
public class PromptService {
    private final ModelMapper modelMapper;
    private final PromptContent4UserMapper promptContentMapper;

    private final DynamicModelCallService modelService;

    private boolean existsPromptContent(String name) {
        return promptContentMapper.selectCount(
                new LambdaQueryWrapper<PromptContent4User>()
                        .eq(PromptContent4User::getName, name)
                        .eq(PromptContent4User::getCreateBy, StpClientUserUtil.getLoginIdAsLong())
        ) > 0;
    }

    public void addPromptContent(PromptContentCreateDTO dto) {
        if (existsPromptContent(dto.getName())) {
            throw new ValidateException("提示词名称已存在");
        }
        PromptContent4User promptContent = new PromptContent4User();
        BeanUtils.copyProperties(dto, promptContent);
        promptContentMapper.insert(promptContent);
    }

    public void editPromptContent(PromptContentUpdateDTO dto) {
        PromptContent4User promptContent = promptContentMapper.selectById(dto.getId());
        if (promptContent == null) {
            throw new ValidateException("提示词未找到");
        }
        if (!StrUtil.equals(promptContent.getName(), dto.getName())) {
            if (existsPromptContent(dto.getName())) {
                throw new ValidateException("提示词名称已存在");
            }
        }
        BeanUtils.copyProperties(dto, promptContent);
        promptContentMapper.updateById(promptContent);
    }

    public void deletePromptContent(Long id) {
        promptContentMapper.deleteById(id);
    }

    public PromptContentDetailDTO getPromptContentDetail(Long id) {
        PromptContentDetailDTO promptContentDetailDTO = new PromptContentDetailDTO();
        PromptContent4User promptContent = promptContentMapper.selectById(id);
        BeanUtils.copyProperties(promptContent, promptContentDetailDTO);
        return promptContentDetailDTO;
    }

    public Page<PromptContentListDTO> queryPromptContent(Page<PromptContentListDTO> page, PromptContentQueryDTO dto) {
        List<PromptContentListDTO> records = promptContentMapper.queryPromptContent(page, dto);
        page.setRecords(records);
        return page;
    }

    public void autoPromptContent(HttpServletResponse response, PromptContentAutoDTO dto) {
        AiModel model = modelMapper.selectOne(
                new LambdaQueryWrapper<AiModel>()
                        .eq(AiModel::isEnable, true)
                        .eq(AiModel::isDefaulted, true)
                        .last("limit 1")
        );
        if (model == null) {
            throw new ValidateException("模型不存在");
        }

        // 设置模型名称，历史记录中需要
        String url = model.getStreamUrl();

        ModelProcessDTO req = new ModelProcessDTO();
        req.setClientId(IdUtil.nanoId());
        req.setModelType(model.getCode());
        req.setSystemMessage(Constants.DEFAULT_AUTO_PROMPT_CONTENT);
        req.setUserMessage(dto.getUserInput());

        Map<String, Object> paramBody = JSON.parseObject(JSON.toJSONString(req), Map.class);

        List<ModelParamDTO> diversityParams = JSON.parseArray(model.getDiversityParams(), ModelParamDTO.class);
        for (ModelParamDTO diversityParam : diversityParams) {
            paramBody.put(diversityParam.getCode(), diversityParam.getValue());
        }
        List<ModelParamDTO> ioParams = JSON.parseArray(model.getIoParams(), ModelParamDTO.class);
        for (ModelParamDTO ioParam : ioParams) {
            paramBody.put(ioParam.getCode(), ioParam.getValue());
        }
        StringBuffer history = new StringBuffer();
        StringBuffer reasonHistory = new StringBuffer();
        StringBuffer referenceHistory = new StringBuffer();

        ModelCallDTO modelCallDTO = new ModelCallDTO();
        modelCallDTO.setResponse(response);
        modelCallDTO.setMethodName(model.getStreamMethod());
        modelCallDTO.setUrl(url);
        modelCallDTO.setParamBody(paramBody);
        modelCallDTO.setHistory(history);
        modelCallDTO.setReasonHistory(reasonHistory);
        modelCallDTO.setReferenceHistory(referenceHistory);
        modelService.callModelStream(modelCallDTO);
    }

}
