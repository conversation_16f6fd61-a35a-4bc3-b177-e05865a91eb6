package com.fytec.service.prompt;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.prompt.*;
import com.fytec.entity.prompt.PromptCategory;
import com.fytec.entity.prompt.PromptContent;
import com.fytec.entity.prompt.PromptContentCategory;
import com.fytec.mapper.prompt.PromptCategoryMapper;
import com.fytec.mapper.prompt.PromptContentCategoryMapper;
import com.fytec.mapper.prompt.PromptContentMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Service
@Slf4j
@Transactional
@RequiredArgsConstructor
public class PromptMgmtService {
    private final PromptCategoryMapper promptCategoryMapper;
    private final PromptContentMapper promptContentMapper;
    private final PromptContentCategoryMapper promptContentCategoryMapper;

    private boolean existsPromptCategory(String name) {
        return promptCategoryMapper.selectCount(
                new LambdaQueryWrapper<PromptCategory>().eq(PromptCategory::getName, name)
        ) > 0;
    }

    public void addPromptCategory(PromptCategoryCreateDTO dto) {
        if (existsPromptCategory(dto.getName())) {
            throw new ValidateException("提示词分类已存在");
        }
        PromptCategory promptCategory = new PromptCategory();
        BeanUtils.copyProperties(dto, promptCategory);
        promptCategoryMapper.insert(promptCategory);
    }

    public void editPromptCategory(PromptCategoryUpdateDTO dto) {
        PromptCategory promptCategory = promptCategoryMapper.selectById(dto.getId());
        if (promptCategory == null) {
            throw new ValidateException("提示词分类未找到");
        }
        if (!StrUtil.equals(promptCategory.getName(), dto.getName())) {
            if (existsPromptCategory(dto.getName())) {
                throw new ValidateException("提示词分类已存在");
            }
        }
        BeanUtils.copyProperties(dto, promptCategory);
        promptCategoryMapper.updateById(promptCategory);
    }

    public void deletePromptCategory(Long id) {
        promptContentCategoryMapper.delete(
                new LambdaQueryWrapper<PromptContentCategory>()
                        .eq(PromptContentCategory::getCategoryId, id)
        );
        promptCategoryMapper.deleteById(id);
    }

    public PromptCategory getPromptCategoryDetail(Long id) {
        return promptCategoryMapper.selectById(id);
    }

    public List<PromptCategoryDTO> queryPromptCategory(PromptCategoryQueryDTO dto) {
        return promptCategoryMapper.queryPromptCategory(dto);
    }

    //#################################提示词################################

    private boolean existsPromptContent(String name) {
        return promptContentMapper.selectCount(
                new LambdaQueryWrapper<PromptContent>().eq(PromptContent::getName, name)
        ) > 0;
    }

    public void addPromptContent(PromptContentCreateDTO dto) {
        if (existsPromptContent(dto.getName())) {
            throw new ValidateException("提示词名称已存在");
        }
        PromptContent promptContent = new PromptContent();
        BeanUtils.copyProperties(dto, promptContent);
        promptContentMapper.insert(promptContent);

        if (CollUtil.isNotEmpty(dto.getCategoryIds())) {
            for (Long categoryId : dto.getCategoryIds()) {
                PromptContentCategory promptContentCategory = new PromptContentCategory();
                promptContentCategory.setContentId(promptContent.getId());
                promptContentCategory.setCategoryId(categoryId);
                promptContentCategoryMapper.insert(promptContentCategory);
            }
        }
    }

    public void editPromptContent(PromptContentUpdateDTO dto) {
        PromptContent promptContent = promptContentMapper.selectById(dto.getId());
        if (promptContent == null) {
            throw new ValidateException("提示词未找到");
        }
        if (!StrUtil.equals(promptContent.getName(), dto.getName())) {
            if (existsPromptContent(dto.getName())) {
                throw new ValidateException("提示词名称已存在");
            }
        }
        BeanUtils.copyProperties(dto, promptContent);
        promptContentMapper.updateById(promptContent);

        promptContentCategoryMapper.delete(
                new LambdaQueryWrapper<PromptContentCategory>()
                        .eq(PromptContentCategory::getContentId, dto.getId())
        );

        if (CollUtil.isNotEmpty(dto.getCategoryIds())) {
            for (Long categoryId : dto.getCategoryIds()) {
                PromptContentCategory promptContentCategory = new PromptContentCategory();
                promptContentCategory.setContentId(promptContent.getId());
                promptContentCategory.setCategoryId(categoryId);
                promptContentCategoryMapper.insert(promptContentCategory);
            }
        }
    }

    public void deletePromptContent(Long id) {
        promptContentCategoryMapper.delete(
                new LambdaQueryWrapper<PromptContentCategory>()
                        .eq(PromptContentCategory::getContentId, id)
        );
        promptContentMapper.deleteById(id);
    }

    public PromptContentDetailDTO getPromptContentDetail(Long id) {
        PromptContentDetailDTO promptContentDetailDTO = new PromptContentDetailDTO();
        PromptContent promptContent = promptContentMapper.selectById(id);
        BeanUtils.copyProperties(promptContent, promptContentDetailDTO);
        List<Long> categoryIds = promptContentCategoryMapper.selectList(
                new LambdaQueryWrapper<PromptContentCategory>()
                        .eq(PromptContentCategory::getContentId, id)
        ).stream().map(PromptContentCategory::getCategoryId).toList();

        List<String> categories = promptCategoryMapper.selectByIds(categoryIds)
                .stream().map(PromptCategory::getName).toList();
        promptContentDetailDTO.setCategoryIds(categoryIds);
        promptContentDetailDTO.setCategories(StrUtil.join(",", categories));
        return promptContentDetailDTO;
    }

    public Page<PromptContentListDTO> queryPromptContent(Page<PromptContentListDTO> page, PromptContentQueryDTO dto) {
        List<PromptContentListDTO> records = promptContentMapper.queryPromptContent(page, dto);
        page.setRecords(records);
        return page;
    }

    public List<PromptContentListDTO> queryOutOfCategoryPromptContent( PromptContentQueryDTO dto) {
        return promptContentMapper.queryOutOfCategoryPromptContent(dto);
    }

    public void savePromptContentCategory(PromptContentCategoryDTO dto) {
        if (CollUtil.isNotEmpty(dto.getDeleteRelations())) {
            for (Map.Entry<Long, List<Long>> deletes : dto.getDeleteRelations().entrySet()) {
                Long categoryId = deletes.getKey();
                List<Long> contentIds = deletes.getValue();
                for (Long contentId : contentIds) {
                    promptContentCategoryMapper.delete(
                            new LambdaQueryWrapper<PromptContentCategory>()
                                    .eq(PromptContentCategory::getContentId, contentId)
                                    .eq(PromptContentCategory::getCategoryId, categoryId)
                    );
                }
            }
        }
        if (CollUtil.isNotEmpty(dto.getInsertRelations())) {
            for (Map.Entry<Long, List<Long>> inserts : dto.getInsertRelations().entrySet()) {
                Long categoryId = inserts.getKey();
                List<Long> contentIds = inserts.getValue();
                for (Long contentId : contentIds) {
                    PromptContentCategory promptContentCategory = new PromptContentCategory();
                    promptContentCategory.setContentId(contentId);
                    promptContentCategory.setCategoryId(categoryId);
                    promptContentCategoryMapper.insert(promptContentCategory);
                }
            }
        }
    }
}
