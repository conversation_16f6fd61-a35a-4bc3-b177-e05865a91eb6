package com.fytec.service.model;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fytec.config.ClientProperties;
import com.fytec.constant.Constants;
import com.fytec.constant.enums.ReRankerModelEnum;
import com.fytec.dto.flow.WorkflowDTO;
import com.fytec.dto.knowledge.KnowLedgeDocSearchDTO;
import com.fytec.dto.knowledge.KnowledgeConfigDTO;
import com.fytec.dto.knowledge.KnowledgeDTO;
import com.fytec.dto.knowledge.KnowledgeDocDTO;
import com.fytec.dto.llm.ChatToolDTO;
import com.fytec.dto.llm.ModelProcessDTO;
import com.fytec.dto.llm.VectorResultDTO;
import com.fytec.dto.plugin.PluginDTO;
import com.fytec.entity.plugin.PluginPublish;
import com.fytec.entity.workflow.WorkflowPublish;
import com.fytec.mapper.plugin.PluginPublishMapper;
import com.fytec.mapper.workflow.WorkflowPublishMapper;
import com.fytec.reranker.DynamicReRankerCallService;
import com.fytec.service.knowledge.KnowledgeService;
import com.volcengine.ark.runtime.model.completion.chat.ChatFunction;
import com.volcengine.ark.runtime.model.completion.chat.ChatTool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.fytec.constant.enums.ReRankerModelEnum.gte;

@Service
@Slf4j
@Transactional
@RequiredArgsConstructor
public class ModelSkillService {
    private final WorkflowPublishMapper workflowPublishMapper;
    private final PluginPublishMapper pluginPublishMapper;
    private final ClientProperties clientProperties;

    private final KnowledgeService knowledgeService;
    private final DynamicReRankerCallService reRankerCallService;


    public List<ChatToolDTO> prepareWorkflowTools(List<WorkflowDTO> workflowDTOList) {
        if (CollUtil.isEmpty(workflowDTOList)) {
            return new ArrayList<>();
        }

        List<ChatToolDTO> tools = new ArrayList<>();
        for (WorkflowDTO workflowDTO : workflowDTOList) {
            ChatToolDTO chatToolDTO = createWorkflowChatTool(workflowDTO);
            if (chatToolDTO != null) {
                tools.add(chatToolDTO);
            }
        }
        return tools;
    }

    public List<ChatToolDTO> preparePluginTools(List<PluginDTO> pluginDTOList) {
        if (CollUtil.isEmpty(pluginDTOList)) {
            return new ArrayList<>();
        }

        List<ChatToolDTO> tools = new ArrayList<>();
        for (PluginDTO pluginDTO : pluginDTOList) {
            ChatToolDTO chatToolDTO = createPluginChatTool(pluginDTO);
            if (chatToolDTO != null) {
                tools.add(chatToolDTO);
            }
        }
        return tools;
    }

    /**
     * 根据工作流DTO创建聊天工具DTO
     */
    private ChatToolDTO createWorkflowChatTool(WorkflowDTO workflowDTO) {
        WorkflowPublish workflowPublish = getWorkflowPublish(workflowDTO);
        if (workflowPublish == null || StringUtils.isBlank(workflowPublish.getFunctionCallTool())) {
            return null;
        }

        ChatTool chatTool = parseChatTool(workflowPublish.getFunctionCallTool());
        if (chatTool == null) {
            return null;
        }

        return buildChatToolDTO(
                workflowPublish.getId(),
                workflowPublish.getVersion(),
                workflowPublish.getResourceId(),
                Constants.RESOURCE_TYPE.workflow.name(),
                chatTool
        );
    }

    /**
     * 根据插件DTO创建聊天工具DTO
     */
    private ChatToolDTO createPluginChatTool(PluginDTO pluginDTO) {
        PluginPublish pluginPublish = pluginPublishMapper.selectById(pluginDTO.getId());
        if (pluginPublish == null || StringUtils.isBlank(pluginPublish.getFunctionCallTool())) {
            return null;
        }

        ChatTool chatTool = parseChatTool(pluginPublish.getFunctionCallTool());
        if (chatTool == null) {
            return null;
        }

        return buildChatToolDTO(
                pluginPublish.getId(),
                pluginPublish.getVersion(),
                pluginPublish.getResourceId(),
                Constants.RESOURCE_TYPE.plugin.name(),
                chatTool
        );
    }

    /**
     * 获取工作流发布信息
     */
    private WorkflowPublish getWorkflowPublish(WorkflowDTO workflowDTO) {
        return workflowPublishMapper.selectOne(
                new LambdaQueryWrapper<WorkflowPublish>()
                        .eq(WorkflowPublish::getResourceId, workflowDTO.getResourceId())
                        .eq(WorkflowPublish::getId, workflowDTO.getId())
        );
    }

    /**
     * 解析函数调用工具JSON字符串为ChatTool对象
     */
    private ChatTool parseChatTool(String functionCallToolJson) {
        try {
            JSONObject jsonObject = JSON.parseObject(functionCallToolJson);
            if (jsonObject == null) {
                return null;
            }

            ChatTool chatTool = new ChatTool();
            chatTool.setType(jsonObject.getString("type"));

            ChatFunction function = parseChatFunction(jsonObject.getString("function"));
            if (function == null) {
                return null;
            }

            chatTool.setFunction(function);
            return chatTool;
        } catch (Exception e) {
            log.warn("解析ChatTool失败: {}", functionCallToolJson, e);
            return null;
        }
    }

    /**
     * 解析函数JSON字符串为ChatFunction对象
     */
    private ChatFunction parseChatFunction(String functionJson) {
        if (StringUtils.isBlank(functionJson)) {
            return null;
        }

        try {
            JSONObject functionObj = JSON.parseObject(functionJson);
            if (functionObj == null) {
                return null;
            }

            ChatFunction function = new ChatFunction();
            function.setName(functionObj.getString("name"));
            function.setDescription(functionObj.getString("description"));

            // 处理parameters参数（可选）
            String parametersJson = functionObj.getString("parameters");
            if (StringUtils.isNotBlank(parametersJson)) {
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode parameters = objectMapper.readTree(parametersJson);
                function.setParameters(parameters);
            }

            return function;
        } catch (Exception e) {
            log.warn("解析ChatFunction失败: {}", functionJson, e);
            return null;
        }
    }

    /**
     * 构建ChatToolDTO对象
     */
    private ChatToolDTO buildChatToolDTO(Long id, String version, Long resourceId,
                                         String type, ChatTool chatTool) {
        ChatToolDTO chatToolDTO = new ChatToolDTO();
        chatToolDTO.setId(id);
        chatToolDTO.setVersion(version);
        chatToolDTO.setResourceId(resourceId);
        chatToolDTO.setClientId(clientProperties.getClientId());
        chatToolDTO.setType(type);
        chatToolDTO.setChatTool(chatTool);
        return chatToolDTO;
    }

    public List<VectorResultDTO> addKnowledge(ModelProcessDTO processDTO, KnowledgeConfigDTO knowledgeConfig) {
        log.info("Model Skill addKnowledge: {}", knowledgeConfig);
        // 检查是否有知识库配置
        if (ObjectUtil.isEmpty(knowledgeConfig)) {
            return null;
        }

        // 解析知识库配置
        List<KnowledgeConfigDTO> configs = mergeKnowledgeConfigs(knowledgeConfig, null);

        // 执行知识库检索
        List<VectorResultDTO> allDocuments = searchKnowledgeDocuments(configs, processDTO.getUserMessage());
        log.info("Model Skill allDocuments: {}", allDocuments);
        if (allDocuments.isEmpty()) {
            return null;
        }

        // 去重处理
        allDocuments = deduplicateDocuments(allDocuments);

        // 获取重排和过滤参数
        SearchParams searchParams = extractSearchParams(configs);

        // 执行重排序或排序
        allDocuments = applySortingOrReranking(allDocuments, searchParams, processDTO.getUserMessage());

        // 验证最小文档数量要求
        validateMinimumDocumentCount(allDocuments, searchParams.minDocLen);

        // 补充文档名称信息
        enrichDocumentNames(allDocuments);

        // 构建知识库内容并更新请求
        buildKnowledgeContent(processDTO, allDocuments);

        return allDocuments;
    }

    /**
     * 解析知识库配置
     */
    private List<KnowledgeConfigDTO> mergeKnowledgeConfigs(KnowledgeConfigDTO knowledgeConfig, KnowledgeConfigDTO positiveKnowledgeConfig) {
        List<KnowledgeConfigDTO> configs = new LinkedList<>();
        configs.add(knowledgeConfig);
        //标注、正样本知识库，如果需要查询，可自行添加
        if (ObjectUtil.isNotEmpty(positiveKnowledgeConfig)) {
            configs.add(positiveKnowledgeConfig);
        }
        return configs;
    }

    /**
     * 执行知识库文档检索
     */
    private List<VectorResultDTO> searchKnowledgeDocuments(List<KnowledgeConfigDTO> configs, String userInput) {
        List<VectorResultDTO> allDocuments = new LinkedList<>();
        if (StringUtils.isBlank(userInput)) {
            return allDocuments;
        }

        for (KnowledgeConfigDTO config : configs) {
            if (CollUtil.isEmpty(config.getKnowledgeList())) {
                continue;
            }
            // 判断是否需要分别处理每个知识库
            if (needSeparateProcessing(config)) {
                allDocuments.addAll(searchKnowledgeSeparately(config, userInput));
            } else {
                allDocuments.addAll(searchKnowledgeTogether(config, userInput));
            }
        }

        return allDocuments;
    }

    /**
     * 判断是否需要分别处理知识库（权重不同或阈值不同）
     */
    private boolean needSeparateProcessing(KnowledgeConfigDTO config) {
        Float referenceMinScore = null;

        for (KnowledgeDTO knowledge : config.getKnowledgeList()) {
            // 检查权重是否不同
            if (knowledge.getWeight() != null && knowledge.getWeight() != 0 && knowledge.getWeight() != 1) {
                return true;
            }

            // 检查阈值是否不同
            if (referenceMinScore == null) {
                referenceMinScore = knowledge.getMinScore();
            } else if (!Objects.equals(knowledge.getMinScore(), referenceMinScore)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 统一处理知识库检索（相同配置）
     */
    private List<VectorResultDTO> searchKnowledgeTogether(KnowledgeConfigDTO config, String userInput) {
        List<Long> knowledgeIds = config.getKnowledgeList().stream()
                .map(KnowledgeDTO::getId)
                .collect(Collectors.toList());

        KnowLedgeDocSearchDTO searchDTO = buildSearchDTO(knowledgeIds, userInput, config);
        return knowledgeService.docSearchByDto(searchDTO);
    }

    /**
     * 分别处理知识库检索（不同配置）
     */
    private List<VectorResultDTO> searchKnowledgeSeparately(KnowledgeConfigDTO config, String userInput) {
        List<VectorResultDTO> results = new LinkedList<>();

        for (KnowledgeDTO knowledge : config.getKnowledgeList()) {
            List<Long> singleKnowledgeId = Collections.singletonList(knowledge.getId());
            KnowLedgeDocSearchDTO searchDTO = buildSearchDTO(singleKnowledgeId, userInput, knowledge);
            List<VectorResultDTO> docs = knowledgeService.docSearchByDto(searchDTO);

            // 应用权重调整
            applyWeightAdjustment(docs, knowledge.getWeight());
            results.addAll(docs);
        }
        return results;
    }

    /**
     * 构建检索DTO
     */
    private KnowLedgeDocSearchDTO buildSearchDTO(List<Long> knowledgeIds, String userInput,
                                                 KnowledgeConfigDTO config) {
        KnowLedgeDocSearchDTO searchDTO = new KnowLedgeDocSearchDTO();
        searchDTO.setKnowledgeIds(knowledgeIds);
        searchDTO.setQuery(userInput.trim());
        searchDTO.setTopK(config.getTopK() == 0 ? 5 : config.getTopK());
        searchDTO.setMinScore(config.getMinScore());
        return searchDTO;
    }

    /**
     * 构建检索DTO（单个知识库）
     */
    private KnowLedgeDocSearchDTO buildSearchDTO(List<Long> knowledgeIds, String userInput,
                                                 KnowledgeDTO knowledge) {
        KnowLedgeDocSearchDTO searchDTO = new KnowLedgeDocSearchDTO();
        searchDTO.setKnowledgeIds(knowledgeIds);
        searchDTO.setQuery(userInput.trim());
        searchDTO.setTopK(knowledge.getTopK() == 0 ? 5 : knowledge.getTopK());
        searchDTO.setMinScore(knowledge.getMinScore());
        return searchDTO;
    }

    /**
     * 应用权重调整
     */
    private void applyWeightAdjustment(List<VectorResultDTO> docs, Float weight) {
        if (weight == null || weight == 1) {
            return;
        }

        docs.forEach(doc -> doc.setScore(weight * doc.getScore()));
    }

    /**
     * 文档去重
     */
    private List<VectorResultDTO> deduplicateDocuments(List<VectorResultDTO> documents) {
        if (documents.isEmpty()) {
            return documents;
        }

        List<VectorResultDTO> deduplicatedDocs = new LinkedList<>();
        Set<String> textSet = new HashSet<>();

        for (VectorResultDTO doc : documents) {
            String text = doc.getText();
            if (!textSet.contains(text)) {
                textSet.add(text);
                deduplicatedDocs.add(doc);
            }
        }
        return deduplicatedDocs;
    }

    /**
     * 提取搜索参数
     */
    private SearchParams extractSearchParams(List<KnowledgeConfigDTO> configs) {
        SearchParams params = new SearchParams();

        for (KnowledgeConfigDTO config : configs) {
            // 提取重排序类型
            if (StringUtils.isNotBlank(config.getRerankerType()) &&
                    config.getRerank() != null && config.getRerank()) {
                params.rerankerType = config.getRerankerType();
            }

            // 提取最小文档长度
            if (config.getMinDocLen() != null && params.minDocLen < config.getMinDocLen()) {
                params.minDocLen = config.getMinDocLen();
            }

            // 提取topK
            params.topK = config.getTopK();
        }

        return params;
    }

    /**
     * 应用排序或重排序
     */
    private List<VectorResultDTO> applySortingOrReranking(List<VectorResultDTO> documents,
                                                          SearchParams params, String userInput) {
        if (documents.size() <= params.topK) {
            return documents;
        }

        if (StringUtils.isNotBlank(params.rerankerType)) {
            log.info("执行重排序: {}", params.rerankerType);
            ReRankerModelEnum rankerType = ReRankerModelEnum.valueOf(params.rerankerType);
            return reRankerCallService.filterKnowledgeDocsByReRanker(
                    rankerType, userInput.trim(), params.topK, documents);
        } else {
            // 按分数排序并截取
            return documents.stream()
                    .sorted(Comparator.comparingDouble(VectorResultDTO::getScore).reversed())
                    .limit(params.topK)
                    .collect(Collectors.toList());
        }
    }

    /**
     * 验证最小文档数量
     */
    private void validateMinimumDocumentCount(List<VectorResultDTO> documents, Integer minDocLen) {
        if (minDocLen > 0 && minDocLen > documents.size()) {
            throw new ValidateException("未查询到相关数据，请换一个问题");
        }
    }

    /**
     * 补充文档名称信息
     */
    private void enrichDocumentNames(List<VectorResultDTO> documents) {
        // 收集已有的文档名称
        Map<Long, String> existingDocNames = documents.stream()
                .filter(doc -> StringUtils.isNotBlank(doc.getDoc()))
                .collect(Collectors.toMap(
                        VectorResultDTO::getDocId,
                        VectorResultDTO::getDoc,
                        (existing, replacement) -> existing));

        // 查找缺失文档名称的文档ID
        Set<Long> missingDocNameIds = documents.stream()
                .filter(doc -> StringUtils.isBlank(doc.getDoc()))
                .map(VectorResultDTO::getDocId)
                .filter(docId -> !existingDocNames.containsKey(docId))
                .collect(Collectors.toSet());

        // 批量查询缺失的文档名称
        if (!missingDocNameIds.isEmpty()) {
            Map<Long, String> queriedDocNames = knowledgeService.docSearchNameByIds(missingDocNameIds)
                    .stream()
                    .collect(Collectors.toMap(
                            KnowledgeDocDTO::getId,
                            KnowledgeDocDTO::getFileName));
            existingDocNames.putAll(queriedDocNames);
        }

        // 补充文档名称
        documents.forEach(doc -> {
            if (StringUtils.isBlank(doc.getDoc())) {
                doc.setDoc(existingDocNames.get(doc.getDocId()));
            }
        });
    }

    /**
     * 构建知识库内容并更新请求
     */
    private void buildKnowledgeContent(ModelProcessDTO processDTO, List<VectorResultDTO> documents) {
        if (CollUtil.isEmpty(documents)) {
            return;
        }

        StringBuilder knowledgeContent = new StringBuilder();
        knowledgeContent.append("## 参考内容：\n");

        int index = processDTO.getIndex();
        for (VectorResultDTO doc : documents) {
            index++;
            doc.setIndex(index);

            Map<String, Object> docInfo = buildDocumentInfo(index, doc);
            knowledgeContent.append(JSON.toJSONString(docInfo));
        }

        knowledgeContent.append("## 我的问题是：\n");
        log.info("Model Skill knowledgeContent: {}", knowledgeContent);

        processDTO.setUserMessage(knowledgeContent + processDTO.getUserMessage());
        processDTO.setIndex(index);
    }

    /**
     * 构建单个文档信息
     */
    private Map<String, Object> buildDocumentInfo(int index, VectorResultDTO doc) {
        Map<String, Object> docInfo = new HashMap<>();
        docInfo.put("序号", index);

        if (StringUtils.isNotBlank(doc.getDoc())) {
            docInfo.put("标题", doc.getDoc());
        }
        // TODO: 可以考虑把概述作为文档名字

        docInfo.put("内容", doc.getText());
        return docInfo;
    }

    /**
     * 搜索参数封装类
     */
    private static class SearchParams {
        String rerankerType = gte.name();
        Integer minDocLen = 0;
        int topK = 10;
    }
}
