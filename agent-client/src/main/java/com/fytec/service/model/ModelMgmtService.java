package com.fytec.service.model;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.llm.*;
import com.fytec.entity.llm.AiModel;
import com.fytec.entity.llm.AiModelGroup;
import com.fytec.mapper.model.ModelGroupMapper;
import com.fytec.mapper.model.ModelMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class ModelMgmtService {
    private final ModelMapper modelMapper;
    private final ModelGroupMapper modelGroupMapper;

    private boolean isExist(String code) {
        long count = modelMapper.selectCount(
                new LambdaQueryWrapper<AiModel>()
                        .eq(AiModel::getCode, code)
        );
        return count > 0;
    }

    public void addAiModel(AiModelCreateDTO aiModelCreateDTO) {
        if (isExist(aiModelCreateDTO.getCode())) {
            throw new ValidateException("模型编码已存在");
        }
        AiModel aiModel = new AiModel();
        BeanUtil.copyProperties(aiModelCreateDTO, aiModel);
        aiModel.setDiversityParams(aiModelCreateDTO.getDiversityParams());
        aiModel.setIoParams(aiModelCreateDTO.getIoParams());
        aiModel.setEnable(true);
        modelMapper.insert(aiModel);
    }

    public void editAiModel(AiModelUpdateDTO aiModelUpdateDTO) {
        AiModel aiModel = modelMapper.selectById(aiModelUpdateDTO.getId());
        if (aiModel == null) {
            throw new ValidateException("模型不存在");
        }
        if (!StrUtil.equals(aiModelUpdateDTO.getCode(), aiModel.getCode())) {
            if (isExist(aiModelUpdateDTO.getCode())) {
                throw new ValidateException("模型编码已存在");
            }
        }
        BeanUtil.copyProperties(aiModelUpdateDTO, aiModel, "defaulted");
        aiModel.setDiversityParams(aiModelUpdateDTO.getDiversityParams());
        aiModel.setIoParams(aiModelUpdateDTO.getIoParams());
        modelMapper.updateById(aiModel);
    }

    public void updateAiModelStatus(Long id, boolean isEnable) {
        AiModel aiModel = modelMapper.selectById(id);
        if (aiModel == null) {
            throw new ValidateException("模型不存在");
        }
        aiModel.setEnable(isEnable);
        modelMapper.updateById(aiModel);
    }

    public Page<AiModelListDTO> queryAiModelPaging(AiModelQueryDTO aiModelQueryDTO, Page<AiModelListDTO> page) {
        page.setRecords(modelMapper.queryAiModelPaging(page, aiModelQueryDTO));
        return page;
    }

    public AiModel queryAiModelDetail(Long id) {
        AiModel aiModel = modelMapper.selectById(id);
        if (aiModel == null) {
            throw new ValidateException("模型不存在");
        }

        AiModelGroup aiModelGroup = modelGroupMapper.selectById(aiModel.getGroupId());
        if (aiModelGroup != null) {
            aiModel.setLogo(aiModelGroup.getLogo());
        }
        return aiModel;
    }


    private boolean isGroupExist(String name) {
        long count = modelGroupMapper.selectCount(
                new LambdaQueryWrapper<AiModelGroup>()
                        .eq(AiModelGroup::getGroupName, name)
        );
        return count > 0;
    }

    public Long addAiModelGroup(AiModelGroupCreateDTO dto) {
        if (isGroupExist(dto.getGroupName())) {
            throw new ValidateException("模型组已存在");
        }
        AiModelGroup aiModelGroup = new AiModelGroup();
        BeanUtil.copyProperties(dto, aiModelGroup);
        modelGroupMapper.insert(aiModelGroup);
        return aiModelGroup.getId();
    }

    public void editAiModelGroup(AiModelGroupUpdateDTO dto) {
        AiModelGroup aiModelGroup = modelGroupMapper.selectById(dto.getId());
        if (aiModelGroup == null) {
            throw new ValidateException("模型组不存在");
        }
        if (!StrUtil.equals(dto.getGroupName(), aiModelGroup.getGroupName())) {
            if (isGroupExist(dto.getGroupName())) {
                throw new ValidateException("模型组已存在");
            }
        }
        BeanUtil.copyProperties(dto, aiModelGroup);
        modelGroupMapper.updateById(aiModelGroup);
    }

    public void deleteAiModel(Long id) {
        long count = modelMapper.selectCount(
                new LambdaQueryWrapper<AiModel>().eq(AiModel::getGroupId, id)
        );
        if (count > 0) {
            throw new ValidateException("该模型组下有模型，请先删除模型组下的所有模型");
        }
        modelGroupMapper.deleteById(id);
    }

    public Page<AiModelGroupListDTO> queryAiModelGroupPaging(AiModelGroupQueryDTO dto, Page<AiModelGroupListDTO> page) {
        page.setRecords(modelGroupMapper.queryAiModelGroupPaging(page, dto));
        return page;
    }

    public AiModelGroup queryAiModelGroupDetail(Long id) {
        return modelGroupMapper.selectById(id);
    }

    public void setDefaultAiModel(AiModelUpdateDTO aiModelUpdateDTO) {
        AiModel aiModel = modelMapper.selectById(aiModelUpdateDTO.getId());
        if (aiModel == null) {
            throw new ValidateException("模型不存在");
        }

        modelMapper.update(
                new LambdaUpdateWrapper<AiModel>()
                        .set(AiModel::isDefaulted, false)
                        .eq(AiModel::isEnable, true)
        );

        aiModel.setDefaulted(true);
        modelMapper.updateById(aiModel);

    }
}
