package com.fytec.service.model;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fytec.dto.flow.WorkflowDTO;
import com.fytec.dto.knowledge.KnowledgeConfigDTO;
import com.fytec.dto.knowledge.KnowledgeDTO;
import com.fytec.dto.llm.ChatToolDTO;
import com.fytec.dto.llm.ModelCallDTO;
import com.fytec.dto.llm.ModelProcessExtDTO;
import com.fytec.dto.plugin.PluginDTO;
import com.fytec.entity.history.AiExecuteHistory;
import com.fytec.entity.llm.AiModel;
import com.fytec.mapper.history.AiExecuteHistoryMapper;
import com.fytec.mapper.model.ModelMapper;
import com.fytec.model.DynamicModelCallService;
import com.fytec.service.agent.ConstantService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@Slf4j
@Transactional
@RequiredArgsConstructor
public class ModelExecuteService {

    private final ModelMapper modelMapper;
    private final AiExecuteHistoryMapper aiExecuteHistoryMapper;

    private final ConstantService constantService;

    private final DynamicModelCallService modelCallService;
    private final ModelSkillService modelSkillService;

    @SneakyThrows
    public Map<String, Object> processModelByNonStream(ModelProcessExtDTO dto) {
        AiModel model = modelMapper.selectOne(
                new LambdaQueryWrapper<AiModel>()
                        .eq(AiModel::getCode, dto.getModelType())
                        .last("limit 1")
        );

        dto.setSystemMessage(constantService.convertPromptConstant(dto.getSystemMessage()));
        dto.setUserMessage(constantService.convertPromptConstant(dto.getUserMessage()));

        if (CollUtil.isNotEmpty(dto.getFunctionCallParam())) {
            prepareFunctionCallParam(dto);
        }


        Map<String, Object> paramBody = JSON.parseObject(JSON.toJSONString(dto), Map.class);
        ModelCallDTO modelCallDTO = new ModelCallDTO();
        modelCallDTO.setMethodName(model.getNonStreamMethod());
        modelCallDTO.setUrl(model.getNonStreamUrl());
        modelCallDTO.setParamBody(paramBody);
        StringBuffer history = new StringBuffer();
        StringBuffer reasonHistory = new StringBuffer();
        StringBuffer referenceHistory = new StringBuffer();
        modelCallDTO.setHistory(history);
        modelCallDTO.setReasonHistory(reasonHistory);
        modelCallDTO.setReferenceHistory(referenceHistory);
        return modelCallService.callModelNonStream(modelCallDTO);
    }

    private void prepareFunctionCallParam(ModelProcessExtDTO dto) {
        List<ChatToolDTO> tools = new ArrayList<>();
        Map<String, Object> functionCallParam = dto.getFunctionCallParam();
        if (functionCallParam.containsKey("plugin")) {
            List<PluginDTO> pluginList = JSON.parseArray(JSON.toJSONString(functionCallParam.get("plugin")), PluginDTO.class);
            tools.addAll(modelSkillService.preparePluginTools(pluginList));

        }
        if (functionCallParam.containsKey("workflow")) {
            List<WorkflowDTO> workflowList = JSON.parseArray(JSON.toJSONString(functionCallParam.get("workflow")), WorkflowDTO.class);
            tools.addAll(modelSkillService.prepareWorkflowTools(workflowList));
        }
        dto.setTools(tools);

        if (functionCallParam.containsKey("knowledge")) {
            List<KnowledgeDTO> knowledgeList = JSON.parseArray(JSON.toJSONString(functionCallParam.get("knowledge")), KnowledgeDTO.class);
            KnowledgeConfigDTO knowledgeConfigDTO = JSON.parseObject(JSON.toJSONString(functionCallParam.get("knowledgeGlobalSetting")), KnowledgeConfigDTO.class);
            knowledgeConfigDTO.setKnowledgeList(knowledgeList);
            modelSkillService.addKnowledge(dto, knowledgeConfigDTO);
        }
    }

    @SneakyThrows
    public void processModelByStream(HttpServletResponse response, ModelProcessExtDTO dto) {
        AiModel model = modelMapper.selectOne(
                new LambdaQueryWrapper<AiModel>()
                        .eq(AiModel::getCode, dto.getModelType())
                        .last("limit 1")
        );

        dto.setClientId(UUID.randomUUID().toString());
        dto.setSystemMessage(constantService.convertPromptConstant(dto.getSystemMessage()));
        dto.setUserMessage(constantService.convertPromptConstant(dto.getUserMessage()));

        if (CollUtil.isNotEmpty(dto.getFunctionCallParam())) {
            prepareFunctionCallParam(dto);
        }

        //查询历史---start
        List<AiExecuteHistory> aiExecuteHistories = aiExecuteHistoryMapper.selectList(
                new LambdaQueryWrapper<AiExecuteHistory>()
                        .eq(AiExecuteHistory::getConversationId, dto.getConversationName())
                        .eq(AiExecuteHistory::getThirdUserId, dto.getThirdUserId())
                        .orderByDesc(AiExecuteHistory::getCreateTime)
                        .last(StrUtil.format("LIMIT {}", dto.getChatHistoryRound()))
        );

        List<Map<String, String>> histories = new ArrayList<>();
        if (CollUtil.isNotEmpty(aiExecuteHistories)) {
            for (int i = aiExecuteHistories.size() - 1; i >= 0; i--) {
                AiExecuteHistory aiExecuteHistory = aiExecuteHistories.get(i);

                Map<String, String> map = new HashMap<>();
                map.put(aiExecuteHistory.getUserInput(), aiExecuteHistory.getContent());
                histories.add(map);
            }
        }
        dto.setHistories(histories);
        //查询历史---end

        Map<String, Object> paramBody = JSON.parseObject(JSON.toJSONString(dto), Map.class);


        ModelCallDTO modelCallDTO = new ModelCallDTO();
        modelCallDTO.setResponse(response);
        modelCallDTO.setMethodName(model.getStreamMethod());
        modelCallDTO.setUrl(model.getStreamUrl());
        modelCallDTO.setParamBody(paramBody);

        StringBuffer history = new StringBuffer();
        StringBuffer reasonHistory = new StringBuffer();
        StringBuffer referenceHistory = new StringBuffer();
        modelCallDTO.setHistory(history);
        modelCallDTO.setReasonHistory(reasonHistory);
        modelCallDTO.setReferenceHistory(referenceHistory);
        modelCallService.callModelStream(modelCallDTO);
    }
}
