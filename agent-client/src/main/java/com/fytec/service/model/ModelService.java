package com.fytec.service.model;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fytec.dto.llm.AiModelQueryDTO;
import com.fytec.dto.llm.ModelParamDTO;
import com.fytec.dto.model.ModelDTO;
import com.fytec.dto.model.ModelGroupListDTO;
import com.fytec.dto.model.ModelListDTO;
import com.fytec.entity.llm.AiModel;
import com.fytec.entity.llm.EmbeddedModel;
import com.fytec.mapper.model.EmbeddedModelMapper;
import com.fytec.mapper.model.ModelMapper;
import com.fytec.satoken.StpClientUserUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
@Transactional
@AllArgsConstructor
public class ModelService {

    private final ModelMapper modelMapper;

    private final EmbeddedModelMapper embeddedModelMapper;

    public List<ModelGroupListDTO> findModelGroup() {
        AiModelQueryDTO dto = new AiModelQueryDTO();
        if (!StpClientUserUtil.hasRole("admin")) {
            dto.setOpen(true);
        }
        List<ModelListDTO> modelList = modelMapper.findAll(dto);
        if (CollUtil.isEmpty(modelList)) {
            return null;
        }
        List<ModelGroupListDTO> result = new ArrayList<>();
        Map<Long, ModelGroupListDTO> groupMap = new HashMap<>();
        for (ModelListDTO model : modelList) {
            ModelGroupListDTO group = groupMap.get(model.getGroupId());
            if (group == null) {
                group = new ModelGroupListDTO();
                group.setGroupId(model.getGroupId());
                group.setGroupName(model.getGroupName());
                group.setGroupLogo(model.getGroupLogo());
                group.setModelList(new ArrayList<>());
                groupMap.put(model.getGroupId(), group);
                result.add(group);
            }
            // 过滤掉enable不为0的
            ModelDTO modelDTO = new ModelDTO();
            BeanUtils.copyProperties(model, modelDTO);
            modelDTO.setDiversityParams(JSON.parseArray(model.getDiversityParams(), ModelParamDTO.class));
            modelDTO.setIoParams(JSON.parseArray(model.getIoParams(), ModelParamDTO.class));

            group.getModelList().add(modelDTO);
        }

        return result;
    }

    public ModelDTO findDefaultModel() {
        AiModel model = modelMapper.selectOne(new LambdaQueryWrapper<AiModel>()
                .eq(AiModel::isDefaulted, 1).last("limit 1"));
        if (model == null) {
            return null;
        }
        ModelDTO modelDTO = new ModelDTO();
        BeanUtils.copyProperties(model, modelDTO);
        modelDTO.setDiversityParams(JSON.parseArray(model.getDiversityParams(), ModelParamDTO.class));
        modelDTO.setIoParams(JSON.parseArray(model.getIoParams(), ModelParamDTO.class));
        return modelDTO;
    }

    public List<EmbeddedModel> findEmbeddedModel() {
        return embeddedModelMapper.selectList(
                new LambdaQueryWrapper<EmbeddedModel>()
                        .eq(EmbeddedModel::isEnable, true)
                        .orderByAsc(EmbeddedModel::getId)
        );
    }

    public List<ModelListDTO> queryModelList(AiModelQueryDTO aiModelQueryDTO) {
        if (!StpClientUserUtil.hasRole("admin")) {
            aiModelQueryDTO.setOpen(true);
        }
        return modelMapper.findAll(aiModelQueryDTO);
    }
}
