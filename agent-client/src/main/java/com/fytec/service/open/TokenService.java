package com.fytec.service.open;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import com.fytec.dto.token.OauthClientDetailDTO;
import com.fytec.entity.system.OauthClientDetail;

import com.fytec.mapper.system.OauthClientDetailMapper;

import com.fytec.properties.FytecTokenProperties;
import com.fytec.util.HmacUtils;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.List;

@Service
@RequiredArgsConstructor
public class TokenService {
    @Resource
    private OauthClientDetailMapper oauthClientDetailMapper;

    public List<OauthClientDetailDTO> queryOauthClientDetail(String clientName) {


        LambdaQueryWrapper<OauthClientDetail> queryWrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isNotBlank(clientName)) {
            queryWrapper.like(OauthClientDetail::getName, clientName);
        }

        return oauthClientDetailMapper.selectList(queryWrapper).stream().map(t -> {
            OauthClientDetailDTO dto = new OauthClientDetailDTO();
            BeanUtil.copyProperties(t, dto);
            return dto;
        }).toList();
    }
}
