package com.fytec.service.system;

import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.CurrentUserDTO;
import com.fytec.dto.system.*;
import com.fytec.entity.system.SysRole;
import com.fytec.entity.system.SysUser;
import com.fytec.entity.system.SysUserOrganization;
import com.fytec.entity.system.SysUserRole;
import com.fytec.mapper.system.*;
import com.fytec.satoken.StpClientUserUtil;
import com.fytec.schedule.oa.user.OaUser;
import com.fytec.util.TreeBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class SysUserService {
    private final SysUserMapper sysUserMapper;
    private final SysRoleMapper sysRoleMapper;
    private final SysUserRoleMapper sysUserRoleMapper;
    private final SysUserOrganizationMapper sysUserOrganizationMapper;
    private final SysMenuMapper sysMenuMapper;

    private final SysOrganizationService sysOrganizationService;

    public boolean checkDuplicateUser(String nameOrTel) {
        Long count = sysUserMapper.checkDuplicateUser(nameOrTel);
        return count > 0;
    }


    public void addUser(SysUserCreateDTO sysUserCreateDTO) {
        if (checkDuplicateUser(sysUserCreateDTO.getLoginName())) {
            throw new ValidateException("登录名已存在");
        }
        if (checkDuplicateUser(sysUserCreateDTO.getTel())) {
            throw new ValidateException("电话已存在");
        }
        SysUser sysUser = new SysUser();
        BeanUtil.copyProperties(sysUserCreateDTO, sysUser, "password");
        sysUser.setPassword(BCrypt.hashpw(sysUserCreateDTO.getPassword()));
        sysUserMapper.insert(sysUser);

        //给用户添加角色
        if (sysUserCreateDTO.getRoleIds() != null) {
            for (Long roleId : sysUserCreateDTO.getRoleIds()) {
                SysUserRole sysUserRole = new SysUserRole();
                sysUserRole.setUserId(sysUser.getId());
                sysUserRole.setRoleId(roleId);
                sysUserRoleMapper.insert(sysUserRole);
            }
        }

        if (sysUserCreateDTO.getGroupIds() != null) {
            for (Long groupId : sysUserCreateDTO.getGroupIds()) {
                SysUserOrganization sysUserOrganization = new SysUserOrganization();
                sysUserOrganization.setUserId(sysUser.getId());
                sysUserOrganization.setOrgId(groupId);
                sysUserOrganizationMapper.insert(sysUserOrganization);
            }

        }
    }

    public void updateUser(SysUserUpdateDTO updateSysUserDTO) {
        SysUser sysUser = sysUserMapper.selectById(updateSysUserDTO.getId());
        if (sysUser == null) {
            throw new ValidateException("用户不存在");
        }

        String loginName = updateSysUserDTO.getLoginName();
        if (!sysUser.getLoginName().equals(loginName)) {
            if (checkDuplicateUser(updateSysUserDTO.getLoginName())) {
                throw new ValidateException("登录名已存在");
            }
        }

        String tel = updateSysUserDTO.getTel();
        if (!StrUtil.equals(sysUser.getTel(), tel)) {
            if (checkDuplicateUser(updateSysUserDTO.getTel())) {
                throw new ValidateException("电话已存在");
            }
        }

        BeanUtil.copyProperties(updateSysUserDTO, sysUser, "password");
        if (StringUtils.isNotBlank(updateSysUserDTO.getPassword())) {
            sysUser.setPassword(BCrypt.hashpw(updateSysUserDTO.getPassword()));
        }
        sysUserMapper.updateById(sysUser);

        sysUserRoleMapper.delete(
                new LambdaQueryWrapper<SysUserRole>()
                        .eq(SysUserRole::getUserId, sysUser.getId())
        );
        if (updateSysUserDTO.getRoleIds() != null) {
            for (Long roleId : updateSysUserDTO.getRoleIds()) {
                SysUserRole sysUserRole = new SysUserRole();
                sysUserRole.setUserId(sysUser.getId());
                sysUserRole.setRoleId(roleId);
                sysUserRoleMapper.insert(sysUserRole);
            }
        }


        sysUserOrganizationMapper.delete(
                new LambdaQueryWrapper<SysUserOrganization>()
                        .eq(SysUserOrganization::getUserId, sysUser.getId())
        );
        if (updateSysUserDTO.getGroupIds() != null) {
            for (Long groupId : updateSysUserDTO.getGroupIds()) {
                SysUserOrganization sysUserOrganization = new SysUserOrganization();
                sysUserOrganization.setUserId(sysUser.getId());
                sysUserOrganization.setOrgId(groupId);
                sysUserOrganizationMapper.insert(sysUserOrganization);
            }
        }
    }

    public void deleteUser(Long id) {
        SysUser sysUser = sysUserMapper.selectById(id);
        if (sysUser == null) {
            throw new ValidateException("用户不存在");
        }
        sysUserRoleMapper.delete(
                new LambdaQueryWrapper<SysUserRole>()
                        .eq(SysUserRole::getUserId, sysUser.getId())
        );

        sysUserOrganizationMapper.delete(
                new LambdaQueryWrapper<SysUserOrganization>()
                        .eq(SysUserOrganization::getUserId, sysUser.getId())
        );
        sysUserMapper.deleteById(id);
    }

    public Page<SysUserListDTO> queryUser(Page<SysUserListDTO> page, SysUserQueryDTO searchUserDTO) {
        List<SysUserListDTO> sysUserListDTOList = sysUserMapper.queryUser(page, searchUserDTO);
        page.setRecords(sysUserListDTOList);
        return page;
    }

    public SysUserDetailDTO getUserDetail(Long id) {
        SysUser sysUser = sysUserMapper.selectById(id);

        List<SysUserRole> sysUserRoles = sysUserRoleMapper.selectList(
                new LambdaQueryWrapper<SysUserRole>()
                        .eq(SysUserRole::getUserId, id)
                        .select(SysUserRole::getRoleId)
        );
        List<Long> roleIds = sysUserRoles.stream().map(SysUserRole::getRoleId).toList();

        //设置返回对象
        SysUserDetailDTO result = new SysUserDetailDTO();
        if (sysUser != null) {
            BeanUtils.copyProperties(sysUser, result);
            result.setRoleIds(roleIds);
        }
        return result;
    }

    public CurrentUserDTO getCurrentUser() {
        Long userId = StpClientUserUtil.getLoginIdAsLong();
        SysUser sysUser = sysUserMapper.selectById(userId);
        if (sysUser != null) {
            CurrentUserDTO currentUserDTO = new CurrentUserDTO();
            BeanUtils.copyProperties(sysUser, currentUserDTO);
            List<String> roles = sysUserRoleMapper.selectRoleListByUserId(userId);
            List<Long> roleIds = sysUserRoleMapper.selectRoleIdByUserId(userId);
            currentUserDTO.setRoles(roles);

            List<String> groups = sysOrganizationService.selectOrganizationsByUserId(userId);
            currentUserDTO.setGroups(groups);

            List<SysMenuTreeDTO> treeList;
            if (roles.contains("admin")) {
                treeList = sysMenuMapper.selectMenuListByRoles(null);
            } else {
                treeList = sysMenuMapper.selectMenuListByRoles(roleIds);
            }
            treeList = (List<SysMenuTreeDTO>) TreeBuilder.buildListToTree(treeList);
            currentUserDTO.setMenus(treeList);

            return currentUserDTO;
        }
        return null;
    }

    public void updateUserPassword(SysUserPasswordUpdateDTO dto) {
        SysUser sysUser = sysUserMapper.selectById(dto.getId());
        if (sysUser == null) {
            throw new ValidateException("用户不存");
        }
        if (!BCrypt.checkpw(dto.getPassword(), sysUser.getPassword())) {
            throw new ValidateException("原密码不正确");
        }
        sysUser.setPassword(BCrypt.hashpw(dto.getNewPassword()));
        sysUserMapper.updateById(sysUser);
    }

    public void updateUserImage(SysUserImageUpdateDTO dto) {
        SysUser sysUser = sysUserMapper.selectById(dto.getId());
        if (sysUser == null) {
            throw new ValidateException("用户不存");
        }
        sysUser.setImageUrl(dto.getImageUrl());
        sysUserMapper.updateById(sysUser);
    }


    public List<SysUser> fetchUsersSyncFromOA() {
        return sysUserMapper.selectList(new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getStatus, "A")
                .eq(SysUser::getThirdUserType, "FYTEC_OA"));
    }

    public void addOaUser(OaUser oaUser) {
        String oaUserId = oaUser.getId();
        long count = sysUserMapper.selectCount(new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getStatus, "A")
                .eq(SysUser::getThirdUserType, "FYTEC_OA")
                .eq(SysUser::getThirdUserId, oaUserId)
        );
        if (count > 0) {
            log.warn("oa用户已存在");
            return;
        }

        SysUser user = null;
        String tel = oaUser.getTel();
        if (StringUtils.isNotBlank(tel)) {
            user = sysUserMapper.selectOne(new LambdaQueryWrapper<SysUser>()
                    .eq(SysUser::getTel, tel)
                    .eq(SysUser::getStatus, "A")
                    .isNull(SysUser::getThirdUserType)
            );
        }

        String loginName = oaUser.getUserName();
        if (StringUtils.isNotBlank(loginName)) {
            if (user == null) {
                user = sysUserMapper.selectOne(new LambdaQueryWrapper<SysUser>()
                        .eq(SysUser::getLoginName, loginName)
                        .eq(SysUser::getStatus, "A")
                        .isNull(SysUser::getThirdUserType)
                );
            }
        }

        if (user == null) {
            //添加用户
            user = new SysUser();
            user.setThirdUserType("FYTEC_OA");
            user.setThirdUserId(oaUser.getId());
            user.setLoginName(loginName);
            user.setTel(tel);
            user.setName(oaUser.getName());
            user.setStatus("A");
            user.setDeptCode(oaUser.getDeptCode());
            user.setIsSys("N");
            user.setCreateBy("1");
            sysUserMapper.insert(user);


            SysRole sysRole = sysRoleMapper.selectOne(
                    new LambdaQueryWrapper<SysRole>()
                            .eq(SysRole::getRole, "member")
                            .last("LIMIT 1")
            );

            SysUserRole sysUserRole = new SysUserRole();
            sysUserRole.setUserId(user.getId());
            sysUserRole.setRoleId(sysRole.getId());
            sysUserRoleMapper.insert(sysUserRole);
        } else {
            //电话号码, 登录名相同的用户，更新OA信息
            user.setThirdUserType("FYTEC_OA");
            user.setThirdUserId(oaUser.getId());
            user.setLoginName(loginName);
            user.setTel(tel);
            user.setName(oaUser.getName());
            user.setDeptCode(oaUser.getDeptCode());
            sysUserMapper.updateById(user);
        }
    }

    public String getUserName(String id) {
        SysUser sysUser = sysUserMapper.selectById(id);
        if (sysUser == null) {
           return "";
        }
        return sysUser.getName();
    }
}
