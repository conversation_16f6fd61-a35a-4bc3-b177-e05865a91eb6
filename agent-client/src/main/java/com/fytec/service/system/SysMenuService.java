package com.fytec.service.system;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fytec.dto.system.SysMenuCreateDTO;
import com.fytec.dto.system.SysMenuDetailDTO;
import com.fytec.dto.system.SysMenuTreeDTO;
import com.fytec.dto.system.SysMenuUpdateDTO;
import com.fytec.entity.system.SysMenu;
import com.fytec.mapper.system.SysMenuMapper;
import com.fytec.util.TreeBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class SysMenuService {
    private final SysMenuMapper sysMenuMapper;

    public void addMenu(SysMenuCreateDTO sysMenuCreateDTO) {
        if (ObjectUtil.isNotEmpty(sysMenuCreateDTO.getParentId())) {
            SysMenu sysMenu_Parent = sysMenuMapper.selectById(sysMenuCreateDTO.getParentId());
            if (sysMenu_Parent == null) {
                throw new ValidateException("父菜单不存在");
            }
        }

        SysMenu sysMenu = new SysMenu();
        BeanUtil.copyProperties(sysMenuCreateDTO, sysMenu);
        sysMenu.setIsVisible("Y");
        sysMenu.setStatus("A");
        sysMenuMapper.insert(sysMenu);
    }

    public void updateMenu(SysMenuUpdateDTO sysMenuUpdateDTO) {
        if (ObjectUtil.isNotEmpty(sysMenuUpdateDTO.getParentId())) {
            SysMenu sysMenu_Parent = sysMenuMapper.selectById(sysMenuUpdateDTO.getParentId());
            if (sysMenu_Parent == null) {
                throw new ValidateException("父菜单不存在");
            }
        }

        SysMenu sysMenu = sysMenuMapper.selectById(sysMenuUpdateDTO.getId());
        BeanUtil.copyProperties(sysMenuUpdateDTO, sysMenu);
        sysMenuMapper.updateById(sysMenu);
    }

    public void deleteMenu(Long id) {
        sysMenuMapper.deleteById(id);

        List<SysMenu> menus = sysMenuMapper.selectList(
                new LambdaQueryWrapper<SysMenu>().eq(SysMenu::getParentId, id)
        );
        if (CollUtil.isNotEmpty(menus)) {
            for (SysMenu children : menus) {
                deleteMenu(children.getId());
            }
        }

    }

    public List<SysMenuTreeDTO> searchMenu() {
        List<SysMenuTreeDTO> treeList = sysMenuMapper.selectListOrderBySeq();
        treeList = (List<SysMenuTreeDTO>) TreeBuilder.buildListToTree(treeList);
        return treeList;
    }

    public SysMenuDetailDTO getMenuDetail(Long id) {
        SysMenu sysMenu = sysMenuMapper.selectById(id);
        SysMenuDetailDTO sysMenuDetailDTO = new SysMenuDetailDTO();
        BeanUtils.copyProperties(sysMenu, sysMenuDetailDTO);
        return sysMenuDetailDTO;
    }
}
