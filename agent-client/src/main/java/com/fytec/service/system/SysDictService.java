package com.fytec.service.system;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fytec.cache.DictCacheClient;
import com.fytec.dto.system.DictValueDTO;
import com.fytec.entity.system.SysDictValue;
import com.fytec.mapper.system.SysDictValueMapper;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@Transactional
@AllArgsConstructor
public class SysDictService {


    private final SysDictValueMapper valueMapper;


    private final DictCacheClient dictCacheClient;

    public List<DictValueDTO> searchDictValue(String typeCode) {
        return valueMapper.selectByTypeCode(typeCode);
    }

    public Map<String, List<DictValueDTO>> searchMultipleDictValue(String typeCode) {
        List<String> types = splitParam(typeCode);
        if (types == null || types.isEmpty()) {
            return new HashMap<>();
        }

        List<DictValueDTO> result = valueMapper.selectByMultipleTypeCode(types);
        Map<String, List<DictValueDTO>> map = new HashMap<>();
        if (result != null && !result.isEmpty()) {
            for (DictValueDTO d : result) {
                String code = d.getTypeCode();
                List<DictValueDTO> ll = map.computeIfAbsent(code, k -> new ArrayList<>());
                ll.add(d);
            }
        }

        return map;
    }

    public void refreshDictCache() {
        dictCacheClient.deleteAll();
        List<SysDictValue> sysDictValues = valueMapper.selectList(new QueryWrapper<>());
        for (SysDictValue sysDictValue : sysDictValues) {
            dictCacheClient.addDict(sysDictValue.getTypeCode(), sysDictValue.getValue(), sysDictValue.getLabel());
        }
    }

    private List<String> splitParam(String param) {
        if (StringUtils.isNotEmpty(param)) {
            String[] params = param.split(",");
            List<String> levelList = new ArrayList<>();
            Collections.addAll(levelList, params);
            return levelList;
        }

        return null;
    }
}
