package com.fytec.service.system;

import cn.hutool.core.exceptions.ValidateException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.system.*;
import com.fytec.entity.system.SysRole;
import com.fytec.entity.system.SysRoleMenu;
import com.fytec.entity.system.SysUserRole;
import com.fytec.mapper.system.SysRoleMapper;
import com.fytec.mapper.system.SysRoleMenuMapper;
import com.fytec.mapper.system.SysUserRoleMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class SysRoleService {
    private final SysRoleMapper sysRoleMapper;
    private final SysRoleMenuMapper sysRoleMenuMapper;
    private final SysUserRoleMapper sysUserRoleMapper;


    public boolean roleIsExisting(String role) {
        long count = sysRoleMapper.selectCount(
                new LambdaQueryWrapper<SysRole>()
                        .eq(SysRole::getRole, role)
        );
        return count > 0;
    }

    public void addRole(SysRoleCreateDTO sysRoleCreateDTO) {
        if (roleIsExisting(sysRoleCreateDTO.getRole())) {
            throw new ValidateException("角色已存在");
        }

        SysRole sysRole = new SysRole();
        BeanUtils.copyProperties(sysRoleCreateDTO, sysRole);
        sysRole.setIsSys("N");
        sysRoleMapper.insert(sysRole);

        for (Long menuId : sysRoleCreateDTO.getMenuIds()) {
            SysRoleMenu sysRoleMenu = new SysRoleMenu();
            sysRoleMenu.setRoleId(sysRole.getId());
            sysRoleMenu.setMenuId(menuId);
            sysRoleMenuMapper.insert(sysRoleMenu);
        }
    }

    public void updateRole(SysRoleUpdateDTO sysRoleUpdateDTO) {
        SysRole sysRole = sysRoleMapper.selectById(sysRoleUpdateDTO.getId());

        //验证角色ID是否存在
        if (sysRole == null) {
            throw new ValidateException("角色不存在");
        }
        //验证角色是否是系统默认
        if (StringUtils.equals("Y", sysRole.getIsSys())) {
            throw new ValidateException("系统角色不可编辑在");
        }
        //当角色标识改变时，验证角色标识是否重复
        if (!StringUtils.equals(sysRole.getRole(), sysRoleUpdateDTO.getRole())) {
            if (roleIsExisting(sysRoleUpdateDTO.getRole())) {
                throw new ValidateException("角色已存在");
            }
        }
        BeanUtils.copyProperties(sysRoleUpdateDTO, sysRole);
        sysRoleMapper.updateById(sysRole);


        sysRoleMenuMapper.delete(
                new LambdaQueryWrapper<SysRoleMenu>()
                        .eq(SysRoleMenu::getRoleId, sysRoleUpdateDTO.getId())
        );
        for (Long menuId : sysRoleUpdateDTO.getMenuIds()) {
            SysRoleMenu sysRoleMenu = new SysRoleMenu();
            sysRoleMenu.setRoleId(sysRole.getId());
            sysRoleMenu.setMenuId(menuId);
            sysRoleMenuMapper.insert(sysRoleMenu);
        }
    }

    public void deleteRole(Long id) {
        SysRole sysRole = sysRoleMapper.selectById(id);
        //验证角色ID是否存在
        if (sysRole == null) {
            throw new ValidateException("角色不存在");
        }
        //验证角色是否是系统默认
        if (StringUtils.equals("Y", sysRole.getIsSys())) {
            throw new ValidateException("系统角色不可编辑在");
        }

        sysRoleMapper.deleteById(id);
        sysRoleMenuMapper.delete(
                new LambdaQueryWrapper<SysRoleMenu>()
                        .eq(SysRoleMenu::getRoleId, id)
        );
        sysUserRoleMapper.delete(
                new LambdaQueryWrapper<SysUserRole>()
                        .eq(SysUserRole::getRoleId, id)
        );
    }

    public Page<SysRoleListDTO> searchRole(SysRoleSearchDTO sysRoleSearchDTO, Page<SysRoleListDTO> page) {
        //查询角色列表
        List<SysRoleListDTO> result = sysRoleMapper.searchRole(page, sysRoleSearchDTO);
        //给分页添加结果集
        page.setRecords(result);
        return page;
    }

    public SysRoleDetailDTO getRoleDetail(@NotBlank(message = "角色id不能为空") Long id) {
        //获取角色详情
        SysRole sysRole = sysRoleMapper.selectById(id);
        //获取角色下的菜单
        List<SysRoleMenu> roleMenus = sysRoleMenuMapper.selectList(
                new LambdaQueryWrapper<SysRoleMenu>()
                        .eq(SysRoleMenu::getRoleId, id)
                        .select(SysRoleMenu::getMenuId)
        );

        List<Long> menuIds = roleMenus.stream().map(SysRoleMenu::getMenuId).toList();

        //返回结果集
        SysRoleDetailDTO result = new SysRoleDetailDTO();
        BeanUtils.copyProperties(sysRole, result);
        result.setMenuIds(menuIds);
        return result;
    }
}
