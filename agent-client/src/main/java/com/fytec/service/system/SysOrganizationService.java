package com.fytec.service.system;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fytec.dto.system.*;
import com.fytec.entity.system.SysOrganization;
import com.fytec.entity.system.SysUser;
import com.fytec.entity.system.SysUserOrganization;
import com.fytec.mapper.system.SysOrganizationMapper;
import com.fytec.mapper.system.SysUserMapper;
import com.fytec.mapper.system.SysUserOrganizationMapper;
import com.fytec.util.TreeBuilder;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class SysOrganizationService {
    private final SysOrganizationMapper sysOrganizationMapper;
    private final SysUserOrganizationMapper sysUserOrganizationMapper;
    private final SysUserMapper sysUserMapper;

    public boolean existOrganization(long parentId, String name) {
        return sysOrganizationMapper.selectCount(
                new LambdaQueryWrapper<SysOrganization>()
                        .eq(SysOrganization::getName, name)
                        .eq(SysOrganization::getParentId, parentId)
        ) > 0;
    }

    public void addOrganization(SysOrganizationCreateDTO dto) {
        if (existOrganization(dto.getParentId(), dto.getName())) {
            throw new ValidateException("组名已存在");
        }
        SysOrganization group = new SysOrganization();
        BeanUtil.copyProperties(dto, group);
        sysOrganizationMapper.insert(group);
    }

    public void updateOrganization(SysOrganizationUpdateDTO dto) {
        SysOrganization group = sysOrganizationMapper.selectById(dto.getId());
        if (group == null) {
            throw new ValidateException("组不存在");
        }
        if (!StrUtil.equals(group.getName(), dto.getName())) {
            if (existOrganization(dto.getParentId(), dto.getName())) {
                throw new ValidateException("组名已存在");
            }
        }
        BeanUtil.copyProperties(dto, group);
        sysOrganizationMapper.updateById(group);
    }

    public void deleteOrganization(@NotBlank(message = "用户id不能为空") Long id) {
        sysOrganizationMapper.deleteById(id);

        List<SysOrganization> menus = sysOrganizationMapper.selectList(new LambdaQueryWrapper<SysOrganization>().eq(SysOrganization::getParentId, id));
        if (CollUtil.isNotEmpty(menus)) {
            for (SysOrganization children : menus) {
                deleteOrganization(children.getId());
            }
        }
    }

    public SysOrganizationDetailDTO getOrganizationDetail(@NotBlank(message = "用户id不能为空") Long id) {
        SysOrganizationDetailDTO dto = new SysOrganizationDetailDTO();
        SysOrganization group = sysOrganizationMapper.selectById(id);
        BeanUtil.copyProperties(group, dto);
        return dto;
    }

    public List<SysOrganizationTreeDTO> queryOrganization(SysOrganizationQueryDTO dto) {
        List<SysOrganizationTreeDTO> groupList = sysOrganizationMapper.queryOrganization(dto);
        if (dto.isWithUser()) {
            List<SysUser> sysUsers = sysUserMapper.selectList(
                    new LambdaQueryWrapper<SysUser>()
                            .isNotNull(SysUser::getDeptCode)
                            .eq(SysUser::getStatus, "A")
                            .like(StrUtil.isNotBlank(dto.getName()), SysUser::getName, dto.getName())
            );
            List<JSONObject> users = sysUsers.stream().map(sysUser -> {
                        JSONObject user = new JSONObject();
                        user.put("id", sysUser.getId());
                        user.put("name", sysUser.getName());
                        user.put("deptCode", sysUser.getDeptCode());
                        return user;
                    })
                    .toList();

            Map<String, List<JSONObject>> departmentUserMap = users.stream().collect(
                    Collectors.groupingBy(
                            jsonObject -> jsonObject.getString("deptCode"),
                            Collectors.toList()
                    )
            );
            for (SysOrganizationTreeDTO group : groupList) {
                group.setUsers(departmentUserMap.get(group.getCode()));
            }
        }

        groupList = (List<SysOrganizationTreeDTO>) TreeBuilder.buildListToTree(groupList);
        return groupList;
    }

    public List<String> selectOrganizationsByUserId(Long userId) {
        List<String> orgNames = new ArrayList<>();
        List<SysUserOrganization> userGroups = sysUserOrganizationMapper.selectList(
                new LambdaQueryWrapper<SysUserOrganization>()
                        .eq(SysUserOrganization::getUserId, userId)
        );
        for (SysUserOrganization sysUserOrganization : userGroups) {
            long orgId = sysUserOrganization.getOrgId();
            StringBuilder orgName = new StringBuilder();
            appendOrganizationName(orgId, orgName);
            orgNames.add(orgName.toString());
        }
        return orgNames;
    }

    private void appendOrganizationName(long groupId, StringBuilder groupName) {
        SysOrganization sysOrganization = sysOrganizationMapper.selectById(groupId);
        if (sysOrganization == null) {
            return;
        }
        if (sysOrganization.getParentId() != null) {
            appendOrganizationName(sysOrganization.getParentId(), groupName);
            groupName.append(" / ");
        }
        groupName.append(sysOrganization.getName());
    }
}
