package com.fytec.service.application;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.application.*;
import com.fytec.entity.application.ApplicationBasic;
import com.fytec.entity.llm.AiModel;
import com.fytec.mapper.application.ApplicationBasicMapper;
import com.fytec.mapper.model.ModelMapper;
import com.fytec.satoken.StpClientUserUtil;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


@Service
@Transactional
@Slf4j
@RequiredArgsConstructor
public class ApplicationMgmtService {
    private final ApplicationBasicMapper applicationBasicMapper;
    private final ModelMapper modelMapper;
    private final StpClientUserUtil stpClientUserUtil;

    private boolean isApplicationExist(String name) {
        return applicationBasicMapper.selectCount(
                new LambdaQueryWrapper<ApplicationBasic>()
                        .eq(ApplicationBasic::getName, name)
        ) > 0;
    }

    public void addApplication(ApplicationCreateDTO dto) {
        if (isApplicationExist(dto.getName())) {
            throw new ValidateException("应用已存在");
        }
        ApplicationBasic applicationBasic = new ApplicationBasic();
        BeanUtils.copyProperties(dto, applicationBasic);
        applicationBasic.setAgents(JSON.toJSONString(dto.getAgents()));
        applicationBasic.setModels(JSON.toJSONString(dto.getModels()));
        applicationBasicMapper.insert(applicationBasic);
    }

    public void updateApplication(ApplicationUpdateDTO dto) {
        ApplicationBasic applicationBasic = applicationBasicMapper.selectById(dto.getId());
        if (applicationBasic == null) {
            throw new ValidateException("应用不存在");
        }
        if (!StrUtil.equals(dto.getName(), applicationBasic.getName()) && isApplicationExist(dto.getName())) {
            throw new ValidateException("应用已存在");
        }

        BeanUtils.copyProperties(dto, applicationBasic);
        applicationBasic.setAgents(JSON.toJSONString(dto.getAgents()));
        applicationBasic.setModels(JSON.toJSONString(dto.getModels()));
        applicationBasicMapper.updateById(applicationBasic);
    }

    public void deleteApplication(Long id) {
        applicationBasicMapper.deleteById(id);
    }

    public Page<ApplicationBasicDTO> queryApplicationPage(Page<ApplicationBasicDTO> page, ApplicationQueryDTO dto) {
        List<ApplicationBasicDTO> records = applicationBasicMapper.queryApplicationPage(page, dto);
        page.setRecords(records);
        return page;
    }

    @SneakyThrows
    public ApplicationBasicDTO getApplicationDetail(Long id) {
        ApplicationBasicDTO applicationBasicDTO = new ApplicationBasicDTO();
        ApplicationBasic applicationBasic = applicationBasicMapper.selectById(id);
        if (applicationBasic == null) {
            return null;
        }
        BeanUtils.copyProperties(applicationBasic, applicationBasicDTO);
        applicationBasicDTO.setAgents(JSONArray.parseArray(applicationBasic.getAgents(), ApplicationAgentGroupDTO.class));
        List<ApplicationModelDTO> models = JSONArray.parseArray(applicationBasic.getModels(), ApplicationModelDTO.class);
        if (!StpClientUserUtil.hasRole("admin")) {
            if (CollUtil.isNotEmpty(models)) {
                List<Long> modelIds = models.stream().map(ApplicationModelDTO::getId).toList();
                List<AiModel> aiModels = modelMapper.selectByIds(modelIds);
                Set<Long> openModelIds = aiModels.stream().filter(AiModel::isOpen).map(AiModel::getId).collect(Collectors.toSet());
                List<ApplicationModelDTO> filterModels = models.stream().filter(model -> openModelIds.contains(model.getId())).toList();
                applicationBasicDTO.setModels(filterModels);
            }
        } else {
            applicationBasicDTO.setModels(models);
        }

        return applicationBasicDTO;
    }
}
