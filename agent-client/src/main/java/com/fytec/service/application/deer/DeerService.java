package com.fytec.service.application.deer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.fytec.config.ClientProperties;
import com.fytec.config.milvus.MilvusClient;
import com.fytec.dto.application.ChatExecuteDTO;
import com.fytec.entity.agent.AgentPublishHistory;
import com.fytec.entity.knowledge.KnowledgeDoc;
import com.fytec.entity.knowledge.KnowledgeGroup;
import com.fytec.mapper.agent.AgentPublishHistoryMapper;
import com.fytec.service.knowledge.KnowledgeGroupService;
import com.fytec.service.knowledge.KnowledgeService;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class DeerService {
    private final ClientProperties clientProperties;
    private final AgentPublishHistoryMapper agentPublishHistoryMapper;

    private final KnowledgeService knowledgeService;
    private final KnowledgeGroupService knowledgeGroupService;

    public Flux<ServerSentEvent> executeDeerChat(ChatExecuteDTO dto, String clientToken) {
        WebClient webClient = WebClient.create(clientProperties.getDeer().getUrlPrefix());
        AtomicReference<StringBuilder> responseBuilder = new AtomicReference<>(new StringBuilder());
        return webClient.post()
                .uri("/api/chat/stream")
                .header("Authorization", StrUtil.format("Bearer {}", clientToken))
                .header("Content-Type", "application/json")
                .bodyValue(JSON.toJSONString(dto.getChatRequest()))
                .retrieve()
                .bodyToFlux(ServerSentEvent.class)
                .doOnNext(event -> {
                    // 处理每个SSE事件
                    String eventType = event.event();
                    Object data = event.data();
                    if (data != null) {
                        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(data));
                        if (StrUtil.equals("message_chunk", eventType)) {
                            if (StrUtil.equals("coordinator", jsonObject.getString("agent"))) {
                                if (jsonObject.containsKey("content")) {
                                    responseBuilder.get().append(jsonObject.getString("content"));
                                }
                                if (jsonObject.containsKey("finish_reason")
                                        && !StrUtil.equals("stop", jsonObject.getString("finish_reason"))) {
                                    responseBuilder.set(new StringBuilder());
                                }
                            }
                            if (StrUtil.equals("reporter", jsonObject.getString("agent"))) {
                                if (jsonObject.containsKey("content")) {
                                    responseBuilder.get().append(jsonObject.getString("content"));
                                }
                            }
                        }
                    }
                })
                .doOnCancel(() -> {
                    // 前端中止请求时会进入这里
                    log.info("Request was cancelled by client");
                })
                .doOnComplete(() -> {
                    // 使用 Mono.fromRunnable 包装阻塞操作，并指定在弹性调度器上执行
                    Mono.fromRunnable(() -> saveHistory(dto.getHistoryId(), responseBuilder.toString()))
                            .subscribeOn(Schedulers.boundedElastic())
                            .subscribe();
                });
    }


    @Transactional
    public void saveHistory(ChatExecuteDTO dto) {
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(dto.getChatRequest()));
        Long agentPublishId = jsonObject.getLong("agentPublishId");
        Long messageId = agentPublishHistoryMapper.getMaxMessageId(agentPublishId, dto.getUserId());
        messageId = messageId + 1;

        AgentPublishHistory agentPublishHistory = new AgentPublishHistory();
        agentPublishHistory.setConversationId(dto.getConversationId());
        agentPublishHistory.setAgentPublishId(agentPublishId);
        agentPublishHistory.setMessageId(messageId);
        Map<String, Object> extraInfo = new HashMap<>();
        extraInfo.put("deep_research", true);
        extraInfo.put("deep_research_id", jsonObject.getString("thread_id"));
        agentPublishHistory.setExtraInfo(JSON.toJSONString(extraInfo));
        agentPublishHistory.setUserId(dto.getUserId());
        agentPublishHistory.setCreateBy(String.valueOf(dto.getUserId()));
        agentPublishHistory.setUpdateBy(String.valueOf(dto.getUserId()));
        JSONArray messages = jsonObject.getJSONArray("messages");
        if (messages == null) {
            throw new ValidateException("messages is null");
        }
        JSONObject lastMessage = messages.getJSONObject(messages.size() - 1);
        agentPublishHistory.setUserInput(lastMessage.getString("content"));
        agentPublishHistoryMapper.insert(agentPublishHistory);
        dto.setHistoryId(agentPublishHistory.getId());
//        return agentPublishHistory.getId();
    }


    @Transactional
    public void saveHistory(Long id, String answer) {
        AgentPublishHistory agentPublishHistory = agentPublishHistoryMapper.selectById(id);
        agentPublishHistory.setAnswer(answer);
        agentPublishHistoryMapper.updateById(agentPublishHistory);
    }

    @SneakyThrows
    public JSONObject stopDeerChat(String threadId, String clientToken) {
        Map<String, Object> params = new HashMap<>();
        params.put("thread_id", threadId);
        String url = StrUtil.format("{}/api/chat/stop", clientProperties.getDeer().getUrlPrefix());
        HttpResponse<String> response = Unirest.post(url)
                .header("Authorization", StrUtil.format("Bearer {}", clientToken))
                .header("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .body(JSON.toJSONString(params))
                .asString();
        if (response.getStatus() != 200) {
            throw new ValidateException("任务取消失败");
        }
        return JSON.parseObject(response.getBody());
    }

    public void addRagResourceWithFilter(ChatExecuteDTO dto) {
        Map<String, String> knowledgeFilterMap = new HashMap<>();
        // 添加知识库过滤
        if (CollUtil.isNotEmpty(dto.getKnowledgeId())) {
            for (Long knowledgeId : dto.getKnowledgeId()) {
                knowledgeFilterMap.put(knowledgeId.toString(), "");
            }
        }

        // 添加知识库组过滤，知识库id为键
        if (CollUtil.isNotEmpty(dto.getGroupId())) {
            Map<Long, List<Long>> knowledgeToGroupIds = new HashMap<>();
            for (Long groupId : dto.getGroupId()) {
                KnowledgeGroup knowledgeGroup = knowledgeGroupService.getKnowledgeGroup(groupId);
                if (knowledgeGroup != null) { // Add null safety
                    knowledgeToGroupIds
                            .computeIfAbsent(knowledgeGroup.getKnowledgeId(), k -> new ArrayList<>())
                            .add(knowledgeGroup.getId());
                }
            }
            knowledgeToGroupIds.forEach((knowledgeId, groupIds) -> {
                if (!groupIds.isEmpty()) {
                    String groupIdsStr = groupIds.stream()
                            .map(String::valueOf)
                            .collect(Collectors.joining(","));
                    String filterExpression = String.format("%s in [%s]",
                            MilvusClient.GROUP_ID, groupIdsStr);
                    knowledgeFilterMap.put(knowledgeId.toString(), filterExpression);
                }
            });
            log.info("knowledgeFilterMap: {}", knowledgeFilterMap);
        }

        // 添加知识库文档过滤，知识库id为键
        if (CollUtil.isNotEmpty(dto.getFileDocIds())) {
            //根据文档id查询出对应的知识库id，进行分组
            Map<Long, Set<Long>> knowledgeDocMap = new HashMap<>();
            List<KnowledgeDoc> docs = knowledgeService.getKnowledgeDoc(dto.getFileDocIds());
            for (KnowledgeDoc doc : docs) {
                knowledgeDocMap.computeIfAbsent(
                        doc.getKnowledgeId(),
                        k -> new HashSet<>()
                ).add(doc.getId());
            }

            // 添加知识库文档过滤
            for (Long knowledgeId : knowledgeDocMap.keySet()) {
                Set<Long> docIds = knowledgeDocMap.get(knowledgeId);
                if (CollUtil.isNotEmpty(docIds)) {
                    String docIdFilter = StrUtil.format("{} in [{}]", MilvusClient.DOC_ID, StrUtil.join(",", docIds));
                    knowledgeFilterMap.merge(knowledgeId.toString(), docIdFilter, (oldValue, newValue) -> oldValue + " || " + newValue);
                } else {
                    knowledgeFilterMap.putIfAbsent(knowledgeId.toString(), "");
                }
            }
        }
        log.info("知识库过滤条件：{}", knowledgeFilterMap);

        if(CollUtil.isNotEmpty(knowledgeFilterMap)) {
            Map<String, Object> chatRequest = dto.getChatRequest();
            Map<String, Object> resource = new HashMap<>();
            resource.put("knowledge_filter", knowledgeFilterMap);
            chatRequest.put("resources", CollUtil.newArrayList(resource));
        }
    }
}
