package com.fytec.service.application.aiwork;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fytec.config.milvus.MilvusClient;
import com.fytec.constant.Constants;
import com.fytec.dto.agent.AgentExecuteDTO;
import com.fytec.dto.application.aiwork.AiWorkExecuteDTO;
import com.fytec.dto.application.aiwork.AiWorkParseFileDTO;
import com.fytec.dto.application.aiwork.DocParseFileDTO;
import com.fytec.dto.knowledge.KnowledgeDocAutoDTO;
import com.fytec.dto.knowledge.KnowledgeFileDTO;
import com.fytec.dto.speech.SpeechStreamDTO;
import com.fytec.entity.application.AiChatHistory;
import com.fytec.entity.knowledge.KnowledgeDoc;
import com.fytec.entity.knowledge.KnowledgeGroup;
import com.fytec.entity.note.Note;
import com.fytec.mapper.application.AiChatHistoryMapper;
import com.fytec.satoken.StpClientUserUtil;
import com.fytec.service.agent.AgentService;
import com.fytec.service.knowledge.KnowledgeGroupService;
import com.fytec.service.knowledge.KnowledgeService;
import com.fytec.service.note.NoteService;
import com.fytec.service.speech.SpeechProcessService;
import com.fytec.utils.DocUtil;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class AiWorkService {

    private final AgentService agentService;

    private final AiChatHistoryMapper aiChatHistoryMapper;

    private final KnowledgeService knowledgeService;

    private final KnowledgeGroupService knowledgeGroupService;

    private final NoteService noteService;

    private final SpeechProcessService speechProcessService;

    public void execute(AiWorkExecuteDTO dto, HttpServletResponse response) {
        if (!validationFileSize(dto)) {
            throw new ValidateException("文件大小超过限制");
        }

        AgentExecuteDTO executeDTO = new AgentExecuteDTO();
        executeDTO.setModelId(dto.getModelId());
        executeDTO.setConversationId(dto.getConversationId());
        executeDTO.setAgentPublishId(dto.getAgentPublishId());
        executeDTO.setMessageId(dto.getMessageId());
        executeDTO.setUserInput(dto.getUserInput());
        executeDTO.setEnablePluginSearch(dto.isEnablePluginSearch());
        executeDTO.setEnableCitation(dto.isEnableCitation());
        if (dto.isEnablePluginKnowledge()) {
            Map<Long, String> knowledgeFilterMap = new HashMap<>();
            if (CollUtil.isEmpty(dto.getKnowledgeId())) {
                Map<String, Long> initResult = knowledgeGroupService.initKnowledgeGroup();
                executeDTO.setKnowledgeIds(CollUtil.newArrayList(initResult.get("knowledgeId")));
                knowledgeFilterMap.put(initResult.get("knowledgeId"), "");
            } else {
                executeDTO.setKnowledgeIds(dto.getKnowledgeId());
                for (Long knowledgeId : dto.getKnowledgeId()) {
                    knowledgeFilterMap.put(knowledgeId, "");
                }
            }

            if (CollUtil.isNotEmpty(dto.getGroupId())) {
                Map<Long, Set<Long>> knowledgeToGroupIds = new HashMap<>();
                for (Long groupId : dto.getGroupId()) {
                    KnowledgeGroup knowledgeGroup = knowledgeGroupService.getKnowledgeGroup(groupId);
                    if (knowledgeGroup == null) {
                        throw new ValidateException("知识库组不存在");
                    }

                    List<Long> groups = knowledgeGroupService.getGroupIdWithChildrenIterative(groupId);

                    // Add null safety
                    knowledgeToGroupIds
                            .computeIfAbsent(knowledgeGroup.getKnowledgeId(), k -> new HashSet<>())
                            .addAll(groups);
                }

                knowledgeToGroupIds.forEach((knowledgeId, groupIds) -> {
                    if (!groupIds.isEmpty()) {
                        String groupIdsStr = groupIds.stream()
                                .map(String::valueOf)
                                .collect(Collectors.joining(","));
                        String filterExpression = String.format("%s in [%s]",
                                MilvusClient.GROUP_ID, groupIdsStr);
                        knowledgeFilterMap.put(knowledgeId, filterExpression);
                    }
                });
            }

            log.info("knowledgeFilterMap: {}", knowledgeFilterMap);
            executeDTO.setKnowledgefilterMap(knowledgeFilterMap);
        }

        if (CollUtil.isNotEmpty(dto.getFileDocIds())) {
            executeDTO.setFileDocIds(dto.getFileDocIds());
        }

        if (ObjectUtil.isNotEmpty(dto.getNoteId())) {
            StringBuilder sb = new StringBuilder();
            Note note = noteService.getNoteDetail(dto.getNoteId());
            sb.append("\n\n");
            sb.append("## 笔记知识参考内容\n");

            String content = Jsoup.parse(note.getContent()).text();
            int maxLen = Math.min(40000, content.length());
            String subContent = content.substring(0, maxLen);
            sb.append(subContent);
            executeDTO.setAppendContent(sb.toString());
        }

        //保存会话历史
        saveChatHistory(dto.getApplicationId(), dto.getApplicationRelatedFunction(),
                dto.getConversationId(), dto.getUserInput());
        agentService.executePublishedAgent(response, executeDTO);
    }

    private boolean validationFileSize(AiWorkExecuteDTO dto) {
        JSONObject jsonObject = JSON.parseObject(dto.getApplicationRelatedFunction());
        String refType = jsonObject.getString("refType");
        if (!StrUtil.equals(refType, "chat") && !StrUtil.equals(refType, "writing")) {
            return true;
        }

        long size = 0;
        List<Long> docIds = new ArrayList<>();
        if (CollUtil.isNotEmpty(dto.getFileDocIds())) {
            docIds.addAll(dto.getFileDocIds());
        }
        if (CollUtil.isNotEmpty(dto.getFileImageIds())) {
            docIds.addAll(dto.getFileImageIds());
        }

        //文件为空，直接返回true
        if (CollUtil.isEmpty(docIds)) {
            return true;
        }
        //文件数量限制，总共不超过10个文件
        if (docIds.size() > 10) {
            return false;
        }

        //文件大小限制，文件不超过50M
        List<KnowledgeDoc> docs = knowledgeService.getKnowledgeDoc(docIds);
        for (KnowledgeDoc doc : docs) {
            size += doc.getFileSize();
        }
        return size <= 1024 * 1024 * 50;
    }


    private void saveChatHistory(Long applicationId, String applicationRelatedFunction,
                                 String conversationId, String userInput) {
        //查询是否存在会话历史
        AiChatHistory history = aiChatHistoryMapper.selectOne(new LambdaQueryWrapper<AiChatHistory>()
                .eq(AiChatHistory::getApplicationId, applicationId)
                .eq(AiChatHistory::getConversationId, conversationId)
                .eq(AiChatHistory::getCreateBy, StpClientUserUtil.getLoginIdAsLong())
                .last("limit 1")
        );
        if (history != null) {
            aiChatHistoryMapper.updateById(history);
            return;
        }
        history = new AiChatHistory();
        history.setConversationId(conversationId);
        history.setApplicationId(applicationId);
        history.setApplicationRelatedFunction(applicationRelatedFunction);
        history.setConversationTitle(userInput.substring(0, Math.min(userInput.length(), 100)));
        history.setCreateBy(StpClientUserUtil.getLoginIdAsString());
        history.setCreateTime(LocalDateTime.now());
        aiChatHistoryMapper.insert(history);
    }

    public KnowledgeDocAutoDTO uploadFileToKnowledge(AiWorkParseFileDTO dto) {
        if (CollUtil.isNotEmpty(dto.getFiles())) {
            List<KnowledgeFileDTO> files = new ArrayList<>();
            Map<String, Long> initResult = knowledgeGroupService.initKnowledgeGroup();
            for (DocParseFileDTO file : dto.getFiles()) {
                KnowledgeFileDTO knowledgeFile = new KnowledgeFileDTO();
                String extName = file.getFileName().substring(file.getFileName().lastIndexOf(".") + 1).toLowerCase();
                Constants.KNOWLEDGE_DOC_TYPE docType = DocUtil.convertKnowledgeDocType(extName);
                if (docType == null) {
                    continue;
                }
                knowledgeFile.setDocType(docType.name());
                knowledgeFile.setFileName(file.getFileName());
                knowledgeFile.setFileType(extName);
                knowledgeFile.setFileUrl(file.getFileUrl());
                knowledgeFile.setFileSize(file.getFileSize());
                knowledgeFile.setVisible(file.isVisible());
                knowledgeFile.setGroupId(file.getGroupId() == null ? initResult.get("defaultGroupId") : file.getGroupId());
                files.add(knowledgeFile);
            }
            KnowledgeDocAutoDTO knowledgeDocAutoDTO = new KnowledgeDocAutoDTO();
            knowledgeDocAutoDTO.setKnowledgeId(dto.getKnowledgeId() == null ? initResult.get("knowledgeId") : dto.getKnowledgeId());
            knowledgeDocAutoDTO.setDocSourceType(Constants.KNOWLEDGE_DOC_SOURCE_TYPE.upload.name());
            knowledgeDocAutoDTO.setFiles(files);
            knowledgeDocAutoDTO.setUserId(StpClientUserUtil.getLoginIdAsString());
            knowledgeService.autoProcessData(knowledgeDocAutoDTO);
            return knowledgeDocAutoDTO;
        }
        return null;
    }

    /**
     * 语音识别
     *
     * @param url
     */
    @SneakyThrows
    public String speechRecognition(String url) {
        SpeechStreamDTO speechStreamDTO = new SpeechStreamDTO();
        speechStreamDTO.setUrl(url);
        return speechProcessService.processOneSentence(speechStreamDTO);
    }
}
