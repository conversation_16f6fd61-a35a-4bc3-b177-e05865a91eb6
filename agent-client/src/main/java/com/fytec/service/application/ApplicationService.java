package com.fytec.service.application;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fytec.dto.application.ChatExecuteDTO;
import com.fytec.dto.application.ChatHistoryQueryDTO;
import com.fytec.dto.application.ChatHistoryUpdateDTO;
import com.fytec.entity.agent.AgentPublishHistory;
import com.fytec.entity.application.AiChatHistory;
import com.fytec.mapper.agent.AgentPublishHistoryMapper;
import com.fytec.mapper.application.AiChatHistoryMapper;
import com.fytec.satoken.StpClientUserUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;


@Service
@Transactional
@Slf4j
@RequiredArgsConstructor
public class ApplicationService {
    private final AiChatHistoryMapper aiChatHistoryMapper;
    private final AgentPublishHistoryMapper agentPublishHistoryMapper;

    public Long executeChat(ChatExecuteDTO dto) {
        AiChatHistory history = new AiChatHistory();
        BeanUtil.copyProperties(dto, history);
        String conversationTitle = dto.getConversationTitle().substring(0, Math.min(history.getConversationTitle().length(), 100));
        history.setConversationTitle(conversationTitle);
        aiChatHistoryMapper.insert(history);
        return history.getId();
    }

    public void deleteHistory(Long id) {
        AiChatHistory aiChatHistory = aiChatHistoryMapper.selectById(id);
        if (aiChatHistory == null) {
            throw new ValidateException("历史记录不存在");
        }
        aiChatHistoryMapper.deleteById(id);
        agentPublishHistoryMapper.delete(
                new LambdaQueryWrapper<AgentPublishHistory>()
                        .eq(AgentPublishHistory::getConversationId, aiChatHistory.getConversationId())
        );
    }

    public void editHistory(ChatHistoryUpdateDTO dto) {
        AiChatHistory aiChatHistory = aiChatHistoryMapper.selectById(dto.getId());
        aiChatHistory.setConversationTitle(dto.getConversationTitle());
        aiChatHistoryMapper.updateById(aiChatHistory);
    }

    public List<Map<String, Object>> queryHistory(ChatHistoryQueryDTO dto) {
        LambdaQueryWrapper<AiChatHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(AiChatHistory::getApplicationId, dto.getApplicationId())
                .eq(AiChatHistory::getCreateBy, StpClientUserUtil.getLoginIdAsLong())
                .orderByDesc(AiChatHistory::getUpdateTime);
        if (StrUtil.isNotBlank(dto.getConversationTitle())) {
            queryWrapper.like(AiChatHistory::getConversationTitle, dto.getConversationTitle());
        }
        if (dto.getAgentId() != null) {
            queryWrapper.eq(AiChatHistory::getAgentId, dto.getAgentId());
        }
        List<AiChatHistory> aiChatHistories = aiChatHistoryMapper.selectList(queryWrapper);

        Map<String, List<AiChatHistory>> groupMap = groupByType(aiChatHistories, dto.getGroupType());

        List<Map<String, Object>> results = new ArrayList<>();
        for (String key : groupMap.keySet()) {
            Map<String, Object> result = new HashMap<>();
            List<AiChatHistory> histories = groupMap.get(key);
            result.put("aiChatHistoriesDate", key);
            result.put("aiChatHistories", histories);
            results.add(result);
        }
        return results;
    }

    private Map<String, List<AiChatHistory>> groupByType(List<AiChatHistory> aiChatHistories, String groupType) {
        if (StrUtil.isBlank(groupType)) {
            groupType = "other";
        }
        switch (groupType) {
            case "date":
                return groupByDate(aiChatHistories);
            default:
                return groupByOther(aiChatHistories);
        }
    }

    private Map<String, List<AiChatHistory>> groupByDate(List<AiChatHistory> aiChatHistories) {
        Map<String, List<AiChatHistory>> groupMap = new LinkedHashMap<>();
        for (AiChatHistory aiChatHistory : aiChatHistories) {
            //格式化为yyyy年MM月dd日
            String date = aiChatHistory.getCreateTime().toLocalDate().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"));
            groupMap.computeIfAbsent(date, k -> new ArrayList<>()).add(aiChatHistory);
        }
        return groupMap;
    }

    private Map<String, List<AiChatHistory>> groupByOther(List<AiChatHistory> aiChatHistories) {
        Map<String, List<AiChatHistory>> groupMap = new LinkedHashMap<>();
        groupMap.put("今天", new ArrayList<>());
        groupMap.put("昨天", new ArrayList<>());
        groupMap.put("7天内", new ArrayList<>());
        groupMap.put("更久", new ArrayList<>());

        LocalDate today = LocalDate.now();
        LocalDate yesterday = today.minusDays(1);
        LocalDate sevenDaysAgo = today.minusDays(7);

        for (AiChatHistory aiChatHistory : aiChatHistories) {
            LocalDate date = aiChatHistory.getCreateTime().toLocalDate();
            if (date.isEqual(today)) {
                groupMap.get("今天").add(aiChatHistory);
            } else if (date.isEqual(yesterday)) {
                groupMap.get("昨天").add(aiChatHistory);
            } else if (date.isAfter(sevenDaysAgo)) {
                groupMap.get("7天内").add(aiChatHistory);
            } else {
                groupMap.get("更久").add(aiChatHistory);
            }
        }
        return groupMap;
    }


    public AiChatHistory getHistoryDetail(Long id) {
        return aiChatHistoryMapper.selectById(id);
    }
}
