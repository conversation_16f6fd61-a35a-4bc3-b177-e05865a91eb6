package com.fytec.schedule.oa.user;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fytec.config.SchedulerProperties;
import com.fytec.entity.system.SysOrganization;
import com.fytec.entity.system.SysUser;
import com.fytec.mapper.system.SysOrganizationMapper;
import com.fytec.service.system.SysUserService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年12月26日 15:44
 */
@Component
@RequiredArgsConstructor
public class OaUserSyncScheduled {

    private static final String HR_USER_URL = "https://fykj.fengyuntec.com/api/organization/employee4mf?token=e010b016-9beb-470a-abbb-38ee9614eb2c&includeDimission=N";
    private static final Logger log = LoggerFactory.getLogger(OaUserSyncScheduled.class);
    private final SysUserService userService;
    private final SchedulerProperties schedulerProperties;
    private final SysOrganizationMapper organizationMapper;

    @Scheduled(cron = "1 1 2 * * ? ")
//    @Scheduled(fixedDelay = 1000 * 60 * 60 * 24)
    @Transactional
    public void sysUserFromHrTask() {
        Boolean scheduleRun = schedulerProperties.getOa().getEnabledSync();
        log.info("==================scheduleRun: {}", scheduleRun);
        if (scheduleRun != null && !scheduleRun) {
            return;
        }

        String result;
        HttpGet httpGet = new HttpGet(HR_USER_URL);
        httpGet.setHeader("Content-type", "application/json");
        HttpResponse response;
        try (CloseableHttpClient httpclient = HttpClients.createDefault();) {
            response = httpclient.execute(httpGet);
            if (response.getEntity() == null) {
                return;
            }
            result = EntityUtils.toString(response.getEntity());
        } catch (IOException e) {
            return;
        }

        try {
            OaUserResultWrapper hr = JSON.parseObject(result, OaUserResultWrapper.class);
            String code = hr.getCode();
            if (!"200".equalsIgnoreCase(code)) {
                return;
            }

            // 同步前把组织同步状态改为false
            organizationMapper.update(
                    new LambdaUpdateWrapper<SysOrganization>()
                            .set(SysOrganization::isSync, false)
                            .eq(SysOrganization::getSourceFrom, "FYTEC_OA")
            );

            OaDepartment department = hr.getResult();
            List<OaUser> employeeList = new ArrayList<>();
            //同步组织及用户，把同步到的以及新增的组织同步状态改为true
            processChildrenDeptAndEmployee(department, null, employeeList);

            //删除未同步到的组织
            organizationMapper.delete(
                    new LambdaQueryWrapper<SysOrganization>()
                            .eq(SysOrganization::isSync, false)
                            .eq(SysOrganization::getSourceFrom, "FYTEC_OA")
            );

            List<SysUser> users = userService.fetchUsersSyncFromOA();
            List<String> userThirdIds = users.stream().map(SysUser::getThirdUserId).toList();
            List<String> oaUserIds = employeeList.stream().map(OaUser::getId).toList();
            //需要添加User
            for (OaUser oaUser : employeeList) {
                String oaUserId = oaUser.getId();
                if (!userThirdIds.contains(oaUserId)) {
                    userService.addOaUser(oaUser);
                }
            }
            for (SysUser user : users) {
                String userThirdId = user.getThirdUserId();
                if (!oaUserIds.contains(userThirdId)) {
                    userService.deleteUser(user.getId());
                }
            }
        } catch (Exception e) {
            log.error("同步OA用户失败", e);
        }
    }

    public void processChildrenDeptAndEmployee(OaDepartment department, String parentCode,
                                               List<OaUser> employeeList) {
        SysOrganization sysOrganization = organizationMapper.selectOne(
                new LambdaQueryWrapper<SysOrganization>()
                        .eq(SysOrganization::getCode, department.getCode())
                        .eq(SysOrganization::getSourceFrom, "FYTEC_OA")
                        .last("limit 1")
        );
        if (sysOrganization == null) {
            sysOrganization = new SysOrganization();
            if (StrUtil.equals(department.getCode(), "A00")) {
                sysOrganization.setName("江苏风云科技服务有限公司");
            } else {
                sysOrganization.setName(department.getName());
            }
            sysOrganization.setCode(department.getCode());
            if (StrUtil.isNotEmpty(parentCode)) {
                SysOrganization parentOrganization = organizationMapper.selectOne(
                        new LambdaQueryWrapper<SysOrganization>()
                                .eq(SysOrganization::getCode, parentCode)
                                .eq(SysOrganization::getSourceFrom, "FYTEC_OA")
                                .last("limit 1")
                );
                if (parentOrganization != null) {
                    sysOrganization.setParentId(parentOrganization.getId());
                }
            }

            sysOrganization.setSourceFrom("FYTEC_OA");
            sysOrganization.setSync(true);
            sysOrganization.setSyncTime(LocalDateTime.now());
            sysOrganization.setCreateBy("99");
            sysOrganization.setUpdateBy("99");
            organizationMapper.insert(sysOrganization);
        } else {
            sysOrganization.setSync(true);
            sysOrganization.setSyncTime(LocalDateTime.now());
            sysOrganization.setUpdateBy("99");
            organizationMapper.updateById(sysOrganization);
        }

        List<OaUser> oaUsers = department.getEmployee();
        if (CollectionUtils.isNotEmpty(oaUsers)) {
            for (OaUser oaUser : oaUsers) {
                oaUser.setDeptCode(department.getCode());
                employeeList.add(oaUser);
            }
        }
        if (CollectionUtils.isNotEmpty(department.getChilren())) {
            for (int i = 0; i < department.getChilren().size(); i++) {
                processChildrenDeptAndEmployee(department.getChilren().get(i), department.getCode(), employeeList);
            }
        }
    }
}
