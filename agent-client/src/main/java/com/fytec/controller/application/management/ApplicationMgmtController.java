package com.fytec.controller.application.management;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.application.ApplicationBasicDTO;
import com.fytec.dto.application.ApplicationCreateDTO;
import com.fytec.dto.application.ApplicationQueryDTO;
import com.fytec.dto.application.ApplicationUpdateDTO;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.application.ApplicationMgmtService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Validated
@Tag(name = "应用管理")
@RequestMapping("/api/application/mgmt")
@RestController
@RequiredArgsConstructor
public class ApplicationMgmtController {
    private final ApplicationMgmtService applicationMgmtService;

    @PostMapping(value = "/basic/add")
    @Operation(summary = "添加应用")
    @SaCheckPermission4FytecClient(value = "application-mgmt:add", orRole = "admin")
    public R<Void> addApplication(@Validated @RequestBody ApplicationCreateDTO dto) {
        applicationMgmtService.addApplication(dto);
        return R.ok();
    }

    @PostMapping(value = "/basic/update")
    @Operation(summary = "修改应用")
    @SaCheckPermission4FytecClient(value = "application-mgmt:update", orRole = "admin")
    public R<Void> updateApplication(@Validated @RequestBody ApplicationUpdateDTO dto) {
        applicationMgmtService.updateApplication(dto);
        return R.ok();
    }

    @GetMapping(value = "/basic/delete")
    @Operation(summary = "删除应用")
    @SaCheckPermission4FytecClient(value = "application-mgmt:delete", orRole = "admin")
    public R<Void> deleteApplication(Long id) {
        applicationMgmtService.deleteApplication(id);
        return R.ok();
    }

    @GetMapping(value = "/basic/page")
    @Operation(summary = "查询应用列表")
    @SaCheckPermission4FytecClient(value = "application-mgmt:page", orRole = "admin")
    public R<Page<ApplicationBasicDTO>> queryApplicationPage(ApplicationQueryDTO dto, Page<ApplicationBasicDTO> page) {
        return R.ok(applicationMgmtService.queryApplicationPage(page, dto));
    }

    @GetMapping(value = "/basic/detail")
    @Operation(summary = "应用详情")
    @SaCheckPermission4FytecClient(value = "application-mgmt:detail", orRole = "admin")
    public R<ApplicationBasicDTO> getApplicationDetail(Long id) {
        return R.ok(applicationMgmtService.getApplicationDetail(id));
    }
}
