package com.fytec.controller.application.aiwork;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.knowledge.KnowledgeDocAutoDTO;
import com.fytec.dto.note.NoteDTO;
import com.fytec.entity.note.Note;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.knowledge.KnowledgeService;
import com.fytec.service.note.NoteService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Validated
@Tag(name = "笔记")
@RequestMapping("/api/note")
@RestController
@RequiredArgsConstructor
public class NoteController {
    private final NoteService noteService;
    private final KnowledgeService knowledgeService;

    @PostMapping(value = "/save")
    @Operation(summary = "保存笔记")
    @SaCheckPermission4FytecClient(value = "note:save", orRole = "admin")
    public R<Long> saveNote(@Validated @RequestBody NoteDTO dto) {
        return R.ok(noteService.saveNote(dto));
    }

    @GetMapping(value = "/delete")
    @Operation(summary = "删除笔记")
    @SaCheckPermission4FytecClient(value = "note:delete", orRole = "admin")
    public R<Void> deleteNote(Long id) {
        noteService.deleteNote(id);
        return R.ok();
    }

    @GetMapping(value = "/detail")
    @Operation(summary = "笔记详情")
    @SaCheckPermission4FytecClient(value = "note:detail", orRole = "admin")
    public R<Note> getNoteDetail(Long id) {
        return R.ok(noteService.getNoteDetail(id));
    }

    @GetMapping(value = "/list")
    @Operation(summary = "笔记列表")
    @SaCheckPermission4FytecClient(value = "note:list", orRole = "admin")
    public R<List<Map<String, Object>>> queryNoteList(NoteDTO dto) {
        return R.ok(noteService.queryNoteList(dto));
    }

    @GetMapping(value = "/page")
    @Operation(summary = "笔记列表")
    @SaCheckPermission4FytecClient(value = "note:list", orRole = "admin")
    public R<Page<Note>> queryNotePage(NoteDTO dto, Page<Note> page) {
        return R.ok(noteService.queryNotePage(dto, page));
    }

    @SneakyThrows
    @GetMapping(value = "/addKnowledge")
    @Operation(summary = "笔记保存在知识库")
    @SaCheckPermission4FytecClient(value = "note:addKnowledge", orRole = "admin")
    public R<Long> addKnowledge(Long id) {
        KnowledgeDocAutoDTO dto = noteService.addKnowledge(id);
        CompletableFuture<Long> knowledgeDocId = knowledgeService.autoProcessDoc(dto.getFiles().getFirst(), dto);
        noteService.updateNoteKnowledgeId(id, knowledgeDocId.get());
        return R.ok(knowledgeDocId.get());
    }


    @GetMapping(value = "/removeKnowledge")
    @Operation(summary = "笔记保存在知识库")
    @SaCheckPermission4FytecClient(value = "note:removeKnowledge", orRole = "admin")
    public R<Void> removeKnowledge(Long id) {
        noteService.removeKnowledge(id);
        return R.ok();
    }
}
