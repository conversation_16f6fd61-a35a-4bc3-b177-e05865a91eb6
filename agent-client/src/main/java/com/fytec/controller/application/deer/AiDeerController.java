package com.fytec.controller.application.deer;

import com.alibaba.fastjson2.JSONObject;
import com.fytec.dto.application.ChatExecuteDTO;
import com.fytec.satoken.StpClientUserUtil;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.application.deer.DeerService;
import com.fytec.token.ClientTokenService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.util.Map;

@Slf4j
@Validated
@Tag(name = "Ai对话")
@RequestMapping("/api/application/chat")
@RestController
@RequiredArgsConstructor
public class AiDeerController {
    private final DeerService deerService;
    private final ClientTokenService clientTokenService;

    @PostMapping(value = "/dp/stream")
    @Operation(summary = "深度研究")
    @SaCheckPermission4FytecClient(value = "application:chat:execute", orRole = "admin")
    public Flux<ServerSentEvent> executeDeerChat(@Validated @RequestBody ChatExecuteDTO dto) {
        //获取token
        String clientToken = clientTokenService.getToken4Python();
        dto.setUserId(StpClientUserUtil.getLoginIdAsLong());
        //创建对话历史，方便后续更新
        deerService.saveHistory(dto);
        //rag资源处理
        deerService.addRagResourceWithFilter(dto);
        //执行深度研究
        return deerService.executeDeerChat(dto, clientToken);
    }

    @GetMapping(value = "/dp/stop")
    @Operation(summary = "暂停深度研究")
    @SaCheckPermission4FytecClient(value = "application:chat:execute", orRole = "admin")
    public R<JSONObject> stopDeerChat(String threadId) {
        String clientToken = clientTokenService.getToken4Python();
        return R.ok(deerService.stopDeerChat(threadId, clientToken));
    }

}
