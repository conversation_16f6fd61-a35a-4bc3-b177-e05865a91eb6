package com.fytec.controller.application.chat;

import com.fytec.dto.application.*;
import com.fytec.entity.application.AiChatHistory;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.application.ApplicationService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@Validated
@Tag(name = "Ai对话")
@RequestMapping("/api/application/chat")
@RestController
@RequiredArgsConstructor
public class AiChatController {
    private final ApplicationService applicationService;

    @PostMapping(value = "/execute")
    @Operation(summary = "发起会话")
    @SaCheckPermission4FytecClient(value = "application:chat:execute", orRole = "admin")
    public R<Long> executeChat(@Validated @RequestBody ChatExecuteDTO dto) {
        return R.ok(applicationService.executeChat(dto));
    }

    @GetMapping(value = "/history/detail")
    @Operation(summary = "会话详情")
    @SaCheckPermission4FytecClient(value = "application:history:detail", orRole = "admin")
    public R<AiChatHistory> getHistoryDetail(Long id) {
        return R.ok(applicationService.getHistoryDetail(id));
    }


    @GetMapping(value = "/history/list")
    @Operation(summary = "会话列表")
    @SaCheckPermission4FytecClient(value = "application:history:list", orRole = "admin")
    public R<List<Map<String, Object>>> queryHistory(ChatHistoryQueryDTO dto) {
        return R.ok(applicationService.queryHistory(dto));
    }


    @PostMapping(value = "/history/edit")
    @Operation(summary = "会话编辑")
    @SaCheckPermission4FytecClient(value = "application:history:edit", orRole = "admin")
    public R<Void> editHistory(@Validated @RequestBody ChatHistoryUpdateDTO dto) {
        applicationService.editHistory(dto);
        return R.ok();
    }

    @GetMapping(value = "/history/delete")
    @Operation(summary = "会话删除")
    @SaCheckPermission4FytecClient(value = "application:history:delete", orRole = "admin")
    public R<Void> deleteHistory(Long id) {
        applicationService.deleteHistory(id);
        return R.ok();
    }
}
