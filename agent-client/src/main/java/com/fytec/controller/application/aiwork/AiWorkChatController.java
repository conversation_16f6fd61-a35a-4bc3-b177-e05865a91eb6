package com.fytec.controller.application.aiwork;


import com.fytec.dto.application.aiwork.AiWorkExecuteDTO;
import com.fytec.dto.application.aiwork.AiWorkParseFileDTO;
import com.fytec.dto.knowledge.KnowledgeDocAutoDTO;
import com.fytec.dto.knowledge.KnowledgeFileDTO;
import com.fytec.satoken.StpClientUserUtil;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.application.aiwork.AiWorkService;
import com.fytec.service.knowledge.KnowledgeWithoutTranService;
import com.fytec.util.FileUtil;
import com.fytec.util.MinioUtil;
import com.fytec.util.R;
import com.fytec.util.UUIDGenerator;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Validated
@Tag(name = "SIP Ai Work")
@RequestMapping("/api/ai-work/chat")
@RestController
@RequiredArgsConstructor
public class AiWorkChatController {

    private final AiWorkService aiWorkService;
    private final KnowledgeWithoutTranService knowledgeService;

    private final MinioUtil minioUtil;

    @PostMapping(value = "/execute")
    @Operation(summary = "执行问答")
    @SaCheckPermission4FytecClient(value = "ai_work:chat:execute", orRole = "admin")
    public void execute(HttpServletResponse response, @Validated @RequestBody AiWorkExecuteDTO dto) {
        aiWorkService.execute(dto, response);
    }


    @SneakyThrows
    @PostMapping("/file")
    @Operation(summary = "上传")
    @SaCheckPermission4FytecClient(value = "ai_work:file:upload", orRole = "admin")
    public R<?> uploadFileToKnowledge(@RequestBody AiWorkParseFileDTO dto) {
        KnowledgeDocAutoDTO knowledgeDocAutoDTO = aiWorkService.uploadFileToKnowledge(dto);
        knowledgeDocAutoDTO.setUserId(StpClientUserUtil.getLoginIdAsString());

        List<Long> docIds = new ArrayList<>();
        for (KnowledgeFileDTO file : knowledgeDocAutoDTO.getFiles()) {
            docIds.add(file.getDoc().getId());
            knowledgeService.autoProcessDocV2(file, knowledgeDocAutoDTO);
        }
        return R.ok(docIds);
    }

    @SneakyThrows
    @PostMapping("/speech/recognition")
    @Operation(summary = "语音识别")
    @SaCheckPermission4FytecClient(value = "ai_work:speech:recognition", orRole = {"admin", "manage", "normal"})
    public R<?> speechRecognition(@RequestParam(name = "audio") MultipartFile multipartFile) {
        String fileName = UUIDGenerator.getUUID() + FileUtil.getFileEndfix(multipartFile.getOriginalFilename());
        minioUtil.upload(multipartFile, fileName);
        String url = minioUtil.getUrl(fileName);
        String data = aiWorkService.speechRecognition(url);
        return R.ok(data);
    }
}
