package com.fytec.controller.login;

import cn.dev33.satoken.stp.SaTokenInfo;
import cn.hutool.core.exceptions.ValidateException;
import com.fytec.dto.LoginDTO;
import com.fytec.service.LoginService;
import com.fytec.util.CaptchaGenerator;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.concurrent.TimeUnit;

@Slf4j
@Validated
@Tag(name = "登录")
@RequestMapping("/api")
@RestController
@RequiredArgsConstructor
public class LoginController {
    @Value("${env.loginType}")
    private String loginType;

    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/CBC/PKCS5Padding";
    private final static String CAPTCHA_KEY = "captcha_";
    private final LoginService loginService;
    private final RedisTemplate<String, String> redisTemplate;

    public static void main(String[] args) {
        String encode = Base64.getEncoder().encodeToString("7890@uiop".getBytes(StandardCharsets.UTF_8));
        System.out.println("转换后" + encode);
        // 使用 Base64 解码器将 Base64 字符串解码为字节数组
        byte[] decodedBytes = Base64.getDecoder().decode(encode);
        // 将解码后的字节数组转换为字符串
        String password = new String(decodedBytes, StandardCharsets.UTF_8);
        System.out.println("转换后" + password);
    }

    @PostMapping("/login")
    @Operation(summary = "登录")
    public R<SaTokenInfo> login(@Validated @RequestBody LoginDTO loginDTO) {
        SaTokenInfo saTokenInfo;
        if(StringUtils.isBlank(loginDTO.getLoginType())){
            loginDTO.setLoginType(loginType);
        }
        if (loginDTO.getLoginType().equals("oa")) {
            saTokenInfo = loginService.generateLoginAccessTokenByOa(loginDTO);
        } else {
            saTokenInfo = loginService.generateLoginAccessToken(loginDTO);
        }

        return R.ok(saTokenInfo);
    }

    @PostMapping("/login/captcha")
    @Operation(summary = "图形验证码登录")
    public R<SaTokenInfo> loginCaptcha(@Validated @RequestBody LoginDTO loginDTO) {
        if(StringUtils.isBlank(loginDTO.getLoginType())){
            loginDTO.setLoginType(loginType);
        }
        if (StringUtils.isBlank(loginDTO.getCaptchaCode()) ||
                StringUtils.isBlank(loginDTO.getCaptchaCodeId())) {
            throw new ValidateException("图形验证码不能为空");
        }
        //图形验证码验证
        String captchaCode = redisTemplate.opsForValue().get(CAPTCHA_KEY + loginDTO.getCaptchaCodeId());
        if (StringUtils.isBlank(captchaCode)) {
            throw new ValidateException("图形验证码不存在");
        }
        if (!captchaCode.equalsIgnoreCase(loginDTO.getCaptchaCode())) {
            throw new ValidateException("图形验证码错误");
        }
        //密码解密
        //Base64.getEncoder().encodeToString(loginDTO.getPassword().getBytes(StandardCharsets.UTF_8))
        // 使用 Base64 解码器将 Base64 字符串解码为字节数组
        byte[] decodedBytes = Base64.getDecoder().decode(loginDTO.getPassword());
        // 将解码后的字节数组转换为字符串
        String password = new String(decodedBytes, StandardCharsets.UTF_8);
        loginDTO.setPassword(password);

        SaTokenInfo saTokenInfo;
//        if (loginDTO.getLoginType().equals("oa")) {
//            saTokenInfo = loginService.generateLoginAccessTokenByOa(loginDTO);
//        } else {
//            saTokenInfo = loginService.generateLoginAccessToken(loginDTO);
//        }
        saTokenInfo = loginService.generateLoginAccessToken(loginDTO);


        redisTemplate.delete(CAPTCHA_KEY + loginDTO.getCaptchaCodeId());
        return R.ok(saTokenInfo);
    }

    @GetMapping("/captcha/graph")
    @Operation(summary = "图形验证码生成")
    public R<?> generateCaptcha(String id) throws Exception {
        //生成图形验证码
        String captchaCode = CaptchaGenerator.generateCaptchaCode(4);
        log.info("图形验证码为：" + captchaCode);
        //Base64编码
        String captchaCode_base64 = CaptchaGenerator.outputImageBase64(300, 80, captchaCode);
        //保存图形码 五分钟
        redisTemplate.opsForValue().set(CAPTCHA_KEY + id, captchaCode, 300, TimeUnit.SECONDS);
        return R.ok(captchaCode_base64);
    }

}
