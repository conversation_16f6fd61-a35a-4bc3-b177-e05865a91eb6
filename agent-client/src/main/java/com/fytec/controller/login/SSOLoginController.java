package com.fytec.controller.login;

import cn.dev33.satoken.stp.SaTokenInfo;
import com.fytec.dto.SSOLoginDTO;
import com.fytec.service.sso.SSOLoginService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Validated
@Tag(name = "登录")
@RequestMapping("/api")
@RestController
@RequiredArgsConstructor
public class SSOLoginController {
    private final SSOLoginService loginService;

    @PostMapping("/sso/login")
    @Operation(summary = "登录")
    public R<SaTokenInfo> login4SipEduSSO(@Validated @RequestBody SSOLoginDTO loginDTO) {
        return R.ok(loginService.login4SipEduSSO(loginDTO));
    }


    @PostMapping("/sso/logout")
    @Operation(summary = "登出")
    public R<SaTokenInfo> logout4SipEduSSO(@Validated @RequestBody SSOLoginDTO loginDTO) {
        loginService.logout4SipEduSSO(loginDTO);
        return R.ok();
    }

    @GetMapping("/sso/login/wj-edu")
    @Operation(summary = "吴江教育登录")
    public void login4WuJiangEduSSO(HttpServletRequest request, HttpServletResponse response) {
        loginService.login4WuJiangEduSSO(request, response);
    }

}
