package com.fytec.controller.login;


import com.fytec.dto.CurrentUserDTO;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.system.SysUserService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Validated
@Tag(name = "登录用户信息")
@RequestMapping("/api/current-user")
@RestController
@RequiredArgsConstructor
public class CurrentUserController {

    private final SysUserService sysUserService;

    @PostMapping("")
    @Operation(summary = "获取当前用户")
    @SaCheckPermission4FytecClient(value = "user:current:user", orRole = "admin")
    public R<CurrentUserDTO> getCurrentUser() {
        return R.ok(sysUserService.getCurrentUser());
    }

}
