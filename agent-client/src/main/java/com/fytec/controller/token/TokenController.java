package com.fytec.controller.token;


import com.fytec.dto.token.OauthClientDetailDTO;
import com.fytec.entity.system.OauthClientDetail;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.open.TokenService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Validated
@Tag(name = "访问令牌管理")
@RequestMapping("/api/token")
@RestController
@RequiredArgsConstructor
public class TokenController {

    private final TokenService tokenService;

    @GetMapping("/oauth_client/list")
    @Operation(summary = "oauth客户端列表")
    @SaCheckPermission4FytecClient(value = "oauth_client:list", orRole = "admin")
    public R<List<OauthClientDetailDTO>> queryOauthClientDetail(String clientName) {
        return R.ok(tokenService.queryOauthClientDetail(clientName));
    }
}
