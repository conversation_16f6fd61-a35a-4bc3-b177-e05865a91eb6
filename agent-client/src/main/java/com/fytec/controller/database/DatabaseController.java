package com.fytec.controller.database;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.database.ConnectionTestDTO;
import com.fytec.dto.database.DatabaseQueryDTO;
import com.fytec.dto.knowledge.KnowledgeDetailDTO;
import com.fytec.dto.knowledge.KnowledgeQueryDTO;
import com.fytec.satoken.StpClientUserUtil;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.database.DatabaseService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Validated
@Tag(name = "数据库管理")
@RequestMapping("/api/database")
@RestController
@AllArgsConstructor
public class DatabaseController {
    private final DatabaseService databaseService;

    @GetMapping(value = "/list")
    @Operation(summary = "知识库查询")
    @SaCheckPermission4FytecClient(value = "database:list", orRole = "admin")
    public R<Page<JSONObject>> queryDatabase(DatabaseQueryDTO dto, Page<JSONObject> page) {
        dto.setUserId(StpClientUserUtil.getLoginIdAsLong());
        return R.ok(databaseService.queryDatabase(dto, page));
    }

    @GetMapping("/info")
    @Operation(summary = "数据库信息")
    @SaCheckPermission4FytecClient(value = "database:info", orRole = "admin")
    public R<JSONObject> getDatabaseInfo(Long resourceId, Long id) {
        return R.ok(databaseService.getDatabaseInfo(resourceId, id));
    }

    @PostMapping("/conn/test")
    @Operation(summary = "数据源测试")
    @SaCheckPermission4FytecClient(value = "database:connect", orRole = "admin")
    public R<String> testConnection(@RequestBody ConnectionTestDTO dto) {
        if (databaseService.testConn(dto)) {
            return R.ok("连接成功");
        }
        return R.failed("连接失败，请联系管理员");
    }

    @GetMapping("/tables")
    @Operation(summary = "通过数据库id查询数据表信息")
    @SaCheckPermission4FytecClient(value = "database:show", orRole = "admin")
    public R<JSONArray> getTables(String dbId) {
        return R.ok(databaseService.getTables(dbId));
    }


}
