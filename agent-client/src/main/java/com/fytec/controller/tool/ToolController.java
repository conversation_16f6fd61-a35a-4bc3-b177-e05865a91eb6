package com.fytec.controller.tool;

import com.fytec.dto.llm.Txt2ImgProcessDTO;
import com.fytec.dto.response.Txt2ImgDTO;
import com.fytec.dto.tool.InvoiceOcrDTO;
import com.fytec.dto.tool.Txt2ImgExecuteDTO;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.tool.ToolService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@Slf4j
@Validated
@Tag(name = "工具类方法")
@RequestMapping("/api/tool")
@RestController
@RequiredArgsConstructor
public class ToolController {
    private final ToolService toolService;

    @PostMapping(path = "/txt2img")
    @Operation(summary = "文生图")
    @SaCheckPermission4FytecClient(value = "tool:use", orRole = "admin")
    public R<List<Txt2ImgDTO>> generateImageByText(@RequestBody @Validated Txt2ImgExecuteDTO dto) {
        return R.ok(toolService.generateImageByText(dto));
    }

    @SneakyThrows
    @PostMapping(path = "/ocr/invoice")
    @Operation(summary = "发票识别")
    @SaCheckPermission4FytecClient(value = "tool:use", orRole = "admin")
    public R<Map<String, Object>> invoiceOcr(@RequestBody @Validated InvoiceOcrDTO dto) {
        return R.ok(toolService.invoiceOcr(dto).get());
    }
}
