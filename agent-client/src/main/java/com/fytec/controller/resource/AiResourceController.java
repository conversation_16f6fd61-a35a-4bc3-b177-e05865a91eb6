package com.fytec.controller.resource;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.satoken.StpClientUserUtil;
import com.fytec.dto.resource.AddResourceDTO;
import com.fytec.dto.resource.QueryResourceDTO;
import com.fytec.dto.resource.ResourceDTO;
import com.fytec.dto.resource.UpdateResourceDTO;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.resource.ResourceService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

@Slf4j
@Validated
@Tag(name = "Ai资源管理")
@RequestMapping("/api/resource")
@RestController
@AllArgsConstructor
public class AiResourceController {
    private final ResourceService resourceService;

    @PostMapping(value = "/add")
    @Operation(summary = "添加资源")
    @SaCheckPermission4FytecClient(value = "resource:add", orRole = "admin")
    public R<Long> addResource(@Validated @RequestBody AddResourceDTO dto) {
        return R.ok(resourceService.addResource(dto));
    }

    @PostMapping(value = "/update")
    @Operation(summary = "修改资源")
    @SaCheckPermission4FytecClient(value = "resource:update", orRole = "admin")
    public R<Long> updateSource(@Validated @RequestBody UpdateResourceDTO dto) {
        return R.ok(resourceService.updateSource(dto));
    }

    @PostMapping(value = "/copy")
    @Operation(summary = "复制资源")
    @SaCheckPermission4FytecClient(value = "resource:copy", orRole = "admin")
    public R<Long> copyResource(@NotNull(message = "资源ID不能为空") Long id) {
        return R.ok(resourceService.copyResource(id));
    }

    @PostMapping(value = "/delete")
    @Operation(summary = "删除资源")
    @SaCheckPermission4FytecClient(value = "resource:delete", orRole = "admin")
    public R<?> deleteResource(@NotNull(message = "资源ID不能为空") Long id) {
        resourceService.deleteResource(id);
        return R.ok();
    }

    @GetMapping(value = "/page")
    @Operation(summary = "查询资源")
    @SaCheckPermission4FytecClient(value = "resource:page", orRole = "admin")
    public R<Page<ResourceDTO>> queryPage(QueryResourceDTO dto, Page<ResourceDTO> page) {
        // 判断角色
//        dto.setCreateBy(StpClientUserUtil.getLoginIdAsLong());
        return R.ok(resourceService.queryPage(dto, page));
    }


}
