package com.fytec.controller.open.agent.flow.utils.functioncall;

import cn.hutool.core.exceptions.ValidateException;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fytec.controller.open.agent.flow.protocol.AgentHandlerError;
import com.fytec.controller.open.agent.flow.protocol.IAgentHandler;
import com.fytec.controller.open.agent.flow.protocol.IHandlerContext;
import com.fytec.dto.llm.ModelCallDTO;
import com.fytec.entity.llm.AiModel;
import com.fytec.service.prompt.PromptService;
import com.fytec.utils.AiFormatJsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.locks.ReentrantLock;

import static com.fytec.controller.open.agent.flow.utils.functioncall.FunctionCallConfigs.*;
import static org.apache.commons.lang.reflect.MethodUtils.invokeMethod;

@Slf4j
public class FunctionCallFactory {
    // 线程池配置
    // 线程池配置 - 适合高并发且不丢弃任务的场景
    private static final ExecutorService executor = new ThreadPoolExecutor(
            // 核心线程数：根据CPU核心数设置
            Runtime.getRuntime().availableProcessors(),
            // 最大线程数：核心线程数的2倍
            Runtime.getRuntime().availableProcessors() * 2,
            // 空闲线程存活时间
            60L, TimeUnit.SECONDS,
            // 使用无界队列，确保任务不会被丢弃
            new LinkedBlockingQueue<>(),
            // 线程工厂：自定义线程名称，便于排查问题
            new ThreadFactory() {
                private final ReentrantLock lock = new ReentrantLock();
                private int counter = 0;
                @Override
                public Thread newThread(Runnable r) {
                    lock.lock();
                    try {
                        counter++;
                        Thread thread = new Thread(r, "function-call-pool-" + counter);
                        // 设置为守护线程，避免影响JVM退出
                        thread.setDaemon(true);
                        return thread;
                    } finally {
                        lock.unlock();
                    }
                }
            },
            // 拒绝策略：调用者阻塞等待，直到队列有空间
            new RejectedExecutionHandler() {
                @Override
                public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                    try {
                        // 核心：当队列满时，阻塞等待队列有空间
                        // 使用offer并设置超时，避免无限阻塞
                        if (!executor.getQueue().offer(r, 1, TimeUnit.MINUTES)) {
                            throw new RejectedExecutionException("任务提交超时，队列仍满");
                        }
                    } catch (InterruptedException e) {
                        // 恢复中断状态
                        Thread.currentThread().interrupt();
                        throw new RejectedExecutionException("任务提交被中断", e);
                    }
                }
            }
    );
    public static  List<FCallResultDTO> callFunction(IFunctionCallService callService, IHandlerContext context,
                                                     Object instance) {
        return callFunction(callService,context,instance,null);
    }
    public static  List<FCallResultDTO> callFunction(IFunctionCallService callService, IHandlerContext context, Object instance,IAgentHandler execNode){
        // 获取实例的函数调用DTO列表
        List<FCallDTO> fCallDTOs = AnnotationValidatorUtils.buildFCallDTOByClassName(instance.getClass());
        // 如果函数调用DTO列表为空，说明不需要函数调用，直接返回null
        if(fCallDTOs==null||fCallDTOs.isEmpty()){
            // 说明不会需要function call
            return null;
        }
        // 如果execNode不为空，打印正在理解您的意图...判断使用%s工具
        if(execNode!=null){
            List<String> toolNames = new ArrayList<>(fCallDTOs.size());
            fCallDTOs.forEach(e->{
                toolNames.add(e.getTitle());
            });
            execNode.printThink(context, String.format("正在理解您的意图...判断使用%s工具", StringUtils.join(toolNames,",")));
        }

        // 运行函数调用
        List<FCallDTO> realFCallList = ModelRunUtils.run(callService,context,fCallDTOs);
        // 如果函数调用DTO列表为空，说明不需要函数调用，直接返回null
        if(realFCallList==null||realFCallList.isEmpty()){
            // 说明不会需要function call
            if(execNode!=null)
                execNode.printThink(context,  "判断不需要使用工具" );
            return null;
        }else {
            if(execNode!=null){
                List<String> toolNames = new ArrayList<>(realFCallList.size());
                realFCallList.forEach(e->{
                    toolNames.add(e.getTitle());
                });
                execNode.printThink(context, String.format("判断需要使用%s工具", StringUtils.join(toolNames,",")));
            }
        }
        //  多线程反射调用方法获取结果，定义结果集，返回结构都是FCallResultDTO类（里面变量为result,sourceMethodName)
        // 提交所有方法调用任务到线程池
        List<Future<FCallResultDTO>> futures = new ArrayList<>(realFCallList.size());

        for (FCallDTO dto : realFCallList) {
            dto.getParamObjs()[0] = context;// 第一个强制要求为这个
            // 如果context的fCallDTOMap中已经存在相同的id，打印错误信息
            if(context.getFCallDTOMap().containsKey(dto.getId())){
            // 将dto放入context的fCallDTOMap中
            log.error("存在相同id的fcall 工具:{}",dto.getId());
            // 提交方法调用任务到线程池
            }
            context.getFCallDTOMap().put(dto.getId(),dto);
            futures.add(executor.submit(() -> (FCallResultDTO) invokeMethod(
                    instance,dto.getMethodName(),dto.getParamObjs())));
        }
        //  结果集封装为List<FCallResult> 返回
        // 收集结果
        List<FCallResultDTO> results = new ArrayList<>(futures.size());
        for (int i = 0; i < futures.size(); i++) {
            Future<FCallResultDTO> future = futures.get(i);
            try {
                // 设置metaInfo
                // 等待任务完成，可设置超时时间避免无限等待
                // 将结果添加到结果集中
                FCallResultDTO e = future.get(5, TimeUnit.MINUTES);
                e.setMetaInfo(realFCallList.get(i));
                // 如果任务被中断，设置错误信息
                if(execNode!=null){
                    execNode.printThink(context, String.format("%s工具,执行成功，结果为:%s", e.getMetaInfo().getTitle(),e.getResult()));
                }
                results.add(e);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                FCallResultDTO errorResult = new FCallResultDTO();
                // 将错误结果添加到结果集中
                errorResult.setMetaInfo(realFCallList.get(i));
                errorResult.setErrorMsg("调用被中断: " + e.getMessage());
                if(execNode!=null){
                    execNode.printThink(context, String.format("%s工具,%s", errorResult.getMetaInfo().getTitle(),errorResult.getErrorMsg()));
                }
                results.add(errorResult);
            } catch (ExecutionException | TimeoutException e) {
                // 获取实际的异常原因
                Throwable cause = e.getCause();

                FCallResultDTO errorResult = new FCallResultDTO();
                errorResult.setMetaInfo(realFCallList.get(i));

                // 检查是否是自定义异常
                if (cause instanceof AgentHandlerError) {
                    // 处理自定义异常
                    errorResult.setErrorMsg("自定义异常: " + cause.getMessage());
                } else if (cause instanceof InvocationTargetException) {
                    // 如果还是InvocationTargetException，继续获取其原因
                    Throwable targetCause = ((InvocationTargetException) cause).getTargetException();
                    targetCause.printStackTrace();
                    log.error(targetCause.getLocalizedMessage());
                    errorResult.setErrorMsg("反射调用异常: " + targetCause.getMessage());
                } else {
                    // 处理其他异常
                // 将错误结果添加到结果集中
                    errorResult.setErrorMsg("执行异常: " + cause.getMessage());
                }
                if(execNode!=null){
                    execNode.printThink(context, String.format("%s工具,%s", errorResult.getMetaInfo().getTitle(),errorResult.getErrorMsg()));
                }
                results.add(errorResult);
        // 返回结果集
            }
        }
        return results;

    }
    // 实际执行方法
    private static class ModelRunUtils{
        public static List<FCallDTO> run(IFunctionCallService callService,
                                         IHandlerContext context, List<FCallDTO> callDTOList){
            //  通过大模型和智能体，来调用这个call，用默认模型进行处理,获取需要执行的fun id和变量列表
            List<String> tools = new ArrayList<>();
            Map<String,FCallDTO> id2dto = new HashMap<>();
            for (FCallDTO fCallDTO : callDTOList) {
//                String.format(FunctionCallConfigs.DEFAULT_FUNCTION_CALL_TOOL_PROMPT, )
                String paramEach = formatToolPrompt(fCallDTO);
                tools.add(paramEach);
                id2dto.put(fCallDTO.getId(),fCallDTO);
            }
            String prompt = DEFAULT_FUNCTION_CALL_PROMPT
                    .replace(KEY_FUNCTION_CALL_TOOLS, StringUtils.join(tools, "\n"));
            String userInput = "用户问题:" + context.getInput();
            ModelCallDTO result = callService.autoFunctionCallContext(prompt,userInput);
            //执行提示词，获取到function call
            if(StringUtils.isBlank(result.getHistory())){
                System.out.println("ModelRunUtils run history1:null");
                log.info("ModelRunUtils run history2:null");
                return null;
            }
            log.info("ModelRunUtils run history:{}",result.getHistory().toString());
            // format json结果集
            String jsonStr = AiFormatJsonUtil.formatJson(result.getHistory().toString());
//            名称name、工具参数params:{key是参数名，value是参数值}
            //  工具结果集format json后期，获取需要执行的function call
            JSONArray objects = JSON.parseArray(jsonStr);
//            System.o
            List<FCallDTO> filterCallList = new LinkedList<>();
//            log.info("ModelRunUtils run history:{},arr len:{}，format json:{}",result.getHistory().toString(),objects.size(),jsonStr);
            for (Object object : objects) {
                JSONObject obj = (JSONObject) object;
                if(obj.containsKey("name")){
                    String id = obj.getString("name");
                    if(id2dto.containsKey(id)){
                        if(!id2dto.get(id).getParams().isEmpty() && obj.containsKey("params")){
                            JSONObject params = obj.getJSONObject("params");
                            Object[] paramObjs = new Object[id2dto.get(id).getParams().size() + 1]; // 强制要求多一个
                            // 按照顺序，进行编排好
                            for (String key : params.keySet()) {
                                if(key.startsWith("i_")){
                                    int index =Integer.parseInt(key.split("i_")[1]);
                                    paramObjs[index] = params.get(key);
                                }
                            }
                            id2dto.get(id).setParamObjs(paramObjs);
                            log.info("tool {} run args:{}",id2dto.get(id).getTitle(),JSON.toJSONString(paramObjs));

                            filterCallList.add(id2dto.get(id));
                        } else if(id2dto.get(id).getParams().isEmpty()){
                            Object[] paramObjs = new Object[1]; // 强制要求多一个
                            id2dto.get(id).setParamObjs(paramObjs);
                            filterCallList.add(id2dto.get(id));
                        }

                    }
                }
            }
            return filterCallList;
        }
        /**
         * 格式化单个函数调用的工具提示
         * @param dto 函数调用DTO
         * @return 格式化后的字符串
         */
        private static String formatToolPrompt(FCallDTO dto) {
            List<String> params = new LinkedList<>();
            String paramsStr = "";
            if(dto.getParams()!=null){
                // 处理参数数组为字符串（如：param1,param2）
                for (MethodParameterDTO param : dto.getParams()) {
                    params.add(String.format("{参数名:i_%s,参数描述:%s}", param.getIndex(),param.getDesc()));
                }
                if(!params.isEmpty()){
                    paramsStr = String.join(",", params);
                }
            }
            List<String> descs = new LinkedList<>();
            if(StringUtils.isNotBlank(dto.getTitle())){
                descs.add(dto.getTitle());
            }
            if(StringUtils.isNotBlank(dto.getDesc())){
                descs.add(dto.getDesc());
            }
            // 替换模板中的占位符
            return FunctionCallConfigs.DEFAULT_FUNCTION_CALL_TOOL_PROMPT
                    .replace(KEY_NAME, dto.getId())  // 使用id作为工具名称
                    .replace(KEY_DESC, StringUtils.join(descs,";"))  // 描述包含描述和标题
                    .replace(KEY_PARAMS, paramsStr);
        }
    }

    // 通用方法，反射验证等
    private static class AnnotationValidatorUtils{
        /**
         * 验证使用@FCall注解的方法所在的类是否继承了FunctionCallInterface
         * @param clazz 要验证的类
         * @throws IllegalArgumentException 如果验证失败则抛出异常
         */
        public static List<FCallDTO> buildFCallDTOByClassName(Class<?> clazz) {
            // 检查类是否实现了FunctionCallInterface接口
            boolean implementsInterface = false;
            for (Class<?> iface : clazz.getInterfaces()) {
                if (iface == IFunctionCall.class) {
                    implementsInterface = true;
                    break;
                }
            }
            if(!implementsInterface){
                return null;
            }
            // 如果类没有实现接口，但有方法使用了@FCall注解，则抛出异常
            if (implementsInterface) {
                List<FCallDTO> fCallList = new LinkedList<>();
                Method[] methods = clazz.getDeclaredMethods();
                for (Method method : methods) {
                    if (method.isAnnotationPresent(FCall.class)) {
//                        throw new IllegalArgumentException(
//                                "类 " + clazz.getName() + " 的方法 " + method.getName() +
//                                        " 使用了@FCall注解，但该类未实现FunctionCallInterface接口"
//                        );
                        // 获取注解实例
                        FCall fcall = method.getAnnotation(FCall.class);
                        FCallDTO callDTO = new FCallDTO();
                        if(StringUtils.isBlank(fcall.id())){
                            callDTO.setId(method.getName());
                            callDTO.setMethodName(method.getName());
                        }else{
                            callDTO.setId(fcall.id());
                            callDTO.setMethodName(fcall.id());
                        }
                        if(StringUtils.isBlank(fcall.desc())){
                            callDTO.setDesc("");
                        }else{
                            callDTO.setDesc(fcall.desc());
                        }
                        if(StringUtils.isBlank(fcall.title())){
                            callDTO.setTitle("");
                        }else{
                            callDTO.setTitle(fcall.title());
                        }

                        // 获取方法实际参数信息
                        List<MethodParameterDTO> methodParams = getMethodParameters(method);
                        if(methodParams.size()!=fcall.paramsDesc().length){
                            throw new ValidateException("参数解释和参数数目不一样");
                        }
                        for (int i = 0; i < methodParams.size(); i++) {
                            methodParams.get(i).setDesc(fcall.paramsDesc()[i]);
                        }
                        callDTO.setParams(methodParams);
                        fCallList.add(callDTO);
                    }
                }
                return fCallList;
            }
            return null;
        }
        /**
         * 获取方法的所有输入参数信息
         * @param method 目标方法
         * @return 方法参数信息列表
         */
        public static List<MethodParameterDTO> getMethodParameters(Method method) {
            List<MethodParameterDTO> parameters = new ArrayList<>();

            // 获取方法参数类型
            Class<?>[] paramTypes = method.getParameterTypes();
            if (ArrayUtils.isEmpty(paramTypes)) {
                return parameters; // 无参数
            }

            // 获取方法参数对象（包含更多信息）
            Parameter[] params = method.getParameters();

            for (int i = 1; i < params.length; i++) { // 第一个默认为IHandleContext
                Parameter param = params[i];
                MethodParameterDTO dto = new MethodParameterDTO();

                // 参数索引（从0开始）
                dto.setIndex(i);

                // 参数名称（需要Java 8+并在编译时添加-parameters参数才能获取真实名称）
                dto.setName(param.getName());

                // 参数类型（全类名）
                dto.setType(param.getType().getName());

                // 参数类型简单名称
                dto.setSimpleName(param.getType().getSimpleName());

                // 是否为基本类型
                dto.setPrimitive(param.getType().isPrimitive());

                // 是否为数组
                dto.setArray(param.getType().isArray());

                parameters.add(dto);
            }

            return parameters;
        }

    }

}
