package com.fytec.controller.open.rocketmq;

import cn.dev33.satoken.oauth2.annotation.SaCheckClientIdSecret;
import cn.hutool.core.util.StrUtil;
import com.fytec.dto.rocketmq.SendMessageDTO;
import com.fytec.rocketmq.ProducerSingleton;
import com.fytec.util.UUIDGenerator;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.ClientServiceProvider;
import org.apache.rocketmq.client.apis.message.Message;
import org.apache.rocketmq.client.apis.producer.Producer;
import org.apache.rocketmq.client.apis.producer.SendReceipt;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.nio.charset.StandardCharsets;

@Slf4j
@Validated
@Tag(name = "rocketmq")
@RequestMapping("/fytec/api/rocketmq")
@RestController
@RequiredArgsConstructor
public class RocketmqController {
    @Value("${rocketmq.topic}")
    private String topic;

    @PostMapping(value = "/send/message")
    @Operation(summary = "发送消息")
    @SaCheckClientIdSecret
    @SneakyThrows
    public void sendMessageToMQ(@RequestBody SendMessageDTO dto) {
        final ClientServiceProvider provider = ClientServiceProvider.loadService();
        final Producer producer = ProducerSingleton.getInstance(topic);
        // Define your message body.
        byte[] body = dto.getMessage().getBytes(StandardCharsets.UTF_8);
        String runId = dto.getRunId();
        final Message message = provider.newMessageBuilder()
                // Set topic for the current message.
                .setTopic(topic)
                // Message secondary classifier of message besides topic.
                .setTag(runId)
                // Key(s) of the message, another way to mark message besides message id.
                .setKeys(StrUtil.format("MessageKey_{}", UUIDGenerator.getUUID()))
                // Message group decides the message delivery order.
                .setMessageGroup(runId)
                .setBody(body)
                .build();
        try {
            final SendReceipt sendReceipt = producer.send(message);
            log.info("Send message successfully, messageId={}", sendReceipt.getMessageId());
        } catch (Throwable t) {
            log.error("Failed to send message", t);
        }

        //producer.close();
    }


}
