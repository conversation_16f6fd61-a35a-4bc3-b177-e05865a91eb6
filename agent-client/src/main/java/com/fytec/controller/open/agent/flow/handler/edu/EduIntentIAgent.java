package com.fytec.controller.open.agent.flow.handler.edu;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONException;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fytec.controller.open.agent.flow.protocol.AgentHandlerError;
import com.fytec.controller.open.agent.flow.protocol.IAgentHandler;
import com.fytec.controller.open.agent.flow.protocol.IHandlerContext;
import com.fytec.controller.open.agent.flow.protocol.errors.AgentDataNotPermissionException;
import com.fytec.controller.open.agent.flow.utils.edu.GatewayUtils;
import com.fytec.utils.AiFormatJsonUtil;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.*;

// 小易助手，意图识别智能体
@Slf4j
public class EduIntentIAgent implements IAgentHandler {
    @Override
    public void beforeHandle(IHandlerContext context) throws AgentHandlerError {
        IAgentHandler.super.beforeHandle(context);
        // todo 设置一下，添加一下我的个人信息、天气等因素
        //        context.getThirdUserToken()
        if (false) {
            Map<String, String> headers = GatewayUtils.buildDefaultHeader();
            try {
                DataUserInfo userInfo = getUserInfo(headers, context);
                context.getConstants().putIfAbsent("sys_info_username", userInfo.getUserName());
                context.getConstants().putIfAbsent("sys_info_tel", userInfo.getTel());
            } catch (Exception e) {

            }
        }
        if (context.getConstants() != null && context.getConstants().containsKey("sys_info_username")) {
            String username = context.getConstants().get("sys_info_username").trim();
            if (StringUtils.isNotBlank(username)) {
//                printThink(context,"正在获取用户信息，您是");
            }
        }
        printThink(context, "正在理解您的意图...");
    }

    @Override
    public void handle(IHandlerContext context) throws AgentHandlerError {
        IAgentHandler.super.handle(context);
        IAgentHandler.super.print(context);
        // todo 根据output，采用不同的模块
        // 判断意图了
        //判断是哪个智能体
//        List<WorkflowDTO> workflowList = new ;
//        int maxNum = -1;
//        WorkflowDTO nextWork = null;
//        for (WorkflowDTO workflowDTO : context.getWorkflowList()) {
//            String name = workflowDTO.getName();
//            int i = AgentUtil.countStrExistNum(context.getOutput(), name);
//            if(i>maxNum){
//                maxNum = i;
//                nextWork = workflowDTO;
//            }
//        }
//        if(nextWork!=null){
//            context.setNextAgentId(nextWork.get());
//        }
        // 设置返回一个json，里面是相关的智能体id和
        String output = AiFormatJsonUtil.formatJson(context.getOutput());
        JSONObject jsonObject = null;
        try {
            jsonObject = JSONObject.parseObject(output);
            if (jsonObject == null || !jsonObject.containsKey("id") || StringUtils.isBlank(jsonObject.getString("id"))) {
                System.out.println("没有找到id:" + output);
            }
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("格式化失败:" + output);
        }
        // 默认是9
        Long nextAgentId = 9L;
        if (jsonObject != null) {
            try {
                Long id = jsonObject.getLong("id");
                if (id != null && id > 0L) {
                    nextAgentId = id;
                }
                if (jsonObject.containsKey("input")) {
                    String newInput = jsonObject.getString("input");
                    if (StringUtils.isNotBlank(newInput)) {
//                        System.out.println("改写前input 8L:" + context.getInput());
                        if (newInput.equals(context.getInput())) {

                        } else {
                            printThink(context, String.format("将您的问题改写为：%s", newInput));
                        }
                        context.setInput(newInput);
//                        System.out.println("改写后input 8L:" + context.getInput());
                    }

                }
            } catch (Exception e) {
                e.printStackTrace();
                System.out.println("格式化失败:" + jsonObject.toString());
            }
        }

        System.out.println("下一个 节点 id:" + nextAgentId);
//        if (nextAgentId == 9L) {
//            // 改写一下问题表述
//
//        }

        // 根据节点id，查询到智能体的描述？
        String rawInput = context.getRawInput();
        if (StringUtils.isBlank(rawInput)) {
            rawInput = context.getInput();
        }
        String msg = String.format("用户询问“%s”，%s，返回查询结果。", rawInput, AGENT_NAME.getOrDefault("" + nextAgentId, "我将调用“易加助手”智能体"));
        printThink(context, msg);

        context.setNextAgentId(nextAgentId);
    }

    private static final Map<String, String> AGENT_NAME = new HashMap<>();

    static {
        AGENT_NAME.put("9", "我将调用“客服”智能体");
        AGENT_NAME.put("5", "我将调用“数据查询”智能体，查询本地数据库");
        AGENT_NAME.put("31", "我将调用“通用”智能体");
        AGENT_NAME.put("32", "我将调用“视频资源”智能体，查询本地数据库");
    }

    @Override
    public String getThinkResultTemplate() {
        return "%s";
    }

    @Override
    public Long getAgentId() {
        return 8L;
    }

    @Override
    public String getDesc() {
        return "分支判断，判断小易助手想查询哪一个模块数据";
    }

    @Override
    public Long getNextAgentId(IHandlerContext context) {
        String output = context.getOutput();
        String s = AiFormatJsonUtil.formatJson(output);
        JSONObject jsonObject = null;
//        if (jsonObject == null || !jsonObject.containsKey("id") || StringUtils.isBlank(jsonObject.getString("id"))) {
//        }
        // 默认是9
        Long nextAgentId = 9L;
        try {
            jsonObject = JSONObject.parseObject(s);
            Long id = jsonObject.getLong("id");
            if (id != null && id > 0L) {
                nextAgentId = id;
            }
        } catch (Exception e) {
            System.out.println("format json失败:" + s);
        }
        return nextAgentId;
    }

    public static DataUserInfo getUserInfo(Map<String, String> headers, IHandlerContext context) throws Exception {
        // 获取哦token
        String token = context.getThirdUserToken();
        DataUserInfo dataUserInfo = new DataUserInfo();

        {
            JSONObject body = new JSONObject();
            body.put("token", token);
//            body.put("data_type", type);
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonBody = objectMapper.writeValueAsString(body);

            String turl = "http://portapigw.sipedu.cn/sipdata/API/V5/large_model/queryPermission";
//            https://openappportweb.sipedu.cn/openplt-c/api/details/1800861530024075266?isPublic=0

            System.out.println(headers);
            HttpResponse<String> response = Unirest.post(turl)
                    .header("Content-Type", org.springframework.http.MediaType.APPLICATION_JSON_VALUE)
                    .headers(headers)
                    .body(jsonBody)
                    .asString();
            JSONObject parseObj = JSON.parseObject(response.getBody());
            if (parseObj.containsKey("data")) {
                JSONObject data = parseObj.getJSONObject("data");
//                Boolean isAreaAdmin = data.getBoolean("isAreaAdmin");

            } else {
                System.out.println(response.getBody());
            }
        }

        return dataUserInfo;
    }

    @Data
    public static class DataUserInfo {
        private String userName;
        private String tel;
    }

    @Override
    public List<Long> getParallelAgentId() {
        log.debug("教育意图识别8L智能体，执行并行的tip智能体");
        return Collections.singletonList(86L);
    }
}
