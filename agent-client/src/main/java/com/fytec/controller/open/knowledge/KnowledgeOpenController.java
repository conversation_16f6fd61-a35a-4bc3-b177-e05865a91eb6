package com.fytec.controller.open.knowledge;


import cn.dev33.satoken.oauth2.annotation.SaCheckClientToken;
import cn.hutool.core.exceptions.ValidateException;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.knowledge.*;
import com.fytec.dto.knowledge.group.KnowledgeGroupDTO;
import com.fytec.dto.resource.AddResourceDTO;
import com.fytec.dto.resource.UpdateResourceDTO;
import com.fytec.entity.knowledge.KnowledgeDoc;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.knowledge.KnowledgeService;
import com.fytec.service.resource.ResourceService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Validated
@Tag(name = "知识库对外接口")
@RequestMapping("/fytec/api/knowledge")
@RestController
@RequiredArgsConstructor
public class KnowledgeOpenController {

    private final KnowledgeService knowledgeService;

    private final ResourceService resourceService;

    @PostMapping(value = "/doc/auto")
    @Operation(summary = "处理文档数据转换")
    @SaCheckClientToken(scope = "client:execute")
    public R<List<Long>> autoProcessData(@Validated @RequestBody KnowledgeDocAutoDTO dto) {
        knowledgeService.autoProcessData(dto);
        //Async方法获取不到用户信息，需要当成参数往后传递
        List<Long> docIds = new ArrayList<>();
        for (KnowledgeFileDTO file : dto.getFiles()) {
            docIds.add(file.getDoc().getId());
            knowledgeService.autoProcessDoc(file, dto);
        }
        return R.ok(docIds);
    }


    //##################################知识库查询#################################

    @PostMapping(value = "/add")
    @Operation(summary = "添加知识库")
    @SaCheckPermission4FytecClient(value = "resource:add", orRole = "admin")
    public R<Long> addResource(@Validated @RequestBody AddResourceDTO dto) {
        resourceService.addResource(dto);
        return R.ok();
    }

    @PostMapping(value = "/update")
    @Operation(summary = "修改知识库")
    @SaCheckPermission4FytecClient(value = "resource:update", orRole = "admin")
    public R<Long> updateSource(@Validated @RequestBody UpdateResourceDTO dto) {
        resourceService.updateSource(dto);
        return R.ok();
    }

    @GetMapping(value = "/init")
    @Operation(summary = "知识库初始化")
    @SaCheckClientToken(scope = "client:execute")
    public R<Long> initKnowledge(KnowledgeInitDTO dto) {
        Long knowledgeId = knowledgeService.initKnowledge(dto.getName(), dto.getDescription(), dto.getLogo(), dto.getKnowledgeType(),
                dto.getExpireOption(), dto.getStartTime(), dto.getEndTime(), dto.getUserId());
        return R.ok(knowledgeId);
    }

    @GetMapping(value = "/list")
    @Operation(summary = "知识库查询")
    @SaCheckClientToken(scope = "client:execute")
    public R<Page<KnowledgeDetailDTO>> queryKnowledge(KnowledgeQueryDTO dto, Page<KnowledgeDetailDTO> page) {
        return R.ok(knowledgeService.queryKnowledge(dto, page));
    }

    @GetMapping(value = "/detail")
    @Operation(summary = "知识库详情")
    @SaCheckClientToken(scope = "client:execute")
    public R<KnowledgeDetailDTO> getKnowledgeDetail(Long resourceId) {
        return R.ok(knowledgeService.getKnowledgeDetail(resourceId));
    }

    //##################################知识库文档查询#################################
    @GetMapping(value = "/doc/list")
    @Operation(summary = "知识库文档列表")
    @SaCheckClientToken(scope = "client:execute")
    public R<List<KnowledgeDocDTO>> getKnowledgeDocs(KnowledgeDocQueryDTO dto) {
        return R.ok(knowledgeService.getKnowledgeDocs(dto));
    }


    @GetMapping(value = "/doc/list/all-in-one")
    @Operation(summary = "知识库文档列表")
    @SaCheckClientToken(scope = "client:execute")
    public R<List<KnowledgeAllInOneDTO>> queryKnowledgeAllInOne(KnowledgeAllInOneQueryDTO dto) {
        return R.ok(knowledgeService.queryKnowledgeAllInOne(dto));
    }


    @GetMapping(value = "/doc/detail")
    @Operation(summary = "知识库文档详情")
    @SaCheckClientToken(scope = "client:execute")
    public R<KnowledgeDocDetailDTO> getKnowledgeDocDetail(Long docId) {
        return R.ok(knowledgeService.getKnowledgeDocDetail(docId));
    }

    @PostMapping(value = "/doc/batch-detail")
    @Operation(summary = "知识库文档详情")
    @SaCheckClientToken(scope = "client:execute")
    public R<List<KnowledgeDoc>> batchGetKnowledgeDocDetail(@RequestBody KnowledgeDocBatchDTO dto) {
        return R.ok(knowledgeService.batchGetKnowledgeDocDetail(dto));
    }

    @PostMapping(value = "/doc/update-name")
    @Operation(summary = "更新文档名称")
    @SaCheckClientToken(scope = "client:execute")
    public R<Void> updateDocName(@RequestBody KnowledgeDocUpdateDTO dto) {
        knowledgeService.updateDocName(dto);
        return R.ok();
    }

    @PostMapping(value = "/doc/move-group")
    @Operation(summary = "文档移动")
    @SaCheckClientToken(scope = "client:execute")
    public R<Void> updateDocGroup(@RequestBody KnowledgeDocMoveDTO dto) {
        knowledgeService.updateDocGroup(dto);
        return R.ok();
    }

    @GetMapping(value = "/doc/delete")
    @Operation(summary = "删除文档")
    @SaCheckClientToken(scope = "client:execute")
    public R<Void> deleteDoc(String docId) {
        knowledgeService.deleteDoc(docId);
        return R.ok();
    }

    @PostMapping(value = "/doc/batch-delete")
    @Operation(summary = "删除文档")
    @SaCheckClientToken(scope = "client:execute")
    public R<Void> batchDeleteDoc(@RequestBody KnowledgeDocBatchDTO dto) {
        knowledgeService.batchDeleteDoc(dto);
        return R.ok();
    }

    //##################################知识库文档导入#################################
    @PostMapping(value = "/doc/batch-import")
    @Operation(summary = "知识库导入到知识库")
    @SaCheckClientToken(scope = "client:execute")
    public R<List<Long>> batchImportFromAnotherKnowledge(@Validated @RequestBody AiWorkKnowledgeDocBatchImportDTO dto) {
        List<Map<String, Object>> resultMaps = knowledgeService.aiworkBatchImportFromAnotherKnowledge(dto);
        List<Long> targetDocIds = new ArrayList<>();
        for (Map<String, Object> resultMap : resultMaps) {
            targetDocIds.add((Long) resultMap.get("targetDocId"));
            knowledgeService.copySegmentAndVector(dto.getTargetKnowledgeId(),
                    (String) resultMap.get("sourceCollectionName"),
                    (String) resultMap.get("targetCollectionName"),
                    (KnowledgeDoc) resultMap.get("sourceDoc"),
                    (KnowledgeDoc) resultMap.get("targetDoc"));
        }
        return R.ok(targetDocIds);
    }

    //##################################知识库组相关#################################
    @PostMapping(value = "/group/after-update")
    @Operation(summary = "更新知识库组")
    @SaCheckClientToken(scope = "client:execute")
    public R<Void> updateKnowledgeAfterGroupUpdate(@RequestBody KnowledgeGroupDTO dto) {
        knowledgeService.updateKnowledgeAfterGroupUpdate(dto);
        return R.ok();
    }

    @GetMapping(value = "/group/knowledge-detail")
    @Operation(summary = "知识库组对应的知识库性情")
    @SaCheckClientToken(scope = "client:execute")
    public R<?> getKnowledgeDetailByGroup(Long knowledgeId) {
        return R.ok(knowledgeService.getKnowledgeDetailByGroup(knowledgeId));
    }


    @PostMapping(value = "/doc/search")
    @Operation(summary = "知识库搜索")
    @SaCheckClientToken(scope = "client:execute")
    public R<?> searchDocListByQuery(@RequestBody KnowLedgeDocSearchDTO dto) {
        if(dto.getMinScore()==null||dto.getMinScore()<0.3f){
            throw new ValidateException("minScore 最小阈值不能为空且不小于0.3");
        }
        return R.ok(knowledgeService.searchDocListByQuery(dto));
    }
    @PostMapping(value = "/doc/search/segments")
    @Operation(summary = "知识库搜索")
    @SaCheckClientToken(scope = "client:execute")
    public R<?> searchSegmentListByQuery(@RequestBody KnowLedgeDocSearchDTO dto) {
        if(dto.getMinScore()==null||dto.getMinScore()<0.3f){
            throw new ValidateException("minScore 最小阈值不能为空且不小于0.3");
        }
        return R.ok(knowledgeService.searchSegmentListByQuery(dto));
    }
}
