package com.fytec.controller.open.agent.flow.utils.functioncall.demo;

import com.alibaba.fastjson2.JSON;
import com.fytec.controller.open.agent.flow.protocol.IAgentHandler;
import com.fytec.controller.open.agent.flow.protocol.IHandlerContext;
import com.fytec.controller.open.agent.flow.utils.functioncall.*;
import com.fytec.dto.llm.ModelCallDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class FunctionCallDemoEntity implements IFunctionCall {

    @FCall(
            paramsDesc = {
                    "表名(学生表v_student、老师表v_teacher）",
                    "种类(学生、老师)"
            }, id = "", desc = "",title = "调用获取数据库数据")
    public FCallResultDTO callMysqlFun(IHandlerContext context, String tableName,String type){
      log.info("callMysqlFun:{},{}",tableName,type);
        FCallResultDTO fCallResultDTO = new FCallResultDTO();
//        fCallResultDTO.setMetaInfo("callMysqlFun");
        return fCallResultDTO;
    }
    @FCall(
            paramsDesc = {
                    "地区",
                    "日期"
            }, id = "", desc = "",title = "调用获取当前天气信息")
    public FCallResultDTO callTqApi(IHandlerContext context, String area,String date){
        log.info("callTqApi:{},{}",area,date);
        FCallResultDTO fCallResultDTO = new FCallResultDTO();
        return fCallResultDTO;
    }



    public static void main(String [] args){
        IHandlerContext context = new IHandlerContext();
        context.setInput("苏州今天天气怎么样");

        FunctionCallDemoEntity instance = new FunctionCallDemoEntity();
        List<FCallResultDTO> fCallResultDTOS = FunctionCallFactory.callFunction(new IFunctionCallService() {
            @Override
            public ModelCallDTO autoFunctionCallContext(String sysPrompt, String userInput) {
                log.info("@autoFunctionCallContext\t sysPrompt:{}",sysPrompt);
                log.info("@autoFunctionCallContext\t userInput:{}",userInput);

                ModelCallDTO modelCallDTO = new ModelCallDTO();
                StringBuffer history = new StringBuffer();
                history.append("[{\"name\":\"callTqApi\",\"params\":{\"i_0\":\"苏州\",\"i_1\":\"\"}}]");
                modelCallDTO.setHistory(history);
                return modelCallDTO;
            }

            @Override
            public void checkAndBuildEchartFormat(String body, IHandlerContext context, IAgentHandler currentNode) {

            }
        }, context, instance);
        log.info(JSON.toJSONString(fCallResultDTOS));
    }
}
