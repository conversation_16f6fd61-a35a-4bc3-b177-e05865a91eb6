package com.fytec.controller.open.agent.flow.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

//放一些智能体处理工具
public final class AgentUtil {
    public static int countStrExistNum(String context, String name) {
        int count = 0;
        int index = 0;
        while ((index = context.indexOf(name, index)) != -1) {
            count++;
            index += name.length();
        }
        return count;
    }
    public static String getOutput(String output){
        try {
            JSONObject jsonObject = JSON.parseObject(output);
            if(jsonObject!=null){
                return jsonObject.getString("data");
            }
        }catch (Exception e){
            return output;
        }
        return output;
    }
}
