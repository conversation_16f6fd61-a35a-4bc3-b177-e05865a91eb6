package com.fytec.controller.open.agent;

import cn.dev33.satoken.oauth2.annotation.SaCheckClientIdSecret;
import cn.dev33.satoken.oauth2.annotation.SaCheckClientToken;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.agent.*;
import com.fytec.entity.agent.AgentPublishHistory;
import com.fytec.service.agent.AgentService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

@Slf4j
@Validated
@Tag(name = "智能体执行")
@RequestMapping("/fytec/api/agent")
@RestController
@RequiredArgsConstructor
public class AgentExecuteController {
    private final AgentService agentService;

    @PostMapping(value = "/publish/execute")
    @Operation(summary = "执行发布智能体")
    @SaCheckClientIdSecret
    public void executePublishedAgent(HttpServletResponse response, @Validated @RequestBody AgentExecuteDTO dto) {
        agentService.executePublishedAgent(response, dto);
    }

    @GetMapping(value = "/publish/history")
    @Operation(summary = "智能体对话历史")
    @SaCheckClientToken(scope = "client:execute")
    public R<List<Map<String, Object>>> queryPublishedAgentHistory(@NotNull(message = "智能体会话ID不能为空") String conversationId,
                                                                   Long agentPublishId,
                                                                   Long messageId,
                                                                   String userId,
                                                                   Page<AgentPublishHistory> page) {
        return R.ok(agentService.queryPublishedAgentHistory(page, conversationId, agentPublishId, messageId, userId));
    }

    @GetMapping(value = "/publish/history/clear")
    @Operation(summary = "清除智能体历史")
    @SaCheckClientToken(scope = "client:execute")
    public R<Void> clearPublishHistory(@NotNull(message = "智能体会话ID不能为空") String conversationId,
                                       Long agentPublishId,
                                       String userId) {
        agentService.clearPublishHistory(conversationId, agentPublishId, userId);
        return R.ok();
    }


}
