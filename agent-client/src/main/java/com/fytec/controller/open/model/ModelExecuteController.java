package com.fytec.controller.open.model;

import cn.dev33.satoken.oauth2.annotation.SaCheckClientIdSecret;
import com.fytec.dto.llm.ModelProcessDTO;
import com.fytec.dto.llm.ModelProcessExtDTO;
import com.fytec.service.model.ModelExecuteService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Slf4j
@Validated
@Tag(name = "智能体执行")
@RequestMapping("/fytec/api/model")
@RestController
@RequiredArgsConstructor
public class ModelExecuteController {
    private final ModelExecuteService modelExecuteService;

    @PostMapping(value = "/execute/non-stream")
    @Operation(summary = "执行模型-非流方式")
    @SaCheckClientIdSecret
    public R<Map<String, Object>> processModelByNonStream(@Validated @RequestBody ModelProcessExtDTO dto) {
        return R.ok(modelExecuteService.processModelByNonStream(dto));
    }

    @PostMapping(value = "/execute/stream")
    @Operation(summary = "执行模型-流方式")
    @SaCheckClientIdSecret
    public void processModelByStream(HttpServletResponse response, @Validated @RequestBody ModelProcessExtDTO dto) {
        modelExecuteService.processModelByStream(response, dto);
    }
}
