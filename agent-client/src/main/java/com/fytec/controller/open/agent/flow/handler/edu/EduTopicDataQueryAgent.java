package com.fytec.controller.open.agent.flow.handler.edu;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONException;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fytec.constant.AgentFlowConstants;
import com.fytec.controller.open.agent.flow.protocol.AgentHandlerError;
import com.fytec.controller.open.agent.flow.protocol.IAgentHandler;
import com.fytec.controller.open.agent.flow.protocol.IHandlerContext;
import com.fytec.controller.open.agent.flow.utils.SqlQueryUtils;
import com.fytec.controller.open.agent.flow.utils.dataquery.dto.Output;
import com.fytec.controller.open.agent.flow.utils.edu.GatewayUtils;
import com.fytec.controller.open.agent.flow.utils.functioncall.*;
import com.fytec.dto.llm.ModelCallDTO;
import com.fytec.utils.AiFormatJsonUtil;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
@Slf4j
public class EduTopicDataQueryAgent implements IAgentHandler, IFunctionCall {
    @Value("${env.type}")
    private String envType;
    @Value("${env.code}")
    private String envCode;

    @Override
    public Long getAgentId() {
        if("jiaoyu".equals(envType) && "test".equals(envCode)){
            return 92L;// 区分一下环境
        }
        return 114L;
    }


    @Override
    public void beforeHandle(IHandlerContext context) throws AgentHandlerError {
        IAgentHandler.super.beforeHandle(context);
        log.info("EduTopicDataQueryAgent: 执行 function call");

        List<FCallResultDTO> fCallResultDTOS = FunctionCallFactory.callFunction(context.getFunctionCallService(), context, this,this);
        log.info("EduTopicDataQueryAgent: 执行 function call len:{}",fCallResultDTOS==null?0:fCallResultDTOS.size());
//        if(fCallResultDTOS.size()>0){
//            log.info("{}",JSON.);
//        }
        if(fCallResultDTOS!=null){
//            if(!fCallResultDTOS.isEmpty()){
//                printThink(context, String.format("判断使用%s个工具", fCallResultDTOS.size()));
//            }

            List<String> appendInfos = new LinkedList<>();
            for (FCallResultDTO fCallResultDTO : fCallResultDTOS) {
                if(StringUtils.isNotBlank(fCallResultDTO.getResult())){
                    String info = String.format("%s工具返回的数据为:", fCallResultDTO.getMetaInfo().getTitle()) + fCallResultDTO.getResult();
//                    printThink(context,info);
                    appendInfos.add(info);
                } else if(StringUtils.isNotBlank(fCallResultDTO.getErrorMsg())){
                    String info = String.format("工具 %s 结果错误:%s", fCallResultDTO.getMetaInfo().getTitle(),fCallResultDTO.getErrorMsg());
                    log.info(info);

                }else{
                     String info =String.format("工具 %s 结果为空", fCallResultDTO.getMetaInfo().getTitle());
                    log.info(info);
                }
            }
            if(!appendInfos.isEmpty()){
                context.setInput("【已知补充信息开始】\n"+ StringUtils.join(appendInfos,"\n")+"\n【已知补充信息结束】" + context.getInput());
            }

        }
    }

    @Override
    public String getDesc() {
        return "获取主题数据的";
    }
    private Map<String,SqlTableInfo> toolSqlMap = new HashMap<>();
    @Data
    private static class SqlTableInfo{
        private String tableName;//表名字
        private String tableSql;//表sql
        public SqlTableInfo(String tableName,String tableSql){
            this.tableName = tableName;
            this.tableSql = tableSql;
        }
    }
    private FCallResultDTO execByTableInfo(IHandlerContext context,String id, SqlTableInfo tableInfo) throws Exception {
        FCallResultDTO fCallResultDTO = new FCallResultDTO();
        // 调用智能体生成sql，并执行
        long startTime = System.currentTimeMillis();
        log.info("执行工具表:" + tableInfo.getTableName());
        String sql = getSqlByTableInfo(context,tableInfo);
        if(StringUtils.isNotBlank(sql)){
            log.info("生成查询sql:" + sql);
            sql = AiFormatJsonUtil.formatSql(sql);
            Output output = SqlQueryUtils.execSqlQuery(context, sql, 20);

            FCallDTO fCallDTO = context.getFCallDTOMap().get(id);
            fCallResultDTO.setMetaInfo(fCallDTO);
            fCallResultDTO.setResult(fCallResultDTO.getMetaInfo().getTitle()+"工具的数据结果为:\t"
                    + (output.getList()!=null&& !output.getList().isEmpty() ? JSON.toJSONString(output.getList()):"暂无数据"));
            // 解读作为最后一个节点，这边把可能的图表存储下来
            output.setDataSourceType(AgentFlowConstants.DataSourceType.sql.name());
            printExtraInfo(context,System.currentTimeMillis() - startTime, AgentFlowConstants.AgentExtraInfoProtocol.echart.name(),JSON.toJSONString(output));
        }
        return fCallResultDTO;
    }
    private static final String SQL_PROMPT = """
### 人设
你是一位专业的SQL生成专家，擅长根据给定的表结构和用户提问生成可执行的MySQL查询语句

## 核心技能
1. **表结构解析**：精准理解提供的表结构及字段定义（包括字段名、数据类型、COMMENT注释）
2. **字段映射**：将用户提问中的自然语言描述映射到对应的数据库字段
3. **条件转换**：将用户输入的查询条件转化为SQL WHERE子句
4. **SQL构建**：根据查询需求生成符合MySQL语法规范的完整SQL语句
## 已知表结构
{TABLE_INFP}
## 输出规范
1.直接返回最终可执行的MySQL SQL语句，无需附加任何解释或说明
请严格按照上述规则生成SQL语句，确保字段映射准确、语法规范且符合数据权限要求。知识库内容仅供参考。
""";

    private String getSqlByTableInfo(IHandlerContext context, SqlTableInfo tableInfo) {
        // 执行模型能力，和智能体用一个模型吧？
        String prompt = SQL_PROMPT.replace("{TABLE_INFP}",tableInfo.getTableSql());

        ModelCallDTO modelCallDTO = context.getFunctionCallService().autoFunctionCallContext(prompt, context.getInput());

        return modelCallDTO.getHistory().toString();
    }

    public EduTopicDataQueryAgent(){
        {
            SqlTableInfo info = new SqlTableInfo("v_area_ydtj", v_area_ydtj);
            toolSqlMap.put(info.getTableName(),info);
        }
        {
            SqlTableInfo info = new SqlTableInfo("v_teacher_jyrczb", v_teacher_jyrczb);
            toolSqlMap.put(info.getTableName(),info);
        }
        {
            SqlTableInfo info = new SqlTableInfo("v_yjzs", v_yjzs);
            toolSqlMap.put(info.getTableName(),info);
        }
        {
            SqlTableInfo info = new SqlTableInfo("v_hot_resource", v_hot_resource);
            toolSqlMap.put(info.getTableName(),info);
        }
        {
            SqlTableInfo info = new SqlTableInfo("v_area_development", v_area_development);
            toolSqlMap.put(info.getTableName(),info);
        }
        {
            SqlTableInfo info = new SqlTableInfo("v_area_bklq", v_area_bklq);
            toolSqlMap.put(info.getTableName(),info);
        }
        {
            SqlTableInfo info = new SqlTableInfo("v_area_illness", v_area_illness);
            toolSqlMap.put(info.getTableName(),info);
        }
        {
            SqlTableInfo info = new SqlTableInfo("v_area_platform", v_area_platform);
            toolSqlMap.put(info.getTableName(),info);
        }
    }

    // 10种资源，2种通过接口来的，8种通过数据库获取的，需要配置8个智能体和8个知识库处理一下。生成sql并进行处理结果集
    @FCall(
            title = "平台访问情况",
            id="getAreaPlatform",
            desc = "包括如下问题：1、请查询易加分析平台目前累计的访问情况"
    )
    public FCallResultDTO getAreaPlatform(IHandlerContext context) throws Exception {
        return execByTableInfo(context,"getAreaPlatform", toolSqlMap.get("v_area_platform"));
    }
    @FCall(
            id="getIllness",
            title = "区域病情",
            desc = "包括如下问题：1、请查询最近1周/2周景城学校感冒人数\n" +
                    "2、请查询上个月所有小学有多少学生感冒发烧"
    )
    public FCallResultDTO getIllness(IHandlerContext context) throws Exception {
        return execByTableInfo(context,"getIllness", toolSqlMap.get("v_area_illness"));
    }
    @FCall(
            id="getBklq",

            title = "本科/重本录取率",
            desc = "包括如下问题：1、请查询2024年园区高考本科的录取率"
    )
    public FCallResultDTO getBklq(IHandlerContext context) throws Exception {
        return execByTableInfo(context,"getBklq", toolSqlMap.get("v_area_bklq"));
    }
    @FCall(
            title = "发展历程",
            id="getAreaDevelopment",

            desc = "包括如下问题：1、请查询2023年园区教育发生了哪几件大事"
    )
    public FCallResultDTO getAreaDevelopment(IHandlerContext context) throws Exception {
        return execByTableInfo(context,"getAreaDevelopment", toolSqlMap.get("v_area_development"));
    }
    @FCall(
            id="getHot",

            title = "热门资源统计",
            desc = "包括如下问题：1、请查询XX学校当月最热门的5个语文学科教学资源\n" +
                    "2、请查询XX学校2025年最热门的数学学科教学资源"
    )
    public FCallResultDTO getHot(IHandlerContext context) throws Exception {
        return execByTableInfo(context,"getHot",toolSqlMap.get("v_hot_resource"));
    }


    @FCall(
            id="getYjzs",

            title = "小五星评价汇总统计",
            desc = "包括如下问题：1、请查询2024年初中学校思想品德指标平均得分\n" +
                    "2、请查询第一中学近2年小五星评价得分情况"
    )
    public FCallResultDTO getYjzs(IHandlerContext context) throws Exception {
        return execByTableInfo(context,"getYjzs", toolSqlMap.get("v_yjzs"));
    }

    @FCall(
            id="getYdtj",

            title = "流动性情况",
            desc = "包括如下问题：1、请查询2024年有多少教师离职，有多少教师退休\n" +
                    "2、请查询最近两年小学生毕业人数"
    )
    public FCallResultDTO getYdtj(IHandlerContext context) throws Exception {
        return execByTableInfo(context,"getYdtj", toolSqlMap.get("v_area_ydtj"));
    }
    @FCall(
            id="getTeahcerInfo",

            title = "教师人才指数",
            desc = "包括如下问题：1、请查询近5年教育人才绩效指数\n" +
                    "2、请查询2023年小学平均人才指数得分\n" +
                    "3、请查询XXX学校近3年人才指标得分趋势"
    )
    public FCallResultDTO getTeahcerInfo(IHandlerContext context) throws Exception {
        return execByTableInfo(context,"getTeahcerInfo", toolSqlMap.get("v_teacher_jyrczb"));
    }

    @FCall(
            id="getEduResourceInfo",

            title = "教学资源情况统计",
            desc = "例如请查询景城小学总共上传了多少微云课、多少题库资源"
    )
    public FCallResultDTO getEduResourceInfo(IHandlerContext context) throws JsonProcessingException, UnirestException {
//        log.info("查询学校名称getEduResourceInfo:{}",name);
        String token = context.getThirdUserToken();
        Map<String,String> headers = GatewayUtils.buildDefaultHeader();
        FCallResultDTO fCallResultDTO = new FCallResultDTO();
        {
            JSONObject body = new JSONObject();
            body.put("token", token);
            body.put("resultType", "4");
            body.put("schoolid", "15063F3DB04D4BFEB11743896BBAE65F");// 默认景城学校

            ObjectMapper objectMapper = new ObjectMapper();
            String jsonBody = objectMapper.writeValueAsString(body);
            String turl = "http://portapigw.sipedu.cn/sipdata/API/V5/yijia_ztk/ztk_resource_statics/query?" +
                    "token={token}&resultType={resultType}&schoolid={schoolid}";
            HttpResponse<String> response = Unirest.get(turl)
                    .header("Content-Type", org.springframework.http.MediaType.APPLICATION_JSON_VALUE)
                    .headers(headers)
                    .routeParam("token", token)
                    .routeParam("resultType", "4")
                    .routeParam("schoolid", "15063F3DB04D4BFEB11743896BBAE65F")
                    .asString();
            JSONObject parseObj = JSON.parseObject(response.getBody());
            if (parseObj.containsKey("data")) {
//                JSONObject data = parseObj.getJSONObject("data");
//                fCallResultDTO.setResult(data.toJSONString());
                fCallResultDTO.setResult(response.getBody());
                context.getFunctionCallService().checkAndBuildEchartFormat(response.getBody(),context,this);

          } else {
                System.out.println(response.getBody());
            }
        }

        return fCallResultDTO;
    }

    @FCall(
            title = "学校规模数量",
            id="getEduSchoolInfo",

            desc = "包括不限于涉及这类问题时候可以获取数据：1、请查询近5年园区学校数量的变化\n" +
                    "2、请查询2023年有多少所公办幼儿园" +
                    "3、请查询近3年园区学生规模的变化",
            paramsDesc = {
                    "年份，默认为2024",
                    "字典值，必填参数,1表示查询学校、教师、学生数量,2表示查询各学段公办民办学校数量"
            }
    )
    public FCallResultDTO getEduSchoolInfo(IHandlerContext context,String year,String type) throws JsonProcessingException, UnirestException, AgentHandlerError {
        String token = context.getThirdUserToken();
        FCallResultDTO fCallResultDTO = new FCallResultDTO();
        Map<String,String> headers = GatewayUtils.buildDefaultHeader();
        {
//            JSONObject body = new JSONObject();
//            body.put("token", token);
//            body.put("year", year);
//            body.put("resultType", type);
            if(StringUtils.isBlank(token)){
                throw new AgentHandlerError("查询学校规模数量，token为空");
            }

//            ObjectMapper objectMapper = new ObjectMapper();
//            String jsonBody = objectMapper.writeValueAsString(body);
            String turl = "http://portapigw.sipedu.cn/sipdata/API/V5/ztk_school/query?" +
                    "token={token}&year={year}&resultType={resultType}";
            HttpResponse<String> response = Unirest.get(turl)
                    .header("Content-Type", org.springframework.http.MediaType.APPLICATION_JSON_VALUE)
                    .headers(headers)
                    .routeParam("token", token)
                    .routeParam("year", year)
                    .routeParam("resultType", type)
//                    .body(jsonBody)
                    .asString();
            JSONObject parseObj = JSON.parseObject(response.getBody());
            if (parseObj.containsKey("data")) {
//                JSONObject data = parseObj.getJSONObject("data");
//                fCallResultDTO.setResult(data.toJSONString());
                fCallResultDTO.setResult(response.getBody());
                context.getFunctionCallService().checkAndBuildEchartFormat(response.getBody(),context,this);


            } else {
                System.out.println(response.getBody());
            }
        }

        return fCallResultDTO;
    }

    private static final String v_hot_resource = """
            视图名称	字段名称	字段含义	字段类型	字段长度
v_hot_resource
(热门资源)	id	主键	varchar	50
	schoolid	学校ID	varchar	50
	xxmc	学校名称	varchar	200
	subjectname	学科	varchar	50
	title	资源标题	varchar	50
	year	年份	varchar	50
	month	月份	varchar	50
	hotcount	热度数量	int4	
	updatedate	更新时间	timestamp	
""";
    private static final String v_area_development = """
            视图名称	字段名称	字段含义	字段类型	字段长度
v_area_development
(区域发展历程)	id	主键ID	varchar	50
	year	年份	varchar	50
	publishtime	发布时间	varchar	50
	title	标题	varchar	200
	content	内容	varchar	2000
	url	相关新闻地址	varchar	200
	image	相关图片	varchar	200            
""";
    private static final String v_area_bklq = """
            视图名称	字段名称	字段含义	字段类型	字段长度
v_area_bklq
(区域本科录取率)	year	年份	varchar	50
	bk1plql	重点本科录取率	float8	
	bk2plql	本科录取率	float8	            
""";
    private static final String v_area_illness = """
            视图名称	字段名称	字段含义	字段类型	字段长度
v_area_illness
(区域病情汇总)	id	主键	varchar	50
	day	病情日期	varchar	50
	schoolid	学校ID	varchar	200
	xxmc	学校名称	varchar	200
	gmrs	感冒人数	int4	
	sbrs	水痘人数	int4	
	szkb	手足口病	int4	
	gzrs	骨折人数	int4	
	otrs	呕吐人数	int4	
	fsrs	发烧人数	int4	
	qtrs	其他人数	int4	
	xxlb	学校类别	varchar	200            
""";
    private static final String v_area_platform = """
            视图名称	字段名称	字段含义	字段类型	字段长度
v_area_platform
(平台访问情况)	name	平台名称	varchar	50
	totalcount	总访问量	int4	
	studentcount	学生访问量	int4	
	teachercount	教师访问量	int4	
	othercount	其他访问量	int4	
	type	统计维度 1按月 2按学期	int4	
	year	年份/学年	varchar	
	datevalue	月份/学期	varchar	            
""";

    private static final String v_yjzs = """
            视图名称	字段名称	字段含义	字段类型	字段长度
            v_area_yjzs
            (小五星区域汇总)	id	主键	varchar	
            	year	年份	numeric	
            	schooltypename	学段	varchar	
            	quotaname	指标名称	varchar	
            	parentquotaname	父指标名称	varchar	
            	score	得分	float8	
            	updatedate	更新时间	timestamp	
            	semester	学期	int4	
            v_school_yjzs
            (小五星学校汇总)	id	主键	varchar	
            	year	年份	numeric	
            	schoolid	学校ID	varchar	
            	xxmc	学校名称	varchar	200
            	quotaname	指标名称	varchar	
            	parentquotaname	父指标名称	varchar	
            	score	得分	float8	
            	semester	学期	int4	
            """;

    private static final String v_area_ydtj = """
            视图名称	字段名称	字段含义	字段类型	字段长度
            v_area_ydtj
            (师生流动性统计)	bdrq	年份	numeric	10
            	ydyy	异动原因	varchar	50
            	school_id	学校ID	varchar	50
            	xxmc	学校名称	varchar	200
            	xxlb    学校类别 varchar 字段的字典值为小学,特殊教育学校,其他,幼儿园,普通初中,中等职业学校,普通高中
            	ydrs	异动人数	numeric	10
            	type	1代表教师 2代表学生	numeric	10
            """;
    private static final String v_teacher_jyrczb = """
            视图名称	字段名称	字段含义	字段类型	字段长度
                v_teacher_jyrczb
                  (教师人才指数)	id	主键ID	varchar	20
            	year	年份	numeric	2
            	schoolid	学校ID	varchar	50
            	xxmc	学校名称	varchar	200
            	ggjszs	骨干教师指数	float8	
            	jxnlyjxcgzs	教学能力与教学成果指数	float8	
            	dyglrczs	德育管理人才指数	float8	
            	jyjsrczs	教育技术（保障）人才指数	float8	
            	jygjhzs	教育国际化指数	float8	
            	gxljszs	高学历教师指数	float8	
            	gzcjszs	高职称教师指数	float8	
            	qnggjszs	青年骨干教师指数	float8	
            	qnglrxzs	青年管理人才指数	float8	
            	qyrcjlzs	区域人才交流指数	float8	
            	rcczhjzs	人才成长环境指数	float8	
            	xztdzhzs	行政团队综合指数	float8	
            	jyrcjxzs	教育人才绩效指数	float8	
            	jyrcwdzs	教育人才稳定指数	float8	
            	gxzbhz	刚性指标汇总	float8	
            	rxzbhz	柔性指标汇总	float8	
            	total	总分	float8	
            	updatedate	更新时间	timestamp	
            	tablestatus	数据状态 0正常 9删除	numeric	
            """;
}
