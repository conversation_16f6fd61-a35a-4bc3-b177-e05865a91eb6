package com.fytec.controller.open.agent.flow.handler.edu;

import com.fytec.controller.open.agent.flow.protocol.AgentHandlerError;
import com.fytec.controller.open.agent.flow.protocol.IAgentHandler;
import com.fytec.controller.open.agent.flow.protocol.IHandlerContext;
import com.fytec.handler.IResponseHandlerProxy;
import com.fytec.utils.AiFormatJsonUtil;

public class EduNavQueryAgent implements IAgentHandler {
    @Override
    public Long getAgentId() {
//        return 7L;
        return 1L;
    }

    @Override
    public void handle(IHandlerContext context) throws AgentHandlerError {
        IAgentHandler.super.handle(context);
        if (context.getOutput().startsWith("```json")) {
            String out = AiFormatJsonUtil.formatJson(context.getOutput());
            context.setOutput(out);
            if (context.getHistoryDTO() != null) {
                // 需要更新
                context.getAgentFlowService().updateHistoryAnswer(context.getOutput(), context.getHistoryDTO().getId());
            }
        }
    }

    @Override
    public String getDesc() {
        return "小易助手-快捷/应用入口";
    }
}
