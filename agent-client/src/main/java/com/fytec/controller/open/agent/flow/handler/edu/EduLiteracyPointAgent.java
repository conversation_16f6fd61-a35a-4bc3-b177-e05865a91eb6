package com.fytec.controller.open.agent.flow.handler.edu;

import com.alibaba.fastjson2.JSON;
import com.fytec.controller.open.agent.flow.protocol.AgentHandlerError;
import com.fytec.controller.open.agent.flow.protocol.IAgentHandler;
import com.fytec.controller.open.agent.flow.protocol.IHandlerContext;
import com.fytec.controller.open.agent.flow.utils.dataquery.DatabaseHelper;
import com.fytec.controller.open.agent.flow.utils.dataquery.DatabaseUtil;
import com.fytec.controller.open.agent.flow.utils.functioncall.FCallResultDTO;
import com.fytec.controller.open.agent.flow.utils.functioncall.FunctionCallFactory;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.ZonedDateTime;
import java.util.*;

/**
 * 素养点智能体
 * 先查询得到数据，然后再处理
 * 需要区分是否传入学生，如果传入学生只需要查询单个学生数据，如果涉及班级，查询班级数据后计算众数等
 *
 * 根据检索条件【学段-学科-年级-班级-学生(学生可选)】获取班级和学生的素养点达成度
 班级知识点-》
 2个维度素养点/知识点，学生的、班级的

 Sql语句如下：
 学科素养点：
 select * from ai_business_subject_literacy t where t.stage_code = 3 and subject_code = 316 and delete_flag=0 and subject_literacy_level = 1;

 班级素养点达成度：
 select * from ai_class_degree t where t.stage_code = 2 and subject_code = 213 and node_type = 3 and parent_ids='-1' and delete_flag=0 and class_id = 'ssdf';
 学生素养点达成度：
 select * from ai_student_degree t where stage_code = 3 and t.subject_code = 316 and node_type = 3 and parent_ids='-1' and delete_flag=0 and t.person_id= 'f167ce8748ce4c4aa4d0be84bff22cc9';

 学科知识点
 select * from ai_book_info t where t.stage_code = 3 and subject_code = 316 and t.type=2 and LENGTH(parent_ids)=35 and delete_flag=0;
 班级知识点达成
 select * from ai_class_degree t where t.stage_code = 3 and subject_code = 316 and node_type = 2 and LENGTH(parent_ids)=35 and delete_flag=0 and class_id = 'a9b9dbc4963348f4bfc7249f3903264a';
 学生知识点达成
 select * from ai_student_degree t where stage_code = 3 and t.subject_code = 316 and node_type = 2 and LENGTH(parent_ids)=35 and t.person_id= 'f34cf16995524681845127990c6ae4ab';

 一个班级70个素颜点和知识点、50条每一个学生对应一条数据，70*学生数。

 中位数、众数、最大值、最小值、平均值。
 */
@Slf4j
public class EduLiteracyPointAgent implements IAgentHandler {
    private final String typeLiteracyCode = "3";
    private final String typePointCode = "2";

    @Override
    public void beforeHandle(IHandlerContext context) throws AgentHandlerError {
        IAgentHandler.super.beforeHandle(context);
        log.info("EduLiteracyPointAgent: 执行 数据查询");
        QueryLiteracyPointDTO queryDto = new QueryLiteracyPointDTO();
        if(context.getConfigs().containsKey(""+getAgentId())){
            Object o = context.getConfigs().get("" + getAgentId());
            if (((Map) o).containsKey("query")) {
                Map queryMap = (Map) ((Map) o).get("query");
                // 把value变成从常量里查询
                queryDto.setType(queryMap.getOrDefault("type","").toString());
                queryDto.setStageCode(queryMap.getOrDefault("stageCode","").toString());
                queryDto.setSubjectCode(queryMap.getOrDefault("subjectCode","").toString());
                queryDto.setClassId(queryMap.getOrDefault("classId","").toString());
                queryDto.setStudentId(queryMap.getOrDefault("studentId","").toString());
            }
        }
//        queryDto.setStageCode("3");
//        queryDto.setSubjectCode("316");
//        queryDto.setClassId("a9b9dbc4963348f4bfc7249f3903264a");
//        queryDto.setStudentId("f34cf16995524681845127990c6ae4ab");
        if(StringUtils.isBlank(queryDto.getClassId())
                || StringUtils.isBlank(queryDto.getSubjectCode())
                || StringUtils.isBlank(queryDto.getSubjectCode())
//                || StringUtils.isBlank(queryDto.getStudentId())
        ){
            if(context.getConfigs()!=null)
                log.error(JSON.toJSONString(context.getConfigs()));
            throw new AgentHandlerError("查询素养点/知识点数据必填项为空");
        }

        List<String> printStrList = null;
        try {
            printStrList = buildData(context,queryDto);
        } catch (Exception e) {
            e.printStackTrace();
            throw new AgentHandlerError("查询素养点/知识点数据失败,"+e.getMessage());
        }
        if(printStrList.isEmpty()){
            throw new AgentHandlerError("班级数据为空");
        }
        printStrList.addFirst("【已知素养点/知识点情况】");
        System.out.println(StringUtils.join(printStrList,"\n"));
        context.setInput(StringUtils.join(printStrList,"\n") + context.getInput());

    }


    @Override
    public Long getAgentId() {
//        return 94L;
        return 116L;

    }

    private String literacy_all_format = "select * from ai_book_info t where t.stage_code =  '%s' and subject_code =  '%s' and t.type=2 and LENGTH(parent_ids)=35 and delete_flag=0 order by sort_i;";
    private String point_all_format = " select * from ai_business_subject_literacy t where t.stage_code = '%s' and subject_code = '%s' and delete_flag=0 and subject_literacy_level = 1;";
    
//    public static void main(String[] args) throws Exception {
//        EduLiteracyPointAgent agent = new EduLiteracyPointAgent();
//        QueryLiteracyPointDTO queryDto = new QueryLiteracyPointDTO();
//
//        queryDto.setStageCode("3");
//        queryDto.setSubjectCode("316");
//        queryDto.setClassId("a9b9dbc4963348f4bfc7249f3903264a");
////        queryDto.setStudentId("f34cf16995524681845127990c6ae4ab");
//
//        IHandlerContext context = new IHandlerContext();
//        List<String> printStrList = agent.buildData(context,queryDto);
//        if(printStrList.isEmpty()){
//            System.out.println("班级数据为空");
//        }
//        printStrList.addFirst("【已知素养点/知识点情况】");
//        System.out.println(StringUtils.join(printStrList,"\n"));
//
//    }

    @Getter
    private enum PointType {
        literacy_class("班级素养点","班级素养点",
                "select * from ai_class_degree t where t.stage_code = '%s' and subject_code = '%s' and node_type = 3 and parent_ids='-1' and delete_flag=0 and class_id = '%s';"),
        point_class("班级知识点","班级知识点",
                "select * from ai_class_degree t where t.stage_code = '%s' and subject_code = '%s' and node_type = 2 and LENGTH(parent_ids)=35 and delete_flag=0 and class_id = '%s';"),
        literacy_student_one("学生素养点","根据某个学生id查询素养点",
                "select * from ai_student_degree t where stage_code = '%s' and t.subject_code = '%s' and node_type = 3 and parent_ids='-1' and delete_flag=0 and t.person_id= '%s';"),
        literacy_student_class("学生素养点","根据班级查询学生素养点",
                "select * from ai_student_degree t where stage_code = '%s' and t.subject_code = '%s' and node_type = 3 and parent_ids='-1' and delete_flag=0 and t.class_id= '%s';"),
        point_student_one("学生知识点","根据某个学生id查询学生知识点",
                "select * from ai_student_degree t where stage_code = '%s' and t.subject_code = '%s' and node_type = 2 and LENGTH(parent_ids)=35 and t.person_id= '%s';"),
        point_student_class("学生知识点","根据班级查询学生知识点",
                "select * from ai_student_degree t where stage_code = '%s' and t.subject_code = '%s' and node_type = 2 and LENGTH(parent_ids)=35 and t.class_id= '%s';"),

        ;
        private String desc;
        private String key;
        private String name;
        private String sqlFormat;

        PointType(String name,String desc,String sqlFormat){
            this.desc = desc;
            this.name = name;
            this.sqlFormat = sqlFormat;
        }
    }
    /**
     * 获取数据，先获取班级的数据，再获取学生或者学生的平均数据。素养点与知识点情况
     * 查询知识点/素养点达成后，还需要得到全部的相关的
     *
     */
    private List<String> buildData(IHandlerContext context, QueryLiteracyPointDTO queryDto) throws Exception {
        String sql_literacy_class_query = String.format(PointType.literacy_class.sqlFormat,queryDto.getStageCode(),queryDto.getSubjectCode(),queryDto.getClassId());
        String sql_point_class_query = String.format(PointType.point_class.sqlFormat,queryDto.getStageCode(),queryDto.getSubjectCode(),queryDto.getClassId());

        // 查询结果是各个素养点以及达成情况，需要把结果集变成实际json或者记录
//        String sql_literacy_all = String.format(literacy_all_format,queryDto.getStageCode(),queryDto.getSubjectCode());


        DatabaseUtil d = getDefaultSqlConnect();

        long startTime = System.currentTimeMillis();
        // 记录节点
        Map<String,LiteracyPointClassItem> mapNodeId = new LinkedHashMap<>();
        List<LiteracyPointClassItem> literacyItemList =  null;
        if(StringUtils.isBlank(queryDto.getType())||typeLiteracyCode.equals(queryDto.getType())){
            List<Map<String, Object>> resultsLiteracy = d.executeQuery(sql_literacy_class_query, null);
            literacyItemList  = queryItems(resultsLiteracy);
            for (com.fytec.controller.open.agent.flow.handler.edu.EduLiteracyPointAgent.LiteracyPointClassItem literacyPointClassItem : literacyItemList) {
                mapNodeId.put(literacyPointClassItem.getId(),literacyPointClassItem);
            }
        }

        List<LiteracyPointClassItem> pointItemList =  null;
        if(StringUtils.isBlank(queryDto.getType())||typePointCode.equals(queryDto.getType())){
            List<Map<String, Object>> resultsPoint = d.executeQuery(sql_point_class_query, null);
            pointItemList  =queryItems(resultsPoint);
            for (com.fytec.controller.open.agent.flow.handler.edu.EduLiteracyPointAgent.LiteracyPointClassItem literacyPointClassItem : pointItemList) {
                mapNodeId.put(literacyPointClassItem.getId(),literacyPointClassItem);
            }
        }

        long classRunTime = System.currentTimeMillis();
        printThink(context,classRunTime - startTime,"查询班级素养点/知识点");

        // 输出记录
        List<String> printStrList  = new LinkedList<>();
        // 判断是否有学生id
        if(StringUtils.isNotBlank(queryDto.getStudentId())){
            // 只查询单个学生的2个数据
            // 查询全部的学生信息，但是以后缀的方式加到班级知识点中吧。
            List<LiteracyPointStudentItem> literacyStudentItemList = null;
            if(StringUtils.isBlank(queryDto.getType())||typeLiteracyCode.equals(queryDto.getType())) {
                String sql_literacy_stu_query = String.format(PointType.point_student_one.sqlFormat,queryDto.getStageCode(),queryDto.getSubjectCode(),queryDto.getClassId());
                List<Map<String, Object>> resultsStudentAllLiteracy = d.executeQuery(sql_literacy_stu_query, null);
                literacyStudentItemList = queryStudentItems(resultsStudentAllLiteracy);
            }
            List<LiteracyPointStudentItem> pointStudentItems = null;
            if(StringUtils.isBlank(queryDto.getType())||typePointCode.equals(queryDto.getType())) {
                String sql_point_stu_query = String.format(PointType.literacy_student_one.sqlFormat,queryDto.getStageCode(),queryDto.getSubjectCode(),queryDto.getClassId());
                List<Map<String, Object>> resultsStudentAllPoint = d.executeQuery(sql_point_stu_query, null);
                pointStudentItems = queryStudentItems(resultsStudentAllPoint);
            }

            //  拼接学生数据
            String name = null;
            List<String> stuDescList = new LinkedList<>();
            if(literacyStudentItemList!=null && !literacyStudentItemList.isEmpty()){
                name = literacyStudentItemList.getFirst().getName();
                for (com.fytec.controller.open.agent.flow.handler.edu.EduLiteracyPointAgent.LiteracyPointStudentItem item : literacyStudentItemList) {
                    stuDescList.add(String.format("\n素养点%s的达成情况: %s", item.getNodeName(),item.getDegree()*100));
                }
            }
            if(pointStudentItems!=null && !pointStudentItems.isEmpty()){
                name = pointStudentItems.getFirst().getName();
                for (com.fytec.controller.open.agent.flow.handler.edu.EduLiteracyPointAgent.LiteracyPointStudentItem item : pointStudentItems) {
                    stuDescList.add(String.format("\n知识点点%s的达成情况: %s", item.getNodeName(),item.getDegree()*100));
                }
            }
            if(name!=null){
                String formatName = "知识点/素养点";
                if(typePointCode.equals(queryDto.getType())){
                    formatName = "知识点";
                }else if(typeLiteracyCode.equals(queryDto.getType())){
                    formatName = "素养点";
                }
                stuDescList.addFirst(String.format("学生%s的%s达成情况:",name,formatName));
            }
            if(!stuDescList.isEmpty())
                printStrList.addAll(stuDescList);
        }else{
            if(StringUtils.isBlank(queryDto.getType())||typeLiteracyCode.equals(queryDto.getType())) {
                String sql_literacy_stu_query = String.format(PointType.point_student_class.sqlFormat,queryDto.getStageCode(),queryDto.getSubjectCode(),queryDto.getClassId());
                List<Map<String, Object>> resultsStudentAllLiteracy = d.executeQuery(sql_literacy_stu_query, null);
                List<LiteracyPointStudentItem> literacyStudentItemList = queryStudentItems(resultsStudentAllLiteracy);
                // 把学生数据放入到不同节点上，并计算值。理论上节点id不会冲突
                for (com.fytec.controller.open.agent.flow.handler.edu.EduLiteracyPointAgent.LiteracyPointStudentItem item : literacyStudentItemList) {
                    String nodeId = item.getNodeId();
                    LiteracyPointClassItem literacyPointClassItem = mapNodeId.get(nodeId);
                    if(literacyPointClassItem==null){
                        printThink(context, String.format("查询素养点时数据存在异常,学生%s未在此班级下", item.getName()));
                    }else {
                        literacyPointClassItem.getValues().add(item.getDegree());
                    }
                }
            }
            if(StringUtils.isBlank(queryDto.getType())||typePointCode.equals(queryDto.getType())) {
                String sql_point_stu_query = String.format(PointType.literacy_student_class.sqlFormat,queryDto.getStageCode(),queryDto.getSubjectCode(),queryDto.getClassId());
                List<Map<String, Object>> resultsStudentAllPoint = d.executeQuery(sql_point_stu_query, null);
                List<LiteracyPointStudentItem> pointStudentItems = queryStudentItems(resultsStudentAllPoint);
                for (com.fytec.controller.open.agent.flow.handler.edu.EduLiteracyPointAgent.LiteracyPointStudentItem item : pointStudentItems) {
                    String nodeId = item.getNodeId();
                    LiteracyPointClassItem literacyPointClassItem = mapNodeId.get(nodeId);
                    if(literacyPointClassItem==null){
                        printThink(context, String.format("查询知识点时数据存在异常,学生%s未在此班级下", item.getName()));
                    }else {
                        literacyPointClassItem.getValues().add(item.getDegree());
                    }
                }
            }
            // 查询全部的学生信息，但是以后缀的方式加到班级知识点中吧。
            // 计算全部节点
            for (String nodeId : mapNodeId.keySet()) {
                LiteracyPointClassItem nodeItem = mapNodeId.get(nodeId);
                if(!nodeItem.getValues().isEmpty()){
                    nodeItem.calculateDimensions();
                }
            }
        }
        printThink(context,System.currentTimeMillis() - classRunTime,"查询学生素养点/知识点");

        // 输出
        if(literacyItemList!=null && !literacyItemList.isEmpty()){
            printStrList.add("班级素养点达成情况:");
            for (com.fytec.controller.open.agent.flow.handler.edu.EduLiteracyPointAgent.LiteracyPointClassItem item : literacyItemList) {
                printStrList.add(item.toTypeString());
            }
        }
        if(pointItemList!=null && !pointItemList.isEmpty()){
            printStrList.add("班级知识点达成情况:");
            for (com.fytec.controller.open.agent.flow.handler.edu.EduLiteracyPointAgent.LiteracyPointClassItem item : pointItemList) {
                printStrList.add(item.toTypeString());
            }
        }

        d.closeDataSource();

       return printStrList;
    }



    private  List<LiteracyPointClassItem> queryItems(List<Map<String, Object>> results){
        List<LiteracyPointClassItem> itemList =  new LinkedList<LiteracyPointClassItem>();
        Map<String,String> itemId2Name = new HashMap<>();
        for (Map<String, Object> result : results) {
            LiteracyPointClassItem e = new LiteracyPointClassItem();
            e.setId(result.getOrDefault("node_id","").toString());
            e.setName(result.getOrDefault("node_name","").toString());
            String parentId = result.getOrDefault("parent_id", "").toString();
            if(!"-1".equals(parentId)){
                e.setParentId(parentId);
            }
            // 素养点的达成情况
            {
                String currentDegre = result.getOrDefault("current_degree", "0").toString();
                if("-1.0".equals(currentDegre)){
                    currentDegre = "0";
                }
                e.setCurrentDegre(Float.parseFloat(currentDegre));
            }
            {
                String totalDegre = result.getOrDefault("total_degree", "0").toString();
                if("-1.0".equals(totalDegre)){
                    totalDegre = "0";
                }
                e.setTotalDegre(Float.parseFloat(totalDegre));
            }
            itemList.add(e);
            itemId2Name.put(e.getId(),e.getName());
        }
        // id变成名称
        for (com.fytec.controller.open.agent.flow.handler.edu.EduLiteracyPointAgent.LiteracyPointClassItem item : itemList) {
            if(item.getParentId()!=null)
                item.setParentName(itemId2Name.getOrDefault(item.getParentId(),null));
        }
        return itemList;
    }
    private  List<LiteracyPointStudentItem> queryStudentItems(List<Map<String, Object>> results){
        List<LiteracyPointStudentItem> itemList =  new LinkedList<LiteracyPointStudentItem>();
         for (Map<String, Object> result : results) {
            LiteracyPointStudentItem e = new LiteracyPointStudentItem();
             e.setNodeId(result.getOrDefault("person_id","").toString());
             e.setName(result.getOrDefault("person_na","").toString());

             e.setNodeId(result.getOrDefault("node_id","").toString());
             e.setNodeName(result.getOrDefault("node_name","").toString());

            // 达成情况
             String currentDegre = result.getOrDefault("degree", "0").toString();
             if("-1.0".equals(currentDegre)){
                 currentDegre = "0";
             }
             e.setDegree(Float.parseFloat(currentDegre));
            itemList.add(e);
         }
        return itemList;
    }

    private DatabaseUtil getDefaultSqlConnect() throws Exception {
        Map<String,String> sqlConfigs = new HashMap<>();
        String version = sqlConfigs.getOrDefault("version", "8");
        String host = sqlConfigs.getOrDefault("host", "***********");
        String port = sqlConfigs.getOrDefault("port", "3306");
//        String port = sqlConfigs.getOrDefault("port", "443");
        String schema = sqlConfigs.getOrDefault("schema", "yj_xueyuan");
        String username = sqlConfigs.getOrDefault("username", "root");
        String password = sqlConfigs.getOrDefault("password", "Yjjy_fykj_7890");

        return DatabaseHelper.build("mysql", version,
                host,
                port,
                schema,
                username,
                password);
    }
    @Data
    private static class LiteracyPointClassItem{
        private String id;
        private String name;
        private String parentName;
        private String parentId;
        private Float currentDegre;
        private Float totalDegre;

        private List<Float> values = new LinkedList<>();// 全部的

        private Float[] valuesArray;
        private Float valueMin;// 最小值
        private Float valueMax;// 最大值
        private Float valueMode;// 众数
        private Float valueAvg;// 平均数
        private Float valueMedian;// 中位数

        public String toTypeString() {
            // 输出本节点情况，如果还有具体值，输出几个维度的
            StringBuilder sb = new StringBuilder();
            if(StringUtils.isNotBlank(getParentName())){
                sb.append("父节点:").append(getParentName()).append(",");
            }
            sb.append("名称:").append(getName());
            sb.append(",当成节点完成度:").append(getCurrentDegre());
            if(StringUtils.isBlank(getParentName())){
                sb.append(",包含下属节点的总节点完成度:").append(getTotalDegre());
            }
            if(!getValues().isEmpty()){
                // 说明有几个维度数据
                sb.append(",细节情况:");
                sb.append("最小值:").append(getValueMin());
                sb.append(",最大值:").append(getValueMax());
                sb.append(",众数:").append(getValueMode());
                sb.append(",平均数:").append(getValueAvg());
                sb.append(",中位数:").append(getValueMedian());
            }
            return sb.toString();
        }

        public void calculateDimensions() {
            if (values.isEmpty()) {
                return;
            }
            valuesArray = values.toArray(new Float[0]);
            valueMin = Collections.min(values);
            valueMax = Collections.max(values);
            valueAvg = calculateAverage(valuesArray);
            valueMedian = calculateMedian(valuesArray);
            valueMode = calculateMode(valuesArray);
        }

        private float calculateAverage(Float[] valuesArray) {
            float sum = 0;
            for (Float value : valuesArray) {
                sum += value;
            }
            return sum / valuesArray.length;
        }

        private float calculateMedian(Float[] valuesArray) {
            Arrays.sort(valuesArray);
            int middle = valuesArray.length / 2;
            if (valuesArray.length % 2 == 0) {
                return (valuesArray[middle - 1] + valuesArray[middle]) / 2;
            } else {
                return valuesArray[middle];
            }
        }

        private float calculateMode(Float[] valuesArray) {
            Map<Float, Integer> frequencyMap = new HashMap<>();
            for (Float value : valuesArray) {
                frequencyMap.put(value, frequencyMap.getOrDefault(value, 0) + 1);
            }

            float mode = valuesArray[0];
            int maxCount = 0;
            for (Map.Entry<Float, Integer> entry : frequencyMap.entrySet()) {
                if (entry.getValue() > maxCount) {
                    maxCount = entry.getValue();
                    mode = entry.getKey();
                }
            }
            return mode;
        }

    }
    @Data
    private static class LiteracyPointStudentItem{
        private String id; // 学生id
        private String name;//学生姓名
        private String nodeId;//素养点/知识点id
        private String nodeName;//素养点/知识点名称
        private Float degree; // 完成度

    }
    @Data
    private static class LiteracyItem{
        private String id;
        private String name;
        private String parentName;
    }

    @Data
    public static class QueryLiteracyPointDTO {
        private String type;// 2知识点，3素养点，不传则查询全部
        private String stageCode;//学段
        private String subjectCode;//学科
        private String classId;//班级
        private String gradeCode;// 年级
        private String studentId;
    }
}
