package com.fytec.controller.open.agent.flow.utils.dataquery;

public class DatabaseHelper {
    public static DatabaseUtil build(String dbType, String version, String host, String port
            , String schema, String username, String password) throws Exception {
        String jdbcUrl;
        DatabaseUtil db;
        if ("mysql".equalsIgnoreCase(dbType)) {
            db = new MySQLDatabaseUtil();
            if("8".equals(version)){
                jdbcUrl = "jdbc:mysql://" + host + ":" + port + "/" + schema + "?useUnicode=true&characterEncoding=utf8&allowPublicKeyRetrieval=true&useSSL=false&serverTimezone=GMT%2b8";
            }else {
                jdbcUrl = "jdbc:mysql://" + host + ":" + port + "/" + schema + "?useUnicode=true&characterEncoding=utf8&allowPublicKeyRetrieval=true&useSSL=false&serverTimezone=GMT%2b8";
            }
        } else {
            throw new IllegalArgumentException("系统暂不支持数据源类型：[" + dbType + "]");
        }
        db.init(jdbcUrl, username, password);

        return db;
    }
}
