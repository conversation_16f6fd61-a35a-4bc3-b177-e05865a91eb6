package com.fytec.controller.open.knowledge;


import cn.dev33.satoken.oauth2.annotation.SaCheckClientIdSecret;
import com.fytec.dto.knowledge.KnowLedgeDocSearchDTO;
import com.fytec.service.knowledge.KnowledgeService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Validated
@Tag(name = "知识库检索-内部服务")
@RequestMapping("/api/private/knowledge")
@RestController
@AllArgsConstructor
public class KnowledgeSearchController {

    private final KnowledgeService knowledgeService;

    @PostMapping(value = "/doc/search")
    @Operation(summary = "知识库搜索")
    @SaCheckClientIdSecret
    public R<?> docSearchByDto(@RequestBody KnowLedgeDocSearchDTO dto) {
        return R.ok(knowledgeService.docSearchByDto(dto));
    }

    @PostMapping(value = "/doc/search/v2")
    @Operation(summary = "知识库搜索")
    @SaCheckClientIdSecret
    public R<?> docSearchV2(@RequestBody KnowLedgeDocSearchDTO dto) {
        return R.ok(knowledgeService.docSearchByDtoWithFilterMap(dto));
    }
}
