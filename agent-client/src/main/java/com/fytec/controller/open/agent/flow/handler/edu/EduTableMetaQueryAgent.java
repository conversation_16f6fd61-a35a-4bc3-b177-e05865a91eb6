package com.fytec.controller.open.agent.flow.handler.edu;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fytec.constant.TableDefinedConstants;
import com.fytec.controller.open.agent.flow.protocol.AgentHandlerError;
import com.fytec.controller.open.agent.flow.protocol.IAgentHandler;
import com.fytec.controller.open.agent.flow.protocol.IHandlerContext;
import com.fytec.controller.open.agent.flow.protocol.errors.AgentDataNotPermissionException;
import com.fytec.controller.open.agent.flow.protocol.errors.AgentIntentNotFounException;
import com.fytec.controller.open.agent.flow.protocol.errors.AgentJsonFormatException;
import com.fytec.controller.open.agent.flow.protocol.errors.AgentSqlQueryException;
import com.fytec.controller.open.agent.flow.utils.edu.GatewayUtils;
import com.fytec.utils.AiFormatJsonUtil;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;
import lombok.Data;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

// 小易助手，意图识别智能体
public class EduTableMetaQueryAgent implements IAgentHandler {
    private static final Logger log = LoggerFactory.getLogger(EduTableMetaQueryAgent.class);

    @Override
    public void handle(IHandlerContext context) throws AgentHandlerError {
        IAgentHandler.super.handle(context);

        //判断要查询哪些表，根据这些表，返回下一个节点id和json
        IAgentHandler.super.print(context);
        if (context.getConstants() == null) {
            context.setConstants(new HashMap<String, String>());
        }
        //改写
//        context.setInput(context.getInput().replace("教师", "老师"));
        // 设置返回一个json，里面是相关的智能体id和
        String output = context.getOutput();
        output = AiFormatJsonUtil.formatJson(output);
        context.setOutput(output);
        JSONObject jsonObject = null;
        try {
            jsonObject = JSONObject.parseObject(output);
        } catch (Exception e) {
            // 说明不是json了
            throw new AgentJsonFormatException();
//            System.out.println("error json:" + output);
        }
        String queryTables = "";
        if (jsonObject != null) {
            if (jsonObject.containsKey("data"))
                queryTables = jsonObject.getString("data");
            //        Long nextAgentId = jsonObject.getLong("id");
            Long nextAgentId = 7L; // sql智能体
            nextAgentId = getNextAgentId(context);
            context.setNextAgentId(nextAgentId);
            if (jsonObject.containsKey("input") && StringUtils.isNotBlank(jsonObject.getString("input"))) {
                System.out.println("BI查询，改写前:" + context.getInput());
                String replaceStr = jsonObject.getString("input");
                if (replaceStr.equals(context.getInput())) {

                } else {
                    printThink(context, String.format("用户的问题可以理解为：%s", replaceStr));
                }
                context.setInput(replaceStr);
                System.out.println("BI查询，改写后:" + context.getInput());
            }
        }

        queryTables += " " + context.getInput();
        //  拿一些表结构
        Set<String> tables = new LinkedHashSet<>();
        List<String> permissonPrompt = new ArrayList<>();
        //  这里获取用户权限
        DataPermission permission = null;
        boolean checkPermisson = true;
        if (checkPermisson && StringUtils.isNotBlank(context.getThirdUserToken())) {
            try {
                Map<String, String> headers = GatewayUtils.buildDefaultHeader();
                permission = getPermission(headers, context, queryTables);
            } catch (Exception e) {
//                if (e instanceof AgentDataNotPermissionException) {
//                    throw new AgentHandlerError("获取数据权限失败:" + e.getMessage());
//                } else {
//                    throw new AgentHandlerError("获取数据权限失败:" + e.getMessage());
//                }
                if (e instanceof AgentDataNotPermissionException) {
                    throw new AgentDataNotPermissionException();
                } else {
                    e.printStackTrace();
                }
            }
        } else {
            if (checkPermisson && StringUtils.isBlank(context.getThirdUserToken())) {
                System.out.println("getThirdUserToken null;");
            }
        }

        if (false) {
            if (queryTables.contains("学生")) {
                tables.add(TableDefinedConstants.v_school_baseinfo);
                tables.add(TableDefinedConstants.v_student_baseinfo);
//            if (permission != null && permission.isAreaAdmin) {
                tables.add(TableDefinedConstants.tj_v_area_edu_size);
//            }
                if (permission != null && permission.getStudentIdList().size() > 0) {
                    permissonPrompt.add("可以查询的学生ID为:" + StringUtils.join(permission.getStudentIdList(), ","));
                }
                if (permission != null && permission.getClassIdList().size() > 0) {
                    permissonPrompt.add("可以查询的班级ID为:" + StringUtils.join(permission.getStudentIdList(), ","));
                }
            }
            if (queryTables.contains("老师")) {
                tables.add(TableDefinedConstants.v_teacher_baseinfo);
                tables.add(TableDefinedConstants.v_teacher_reward);
                tables.add(TableDefinedConstants.v_teacher_title);
                tables.add(TableDefinedConstants.v_teacher_rcjl);
                tables.add(TableDefinedConstants.v_teacher_thesis);
//            if (permission != null && permission.isAreaAdmin) {
                tables.add(TableDefinedConstants.tj_area_teacher_analysis);
                tables.add(TableDefinedConstants.tj_area_teacher_reward);
                tables.add(TableDefinedConstants.tj_v_area_edu_size);
//            }
                if (permission != null && permission.getTeacherIdList().size() > 0) {
                    permissonPrompt.add("可以查询的老师ID为:" + StringUtils.join(permission.getTeacherIdList(), ","));
                }
            }
            //todo 之后不能这样简单判断
            if (queryTables.contains("学校") || queryTables.contains("小学") || tables.size() == 0 || true) {
                //  需要改写,学校改写智能体
                // 改写一下内容，把output作为新input
//            if (queryTables.contains("学") || queryTables.contains("校")) {
                if (queryTables.contains("幼儿园")) {
//                context.getAgentFlowService().execAgentChangeNodeById(6L, context);//学校名称改写智能体
                    //todo 这个改写需要优化
                }
//            context.setInput(context.getOutput());
                // 加入学校相关的表
                tables.add(TableDefinedConstants.v_school_baseinfo);
                tables.add(TableDefinedConstants.v_school_campus);
                tables.add(TableDefinedConstants.v_school_reward);
//            if (permission != null && permission.isAreaAdmin) {
                tables.add(TableDefinedConstants.tj_v_area_edu_size);
//            }
                if (permission != null && permission.getSchoolIdList().size() > 0) {
                    permissonPrompt.add("可以查询的学校ID为:" + StringUtils.join(permission.getSchoolIdList(), ","));
                }
            }
            // 执行sql
//        System.out.println("tables query len:" + tables.size());
            if (tables.size() > 0) {
//            tables = new LinkedHashSet<>();
                tables.add(TableDefinedConstants.tj_v_area_edu_size);
                tables.add(TableDefinedConstants.tj_area_teacher_analysis);
                tables.add(TableDefinedConstants.tj_area_teacher_reward);

                context.getConstants().put("tables", tables.stream().map(t -> t + "\n").collect(Collectors.joining("\n")));
                List<String> appendsDefined = new LinkedList<>();
                for (String table : tables) {
                    TableDefinedConstants.tableDictMap.forEach((key, value) -> {
                        if (table.contains(key)) {
                            appendsDefined.add(value);
                        }
                    });
                }
//            System.out.println("context tables strlen1:" + context.getConstants().get("tables").length());
                System.out.println("appendsDefined len:" + appendsDefined.size());
                if (appendsDefined.size() > 0) {
                    context.getConstants().put("tables", context.getConstants().get("tables").toString() + "\n\n###表字典补充\n" + StringUtils.join(appendsDefined, "\n"));
                }
                if (permissonPrompt.size() > 0) {
                    context.getConstants().put("dataIds", permissonPrompt.stream().map(t -> t + "\n").collect(Collectors.joining("\n")));
                } else {
                    context.getConstants().put("dataIds", "");
                }
//            context.setInput(context.getInput().replace("苏州工业园区", ""));
                // 上层执行吧,并且记录日志
                // 需要给一些数据权限
//            System.out.println("context tables strlen2:" + context.getConstants().get("tables").length());


            } else {
//            throw new AgentIntentNotFounException();
            }
        }
        if (permission != null && checkPermisson) {
            if (permission.isAreaAdmin) {
                // 说明有全部数据权限
                context.getConstants().put("dataIds", "");
            } else {
                // 如果什么权限都没有，抛出异常
                if (permission.getSchoolIdList() != null && !permission.getSchoolIdList().isEmpty()) {
                    permissonPrompt.add("### 权限控制可以查询的，所在/编制学校ID为:" + StringUtils.join(permission.getSchoolIdList(), ","));
                }
                if (permission.getTeacherIdList() != null && !permission.getTeacherIdList().isEmpty()) {
                    permissonPrompt.add("### 权限控制可以查询的，教师ID为:" + StringUtils.join(permission.getTeacherIdList(), ","));
                }
                if (permission.getStudentIdList() != null && !permission.getStudentIdList().isEmpty()) {
                    permissonPrompt.add("### 权限控制可以查询的，学生ID为:" + StringUtils.join(permission.getStudentIdList(), ","));
                }
                if (permission.getClassIdList() != null && !permission.getClassIdList().isEmpty()) {
                    permissonPrompt.add("### 权限控制可以查询的，班级ID为:" + StringUtils.join(permission.getClassIdList(), ","));
                }
                if (!permissonPrompt.isEmpty()) {
                    context.getConstants().put("dataIds", "## 数据权限控制(涉及相关表字段时，应该通过id in 的sql语句方式进行过滤和控制,如果涉及统计类的的问题，返回select '没有权限' as '提示';的sql）:\n" + permissonPrompt.stream().map(t -> t + "\n").collect(Collectors.joining("\n")));
                    // 表结构化的数据判断，哪些主表涉及哪些id，是明确的
                    System.out.println("permissonIds:" + JSON.toJSONString(permission));

// todol                    permissonIds:{"areaAdmin":false,"classIdList":["A180FF7BC9A74C7A81B3E0B1503CFBC0"],"schoolIdList":[],"studentIdList":[],"teacherIdList":["0570D4E5D9FD4B1F8150B5624136B61E"]}
                    // 拿到了权限了，但是不能这么控制？

                    context.getConstants().put("dataPermissionJson", JSON.toJSONString(permission));
                } else {
                    // todo 说明是教研员，考虑学科学段的处理
//                    "subjectList":"语文,数学","xdList":"小学,初中"
//                    throw new AgentDataNotPermissionException();
                    context.getConstants().put("dataIds", "");
                }
            }
        }


        // 如果查出来有限制的数据权限，需要说明
        if (permission != null) {
            if (!permission.isAreaAdmin) {
                List<String> permissionsStr = new LinkedList<>();
                if (permission.getStudentIdList() != null && permission.getStudentIdList().size() > 0) {
                    permissionsStr.add(String.format("%s个学生数据", permission.getStudentIdList().size()));
                }
                if (permission.getClassIdList() != null && permission.getClassIdList().size() > 0) {
                    permissionsStr.add(String.format("%s个班级数据", permission.getClassIdList().size()));
                }
                if (permission.getSchoolIdList() != null && permission.getSchoolIdList().size() > 0) {
                    permissionsStr.add(String.format("%s个学校数据", permission.getSchoolIdList().size()));
                }
                if (permission.getTeacherIdList() != null && permission.getTeacherIdList().size() > 0) {
                    permissionsStr.add(String.format("%s个老师数据", permission.getTeacherIdList().size()));
                }
                if (permissionsStr.size() > 0) {
                    StringBuffer sb = new StringBuffer("因为本次查询设计到内部数据，首先查询用户的数据权限，查询到本用户有权访问：");
                    sb.append(StringUtils.join(permissionsStr, ","));
                    printThink(context, sb.toString());
                } else {
                    printThink(context, "因为本次查询涉及到内部数据，首先查询用户的数据权限，查询到本用户没有数据权限。");
                    throw new AgentDataNotPermissionException("没有权限");
                }
            } else {
                printThink(context, "因为本次查询涉及到内部数据，首先查询用户的数据权限，查询到本用户为区管用户，有权访问全部数据。");
            }
        }
    }

    @Override
    public String getThinkResultTemplate() {
        return "%s";
    }

    @Override
    public String getDesc() {
        return "用来处理图标查询的智能体";
    }

    @Override
    public Long getAgentId() {
        return 5L;
    }

    @Override
    public Long getNextAgentId(IHandlerContext context) {
        return 15L;
    }

    public static DataPermission getPermission(Map<String, String> headers, IHandlerContext context, String queryTables) throws Exception {
        List<String> types = new ArrayList<>();
//        types.add("area");

        if (queryTables.contains("老师")) {
            types.add("teacher");
        }
        if (queryTables.contains("学生")) {
            types.add("student");
        }
        if (queryTables.contains("学校")) {
            types.add("school");
        }
        if (types.isEmpty()) {
            types.add("area");
        }
        // 获取哦token
        String token = context.getThirdUserToken();
        DataPermission dataPermission = new DataPermission();
        String[] keys = {"studentIdList", "teacherIdList", "classIdList", "schoolIdList","subjectList","xdList"};
        int idLen = 0;

        for (String type : types) {
            //        {token:"23419d12-d508-45e9-a74d-0c5e6c2ccxxx",data_type:"student"}
            JSONObject body = new JSONObject();
            body.put("token", token);
            body.put("data_type", type);
//            body.put("appId", GatewayUtils.appId);
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonBody = objectMapper.writeValueAsString(body);
//            System.out.println("query data getPermission body:");
//            System.out.println(jsonBody);

//          String turl = "http://portapigw.sipedu.cn/sipdata/rest/large_model/queryPermission";
            String turl = "http://portapigw.sipedu.cn/sipdata/API/V5/large_model/queryPermission";
            System.out.println(headers);
            HttpResponse<String> response = Unirest.post(turl)
                    .header("Content-Type", org.springframework.http.MediaType.APPLICATION_JSON_VALUE)
                    .headers(headers)
                    .body(jsonBody)
                    .asString();
            JSONObject parseObj = JSON.parseObject(response.getBody());
            if (parseObj.containsKey("data")) {
                JSONObject data = parseObj.getJSONObject("data");
                System.out.println("permisson data:" + data.toString());
                if (data.containsKey("code") && "201".equals(data.getString("code"))) {
                    //错误了
                    //测试和prod环境，账号密码可能不一样，会导致token出错。
                    dataPermission.setAreaAdmin(true);
                    return dataPermission;
                }
                Boolean isAreaAdmin = data.getBoolean("isAreaAdmin");
                if (isAreaAdmin != null && isAreaAdmin) {
                    dataPermission.setAreaAdmin(true);
                    return dataPermission;
                }
                for (String key : keys) {
                    if (data.getString(key) != null && StringUtils.isNotBlank(data.getString(key))) {
                        String[] ids = data.getString(key).split(",");
                        for (String id : ids) {
                            if (StringUtils.isBlank(id)) {
                                continue;
                            }
//                            dataPermission.getStudentIdList().add(id);
                            Field field = dataPermission.getClass().getDeclaredField(key);
                            field.setAccessible(true);
                            ((Set) field.get(dataPermission)).add(id);
                            idLen += 1;
                        }
                    }
                }
            } else {
                System.out.println(response.getBody());
            }
        }
        if (idLen == 0) {
            // 说明这个没有权限了，需要这个数据，但是又没有相关权限，那就需要返回错误
            throw new AgentDataNotPermissionException();
        }
        return dataPermission;
    }

    @Data
    public static class DataPermission {
        private Set<String> studentIdList = new HashSet<>();
        private Set<String> teacherIdList = new HashSet<>();
        private Set<String> classIdList = new HashSet<>();
        private Set<String> schoolIdList = new HashSet<>();
        private Set<String> subjectList = new HashSet<>(); //"subjectList":"语文,数学","xdList":"小学,初中"
        private Set<String> xdList = new HashSet<>();

        private boolean isAreaAdmin = false;

//        参数说明	类型	参数路径	描述
//        studentIdList	String		可查看的学生ID
//        teacherIdList	String		可查看的教师ID
//        classIdList	String		可查看的班级ID
//        schoolIdList	String		可查看的学校ID
//        isAreaAdmin	Boolean		是否区管
    }
}
