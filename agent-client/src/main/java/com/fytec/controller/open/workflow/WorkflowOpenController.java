package com.fytec.controller.open.workflow;

import cn.dev33.satoken.oauth2.annotation.SaCheckClientToken;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.constant.Constants;
import com.fytec.dto.agent.AgentPublishHistoryDTO;
import com.fytec.dto.agent.AgentPublishSftDto;
import com.fytec.dto.flow.WorkflowExecuteDTO;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.agent.AgentSftService;
import com.fytec.service.flow.WorkflowService;
import com.fytec.util.R;
import com.fytec.util.UUIDGenerator;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.ClientServiceProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.concurrent.CompletableFuture;

@Slf4j
@Validated
@Tag(name = "工作流调用")
@RequestMapping("/api/workflow/open")
@RestController
@AllArgsConstructor
public class WorkflowOpenController {

    private final WorkflowService workflowService;


    @PostMapping(value = "/publish/execute")
    @Operation(summary = "执行发布工作流")
    @SaCheckClientToken(scope = "client:execute")
    public R<String> executePublishedWorkflow(@Validated @RequestBody WorkflowExecuteDTO dto) {
        return R.ok(workflowService.executeWorkflow(dto));
    }

    @SneakyThrows
    @PostMapping(value = "/publish/chat/execute")
    @Operation(summary = "执行发布对话流")
    @SaCheckClientToken(scope = "client:execute")
    public SseEmitter executePublishedChatFlow(@Validated @RequestBody WorkflowExecuteDTO dto) {
        String runId = UUIDGenerator.getUUID();
        dto.setRunId(runId);
        workflowService.executeChatFlow(dto);

        String clientId = IdUtil.nanoId();
        final ClientServiceProvider provider = ClientServiceProvider.loadService();
        final SseEmitter sseEmitter = workflowService.getConn(clientId);
        CompletableFuture.runAsync(() -> workflowService.listenExecuteChatFlow(provider, clientId, runId));
        return sseEmitter;
    }

}
