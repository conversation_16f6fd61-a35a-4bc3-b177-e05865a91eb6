package com.fytec.controller.open.agent;

import cn.dev33.satoken.oauth2.annotation.SaCheckClientToken;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.constant.Constants;
import com.fytec.dto.agent.AgentPublishHistoryDTO;
import com.fytec.dto.agent.AgentPublishSftDto;
import com.fytec.dto.agent.AgentSftDto;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.agent.AgentSftService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Validated
@Tag(name = "智能体-知识库强化学习")
@RequestMapping("/api/agent/open")
@RestController
@AllArgsConstructor
public class AgentOpenSftController {
    @Autowired
    private AgentSftService agentSftService;

    @PostMapping(value = "/develop/publish/sft/positive")
    @Operation(summary = "监督强化学习-正样本")
    @SaCheckClientToken(scope = "client:execute")
    public R<Page<AgentPublishHistoryDTO>> sftPublishLogKnowledgePositive(@Validated @RequestBody AgentPublishSftDto dto) {
        dto.setType(Constants.KNOWLEDGE_BASE_TYPE.positive.name());
        agentSftService.sftPublishLogKnowledgePositive(dto);
        return R.ok();
    }

    @PostMapping(value = "/develop/publish/sft/negative")
    @Operation(summary = "监督强化学习-负样本")
    @SaCheckClientToken(scope = "client:execute")
    public R<Page<AgentPublishHistoryDTO>> sftPublishLogKnowledgeNegative(@Validated @RequestBody AgentPublishSftDto dto) {
        dto.setType(Constants.KNOWLEDGE_BASE_TYPE.negative.name());
        agentSftService.sftPublishLogKnowledgeNegative(dto);
        return R.ok();
    }


}
