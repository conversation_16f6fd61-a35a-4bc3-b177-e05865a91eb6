package com.fytec.controller.open.speech;

import cn.dev33.satoken.oauth2.annotation.SaCheckClientToken;
import com.fytec.dto.speech.SpeechStreamDTO;
import com.fytec.service.speech.SpeechProcessService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Validated
@Tag(name = "智能体执行")
@RequestMapping("/fytec/api/speech")
@RestController
@RequiredArgsConstructor
public class SpeechProcessController {
    private final SpeechProcessService speechProcessService;

    @PostMapping(value = "/one-sentence")
    @Operation(summary = "一句话音频识别")
    @SaCheckClientToken(scope = "client:execute")
    public R<Object> processOneSentence(@Validated @RequestBody SpeechStreamDTO dto) {
        return R.ok(speechProcessService.processOneSentence(dto));
    }
}
