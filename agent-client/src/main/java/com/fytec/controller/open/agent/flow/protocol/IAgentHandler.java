package com.fytec.controller.open.agent.flow.protocol;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONException;
import com.alibaba.fastjson2.JSONObject;
import com.fytec.constant.AgentFlowConstants;
import com.fytec.controller.open.agent.flow.service.AgentFlowService;
import com.fytec.handler.IResponseHandlerProxy;
import com.fytec.utils.AiFormatJsonUtil;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

// 智能体处理接口
public interface IAgentHandler {

// This method registers an AgentFlowService with the globalAgentHandlerMap
    default void register(AgentFlowService agentFlowService) {
        // Add the current agent to the globalAgentHandlerMap
        agentFlowService.globalAgentHandlerMap.put(getAgentId(), this);
    }

    Long getAgentId();
//    default Long getAgentId() {
//        //平台配置的智能体id
//        return 0L;
//    }

    default boolean isEnableCitation() {
        return false;
    }

    default boolean getBlockButPrintThink(IHandlerContext context) {
        return false;
    }

    default Long getThinkAgentId(IHandlerContext context) {
        return null;
    }

    default List<Long> getParallelAgentId() {
        return null;
    }

    default String formatJson(String output) {
        return AiFormatJsonUtil.formatJson(output);

    }

    default String getErrorMsg(IHandlerContext context) {
        return "此问题尚在学习中...";
    }

    default String getThinkAgentInput(IHandlerContext context, String answer) {
        return context.getInput();
    }

    default IResponseHandlerProxy getResponseHandlerProxy(RESPONSE_TYPE responseType, final IHandlerContext context) {
        return null;
    }

    default Long getNextAgentId(IHandlerContext context) {
        // todo 未来如果，希望2个智能体并行返回，那么就需要做这个处理了。相当于比如1个流一个非流，多个非流，一起返回给前端。
        // 用线程池并行返回前端。
        return null;
    }

    default String handlerEnd(IHandlerContext context, Map<String, Object> values) {
        return null;
    }

    default String getDesc() {
        return "";
    }

    // 根据id调用平台智能体，然后，得到的结果通过handle处理
    // 历史、调用都可以复用，是一样逻辑
    default void handle(IHandlerContext context) throws AgentHandlerError {
        context.setNextAgentId(null);
        context.setThinkAgentId(null);
        context.setMoreDetailAgentId(null);
//        context.setResponseHandlerProxy(null);
    }

    default void beforeHandle(IHandlerContext context) throws AgentHandlerError {
        context.setNextAgentId(null);
        context.setThinkAgentId(null);
        context.setMoreDetailAgentId(null);
        context.setHistoryDTO(null);
        context.setCurrentHandler(null);
    }

    default void print(IHandlerContext context) {
        System.out.println("handle info getAgentId:" + (context.getAgentId()));
        System.out.println("handle info input:" + (context.getInput() == null ? "null" : context.getInput()));
//        System.out.println("handle info lastInput:" + (context.getLastInput() == null ? "null" : context.getLastInput()));
        System.out.println("handle info output:" + (context.getOutput() == null ? "null" : context.getOutput()));

    }

    @Getter
    public enum RESPONSE_TYPE {
        think("思考过程流"),
        common("正常流");

        private final String desc;

        RESPONSE_TYPE(String desc) {
            this.desc = desc;
        }

    }

    default String getThinkResultTemplate() {
        //输出结果当作模板，思维链输出
        return "%s";
    }
    default void printExtraInfo(IHandlerContext context,Long runtime,String protocol,String value){
        List<AgentFlowConstants.CacheExtraInfo> cache = context.getCacheExtraInfoByAgentId().computeIfAbsent(this.getAgentId(), k -> new LinkedList<AgentFlowConstants.CacheExtraInfo>());
        cache.add(new AgentFlowConstants.CacheExtraInfo(protocol,value));
        // 流调用时候需要输出
        if (context.getResponse() != null) {
            if (!context.isInitResponse()) {
                context.setInitResponse(true);
                context.getResponse().setContentType("text/event-stream");
                context.getResponse().setCharacterEncoding("utf-8");
                context.getResponse().setDateHeader("Expires", 0);
                context.getResponse().setHeader("Cache-Control", "no-cache");
                context.getResponse().setHeader("Pragma", "no-cache");
                context.getResponse().setHeader("X-Accel-Buffering", "no");
            }
            if(runtime==null){
                if (context.getHistoryDTO() != null && context.getHistoryDTO().getRuntime() != null) {
                    runtime = context.getHistoryDTO().getRuntime();
                }
            }

            String s = "event:message\ndata:" + createMessageJson(protocol, context.getAgentId(), context.getCurrentNodeIndex(), value, runtime) + "\n\n";
            try {
                context.getResponse().getWriter().write(s);
                context.getResponse().getWriter().flush();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }
    default void printThink(IHandlerContext context, String... content) {
        printThink(context,null,content);
    }
    default void printThink(IHandlerContext context,Long runtime, String... content) {
        // 把回答结果作为思考过程输出
        if (content != null && getThinkResultTemplate() != null) {
            if (context.getResponse() != null) {
                if (!context.isInitResponse()) {
                    context.setInitResponse(true);
                    context.getResponse().setContentType("text/event-stream");
                    context.getResponse().setCharacterEncoding("utf-8");
                    context.getResponse().setDateHeader("Expires", 0);
                    context.getResponse().setHeader("Cache-Control", "no-cache");
                    context.getResponse().setHeader("Pragma", "no-cache");
                    context.getResponse().setHeader("X-Accel-Buffering", "no");
                }
                // 替换一下，把结果封装为输出
                // 结果可能是字符串，也可能是json
                // 这里不考虑处理json中的字段问题，只要有模板和输入即可
                String result = String.format(getThinkResultTemplate(), content);
                // 输出并刷新
                if(runtime==null){
                    if (context.getHistoryDTO() != null && context.getHistoryDTO().getRuntime() != null) {
                        runtime = context.getHistoryDTO().getRuntime();
                    }
                }

                String s = "event:message\ndata:" + createMessageJson("reasoningChain", context.getAgentId(), context.getCurrentNodeIndex(), result, runtime) + "\n\n";
                try {
                    context.getResponse().getWriter().write(s);
                    context.getResponse().getWriter().flush();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                context.getThinkStrList().add(result);
            }
        }

    }

    static String createMessageJson(String type, String agentId, Integer currentNodeIndex, Object content, Long runtime) {
        JSONObject contentObj = new JSONObject();
        contentObj.put("type", type);
        contentObj.put("nodeIndex", currentNodeIndex);
        contentObj.put("agentId", agentId);
        contentObj.put("content", content);
        contentObj.put("runtime", runtime);

        JSONObject messageObj = new JSONObject();
        messageObj.put("message", contentObj);
        return messageObj.toJSONString();
    }
}
