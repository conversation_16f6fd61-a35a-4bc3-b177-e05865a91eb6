package com.fytec.controller.open.agent.flow.handler.prod;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fytec.controller.open.agent.flow.handler.edu.EduTableQuerySqlAgent;
import com.fytec.controller.open.agent.flow.protocol.AgentHandlerError;
import com.fytec.controller.open.agent.flow.protocol.IAgentHandler;
import com.fytec.controller.open.agent.flow.protocol.IHandlerContext;
import com.fytec.controller.open.agent.flow.utils.dataquery.dto.Output;
import com.fytec.controller.open.agent.flow.utils.dataquery.dto.OutputColumn;
import com.fytec.handler.IResponseHandlerProxy;
import com.fytec.utils.AiFormatJsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

public class ProdEduExamTableQuerySqlAgent implements IAgentHandler {
    private static final Logger log = LoggerFactory.getLogger(ProdEduExamTableQuerySqlAgent.class);
    private long formatAgentId = 136L;

    @Override
    public Long getAgentId() {
        return 146L;
    }

    @Override
    public Long getThinkAgentId(IHandlerContext context) {
//        return 147L;
        return null;
    }
    //todo 可以考虑下一个节点添加分析？


    @Override
    public void beforeHandle(IHandlerContext context) throws AgentHandlerError {
        IAgentHandler.super.beforeHandle(context);
        HashMap<String, String> sqlConfigs = new HashMap<>();
        sqlConfigs.put("host", "************");
        sqlConfigs.put("port", "3306");
        sqlConfigs.put("schema", "demo");
        sqlConfigs.put("username", "root");
        sqlConfigs.put("password", "7890@uiop");
        context.setSqlConfigs(sqlConfigs);
    }

    @Override
    public void handle(IHandlerContext context) throws AgentHandlerError {
        IAgentHandler.super.handle(context);
    }

    @Override
    public String handlerEnd(IHandlerContext context, Map<String, Object> values) {
        EduTableQuerySqlAgent eduTableQuerySqlAgent = new EduTableQuerySqlAgent();
        eduTableQuerySqlAgent.setFormatAgentId(formatAgentId);
        return eduTableQuerySqlAgent.handlerEnd(context, values);
    }

    @Override
    public String getErrorMsg(IHandlerContext context) {
        return "查询遇到一些问题";
    }

    @Override
    public String getDesc() {
        return "sql生成智能体";
    }
}
