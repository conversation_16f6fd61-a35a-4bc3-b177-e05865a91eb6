package com.fytec.controller.open.agent.flow.protocol;

import com.alibaba.fastjson2.JSONObject;
import com.fytec.constant.AgentFlowConstants;
import com.fytec.controller.open.agent.flow.service.AgentFlowService;
import com.fytec.controller.open.agent.flow.utils.functioncall.FCallDTO;
import com.fytec.controller.open.agent.flow.utils.functioncall.IFunctionCallService;
import com.fytec.dto.agent.AgentPublishHistoryDTO;
import com.fytec.dto.flow.WorkflowDTO;
import com.fytec.handler.IResponseHandlerProxy;
import com.fytec.service.flow.FunctionCallService;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Data;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Data
public class IHandlerContext {
    private String agentId;
    private Long agentPublishId;

    private String lastInput;
    private String input;//输入，
    private String output;//输出
    private String sessionId;
    // role content,role: user;assistant
    private List<Map<String, String>> histories;
    // 智能体涉及的流程
    private List<WorkflowDTO> workflowList;
    // 下一个智能体id
    private Long nextAgentId; //下一个节点
    private Long thinkAgentId; //思考智能体
    private Long moreDetailAgentId;//点击查看详情的智能体
    private Long moreTipAgentId;//展示tip的智能体

    private AgentFlowService agentFlowService;
    private Map<String, String> constants;
    private HttpServletResponse response;
    private String thirdUserId;
    private String flowId;//每个流程的id
    private String thirdUserToken;
    private AgentPublishHistoryDTO historyDTO;
    private Map<String, List> knowledgeFilterMap;//过滤知识库用的条件，比如通过权限的
    //    private IResponseHandlerProxy responseHandlerProxy;
    private IAgentHandler currentHandler;

    @Schema(description = "第三方id，可在历史记录时进行查询")
    private String objectId;
    @Schema(description = "用户最初的原始输入")
    private String rawInput;

    private Map<String, String> sqlConfigs;

    private Integer currentNodeIndex;// 思维链的话，就是节点数
    private boolean initResponse = false;
    private List<String> thinkStrList = new LinkedList<>();
    private Long historyKnowledgeId;// 历史知识库id
    @Schema(description = "当前使用的模型")
    private JSONObject currentModel;

    @Schema(hidden = true)
    private String envType;// 环境变量

    @Schema(description = "系统提示词")
    private String sysPrompt;

    @Schema(description = "额外配置key智能体id，value是map，目前包含constants")
    private Map<String, Object> configs;

    private Map<Long,List<AgentFlowConstants.CacheExtraInfo>> cacheExtraInfoByAgentId = new ConcurrentHashMap<>();

    private Map<String, FCallDTO> fCallDTOMap = new ConcurrentHashMap<>();
    //    "configs":{
//        "51":{
//            "constants":{
//                "key":"value",//正则替换
//            }
//        }
    private IFunctionCallService functionCallService;// 用来支撑function call

    public void initResponse() {
        if (!initResponse && response != null) {
            this.initResponse = true;
            response.setContentType("text/event-stream");
            response.setCharacterEncoding("utf-8");
            response.setDateHeader("Expires", 0);
            response.setHeader("Cache-Control", "no-cache");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("X-Accel-Buffering", "no");
        }
    }


}
