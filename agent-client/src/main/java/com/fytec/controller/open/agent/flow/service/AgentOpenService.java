package com.fytec.controller.open.agent.flow.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fytec.dto.agent.*;
import com.fytec.entity.agent.AgentPublish;
import com.fytec.mapper.agent.AgentPublishHistoryMapper;
import com.fytec.mapper.agent.AgentPublishMapper;
import com.fytec.service.agent.AgentService;
import com.fytec.service.agent.AgentSftService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AgentOpenService {
    @Value("${env.type}")
    private String envType;
    @Autowired
    private AgentService agentService;
    @Autowired
    private AgentPublishMapper agentPublishMapper;
    @Autowired
    private AgentPublishHistoryMapper agentPublishHistoryMapper;
    @Autowired
    private AgentSftService agentSftService;

    public AgentPublishDTO publishedAgentDetail(Long agentId) {
        // 根据智能体id，得到发布的智能体信息
        AgentPublish agentPublish = agentPublishMapper.selectOne(new LambdaQueryWrapper<AgentPublish>()
                .eq(AgentPublish::getAgentId, agentId).orderByDesc(AgentPublish::getPublishTime).last("limit 1"));
        AgentPublishDTO dto = new AgentPublishDTO();
        BeanUtils.copyProperties(agentPublish, dto);
        dto.setAgentId("" + agentPublish.getAgentId());
        return dto;
    }
}