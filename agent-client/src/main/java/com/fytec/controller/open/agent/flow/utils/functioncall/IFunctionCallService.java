package com.fytec.controller.open.agent.flow.utils.functioncall;

import com.fytec.controller.open.agent.flow.protocol.IAgentHandler;
import com.fytec.controller.open.agent.flow.protocol.IHandlerContext;
import com.fytec.dto.llm.ModelCallDTO;

public interface IFunctionCallService {
    ModelCallDTO autoFunctionCallContext(String sysPrompt, String userInput);


    void checkAndBuildEchartFormat(String body, IHandlerContext context, IAgentHandler currentNode);
}
