package com.fytec.controller.open.agent.flow.handler.edu;

import com.alibaba.fastjson2.JSONObject;
import com.fytec.controller.open.agent.flow.protocol.AgentHandlerError;
import com.fytec.controller.open.agent.flow.protocol.IAgentHandler;
import com.fytec.controller.open.agent.flow.protocol.IHandlerContext;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

// 小易助手 改写问题
public class EduCustomerInputChangeAgent implements IAgentHandler {

    @Override
    public Long getAgentId() {
        return 9L;
    }

    @Override
    public void beforeHandle(IHandlerContext context) throws AgentHandlerError {
        IAgentHandler.super.beforeHandle(context);
        // 问题改写
        context.getAgentFlowService().execAgentChangeNodeById(17L, context);//
        //特殊规则处理一下：
        String replaceStr = context.getInput().replace("input@", "");
        if (replaceStr.equals(context.getInput())) {
            // 不输出改写
        } else {
            printThink(context, replaceStr);
        }
        context.setInput(replaceStr);
    }

    @Override
    public List<Long> getParallelAgentId() {
        ArrayList<Long> longs = new ArrayList<>(1);
        longs.add(1L);// 快捷助手
        longs.add(28L);//视频的
        return longs;
    }

    @Override
    public String getThinkResultTemplate() {
        return "用户的问题可以理解为:%s";
    }

    @Override
    public String getDesc() {
        return "历史问题改写智能体";
    }

    @Override
    public boolean isEnableCitation() {
        return true;
    }
}
