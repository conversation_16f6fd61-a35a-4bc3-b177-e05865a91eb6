package com.fytec.controller.open.agent.flow.handler.common;

import com.fytec.controller.open.agent.flow.handler.edu.EduTableQuerySqlAgent;
import com.fytec.controller.open.agent.flow.protocol.AgentHandlerError;
import com.fytec.controller.open.agent.flow.protocol.IAgentHandler;
import com.fytec.controller.open.agent.flow.protocol.IHandlerContext;
import com.fytec.entity.agent.AgentHandlerConfig;
import com.fytec.handler.IResponseHandlerProxy;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * Copyright (C), 2025-2099
 * 通用处理器
 *
 * <AUTHOR> lix
 * @date :   2025/6/9 11:28
 * Version: V1.0.0
 */
@Slf4j
public class GenericAgentHandler implements IAgentHandler {

    /**
     * 代理ID
     */
    private final Long agentId;
    /**
     * 描述信息
     */
    private final String desc;
    /**
     * 思考代理ID
     */
    private final Long thinkAgentId;
    /**
     * 格式代理ID
     */
    private final Long formatAgentId;
    /**
     * SQL配置信息，键值对形式
     */
    private final Map<String, String> sqlConfigs;
    /**
     * 错误信息
     */
    private final String errorMsg;
    /**
     * 是否阻塞但打印思考信息
     */
    private final boolean isBlockButPrintThinks;

    private final boolean isEnableCitation;


    /**
     * GenericAgentHandler类的构造方法
     *
     * @param agentHandlerConfig AgentHandlerConfig对象，包含代理处理器配置信息
     */
    public GenericAgentHandler(AgentHandlerConfig agentHandlerConfig) {
        this.agentId = agentHandlerConfig.getAgentId();
        this.desc = agentHandlerConfig.getDescription();
        this.thinkAgentId = agentHandlerConfig.getThinkAgentId();
        this.formatAgentId = agentHandlerConfig.getFormatAgentId();
        this.sqlConfigs = agentHandlerConfig.getSqlConfigs();
        this.errorMsg = agentHandlerConfig.getErrorMsg();
        this.isBlockButPrintThinks = agentHandlerConfig.isBlockButPrintThinks();
        this.isEnableCitation = agentHandlerConfig.isEnableCitation();
    }

    /**
     * 获取代理ID
     *
     * @return 返回代理ID
     */
    @Override
    public Long getAgentId() {
        return agentId;
    }

    /**
     * 重写 getThinkAgentId 方法，返回 thinkAgentId 字段的值。
     *
     * @return thinkAgentId 字段的值，类型为 Long。
     */
    @Override
    public Long getThinkAgentId(IHandlerContext context) {
        return thinkAgentId;
    }
    /**
     * 在处理请求前执行的方法。
     *
     * @param context 处理请求的上下文
     * @throws AgentHandlerError 处理请求时发生错误
     *
     * <p>此方法在处理请求前执行，主要用于设置请求的上下文。
     * 如果存在SQL配置，则将SQL配置设置到上下文中。
     *
     * @see IHandlerContext 处理请求的上下文接口
     * @see AgentHandlerError 处理请求时可能抛出的错误类
     */
    /**
     * 在处理请求前执行的方法。
     *
     * @param context 处理请求的上下文
     * @throws AgentHandlerError 处理请求时发生错误
     */
    @Override
    public void beforeHandle(IHandlerContext context) throws AgentHandlerError {
        IAgentHandler.super.beforeHandle(context);
        if (sqlConfigs != null && !sqlConfigs.isEmpty()) {
            context.setSqlConfigs(sqlConfigs);
        }
    }

    /**
     * 获取错误信息。
     *
     * @param context 上下文对象
     * @return 返回错误信息，如果未设置错误信息则返回"查询遇到一些问题..."
     */
    @Override
    public String getErrorMsg(IHandlerContext context) {
        return errorMsg != null ? errorMsg : "查询遇到一些问题...";
    }

    /**
     * 获取描述信息。
     *
     * @return 如果存在描述信息则返回描述信息，否则返回空字符串。
     */
    @Override
    public String getDesc() {
        return desc != null ? desc : "";
    }

    /**
     * 处理请求结束时的逻辑
     *
     * @param context 上下文对象
     * @param values  请求处理过程中传递的参数
     * @return 返回处理后的字符串结果，如果不需要处理则返回null
     */
    @Override
    public String handlerEnd(IHandlerContext context, Map<String, Object> values) {
        if (formatAgentId != null) {
            // 这里可以使用一个通用的格式化处理器
            return formatResponse(context, values);
        }
        return null;
    }

    /**
     * 格式化响应
     *
     * @param context 上下文对象
     * @param values  包含需要格式化的值的Map
     * @return 格式化后的响应字符串
     */
    private String formatResponse(IHandlerContext context, Map<String, Object> values) {
        // 实现通用的响应格式化逻辑
        // 可以根据formatAgentId调用不同的格式化方式
        EduTableQuerySqlAgent eduTableQuerySqlAgent = new EduTableQuerySqlAgent();
        eduTableQuerySqlAgent.setFormatAgentId(formatAgentId);
        return eduTableQuerySqlAgent.handlerEnd(context, values);
    }


    @Override
    public IResponseHandlerProxy getResponseHandlerProxy(RESPONSE_TYPE responseType, IHandlerContext context) {
        // 改写，思考过程输出，流阻塞
        return new IResponseHandlerProxy() {
            @Override
            public String handler(String responseStr) {
                if (responseStr == null) {
                    return null;
                }
                if (responseStr.startsWith("event:done")) {
                    // 忽略流处理的
                    return null;
                }
                // 作为思考过程来处理
                if (responseStr.contains("\"type\":\"reasoningContent\"")) {
                    return responseStr;
                }
                return null;
            }
        };
    }

    /**
     * 获取是否阻止打印思考信息的标志
     *
     * @return 如果设置了阻止打印思考信息则返回 true，否则返回 false
     */
    @Override
    public boolean getBlockButPrintThink(IHandlerContext context) {
        return isBlockButPrintThinks;
    }

    /**
     * 判断是否启用引用功能
     *
     * @return 如果启用了引用功能则返回true，否则返回false
     */
    @Override
    public boolean isEnableCitation() {
        return isEnableCitation;
    }
}
