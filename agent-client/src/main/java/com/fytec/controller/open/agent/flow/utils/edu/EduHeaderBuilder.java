package com.fytec.controller.open.agent.flow.utils.edu;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson2.JSONObject;
import lombok.Data;
import org.springframework.util.DigestUtils;

@Data
public class EduHeaderBuilder {
    private String appId;
    private String appSecret;
    private String apiToken;

    public EduHeaderBuilder(String appId, String appSecret, String apiToken) {
        this.appId = appId;
        this.appSecret = appSecret;
        this.apiToken = apiToken;
    }

    private String generateSign(long timestamp) {
        String input = appId + appSecret + timestamp + (apiToken == null ? "" : apiToken);
//        try {
//            MessageDigest md = MessageDigest.getInstance("MD5");
//            byte[] digest = md.digest(input.getBytes());
//            StringBuilder sb = new StringBuilder();
//            for (byte b : digest) {
//                sb.append(String.format("%02x", b & 0xff));
//            }
//            return sb.toString();
//        } catch (NoSuchAlgorithmException e) {
//            throw new RuntimeException(e);
//        }
        String sign = DigestUtils.md5DigestAsHex((input).getBytes());
        return sign;
    }
//    public static String generateSign(String appId,String appSecret,String)

    public Map<String,String>  buildDefaultHeader() {
        JSONObject header = new JSONObject();
        long timestamp = new Date().getTime() / 1000; // 获取10位时间戳
        header.put("appId", appId);
        header.put("timestamp", timestamp);
        if (apiToken != null)
            header.put("apiToken", apiToken);
        String sign = generateSign(timestamp);
        header.put("sign", sign);
        Map<String,String> maps = new HashMap<>();

        header.forEach((key,value)->{
            maps.put(key,value==null?null:value.toString());
        });
        return maps;
    }


}