package com.fytec.controller.open.agent.flow.handler.renwu;

import com.alibaba.fastjson2.JSONObject;
import com.fytec.controller.open.agent.flow.protocol.AgentHandlerError;
import com.fytec.controller.open.agent.flow.protocol.IAgentHandler;
import com.fytec.controller.open.agent.flow.protocol.IHandlerContext;
import com.fytec.utils.AiFormatJsonUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

// 小易助手，意图识别智能体
public class RenwuIntentIAgent implements IAgentHandler {
    @Override
    public void beforeHandle(IHandlerContext context) throws AgentHandlerError {
        IAgentHandler.super.beforeHandle(context);
        // 问题改写
//        context.getAgentFlowService().execAgentChangeNodeById(129L, context);//
        //特殊规则处理一下：
        String replaceStr = context.getInput();
        if (replaceStr.equals(context.getInput())) {
            // 不输出改写
        } else {
            printThink(context, String.format("用户的问题可以理解为:%s",context.getInput()));
        }
        context.setInput(replaceStr);
    }

    @Override
    public void handle(IHandlerContext context) throws AgentHandlerError {
        printThink(context, String.format("正在识别您的意图，您的问题是【%s】", context.getInput()));

        IAgentHandler.super.handle(context);
        IAgentHandler.super.print(context);
        // todo 根据output，采用不同的模块
        // 判断意图了
        //判断是哪个智能体

        // 设置返回一个json，里面是相关的智能体id和
        String output = AiFormatJsonUtil.formatJson(context.getOutput());
        JSONObject jsonObject = null;
        try {
            jsonObject = JSONObject.parseObject(output);
            if (jsonObject == null || !jsonObject.containsKey("agentId") || StringUtils.isBlank(jsonObject.getString("agentId"))) {
                System.out.println("没有找到id:" + output);
            }
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("格式化失败:" + output);
        }
        // 默认是119L,数据库查询
        Long nextAgentId = 119L;
        if (jsonObject != null) {
            try {
                Long id = jsonObject.getLong("agentId");
                if (id != null && id > 0L) {
                    nextAgentId = id;
                }
            } catch (Exception e) {
                e.printStackTrace();
                System.out.println("格式化失败:" + jsonObject.toString());
            }
        }

        System.out.println("下一个 节点 id:" + nextAgentId);
//        if (nextAgentId == 9L) {
//            // 改写一下问题表述
//
//        }

        // 根据节点id，查询到智能体的描述？
        printThink(context, "为您@" + AGENT_NAME.getOrDefault(""+nextAgentId, "通用") + "智能体进行回答");

        context.setNextAgentId(nextAgentId);
    }

    private static final Map<String, String> AGENT_NAME = new HashMap<>();

    static {
        AGENT_NAME.put("119", "BI查询");
        AGENT_NAME.put("138", "流程规范");
        AGENT_NAME.put("153", "台账资金");
    }

    @Override
    public String getThinkResultTemplate() {
        return "%s";
    }

    @Override
    public Long getAgentId() {
        return 152L;
    }

    @Override
    public String getDesc() {
        return "分支判断，想查询哪一个模块数据";
    }

    @Override
    public Long getNextAgentId(IHandlerContext context) {
        String output = context.getOutput();
        String s = AiFormatJsonUtil.formatJson(output);
        JSONObject jsonObject = null;
//        if (jsonObject == null || !jsonObject.containsKey("id") || StringUtils.isBlank(jsonObject.getString("id"))) {
//        }
        // 默认是9
        Long nextAgentId = 119L;
        try {
            jsonObject = JSONObject.parseObject(s);
            Long id = jsonObject.getLong("agentId");
            if (id != null && id > 0L) {
                nextAgentId = id;
            }
        } catch (Exception e) {
            System.out.println("format json失败:" + s);
        }
        return nextAgentId;
    }
}
