package com.fytec.controller.open.agent;

import cn.dev33.satoken.oauth2.annotation.SaCheckClientToken;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.agent.AgentPublishHistoryDTO;
import com.fytec.dto.agent.AgentQueryDTO;
import com.fytec.dto.agent.PublishAgentDetailDTO;
import com.fytec.dto.application.ApplicationAgentDTO;
import com.fytec.dto.system.DictValueDTO;
import com.fytec.service.agent.AgentService;
import com.fytec.service.system.SysDictService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Validated
@Tag(name = "智能体管理对外接口")
@RequestMapping("/api/agent/open")
@RestController
@RequiredArgsConstructor
public class AgentManageController {
    private final AgentService agentService;
    private final SysDictService sysDictService;

    @GetMapping(value = "/publish/list")
    @Operation(summary = "智能体列表")
    @SaCheckClientToken(scope = "client:execute")
    public R<Page<ApplicationAgentDTO>> queryPublishedAgentPage(AgentQueryDTO dto, Page<ApplicationAgentDTO> page) {
        return R.ok(agentService.queryPublishedAgentList(dto, page));
    }

    @GetMapping(value = "/publish/detail")
    @Operation(summary = "发布智能体详情")
    @SaCheckClientToken(scope = "client:execute")
    public R<PublishAgentDetailDTO> publishAgentDetail(Long agentPublishId) {
        return R.ok(agentService.publishAgentDetail(agentPublishId));
    }

    @GetMapping(value = "/publish/tags")
    @Operation(summary = "获取智能体标签列表")
    @SaCheckClientToken(scope = "client:execute")
    public R<List<DictValueDTO>> getAgentTags() {
        List<DictValueDTO> agentTags = sysDictService.searchDictValue("AGENT_TAGS");
        return R.ok(agentTags);
    }

    //    /api/agent/publish/list?status=&tags=tag3&onlySelf=false&applicationId=&current=1&size=24
    @GetMapping(value = "/publish/tags/filter")
    @Operation(summary = "获取智能体类型列表")
    @SaCheckClientToken(scope = "client:execute")
    public R<List<ApplicationAgentDTO>> queryPublishedAgentListByTags(AgentQueryDTO dto) {
        return R.ok(agentService.queryPublishedAgentListByTags(dto));
    }

}
