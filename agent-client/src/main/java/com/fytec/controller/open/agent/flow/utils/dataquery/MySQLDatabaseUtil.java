package com.fytec.controller.open.agent.flow.utils.dataquery;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;
import java.lang.reflect.Constructor;
import java.sql.*;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class MySQLDatabaseUtil implements DatabaseUtil {
    private DataSource dataSource;

    @Override
    public void init(String jdbcUrl, String username, String password) throws Exception {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl(jdbcUrl);
        config.setUsername(username);
        config.setPassword(password);
        // 配置其他连接池参数...
        // 最小空闲连接
        config.setMinimumIdle(10);
        // 最大连接数
        config.setMaximumPoolSize(20);
        // 自动提交从池中返回的连接
        config.setAutoCommit(true);
        // 空闲连接超时时间
        config.setIdleTimeout(TimeUnit.MINUTES.toMillis(10));
        // 连接最大存活时间
        config.setMaxLifetime(TimeUnit.MINUTES.toMillis(30));
        // 连接超时时间
        config.setConnectionTimeout(TimeUnit.SECONDS.toMillis(30));

        Constructor<? extends DataSource> constructor = HikariDataSource.class.getConstructor(HikariConfig.class);
        dataSource = constructor.newInstance(config);
    }

    @Override
    public Connection getConnection() throws SQLException {
        return dataSource.getConnection();
    }

    @Override
    public void closeDataSource() {
        if (dataSource instanceof AutoCloseable) {
            try {
                ((AutoCloseable) dataSource).close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
    /**
     * 处理SQL语句，根据是否以LIMIT结尾（忽略大小写和空白）插入ORDER BY xxx
     * @param sql 原始SQL语句
     * @return 处理后的SQL语句
     */
    public static String processSql(String sql,String orderBy) {
        // 处理空字符串情况
        if (sql == null || sql.trim().isEmpty()) {
            return sql;
        }

        // 去除SQL末尾所有空白字符（包括空格、换行、制表符等）
        String trimmedSql = sql.replaceAll("\\s+$", "");

        // 正则匹配LIMIT关键字（不区分大小写，确保是独立单词）
        Pattern pattern = Pattern.compile("\\bLIMIT\\b", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(trimmedSql);

        // 查找最后一个LIMIT的位置
        int lastLimitStart = -1;
        while (matcher.find()) {
            lastLimitStart = matcher.start();
        }

        // 判断是否以LIMIT结尾（最后一个LIMIT后无其他内容）
        boolean endsWithLimit = false;
        if (lastLimitStart != -1) {
            String suffix = trimmedSql.substring(lastLimitStart);
            if (suffix.equalsIgnoreCase("LIMIT")) {
                endsWithLimit = true;
            }
        }

        // 根据判断结果处理SQL
        if (endsWithLimit) {
            // 在LIMIT前插入ORDER BY xxx
            String beforeLimit = trimmedSql.substring(0, lastLimitStart);
            String limitPart = trimmedSql.substring(lastLimitStart);
            return beforeLimit + orderBy + limitPart;
        } else {
            // 在SQL末尾添加ORDER BY xxx
            return trimmedSql + orderBy;
        }
    }
    @Override
    public List<Map<String, Object>> executeQuery(String sqlStr, Map<String, String> configs) throws SQLException {
        List<Map<String, Object>> result = new ArrayList<>();
        Connection connection = getConnection();
        if (sqlStr.toLowerCase().contains("group by")) {
            PreparedStatement preparedStatement = connection.prepareStatement("SET sql_mode=(SELECT REPLACE(@@sql_mode,'ONLY_FULL_GROUP_BY',''));");
            preparedStatement.executeUpdate();
        }
        // 还需要添加order by
        String rawSql = sqlStr;
        if (configs!=null && configs.containsKey("order by")) {
            // todo 之后改成sql分析工具实现
            if (sqlStr.endsWith(";")) {
                sqlStr = sqlStr.substring(0, sqlStr.length() - 1);
            }
//            if(sqlStr.toLowerCase().contains("limit ")){
//                // 我们添加的sql
//                int limitIndex = sqlStr.lastIndexOf("limit ");
//                String substring = sqlStr.substring(0, limitIndex - 1);
//
//
//            }else {
//                sqlStr += " order by " + configs.getOrDefault("order by", "");
//            }
            sqlStr = processSql(sqlStr," order by " + configs.getOrDefault("order by", "") + " ");

            log.info("change sql:" + sqlStr);
        }
        PreparedStatement preparedStatement = connection.prepareStatement(sqlStr);
        ResultSet resultSet;
        try {
            resultSet = preparedStatement.executeQuery();
        } catch (Exception e) {
            // 可能因为转换错误，再尝试一次
            try {
                preparedStatement = connection.prepareStatement(rawSql);
                resultSet = preparedStatement.executeQuery();
            }catch (Exception e2){
                log.error("sql run error:" + sqlStr);
                throw new SQLException(e2.getMessage());
            }
        }
        ResultSetMetaData metaData = resultSet.getMetaData();
        int columnCount = metaData.getColumnCount();
//        HashMap<String, Object> columsNames = new HashMap<String, Object>();
        while (resultSet.next()) {
            Map<String, Object> row = new LinkedHashMap<>();// 保持字段顺序
            for (int i = 1; i <= columnCount; i++) {
                String columnName = metaData.getColumnLabel(i); // 获取列名
                Object value = resultSet.getObject(i); // 获取列的值
                row.put(columnName, value == null ? "" : value);
            }
//            if (row.isEmpty()) {
//                for (int i = 1; i <= columnCount; i++) {
//                    String columnName = metaData.getColumnLabel(i); // 获取列名
//                    Object value = resultSet.getObject(i); // 获取列的值
//                    row.put(columnName, value == null ? "" : value);
//                }
//            }
            result.add(row);
        }
        // 补充一下
//        if (result.size() > 1) {
//            for (Map<String, Object> stringObjectMap : result) {
//                Set<String> keys = columsNames.keySet();
//                for (String key : keys) {
//                    if (!stringObjectMap.containsKey(key)) {
//                        Object dv = columsNames.get(key);
//                        if (dv instanceof String) {
//                            stringObjectMap.put(key, "-");
//                        } else {
//                            stringObjectMap.put(key, 0);
//                        }
//                    }
//                }
//            }
//        }

        return result;
    }

}
