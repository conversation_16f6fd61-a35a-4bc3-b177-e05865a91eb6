package com.fytec.controller.open.plugin;

import cn.dev33.satoken.oauth2.annotation.SaCheckClientIdSecret;
import com.fytec.dto.plugin.PluginExecuteDTO;
import com.fytec.service.plugin.PluginService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Slf4j
@Validated
@Tag(name = "智能体执行")
@RequestMapping("/fytec/api/plugin")
@RestController
@RequiredArgsConstructor
public class PluginExecuteController {
    private final PluginService  pluginService; ;

    @PostMapping(value = "/api/execute")
    @Operation(summary = "执行http插件")
    @SaCheckClientIdSecret
    public R<Map<String, Object>> processModelByNonStream(@Validated @RequestBody PluginExecuteDTO dto) {
        return R.ok(pluginService.executePluginApi(dto));
    }
}
