package com.fytec.controller.open.agent.flow.utils.dataquery.dto;

import com.alibaba.fastjson.JSONObject;
import com.fytec.constant.AgentFlowConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class Output {
    private String dataSourceType = AgentFlowConstants.DataSourceType.sql.name();//默认mysql，可能还有接口
    private List<Object> list;
    private List<OutputColumn> columns;
    private JSONObject chartData;
    private String sql;
    private String sqlChange;
    private String sqlPermission;
    private String answer;

    private Map<String, String> configs;
    @Schema(description = "额外参数")
    private JSONObject extraParams;

    private String rawInput;
    private Map<String, String> constants;
    private String log;

    private boolean showMore = false;//展示查询更多数据
    private Long moreDetailAgentId = null; // 展示详情的agentId
    private Long moreTipAgentId = null;//展示更多可能的tip

    public void clearSqlOutput() {
        this.sql = null;
        this.sqlChange = null;
        this.sqlPermission = null;

    }

    // 需要根据这个去掉限制？或者再次校验
}
