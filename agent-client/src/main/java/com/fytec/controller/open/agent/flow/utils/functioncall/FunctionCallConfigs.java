package com.fytec.controller.open.agent.flow.utils.functioncall;


import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Method;
import java.util.LinkedList;
import java.util.List;

/**
 * function call的默认配置方法类，包含提示词等
 */
public class FunctionCallConfigs {
    public final static String KEY_NAME = "{NAME}";
    public final static String KEY_DESC = "{DESC}";

    public final static String KEY_PARAMS = "{PARAMS}";

    public final static String KEY_FUNCTION_CALL_TOOLS = "{FUNCTION_CALL_TOOLS}";

    public final static String DEFAULT_FUNCTION_CALL_PROMPT = """
            ## 已有工具/接口:
                """ + KEY_FUNCTION_CALL_TOOLS + """
            \n## 输出规则：
            - 结合用户输入判断是否需要通过这些工具/接口先获取相关数据，输出JSON数组，JSON内容第一层里包含工具名称name、工具参数params:{key是参数名，value是参数值}
            - json前后不需要添加其他字符保证结果可以直接被序列化为json。
            """;
    public final static String DEFAULT_FUNCTION_CALL_TOOL_PROMPT = "工具名称:"+KEY_NAME+"工具描述:"+KEY_DESC+",参数:["+KEY_PARAMS+"]";

    // 根据以上提示词，构建反射类




}
