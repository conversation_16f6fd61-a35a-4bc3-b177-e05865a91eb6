package com.fytec.controller.open.agent.flow.utils.edu;

import com.alibaba.fastjson.JSON;
import com.fytec.controller.open.agent.flow.handler.edu.EduTableMetaQueryAgent;
import com.fytec.controller.open.agent.flow.protocol.IHandlerContext;
import com.fytec.controller.open.agent.flow.protocol.errors.AgentDataNotPermissionException;
import com.fytec.controller.open.agent.flow.utils.SqlPermissonInterface;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.statement.Statement;
//import net.sf.jsqlparser.statement.select.SelectItem;

import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.relational.InExpression;
import net.sf.jsqlparser.expression.StringValue;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.select.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.*;

//@Component
public class PermissonSqlChangeUtil implements SqlPermissonInterface {
    private static String[] noPermissionTableName = {"v_area_edu_size", "v_area_teacher_analysis", "v_area_teacher_reward"};
    // 权限字段与表字段的映射（表名 -> 权限字段 -> 表字段）
    private static final Map<String, Map<String, String>> TABLE_PERMISSION_MAPPING = new HashMap<>();

    static {
        // 教师相关表（v_teacher_baseinfo、v_teacher_title等）
        Map<String, String> teacherMainTableMap = new HashMap<>();
        teacherMainTableMap.put("teacherIdList", "uuid");
        teacherMainTableMap.put("schoolIdList", "school_id");
        teacherMainTableMap.put("subjectList", "rjkc");
        teacherMainTableMap.put("xdList", "rjxd");
        TABLE_PERMISSION_MAPPING.put("v_teacher_baseinfo", teacherMainTableMap);

        Map<String, String> teacherTableMap = new HashMap<>();
        teacherTableMap.put("teacherIdList", "teacherid");
        teacherTableMap.put("schoolIdList", "school_id");
        TABLE_PERMISSION_MAPPING.put("v_teacher_title", teacherTableMap);
        TABLE_PERMISSION_MAPPING.put("v_teacher_thesis", teacherTableMap);
        TABLE_PERMISSION_MAPPING.put("v_teacher_rcjl", teacherTableMap);
        TABLE_PERMISSION_MAPPING.put("v_teacher_reward", teacherTableMap);

        // 学校相关表（v_school_baseinfo、v_school_campus等）
        Map<String, String> schoolTableMap = new HashMap<>();
        schoolTableMap.put("schoolIdList", "school_id");
        TABLE_PERMISSION_MAPPING.put("v_school_baseinfo", schoolTableMap);
        TABLE_PERMISSION_MAPPING.put("v_school_campus", schoolTableMap);
        TABLE_PERMISSION_MAPPING.put("v_school_reward", schoolTableMap);

        // 学生相关表（v_student_baseinfo）
        Map<String, String> studentTableMap = new HashMap<>();
        studentTableMap.put("studentIdList", "uuid");
        studentTableMap.put("classIdList", "class_id");
        studentTableMap.put("schoolIdList", "school_id");
        TABLE_PERMISSION_MAPPING.put("v_student_baseinfo", studentTableMap);
    }

    public static String processSqlWithPermissions(String sql, EduTableMetaQueryAgent.DataPermission permission) throws JSQLParserException {
        Statement statement = CCJSqlParserUtil.parse(sql);
        if (statement instanceof Select) {
            Select selectStatement = (Select) statement;
            Select selectBody = selectStatement.getSelectBody();
            if (selectBody instanceof PlainSelect) {
                PlainSelect plainSelect = (PlainSelect) selectBody;
                List<Table> tables = extractTables(plainSelect);
                for (Table table : tables) {
//                    String tableName = table.getName();
                    String permissionCondition = generatePermissionCondition(permission, table);
                    if (!permissionCondition.isEmpty()) {
                        Expression where = plainSelect.getWhere();
                        Expression permissionExpression = CCJSqlParserUtil.parseCondExpression(permissionCondition);
                        if (where != null) {
                            plainSelect.setWhere(new AndExpression(where, permissionExpression));
                        } else {
                            plainSelect.setWhere(permissionExpression);
                        }
                    }
                }

                processSelectExpressions(plainSelect.getSelectItems(), permission);


                return plainSelect.toString();
            }
        } else {
//            throw new V
        }
        return sql;
    }

    private static void processSelectExpressions(List<SelectItem<?>> selectItems, EduTableMetaQueryAgent.DataPermission permission) throws JSQLParserException {
        for (SelectItem selectItem : selectItems) {
//
//            if (selectItem instanceof Select) {
//                SubSelect subSelect = (SubSelect) selectItem;
//                processSelectBody(subSelect.getSelectBody(), permission);
//            } else if (selectItem instanceof Expression) {
//                Expression exprItem = (Expression) selectItem;
//                processExpression(exprItem.getExpression(), permission);
//            }
            processExpression(selectItem.getExpression(), permission);
        }
    }

    private static void processSelectBody(Select selectBody, EduTableMetaQueryAgent.DataPermission permission) throws JSQLParserException {
        if (selectBody instanceof PlainSelect) {
            PlainSelect plainSelect = (PlainSelect) selectBody;
            // 处理主表和JOIN表
            {
                List<Table> tables = extractTables(plainSelect);
                for (Table table : tables) {
//                    String tableName = table.getName();
                    String permissionCondition = generatePermissionCondition(permission, table);
                    if (!permissionCondition.isEmpty()) {
                        Expression where = plainSelect.getWhere();
                        Expression permissionExpression = CCJSqlParserUtil.parseCondExpression(permissionCondition);
                        if (where != null) {
                            plainSelect.setWhere(new AndExpression(where, permissionExpression));
                        } else {
                            plainSelect.setWhere(permissionExpression);
                        }
                    }
                }
            }
            // 处理子查询中的SELECT（如SELECT列表中的子查询）
            processSelectExpressions(plainSelect.getSelectItems(), permission);
            // 处理WHERE条件中的子查询（如有）
            if (plainSelect.getWhere() != null) {
                processExpression(plainSelect.getWhere(), permission);
            }
        } else if (selectBody instanceof SetOperationList) {
            // 处理UNION等集合操作（可选）
            SetOperationList setOperation = (SetOperationList) selectBody;
            for (Select select : setOperation.getSelects()) {
                processSelectBody(select, permission);
            }
        } else if (selectBody instanceof Select) {
            // 递归处理子查询
            Select subSelect = selectBody.getSelectBody();
            if (subSelect != null)
                processSelectBody(((ParenthesedSelect) subSelect).getSelect(), permission);
        }
    }

    private static void processExpression(Expression expression, EduTableMetaQueryAgent.DataPermission permission) throws JSQLParserException {
        if (expression instanceof Select) {
            Select subSelect = (Select) expression;
            processSelectBody(subSelect.getSelectBody(), permission);
        } else if (expression instanceof AndExpression) {
            processExpression(((AndExpression) expression).getLeftExpression(), permission);
            processExpression(((AndExpression) expression).getRightExpression(), permission);
        } else if (expression instanceof InExpression) {
            // 递归处理子表达式（可选扩展）
            int debug = 1;
        }
    }

    private static List<Table> extractTables(PlainSelect plainSelect) {
        List<Table> tables = new ArrayList<>();
        Table fromTable = plainSelect.getFromItem() instanceof Table ? (Table) plainSelect.getFromItem() : null;
        if (fromTable != null) {
            tables.add(fromTable);
        }
        List<Join> joins = plainSelect.getJoins();
        if (joins != null) {
            for (Join join : joins) {
                if (join.getRightItem() instanceof Table) {
                    tables.add((Table) join.getRightItem());
                } else if (join.getRightItem() instanceof Select) {
                    Select subSelect = (Select) join.getRightItem();
                    if (subSelect.getSelectBody() instanceof PlainSelect) {
                        tables.addAll(extractTables((PlainSelect) subSelect.getSelectBody()));
                    }
                }
            }
        }
        return tables;
    }

    private static String generatePermissionCondition(EduTableMetaQueryAgent.DataPermission permission, Table table) {
        List<String> conditions = new ArrayList<>();
        String tableName = table.getName();
        String alias = table.getAlias() == null ? tableName : table.getAlias().getName();
        Map<String, String> fieldMap = TABLE_PERMISSION_MAPPING.getOrDefault(tableName, new HashMap<>());

        // 超级权限：跳过所有过滤
        if (permission.isAreaAdmin()) {
            return "";
        }
        if (!permission.getSubjectList().isEmpty()) {
            buildSqlByList(permission, "subjectList", conditions, fieldMap, alias);
        }
        if (!permission.getXdList().isEmpty()) {
            buildSqlByList(permission, "xdList", conditions, fieldMap, alias);
        }
        // 处理教师ID权限
        if (!permission.getTeacherIdList().isEmpty()) {
            buildSqlByList(permission, "teacherIdList", conditions, fieldMap, alias);
//            String field = fieldMap.get("teacherIdList");
//            if (field != null) {
//                Column expression = new Column(alias + "." + field);
//                if (permission.getTeacherIdList().size() > 1) {
//                    StringBuffer sb = new StringBuffer();
//                    sb.append("(");
//                    for (String id : permission.getTeacherIdList()) {
//                        sb.append(String.format("'%s'", id));
//                    }
//                    sb.append(")");
//                    conditions.add(expression.toString() + " in " + sb.toString());
//                } else {
//                    StringValue value = new StringValue(permission.getTeacherIdList().stream().toList().getFirst());
//                    conditions.add(new EqualsTo(expression, value).toString());
//                }
//            }
        }

        // 处理学校ID权限
        if (!permission.getSchoolIdList().isEmpty()) {
            String field = fieldMap.get("schoolIdList");
            if (field != null) {
                Column expression = new Column(alias + "." + field);
                if (permission.getSchoolIdList().size() > 1) {
                    StringBuffer sb = new StringBuffer();
                    sb.append("(");
                    for (String id : permission.getSchoolIdList()) {
                        sb.append(String.format("'%s'", id));
                    }
                    sb.append(")");
                    conditions.add(expression.toString() + " in " + sb.toString());
                } else {
                    StringValue value = new StringValue(permission.getSchoolIdList().stream().toList().get(0));
                    conditions.add(new EqualsTo(expression, value).toString());
                }

            }
        }

        // 处理班级ID权限
        if (!permission.getClassIdList().isEmpty()) {
            String field = fieldMap.get("classIdList");
            if (field != null) {
                Column expression = new Column(alias + "." + field);
                if (permission.getTeacherIdList().size() > 1) {
                    StringBuffer sb = new StringBuffer();
                    sb.append("(");
                    for (String id : permission.getClassIdList()) {
                        sb.append(String.format("'%s'", id));
                    }
                    sb.append(")");
                    conditions.add(expression.toString() + " in " + sb.toString());
                } else {
                    StringValue value = new StringValue(permission.getClassIdList().stream().toList().get(0));
                    conditions.add(new EqualsTo(expression, value).toString());
                }
            }
        }

        // 处理学生ID权限
        if (!permission.getStudentIdList().isEmpty()) {
            String field = fieldMap.get("studentIdList");
            if (field != null) {
                Column expression = new Column(alias + "." + field);
                if (permission.getStudentIdList().size() > 1) {


                    StringBuffer sb = new StringBuffer();
                    sb.append("(");
                    for (String id : permission.getStudentIdList()) {
                        sb.append(String.format("'%s'", id));
                    }
                    sb.append(")");
                    conditions.add(expression.toString() + " in " + sb.toString());
                } else {
                    StringValue value = new StringValue(permission.getStudentIdList().stream().toList().get(0));
                    conditions.add(new EqualsTo(expression, value).toString());
                }
            }
        }

        return String.join(" AND ", conditions);
    }

    private static void buildSqlByList(EduTableMetaQueryAgent.DataPermission permission,
                                       String key, List<String> conditions, Map<String, String> fieldMap, String alias) {
        //判断是否包含学科学段

        String field = fieldMap.get(key);
        Set<String> values = null;
        try {
            Field fieldValue = permission.getClass().getDeclaredField(key);
            fieldValue.setAccessible(true);
            values = (Set<String>) fieldValue.get(permission);
        } catch (Exception e) {
            e.printStackTrace();
            field = null;
        }
        if (field != null) {
            Column expression = new Column(alias + "." + field);
            if (values.size() > 1) {
                StringBuffer sb = new StringBuffer();
                sb.append("(");
                for (String id : values) {
                    sb.append(String.format("'%s'", id));
                }
                sb.append(")");
                conditions.add(expression.toString() + " in " + sb.toString());
            } else {
                StringValue value = new StringValue(values.stream().toList().getFirst());
                conditions.add(new EqualsTo(expression, value).toString());
            }
        }
    }

    // 根据权限，修改sql表达式
    public static void main(String[] args) {
        String permissionJson = "{\"areaAdmin\":false,\"classIdList\":[\"A180FF7BC9A74C7A81B3E0B1503CFBC0\"],\"schoolIdList\":[],\"studentIdList\":[],\"teacherIdList\":[\"\"],\"subjectList\":[\"语文,数学\"],\"xdList\":[\"小学,初中\"]}";
        System.out.println("权限class:" + permissionJson);
        EduTableMetaQueryAgent.DataPermission dataPermission = JSON.parseObject(permissionJson, EduTableMetaQueryAgent.DataPermission.class);
        String sql = "SELECT COUNT(*) AS '园区老师人数'\n" + "FROM v_teacher_baseinfo\n" + "JOIN v_school_baseinfo ON v_teacher_baseinfo.school_id = v_school_baseinfo.school_id\n" + "WHERE v_teacher_baseinfo.yrxs != '临聘员工' AND v_teacher_baseinfo.data_status = '0' AND v_teacher_baseinfo.organizeschool_name NOT LIKE '%教育局%';";
        String sqlChange = "select      rjkc as '任教学科',     count(*) as '教师人数' from      v_teacher_baseinfo where      yrxs != '临聘员工'      and data_status = '0' group by      rjkc;";
        sql = sqlChange;
        sqlChange = "SELECT year, (SELECT COUNT(*) FROM v_teacher_baseinfo WHERE rjkc = '信息科技' AND STR_TO_DATE(ryqzgsj, '%Y年%m月%d日') < CONCAT(year, '-01-01') AND yrxs != '临聘员工') + (SELECT COUNT(*) FROM v_teacher_baseinfo WHERE rjkc = '信息科技' AND update_time >= CONCAT(year, '-01-01') AND data_status = '9' AND STR_TO_DATE(ryqzgsj, '%Y年%m月%d日') < CONCAT(year, '-01-01')) AS '考虑退休的老师人数', (SELECT COUNT(*) FROM v_teacher_baseinfo WHERE rjkc = '信息科技' AND STR_TO_DATE(ryqzgsj, '%Y年%m月%d日') < CONCAT(year, '-01-01') AND yrxs != '临聘员工' AND data_status = '0') AS '不考虑退休的老师人数' FROM (SELECT 2023 AS year UNION ALL SELECT 2024 UNION ALL SELECT 2025) years ORDER BY year";
//        sql = sqlChange;
        sqlChange = "select live_status, count(*) as 数量 from population where address like '%娄葑街道%' group by live_status;\n";
//        sql = sqlChange;
        sqlChange = "select      case          when age < 30 then '30岁以下'         when age between 30 and 35 then '30-35岁'         else '35岁以上'     end as '年龄分组',     xb as '性别',     count(*) as '教师人数' from v_teacher_baseinfo inner join v_school_baseinfo on v_teacher_baseinfo.organizeschool_id = v_school_baseinfo.school_id where v_school_baseinfo.school_jbz in ('公办', '直属', '街道')   and v_school_baseinfo.school_lb in ('小学', '普通初中', '普通高中')   and v_teacher_baseinfo.data_status = '0'   and v_teacher_baseinfo.yrxs = '教职员工'   and v_school_baseinfo.school_name not like '%苏州工业园区教育局%' group by      case          when age < 30 then '30岁以下'         when age between 30 and 35 then '30-35岁'         else '35岁以上'     end,     xb order by       '年龄分组',     xb";
        sqlChange = "SELECT \n" +
                "    uuid AS '教师ID',\n" +
                "    xm AS '教师姓名',\n" +
                "    school_name AS '所在学校',\n" +
                "    organizeschool_name AS '编制学校',\n" +
                "    jg AS '籍贯',\n" +
                "    csrq AS '出生日期',\n" +
                "    age AS '年龄',\n" +
                "    yrxs AS '用人形式',\n" +
                "    xb AS '性别',\n" +
                "    mz AS '民族',\n" +
                "    zzmm AS '政治面貌',\n" +
                "    hyzk AS '婚姻状况',\n" +
                "    rjkc AS '任教学科',\n" +
                "    rjxd AS '任教学段',\n" +
                "    ryqzgsj AS '入园区工作时间',\n" +
                "    cjgzny AS '参加工作年月',\n" +
                "    zgxl AS '最高学历',\n" +
                "    rylb AS '人员类别',\n" +
                "    zgzc AS '最高职称',\n" +
                "    bzlx AS '编制类型',\n" +
                "    zgxkry AS '最高学科荣誉',\n" +
                "    zj AS '职级',\n" +
                "    jtzw AS '具体职务',\n" +
                "    jszgzzl AS '教师资格证种类'\n" +
                "FROM \n" +
                "    v_teacher_baseinfo t\n" +
                "WHERE \n" +
                "    xm = '肖年志' \n" +
                "    AND yrxs != '临聘员工'\n" +
                "LIMIT 20;";
        sql = sqlChange;

        try {
            System.out.println("原始的SQL：");
            System.out.println(sql);
            String newSql = processSqlWithPermissions(sql, dataPermission);
            System.out.println("生成后的SQL：");
            System.out.println(newSql);
        } catch (JSQLParserException e) {
            e.printStackTrace();
        }
    }

    @Override
    public String permissonCheckSqlAndChange(IHandlerContext context, String sql) throws AgentDataNotPermissionException {
        String permissonJson = null;
        if (context.getConstants() != null
                && context.getConstants().containsKey("dataPermissionJson")) {
            permissonJson = context.getConstants().get("dataPermissionJson");
        }

        if (StringUtils.isBlank(permissonJson)) {
            return sql;
        }
        try {
            System.out.println("原始的SQL：");
            System.out.println(sql);
            EduTableMetaQueryAgent.DataPermission dataPermission = JSON.parseObject(permissonJson, EduTableMetaQueryAgent.DataPermission.class);
            String newSql = processSqlWithPermissions(sql, dataPermission);
            System.out.println("生成后的SQL：");
            System.out.println(newSql);
            return newSql;
        } catch (JSQLParserException e) {
            e.printStackTrace();
            return "select '无权查看' as '提醒'";
        }
    }

}
