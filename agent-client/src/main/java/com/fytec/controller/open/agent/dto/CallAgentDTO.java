package com.fytec.controller.open.agent.dto;

import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Map;

@Data
public class CallAgentDTO {

    @Schema(description = "会话ID")
    @NotBlank(message = "会话ID不能为空(前端生成uuid)")
    private String conversationId;

    @Schema(description = "智能体ID")
    private Long agentPublishId;

    @Schema(description = "智能体ID")
//    @NotNull(message = "智能体ID不能为空")
    private String agentId;

    @Schema(description = "用户输入")
    @NotBlank(message = "用户输入不能为空")
    private String userInput;

    @Schema(description = "用户原始输入")
    private String rawInput;

    @Schema(description = "用户id")
    @NotNull(message = "第三方用户id不能为空")
    private String thirdUserId;

    @Schema(description = "是否开启联网插件(默认关闭)")
    private boolean enablePluginSearch = false;

    @Schema(hidden = false)
    private String flowId;

    @Schema(description = "第三方id，可在历史记录时进行查询")
    private String objectId;


    @Schema(description = "用户id")
//    @NotNull(message = "第三方用户token不能为空")
    private String thirdUserToken;

    @Schema(description = "当前使用的模型",hidden = true)
    private JSONObject currentModel;

    @Schema(description = "系统提示词")
    private String sysPrompt;

    @Schema(description = "额外配置key智能体id，value是map，目前包含constants")
    private Map<String, Object> configs;
//    "configs":{
//        "51":{
//            "constants":{
//                "key":"value",//正则替换
//            }
//        }
//    }
}
