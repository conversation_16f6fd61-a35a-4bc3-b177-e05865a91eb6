package com.fytec.controller.open.agent.flow.utils.dataquery;

import com.fytec.controller.open.agent.flow.protocol.IHandlerContext;
import com.fytec.controller.open.agent.flow.protocol.errors.AgentDataNotPermissionException;
import com.fytec.controller.open.agent.flow.utils.SqlPermissonInterface;
import com.fytec.controller.open.agent.flow.utils.dataquery.permission.ProdShequPermissionInterface;
import com.fytec.controller.open.agent.flow.utils.edu.PermissonSqlChangeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DefaultSqlPermissonInterface implements SqlPermissonInterface {
    @Override
    public String permissonCheckSqlAndChange(IHandlerContext context, String sql) throws AgentDataNotPermissionException {
        return switch (context.getEnvType()) {
            case "prod" -> prodShequFormatSql(context, sql);
            case "jiaoyu" -> jiaoyuYjFormatSql(context, sql);
            default -> sql;
        };
    }

    private String prodShequFormatSql(IHandlerContext context, String sql) throws AgentDataNotPermissionException {
        log.debug(String.format("%s ID change sql", context.getAgentId()));
        if (StringUtils.isNotBlank(context.getAgentId()) && context.getAgentId().equals("133")) {
            // 社区的，需要处理
            return new ProdShequPermissionInterface().permissonCheckSqlAndChange(context, sql);
        }
        return sql;
    }

    private String jiaoyuYjFormatSql(IHandlerContext context, String sql) throws AgentDataNotPermissionException {

        return new PermissonSqlChangeUtil().permissonCheckSqlAndChange(context, sql);
    }


}
