package com.fytec.controller.open.agent.flow.handler.prod;

import com.fytec.controller.open.agent.flow.handler.edu.EduTableQuerySqlAgent;
import com.fytec.controller.open.agent.flow.protocol.AgentHandlerError;
import com.fytec.controller.open.agent.flow.protocol.IAgentHandler;
import com.fytec.controller.open.agent.flow.protocol.IHandlerContext;
import com.fytec.controller.open.agent.flow.service.AgentFlowService;
import com.fytec.handler.IResponseHandlerProxy;
import com.fytec.utils.AiFormatJsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

public class ShequProdTableQuerySqlAgent implements IAgentHandler {
    private static final Logger log = LoggerFactory.getLogger(ShequProdTableQuerySqlAgent.class);
    private long formatAgentId = 136L;

    // BI查询
    @Override
    public Long getAgentId() {
        return 133L;
    }

    @Override
    public Long getThinkAgentId(IHandlerContext context) {
//        return 147L;
        return null;
    }


    @Override
    public void beforeHandle(IHandlerContext context) throws AgentHandlerError {
        IAgentHandler.super.beforeHandle(context);
        HashMap<String, String> sqlConfigs = new HashMap<>();
        sqlConfigs.put("host", "*************");
        sqlConfigs.put("port", "40165");
        sqlConfigs.put("schema", "sip_community_dev");
        sqlConfigs.put("username", "sip_community");
        sqlConfigs.put("password", "Fengyun@qaz2023");
        context.setSqlConfigs(sqlConfigs);
    }


    @Override
    public void handle(IHandlerContext context) throws AgentHandlerError {
        IAgentHandler.super.handle(context);
        // 修改历史返回记录
//        String anserwer = context.getHistoryDTO().getAnswer();
//        anserwer = AiFormatJsonUtil.formatSql(anserwer);
//        context.setInput(String.format("%s，token为%s", anserwer, context.getThirdUserToken()));
//        context.getAgentFlowService().execAgentChangeNodeById(159L, context);//

    }

    @Override
    public String handlerEnd(IHandlerContext context, Map<String, Object> values) {
        EduTableQuerySqlAgent eduTableQuerySqlAgent = new EduTableQuerySqlAgent();
        eduTableQuerySqlAgent.setFormatAgentId(formatAgentId);
        return eduTableQuerySqlAgent.handlerEnd(context, values);
    }

    @Override
    public String getErrorMsg(IHandlerContext context) {
        return "查询遇到一些问题";
    }

    @Override
    public String getDesc() {
        return "sql生成智能体";
    }

    @Override
    public IResponseHandlerProxy getResponseHandlerProxy(RESPONSE_TYPE responseType, IHandlerContext context) {
        // 改写，思考过程输出，流阻塞
        return new IResponseHandlerProxy() {
            @Override
            public String handler(String responseStr) {
                if (responseStr == null) {
                    return null;
                }
                if (responseStr.startsWith("event:done")) {
                    // 忽略流处理的
                    return null;
                }
                // 作为思考过程来处理
                if (responseStr.contains("\"type\":\"reasoningContent\"")) {
//                    responseStr = responseStr.replace("\"type\":\"content\"", "\"type\":\"reasoningContent\"");
                    // 忽略正文，不返回
                    return responseStr;
                }
                // 只展示带有思考过程的

//                return responseStr;
                return null;
            }
        };
    }


}
