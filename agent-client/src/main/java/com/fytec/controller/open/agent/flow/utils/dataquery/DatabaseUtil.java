package com.fytec.controller.open.agent.flow.utils.dataquery;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

public interface DatabaseUtil {
    void init(String jdbcUrl, String username, String password) throws Exception;

    Connection getConnection() throws SQLException;

    void closeDataSource();

    List<Map<String, Object>> executeQuery(String sqlStr,Map<String,String> configs) throws SQLException;

}

