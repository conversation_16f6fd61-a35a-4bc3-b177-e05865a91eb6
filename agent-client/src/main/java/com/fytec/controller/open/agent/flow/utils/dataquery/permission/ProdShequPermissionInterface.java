package com.fytec.controller.open.agent.flow.utils.dataquery.permission;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fytec.controller.open.agent.flow.protocol.IHandlerContext;
import com.fytec.controller.open.agent.flow.protocol.errors.AgentDataNotPermissionException;
import com.fytec.controller.open.agent.flow.utils.SqlPermissonInterface;
import com.fytec.jdbc.MysqlJdbc;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;

import java.util.*;

import static com.fytec.constant.ProjectApiKey.SIP_API_KEY;

public class ProdShequPermissionInterface implements SqlPermissonInterface {
    private static final String _url = "http://demo45.fyxmt.com/community_api/admin/ai-api/addPermission";

    @Override
    public String permissonCheckSqlAndChange(IHandlerContext context, String sql) throws AgentDataNotPermissionException {

        Unirest.setTimeouts(600000, 600000);
        HttpResponse<String> response = null;
        Map<String, String> body = new HashMap<>();
        body.put("sql", sql);
        try {
            response = Unirest.post(_url)
                    .header("Authorization", context.getThirdUserToken())
                    .header("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .body(JSON.toJSONString(body))
                    .asString();
        } catch (UnirestException e) {
//            throw new RuntimeException(e);
            e.printStackTrace();
            throw new AgentDataNotPermissionException(e.getMessage());
//            return sql;
        }
//        JSONObject parseObj = JSON.parseObject(response.getBody());
        if (response.getStatus() == 200) {
            if (response.getBody().contains("\"code\":")) {
                //不是直接的sql，其实有问题
                throw new AgentDataNotPermissionException(response.getBody());
            }
            return response.getBody();
        }
        throw new AgentDataNotPermissionException(response.getBody());

//        return sql;

    }

    public static void main(String[] args) throws AgentDataNotPermissionException {
        String sql = "SELECT COUNT(*) AS '娄葑街道居民数量' FROM population_management p JOIN sc_area s ON p.street_id = s.id WHERE s.name LIKE '娄葑街道' AND p.is_deleted = 0;";
        IHandlerContext context = new IHandlerContext();
        context.setThirdUserToken("bearer b7781759-b6e4-4275-bbfe-2b96f640f1b6");
        String sql2 = new ProdShequPermissionInterface().permissonCheckSqlAndChange(context, sql);
        System.out.println(sql2);
//
//        List<String> columns = new ArrayList<>();
////        String columnsStr = "year,jllx,level_,hjrs";
//        columns = Arrays.asList(columnsStr.split(","));
//        List<Map<String, Object>> basicData = MysqlJdbc.query(url, username, password, driver, sql, columns, null);


    }
}
