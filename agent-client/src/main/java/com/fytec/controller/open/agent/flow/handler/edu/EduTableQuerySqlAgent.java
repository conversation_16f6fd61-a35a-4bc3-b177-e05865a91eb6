package com.fytec.controller.open.agent.flow.handler.edu;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fytec.constant.AgentFlowConstants;
import com.fytec.controller.open.agent.flow.protocol.AgentHandlerError;
import com.fytec.controller.open.agent.flow.protocol.IAgentHandler;
import com.fytec.controller.open.agent.flow.protocol.IHandlerContext;
import com.fytec.controller.open.agent.flow.utils.dataquery.dto.Output;
import com.fytec.controller.open.agent.flow.utils.dataquery.dto.OutputColumn;
import com.fytec.handler.IResponseHandlerProxy;
import com.fytec.utils.AiFormatJsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import java.util.*;

public class EduTableQuerySqlAgent implements IAgentHandler {
    private static final Logger log = LoggerFactory.getLogger(EduTableQuerySqlAgent.class);
//    private long formatAgentId = 24L;

    @Value("${agent.utils.echart.format}")
    private Long formatAgentId;

    @Override
    public Long getAgentId() {
//        return 7L;
        return 15L;
    }
//    用户输入：园区近4年信息科技老师人数趋势 查询sql：xxx

    @Override
    public Long getThinkAgentId(IHandlerContext context) {
        // 返回思考过程的

        if (this.getBlockButPrintThink(context)) {
            return null;
        } else {
            return 19L;
        }
    }

    @Override
    public void beforeHandle(IHandlerContext context) throws AgentHandlerError {
        IAgentHandler.super.beforeHandle(context);
        context.setThinkAgentId(getThinkAgentId(context));
        //todo 可以设置 查看更多（sql工具）、查看tip的智能体id、查看详情的智能体id
    }

    @Override
    public void handle(IHandlerContext context) throws AgentHandlerError {
        IAgentHandler.super.handle(context);
//        context.setThinkAgentId(getThinkAgentId());
        context.setNextAgentId(getNextAgentId(context));
//        if (!context.getConstants().containsKey("max_results_len")) {
//            context.getConstants().put("max_results_len", "20");
//        }
        // 不然让外部控制吧
        // 可以考虑处理一下吧
        // 24L.改写表格的id

    }

    @Override
    public String handlerEnd(IHandlerContext context, Map<String, Object> values) {
        IAgentHandler.super.handlerEnd(context, values);
        List<Map<String, Object>> rLs = (List<Map<String, Object>>) values.get("list");
        Output output = (Output) values.get("output");
        output = JSONObject.parseObject(JSON.toJSONString(output), Output.class);
        values.remove("output");

        // sql结果
        // 智能体：
        StringBuffer input = new StringBuffer();
        input.append(String.format("用户输入:%s\n", context.getInput()));
        List<Map<String, Object>> array = new ArrayList<>();
        boolean hasMore = false;
        if (rLs.size() <= 10) {
            array = rLs;
        } else {
            // 只会给最多10个,避免太多分类
            if (rLs.size() > 100) {
                //这种很多的就需要给的多了
                for (int i = 0; i < 30; i++) {
                    array.add(rLs.get(i));
                }
            } else {
                for (int i = 0; i < 20 && i < rLs.size(); i++) {
                    array.add(rLs.get(i));
                }
            }

            hasMore = true;
        }
        input.append(String.format("已知数据: %s", StringUtils.join(array, "\n")));
        // todo 看看能不能根据得到的1.数据数组，2.结合提示词，生成x轴和y轴的模板
//        {"chartData":{"yAxis":{"name":"教师数量","type":"value"},"xAxis":{"data":["中科大附....，这个data需要补充
        // 园"],"name":"学校名称","type":"category"},
//        "series":[{"data":[22,23,126,1,91,0],"name":"男","type":"bar"}
//        ,{"data":[42,83,286,0,169,21],"name":"女","type":"bar"}]
//        "columns":[{"label":"教师数量","prop":"教师数量"},{"label":"学校名称","prop":"学校名称"},{"label":"性别","prop":"性别"}]

        try {

            String resultJson = context.getAgentFlowService().execAgentNodeById(AgentFlowConstants.AGENT_RUN_TYPE.format,
                    this.formatAgentId, context, input.toString());
            if (resultJson == null) {
                // 忽略
                return null;
            } else {
                log.info("echart format:" + resultJson);
            }

            if (hasMore && StringUtils.isNotBlank(resultJson)) {
                String outputstr = AiFormatJsonUtil.formatJson(resultJson);
                try {
                    JSONObject echartTypeJson = JSON.parseObject(outputstr);
                    if (echartTypeJson.containsKey("chartData")) {
                        JSONObject chartData = echartTypeJson.getJSONObject("chartData");
                        String yLabel = chartData.getJSONObject("yAxis").getString("name");
                        String xLabel = chartData.getJSONObject("xAxis").getString("name");
                        Set<String> seriesKeys;
                        if (echartTypeJson.containsKey("seriesKey")) {
                            String seriesKey = echartTypeJson.getString("seriesKey");
                            if (seriesKey != null) {
                                String[] split = seriesKey.split("@");
                                seriesKeys = new LinkedHashSet<>(Arrays.asList(split));
                                System.out.println("seriesKeys:" + seriesKeys);
                            } else {
                                seriesKeys = null;
                            }

                        } else {
                            seriesKeys = null;
                        }

                        Object series = chartData.get("series");
                        // 取第一个作为labels
                        List<Object> tmpXlabels = new LinkedList<>();
                        if (series instanceof JSONArray) {
                            if (seriesKeys != null && seriesKeys.size() >= 2) {
                                //  valueKey可能因为上面只给10个生成不全，可以考虑自己生成series的
                                //  如果存在serise为空的，那么就需要自己生成，根据seriesKey，而不是依靠大模型生成的了

                                //自己生成写了，也只能bar
                                // ,{"data":[],"name":"初中-女","type":"bar"
                                Set<String> keys = new LinkedHashSet<>();
                                List<String> names = new ArrayList<>(seriesKeys.size());
                                Map<String, List<Map<String, Object>>> dictResult = new HashMap<>();
                                for (Map<String, Object> rL : rLs) {
                                    names.clear();
                                    for (String seriesKey : seriesKeys) {
                                        for (String key : rL.keySet()) {
                                            if (seriesKey.equals(key)) {
                                                names.add(rL.getOrDefault(key, "-").toString());
                                                break;
                                            }
                                        }
                                    }
                                    String newKey = StringUtils.join(names, "-");
                                    keys.add(newKey);
                                    if (!dictResult.containsKey(newKey)) {
                                        dictResult.put(newKey, new LinkedList<>());
                                    }
                                    List<Map<String, Object>> maps = dictResult.get(newKey);
                                    maps.add(rL);
                                }
                                ((JSONArray) series).clear();

                                keys.stream().sorted().forEach(newKey -> {
                                    JSONObject newItem = new JSONObject();
                                    newItem.put("type", "bar");
                                    newItem.put("name", newKey);
                                    LinkedList<Object> data = new LinkedList<>();

                                    for (Map<String, Object> rL : dictResult.get(newKey)) {
                                        Object value = rL.get(yLabel);
                                        if (value == null) {
                                            // 可能字段有一定差异了
                                            for (String key : rL.keySet()) {
                                                if (key.contains(yLabel) || yLabel.contains(key)) {
                                                    data.add(key);
                                                    break;
                                                }
                                            }

                                        } else {
                                            data.add(value);
                                        }
                                    }

                                    newItem.put("data", data);
                                    ((JSONArray) series).add(newItem);
                                });


                            } else {
                                // 补充value
                                for (int i = 0; i < ((JSONArray) series).size(); i++) {
                                    JSONObject eachItem = ((JSONArray) series).getJSONObject(i);
                                    String valueKey = eachItem.getString("name");


                                    // valueKey可能多个
                                    JSONArray data = eachItem.getJSONArray("data");
                                    data.clear();
                                    // 需要添加补充进去
                                    for (Map<String, Object> each : rLs) {
                                        // 还需要判断存在 valueKey
                                        boolean isThisValue = false;

                                        if (seriesKeys == null) {
                                            for (String key : each.keySet()) {
                                                if (valueKey.equals(each.get(key))) {
                                                    isThisValue = true;
                                                    break;
                                                }
                                            }
                                        } else {
                                            List<String> names = new ArrayList<>();
                                            for (String k : seriesKeys) {
                                                for (String key : each.keySet()) {
                                                    if (k.equals(key)) {
                                                        names.add(each.get(key).toString());
                                                    }
                                                }
                                            }
                                            boolean contianAllKey = true;
                                            for (String name : names) {
                                                if (!valueKey.contains(name)) {
                                                    contianAllKey = false;
                                                    break;
                                                }
                                            }
                                            isThisValue = contianAllKey;

                                        }


                                        if (isThisValue) {
                                            // yLabel可能不匹配，比如教师人数和人数
                                            Object orDefault = each.getOrDefault(yLabel, 0);
                                            data.add(orDefault);
                                            if (i == 0 || tmpXlabels.size() == 0) {
                                                for (String key : each.keySet()) {
                                                    if (xLabel.equals(key)) {
                                                        tmpXlabels.add(each.get(key));
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                    }

                                }
                                // 多个,公用一个name，但是区分了多个series，还有一种是名字合并的
                                JSONArray xLabels = chartData.getJSONObject("xAxis").getJSONArray("data");
                                if (!tmpXlabels.isEmpty()) {
                                    xLabels.clear();
                                    xLabels.addAll(tmpXlabels);
                                }
                            }


                        } else {
                            // 一个的
                            List<OutputColumn> columns = output.getColumns();
                            // yLabel 外的作为name，拼接
                            List<String> valueKeys = new LinkedList<>();
                            if (seriesKeys != null && seriesKeys.size() > 0) {
//                                valueKeys = seriesKeys;
                                for (String seriesKey : seriesKeys) {
                                    valueKeys.add(seriesKey);
                                }
                            } else {
                                for (OutputColumn column : columns) {
                                    if (!column.getLabel().equals(yLabel)) {
                                        valueKeys.add(column.getProp());
                                    }
                                }
                            }

                            // 多个,拼接name
                            JSONArray xLabels = chartData.getJSONObject("xAxis").getJSONArray("data");
                            xLabels.clear();
                            JSONArray seriesData = ((JSONObject) series).getJSONArray("data");
                            seriesData.clear();
                            // 补充x轴
                            ArrayList names = new ArrayList<>(valueKeys.size());
                            for (Map<String, Object> each : rLs) {
                                names.clear();
                                Object value = null;
                                for (String key : each.keySet()) {
                                    if (yLabel.equals(key)) {
                                        value = each.get(key);
                                        break;
                                    }
                                }
                                for (String valueKey : valueKeys) {
                                    for (String key : each.keySet()) {
                                        if (value == null) {
                                            // 说明没找到yLabel
                                            if (yLabel.contains(key) || key.contains(yLabel)) {
                                                value = each.get(key);
                                                continue;
                                            }
                                        }
                                        if (valueKey.equals(key) || valueKey.contains(key) || key.contains(valueKey)) {
                                            names.add(each.get(key).toString());
                                        }
                                    }
                                }

                                String newName = StringUtils.join(names, "-");
                                xLabels.add(newName);
                                JSONObject item = new JSONObject();
                                item.put("name", newName);
                                item.put("value", value == null ? 0 : value);
                                seriesData.add(item);
                            }

                        }
                        outputstr = echartTypeJson.toJSONString();
                        resultJson = outputstr;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            return resultJson;

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public String getThinkAgentInput(IHandlerContext context, String answer) {
        // 主要是，有些思考过程考虑要不要放在结果后，再生成，有的可能是提前生成的。。结果看，应该还是哪怕并行，也是先要思考，再给结果
        return String.format(" 用户输入：%s ,查询sql：%s", context.getInput(), answer);
    }

    @Override
    public String getErrorMsg(IHandlerContext context) {
        return "查询遇到一些问题，小易正在学习改正中";
    }

    @Override
    public IResponseHandlerProxy getResponseHandlerProxy(RESPONSE_TYPE responseType, final IHandlerContext context) {
        final boolean blockButPrintThink = this.getBlockButPrintThink(context);
        return new IResponseHandlerProxy() {
            @Override
            public String handler(String responseStr) {
                if (responseStr == null) {
                    return null;
                }
                if (responseStr.startsWith("event:done")) {
                    // 忽略流处理的
                    return null;
                }
                // 作为思考过程来处理
//                res.put("reasoning_content", msg.getString("reasoning_content"));
//                dto.getReasonHistory().append(msg.getString("reasoning_content"));
//                s = "event:message\ndata:{\"message\":{\"type\":\"reasoningContent\",\"content\":\"" + string + "\"}}\n\n";
//                s = "event:message\ndata:{\"message\":{\"type\":\"content\",\"content\":\"" + string + "\"}}\n\n";
                if (responseStr.contains("\"type\":\"content\"")) {
                    if (blockButPrintThink) {
                        return null;// 直接用dsr1了，拦截正文
                    } else {
                        // 并行的，进行改写
                        responseStr = responseStr.replace("\"type\":\"content\"", "\"type\":\"reasoningContent\"");
                    }
                    // 把日志记录
//                    if (context.getHistoryDTO() != null) {
//                        String json = responseStr.replace("event:message\\ndata:", "");
//                        JSONObject jsonObject = JSON.parseObject(json).getJSONObject("message");
//                        if ("reasoningContent".equals(jsonObject.getString("type"))) {
//                            String reasoningContent = jsonObject.getString("content");
//                            //todo 应该要记录这个思考过程
//                        }
//                    }
                }
                return responseStr;
            }
        };
    }

    @Override
    public String getDesc() {
        // 处理sql查询、如果是流，需要调用思考智能体返回思考过程的
        return "sql生成智能体";
    }

    public static void main(String[] args) {
        String t = "{ \"yAxis\": { \"name\": \"教师数量\", \"type\": \"value\" }, \"xAxis\": { \"data\": [ \"中科大附中独墅湖学校\", \"中科大附中独墅湖学校（小学部）\", \"南京航空航天大学苏州附属中学\", \"园区教育运营中心\", \"苏州大学附属中学\", \"苏州工业园区三之三淞江幼儿园\" ], \"name\": \"学校名称\", \"type\": \"category\" }, \"series\": [ { \"data\": [ 22, 23, 126, 1, 91, 0 ], \"name\": \"男\", \"type\": \"bar\" }, { \"data\": [ 42, 83, 286, 0, 169, 21 ], \"name\": \"女\", \"type\": \"bar\" } ] }";
        JSONObject jsonObject = JSON.parseObject(t);

    }

    public void setFormatAgentId(long formatAgentId) {
        this.formatAgentId = formatAgentId;
    }

    @Override
    public boolean getBlockButPrintThink(IHandlerContext context) {
        if (context.getCurrentModel() != null && ("dsr1".equals(context.getCurrentModel().getString("code"))
                || "deep_seek_reasoner_r1".equals(context.getCurrentModel().getString("code")))) {
            return true;
        }
        return false;
    }
}
