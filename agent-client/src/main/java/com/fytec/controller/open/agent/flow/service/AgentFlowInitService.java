package com.fytec.controller.open.agent.flow.service;

import com.fytec.controller.open.agent.flow.handler.edu.*;
import com.fytec.controller.open.agent.flow.handler.prod.*;
import com.fytec.controller.open.agent.flow.handler.renwu.RenwuIntentIAgent;
import com.fytec.controller.open.agent.flow.handler.renwu.RenwuTableQuerySqlAgent;
import com.fytec.controller.open.agent.flow.handler.renwu.RenwuTaiZhangQuerySqlAgent;
import com.fytec.controller.open.agent.flow.handler.wjedu.WjEduTableQuerySqlAgent;
import com.fytec.controller.open.agent.flow.utils.SqlPermissonInterface;
import com.fytec.controller.open.agent.flow.utils.SqlQueryUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Set;

@Slf4j
@Service
public class AgentFlowInitService {
    @Autowired
    private SqlPermissonInterface permissonInterface;


    public void init(AgentFlowService agentFlowService,
                     String envType, Set<String> streamAgentIds, Set<String> sqlAgentIds) {
        // 之后修改成动态或者从配置里拿

        log.info("load AgentFlowService,envType:" + envType);
        if (permissonInterface != null)
            SqlQueryUtils.initPermissionInterface(permissonInterface);

        if ("jiaoyu".equals(envType)) {
            //todo 回头改成反射注解注入吧
            new EduTableMetaQueryAgent().register(agentFlowService);
            new EduIntentIAgent().register(agentFlowService);
            new EduTableQuerySqlAgent().register(agentFlowService);
            new EduCustomerInputChangeAgent().register(agentFlowService);
            new EduNavQueryAgent().register(agentFlowService);
            streamAgentIds.add("9");//目前只有客服是流的,todo 未来这些智能体能否支持流，应该配置出来
            streamAgentIds.add("31"); // 兜底流
            streamAgentIds.add("32"); // 客服视频
            new EduPoilcyQueryAgent().register(agentFlowService);

            streamAgentIds.add("9");//目前只有客服是流的, 未来这些智能体能否支持流，应该配置出来
            sqlAgentIds.add("7");
            sqlAgentIds.add("15");

            streamAgentIds.add("87");// 政策解读

            //主题数据的
            EduTopicDataQueryAgent eduTopicDataQueryAgent = new EduTopicDataQueryAgent();
            eduTopicDataQueryAgent.register(agentFlowService);
            streamAgentIds.add(""+eduTopicDataQueryAgent.getAgentId());

            EduLiteracyPointAgent literacyPointAgent = new EduLiteracyPointAgent();
            literacyPointAgent.register(agentFlowService);
            streamAgentIds.add(""+literacyPointAgent.getAgentId());
        }
        if ("prod".equals(envType)) {
            // 公司环境
            // 一个sql教育智能体
            new ProdEduExamTableQuerySqlAgent().register(agentFlowService);
            sqlAgentIds.add("146");
            //任务
            new RenwuProdIntentIAgent().register(agentFlowService);
            new RenwuProdTableQuerySqlAgent().register(agentFlowService);
            new RenwuProdTaiZhangQuerySqlAgent().register(agentFlowService);
            new RenwuProdFileAgent().register(agentFlowService);
            sqlAgentIds.add("119");
            sqlAgentIds.add("154");
            streamAgentIds.add("138");// 流程规范的
            // 社区的
            new ShequProdIntentIAgent().register(agentFlowService);
            new ShequProdTableQuerySqlAgent().register(agentFlowService);
            sqlAgentIds.add("133");
            streamAgentIds.add("151");
            streamAgentIds.add("155");
        }

        if ("wjedu".equals(envType)) {
            new WjEduTableQuerySqlAgent().register(agentFlowService);
            sqlAgentIds.add("16");
        }
        if ("renwu".equals(envType)) {
            new RenwuIntentIAgent().register(agentFlowService);
            new RenwuTableQuerySqlAgent().register(agentFlowService);
            new RenwuTaiZhangQuerySqlAgent().register(agentFlowService);
            sqlAgentIds.add("119");
            sqlAgentIds.add("153");
            streamAgentIds.add("138");// 流程规范的
        }
    }
}
