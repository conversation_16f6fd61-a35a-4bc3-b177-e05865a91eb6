package com.fytec.controller.open.database;

import cn.dev33.satoken.oauth2.annotation.SaCheckClientIdSecret;
import cn.dev33.satoken.oauth2.annotation.SaCheckClientToken;
import com.fytec.dto.database.ExecuteSqlDTO;
import com.fytec.service.database.DatabaseService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Validated
@Tag(name = "数据库对外接口")
@RequestMapping("/fytec/api/database")
@RestController
@RequiredArgsConstructor
public class DatabaseOpenController {
    private final DatabaseService databaseService;

    @PostMapping("/execute/sql")
    @Operation(summary = "通过数据库id执行sql")
    @SaCheckClientIdSecret
    public R<?> executeSql(@RequestBody ExecuteSqlDTO executeSqlDTO) {
        return R.ok(databaseService.executeSql(executeSqlDTO));
    }


}
