package com.fytec.controller.open.agent.flow.protocol;

import org.apache.commons.lang3.StringUtils;

public class AgentHandlerError  extends Exception  {
    public String getErrorMsg() {
        if(StringUtils.isNotBlank(msg)){
            return msg;
        }
        return this.getClass().getSimpleName();
    }

    public AgentHandlerError(){

    }
    protected String msg = "";
    public AgentHandlerError(String msg){
        this.msg = msg;
    }
}
