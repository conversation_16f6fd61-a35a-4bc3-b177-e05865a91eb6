package com.fytec.controller.open.agent.flow.utils;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.fytec.controller.open.agent.flow.protocol.IHandlerContext;
import com.fytec.controller.open.agent.flow.protocol.errors.AgentSqlQueryException;
import com.fytec.controller.open.agent.flow.protocol.errors.AgentSqlQueryExecException;
import com.fytec.controller.open.agent.flow.utils.dataquery.DatabaseHelper;
import com.fytec.controller.open.agent.flow.utils.dataquery.DatabaseUtil;
import com.fytec.controller.open.agent.flow.utils.dataquery.dto.Output;
import com.fytec.controller.open.agent.flow.utils.dataquery.dto.OutputColumn;
import com.fytec.controller.open.agent.flow.utils.dataquery.dto.TableColumn;
import com.fytec.controller.open.agent.flow.utils.dataquery.dto.TableInfo;
import com.fytec.utils.AiFormatJsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


import static com.alibaba.fastjson.JSON.parseObject;
import static com.alibaba.fastjson.JSON.toJSONString;

@Slf4j
public class SqlQueryUtils {
    private static SqlPermissonInterface permissonInterface = null;// 需要初始化

    public static void initPermissionInterface(SqlPermissonInterface sqlPermissonInterface) {
        permissonInterface = sqlPermissonInterface;
    }

    public static String removeSqlComments(String sql) {
        // 匹配行注释：以"--"开头，后面跟着任意中文内容，直到行尾
        String regex = "--[^\\n]*[\\u4e00-\\u9fa5]+[^\\n]*\\n?";
        return sql.replaceAll(regex, "");
    }

    public static Output execSqlQuery(IHandlerContext context, String rawSql, int maxlen) throws Exception {
        // 执行sql
        Output output = new Output();
        // 执行sql，判断是否是select，只有select权限
        //todo 加个自由query权限的账号
        if (rawSql.contains("-- ")) {
            rawSql = removeSqlComments(rawSql);
        }
        if (StringUtils.isNotBlank(rawSql)) {
            rawSql = rawSql.strip();
        }
//        if (rawSql.startsWith("\\n\\n")) {
//            rawSql = rawSql.replaceAll("\\n\\n", "");
//        }

        if (rawSql.toLowerCase().contains(" delete ") || rawSql.toLowerCase().contains(" alter ")) {
            throw new AgentSqlQueryException("非查询语句无法执行:" + rawSql);
        }
        if (rawSql.toLowerCase().trim().startsWith("select") || rawSql.toLowerCase().trim().startsWith("with")) {
            log.info("sql:" + rawSql.toLowerCase().trim());
            // 考虑怎么把sql变成数据表、和echarts格式
            Map<String, Object> sqlMap = new HashMap<>();
            sqlMap.put("sql", rawSql);
            List<Map<String, Object>> rLs = null;
            String querySql = rawSql;
            if (rawSql.toLowerCase().trim().startsWith("with")) {
                // 提取sql中的临时表
                // tmpTableSql
                List<String> tempTableDefinitions = extractTempTableDefinitions(rawSql);
                // 输出提取结果
                for (String def : tempTableDefinitions) {
                    System.out.println("临时表定义:");
                    System.out.println(def);
                    System.out.println("-------------------");
                    querySql = querySql.replace(def, "");
                }
                if (tempTableDefinitions.size() > 0)
                    querySql = "SELECT " + querySql;
            }
            // 执行sql
            String regx = "((SELECT|select).*?);";
            Pattern p = Pattern.compile(regx);
            // todo 一些特殊sql，以后考虑咋处理
            String replaceSql = querySql.toLowerCase().trim().replace("\n", " ").replace("%y年%m月%d日", "%Y年%m月%d日");
            if (!replaceSql.endsWith(";")) {
                replaceSql += ";";
            }
            HashMap<String, String> configs = new HashMap<>();
            Matcher m = p.matcher(replaceSql);
            if (m.find()) {
                String sql = m.group();
                String ss = sql.toLowerCase();
                if (ss.contains("group by")) {
                    String select = "select";
                    String from = " from ";
                    String groupBy = "group by";
                    String orderBy = "order by";
                    String limit = " limit ";
                    int sIdx = ss.indexOf(select);
                    int fIdx = ss.indexOf(from);
                    int gIdx = ss.indexOf(groupBy);
                    int oIdx = ss.indexOf(orderBy);
                    int lIdx = ss.indexOf(limit);
                    String columns = sql.substring(sIdx + select.length(), fIdx).trim();
                    String groupStr = sql.substring(gIdx + groupBy.length()).trim();
                    if (oIdx > gIdx) {
                        groupStr = sql.substring(gIdx + groupBy.length(), oIdx).trim();
                    } else if (lIdx > gIdx) {
                        groupStr = sql.substring(gIdx + groupBy.length(), lIdx).trim();
                    }
                    String group = groupStr;
                    if (groupStr.contains(" having ")) {
                        group = group.split(" having ")[0];
                    }
                    String[] cos = columns.split(",");

                    boolean hasGroupByColumn = false;
                    for (String co : cos) {
                        String cc = co.trim();
                        if (StringUtils.isEmpty(cc)) continue;
                        String cs = cc.replace("as", " ").replace("AS", " ");
                        String[] ccs = cs.split(" ");
                        for (String c : ccs) {
                            if (StringUtils.isEmpty(c)) continue;

                            if (group.contains(c.trim())) {
                                hasGroupByColumn = true;
                                break;
                            }
                        }
                        if (hasGroupByColumn) break;
                    }
                    if (hasGroupByColumn && !ss.contains(" order by ")) {
                        // 这边可以添加order by字段
                        configs.put("order by", group);
                        log.info("add order by :" + group);
                    }

                    if (!hasGroupByColumn) {
                        StringBuilder sb = new StringBuilder();
                        sb.append("select ");
                        sb.append(columns);
                        sb.append(", ");
                        sb.append(group.replace(";", ""));
                        sb.append(" ");
                        sb.append(sql.substring(fIdx));
                        sql = sb.toString();
                    }
                }
                DatabaseUtil d = getDefaultSqlConnect(context == null ? null : context.getSqlConfigs());
                Map<String, String> debugInfo = new HashMap<>();
                try {
                    output.setSqlChange(sql);
                    output.setConfigs(configs);
                    if (permissonInterface != null
                            && context != null) {
                        String permissionSql = permissonInterface.permissonCheckSqlAndChange(context, rawSql);
                        output.setSqlPermission(permissionSql);
                        rLs = d.executeQuery(permissionSql, configs);
                    } else {
                        rLs = d.executeQuery(rawSql, configs);
                    }
                } catch (SQLException e) {
//                    String errormsg = "error sql execute:【" + (output.getSqlPermission() == null ? rawSql : output.getSqlPermission()) + "】";
                    String errormsg = String.format("error rawsql execute:【 %s 】", rawSql);
//                    + (output.getSqlPermission() == null ? rawSql : output.getSqlPermission()) + "】";
                    if (output.getSqlPermission() != null) {
                        errormsg = String.format("error permissionsql execute:【 %s 】", output.getSqlPermission());
                    }

                    log.error(errormsg);
                    e.printStackTrace();
                    d.closeDataSource();
                    // 执行时候报错
                    throw new AgentSqlQueryExecException(e.getMessage() + "\t" + errormsg);

                }
//                int maxlen = 20;
//                if (!rLs.isEmpty() && rLs.size() > maxlen) {
//                    // 不展示那么多
//                    List<Map<String, Object>> _tmpLog = new ArrayList<>(maxlen);
//                    for (int i = 0; i < rLs.size() && i <= maxlen; i++) {
//                        _tmpLog.add(rLs.get(i));
//                    }
//                    rLs = _tmpLog;
//                }
                String tsql = rawSql.toLowerCase().replaceAll("\n", " ");
                if (rLs.size() >= maxlen
                        && tsql.contains(" limit ")
                        && !tsql.contains(" group by ")) {
                    output.setShowMore(true);
                }
                if (context != null && rLs.size() == 0) {
                    output.setMoreTipAgentId(context.getMoreTipAgentId());
                } else {
//                    output.setMoreDetailAgentId(context.getMoreDetailAgentId());
                }
                d.closeDataSource();
            } else {
                throw new RuntimeException("sql error not found select:" + rawSql);
            }
            if (rLs != null) {
                // 判断可能出现这种数据不匹配为空的
                //[{"教师人数":409},{"任教学科":"体育","教师人数":941},{"任教学科":"信息科技","教师人数":352},
                if (rLs.size() > 1) {
                    // 多个需要核对一下key和value，0、1一起算一下
//                    Set<String> keys = new HashSet<>();
//                    for (Map<String, Object> l : rLs) {
//                        Set<String> strings = l.keySet();
//                        keys.addAll(strings);
//                    }
//                    for (Map<String, Object> l : rLs) {
//                        for (String key : keys) {
//                            if (!l.containsKey(key)) {
//                                System.out.println("补充字段:" + key);
//                                l.put(key, "-");
//                            }
//                        }
//                    }
                    for (Map<String, Object> rL : rLs) {
                        for (String key : rL.keySet()) {
                            if (rL.get(key) == null) {
                                rL.put(key, "-");
                            }
                        }
                    }
                }
                if (context != null) {
                    buildEchartJsonByResults(output, context.getInput(), sqlMap.getOrDefault("sqlChange", rawSql).toString(), rLs);
                    // 如果用户输入中有涉及图表
                    String type = checkHasEchartType(context.getInput());
                    boolean isBarDataNull = false;

//                    if (output.getExtraParams() != null && output.getExtraParams().getString("type").equals("chart")) {
//                        JSONObject xAxis = output.getChartData().getJSONObject("xAxis");
//                        if(xAxis!=null){
//                            JSONArray xData = xAxis.getJSONArray("data");
//                            if(xData!=null&&xData.)
//                        }
//                    }

                    if (StringUtils.isNotBlank(type)
                            && output.getExtraParams().containsKey("category")
                            && ("table".equals(output.getExtraParams().getString("category"))
                            || isBarDataNull
                    )
                            && !rLs.isEmpty()
                        // 避免说太多导致分类输出太慢了
                    ) {
                        log.info("自动识别的图表类型:" + type);
                        //  说明要通过ai转换结构了
                        if (context.getCurrentHandler() != null) {
                            log.info("echart转换智能体：" + context.getInput() + "\textraParams:" + output.getExtraParams().toJSONString());

                            HashMap<String, Object> values = new HashMap<>();
                            values.put("list", rLs);
                            values.put("output", output);
                            String resultJson = context.getCurrentHandler().handlerEnd(context, values);
                            if (StringUtils.isNotBlank(resultJson)) {
//                                log.info("echart转换智能体：" + context.getInput() + "\textraParams:" + output.getExtraParams().toJSONString());
                                String outputstr = AiFormatJsonUtil.formatJson(resultJson);
                                log.warn("得到auto echart模板:outputstr：" + outputstr);
                                try {
                                    JSONObject echartTypeJson = JSON.parseObject(outputstr, Feature.OrderedField);
                                    if (echartTypeJson.containsKey("extraParams")) {
                                        if (echartTypeJson.containsKey("chartData")) {
                                            output.setChartData(echartTypeJson.getJSONObject("chartData"));
                                            output.setExtraParams(echartTypeJson.getJSONObject("extraParams"));
                                        } else if (echartTypeJson.containsKey("yAxis") && echartTypeJson.containsKey("xAxis")) {
                                            // 说明错位了
                                            output.setChartData(echartTypeJson);
                                            output.setExtraParams(echartTypeJson.getJSONObject("extraParams"));
                                        }
                                    }

                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            } else {
                                log.info("处理echart模板数据为空");
                            }

                        } else {
                            log.info("未设置echart转换智能体");
                        }
                    }
                }
                output.setList((List) rLs);
            }
            return output;
        } else if (rawSql.toLowerCase().contains("delete ")) {
            log.error("sql not permission:" + rawSql);
            throw new RuntimeException("sql not permission:" + rawSql);
        } else {
            output.setSql(rawSql);
            log.error("非查询语句，没有权限:" + rawSql);
            throw new AgentSqlQueryException("非查询语句，没有权限:" + rawSql);

        }
//        return null;
    }

    public static String checkHasEchartType(String userInput) {
        String type = null;
        if (userInput.contains("柱状") || userInput.contains("条状") || userInput.contains("柱形")) {
            type = "bar";
        } else if (userInput.contains("折线")) {
            type = "line";
        } else if (userInput.contains("饼图") || userInput.contains("饼状图")) {
            type = "pie";
        }
        return type;
    }

    public static List<String> extractTempTableDefinitions(String sql) {
        List<String> definitions = new ArrayList<>();

        // 移除SQL中的注释
        sql = removeComments(sql);

        // 编译WITH子句的正则表达式模式
        Pattern withPattern = Pattern.compile(
                "(?i)^WITH[\\s\\S]*?\\)\\s*(SELECT|select)",
                Pattern.CASE_INSENSITIVE
        );
        Matcher withMatcher = withPattern.matcher(sql);

        if (withMatcher.find()) {
            String withClause = withMatcher.group(0).trim();

            // 分割多个临时表定义
//            String[] tempTableDefs = splitTempTableDefs(withClause);

            definitions.addAll(Arrays.asList(withClause));
        }

        return definitions;
    }

    private static String removeComments(String sql) {
        // 移除单行注释
        sql = sql.replaceAll("--.*?$", "");
        // 移除多行注释
        sql = sql.replaceAll("/\\*.*?\\*/", "");
        return sql;
    }

    private static String[] splitTempTableDefs(String withClause) {
        List<String> defs = new ArrayList<>();
        int parenCount = 0;
        StringBuilder currentDef = new StringBuilder();

        for (char c : withClause.toCharArray()) {
            if (c == '(') parenCount++;
            if (c == ')') parenCount--;

            currentDef.append(c);

            // 当括号平衡且遇到逗号时，分割定义
            if (parenCount == 0 && c == ',') {
                defs.add(currentDef.toString().trim());
                currentDef.setLength(0);
            }
        }

        // 添加最后一个定义
        if (currentDef.length() > 0) {
            defs.add(currentDef.toString().trim());
        }

        // 过滤空定义
        return defs.stream()
                .filter(def -> !def.isEmpty())
                .toArray(String[]::new);
    }

    private static void buildEchartJsonByResults(Output output, String userInput, String rawSql, List<Map<String, Object>> rLs) {
        if (rawSql.endsWith(";")) {
            rawSql = rawSql.substring(0, rawSql.length() - 1);
        }
        String sql = rawSql;
        if (sql.toLowerCase().trim().startsWith("with")) {
            // 提取sql中的临时表
            // tmpTableSql
            List<String> tempTableDefinitions = extractTempTableDefinitions(sql);
            // 输出提取结果
            for (String def : tempTableDefinitions) {
                System.out.println("临时表定义:");
                System.out.println(def);
                System.out.println("-------------------");
                sql = sql.replace(def, "");
            }
        }
        boolean isUnionAll = false;
        if (sql.toLowerCase().contains(" union all ")) {
            // 说明是多个合并的，取一个即可
            sql = sql.split(" union all ")[0] + ";";
            isUnionAll = true;
        }

        String type = "chart";
        String category = "table";
        List<String> groupBys = new ArrayList<>();
        List<String> groupByName2AsName = new ArrayList<>();

//        {"pie", "bar", "line"};
        String ss = sql.toLowerCase().replaceAll("\n", " ");
        Map<String, Integer> nameOrValueLevel = new HashMap<>();
        List<Map<String, Object>> ls = rLs;

        if (ss.contains("group by")) {
//            category =
            category = "bar";

            String select = "select";
            String from = " from ";
            String groupBy = "group by";
            String orderBy = "order by";
            String limit = "limit";
            int oIdx = ss.indexOf(orderBy);
            int lIdx = ss.indexOf(limit);
            int sIdx = ss.indexOf(select);
            int fIdx = ss.indexOf(from);
            int gIdx = ss.indexOf(groupBy);
            String columns = sql.substring(sIdx + select.length(), fIdx).trim();
            String groupStr = sql.substring(gIdx + groupBy.length()).trim();
            if (oIdx > gIdx) {
                groupStr = sql.substring(gIdx + groupBy.length(), oIdx).trim();
            } else if (lIdx > gIdx) {
                groupStr = sql.substring(gIdx + groupBy.length(), lIdx).trim();
            }
            String group = groupStr;


            String[] cos = columns.split(",");
            // year(str_to_date(ryqzgsj, '%Y年%m月%d日')) as '年份',     count(*) as '信息科技老师数量'
            // 这种情况下就不能光通过,做分隔了
            // 先判断有没有as

            if (columns.toLowerCase().contains(" as ")) {
                String[] asCos = columns.replace(" AS ", " as ").split(" as ");
                List<String> tmpCols = new LinkedList<>();
                StringBuffer sb = new StringBuffer();
                for (String co : cos) {
                    if (!co.toLowerCase().contains(" as ")) {
                        // 需要append到前一个
                        sb.append(co);
                        sb.append(",");
                    } else {
                        sb.append(co);
                        tmpCols.add(sb.toString());
                        sb.setLength(0);
                    }
                }
                if (!sb.toString().isEmpty()) {
                    tmpCols.add(sb.toString());
                }

                cos = StringUtils.split(StringUtils.join(tmpCols, "@@"), "@@");
            }

            /**
             *  这种复杂的sql也需要做判断
             *  sql: SELECT YEAR(FROM_UNIXTIME(UNIX_TIMESTAMP(`最高职称获得时间`))) AS 获得时间年份, COUNT(*) AS 高级教师人数 FROM yj_teacher WHERE `最高职称` = '高级' AND YEAR(FROM_UNIXTIME(UNIX_TIMESTAMP(`最高职称获得时间`))) >= YEAR(CURRENT_DATE) - 4
             *   GROUP BY YEAR(FROM_UNIXTIME(UNIX_TIMESTAMP(`最高职称获得时间`)));
             *
             *   YEAR(FROM_UNIXTIME(UNIX_TIMESTAMP(`最高职称获得时间`)))对应到as后的这个
             */
//            sql: select live_status, count(*) as 数量 from population where address like '%娄葑街道%' group by live_status;

            // 字段name还是value的优先级
            for (String co : cos) {
                String cc = co.trim();
                if (StringUtils.isEmpty(cc)) continue;
                String cs = null;
                String[] ccs = null;

                if (cc.contains(" as ") || cc.contains(" AS ")) {
                    cs = cc.replace(" as ", "@@@").replace(" AS ", "@@@");
                    //判断是不是多个case和then的情况
                    /**
                     *  SELECT      CASE          WHEN 年龄 >= 20 AND 年龄 < 30 THEN '20 - 29 岁'         WHEN 年龄 >= 30 AND 年龄 < 40 THEN '30 - 39 岁'
                     *  WHEN 年龄 >= 40 AND 年龄 < 50 THEN '40 - 49 岁'         WHEN 年龄 >= 50 AND 年龄 < 60 THEN '50 - 59 岁'     END AS 年龄段,
                     *  COUNT(*) AS 人数 FROM      yj_teacher WHERE      所在区域 = '园区' AND 职级 IN ('中层副职', '中层正职') GROUP BY      CASE          WHEN 年龄 >= 20 AND 年龄 < 30 THEN '20 - 29 岁'         WHEN 年龄 >= 30 AND 年龄 < 40 THEN '30 - 39 岁'
                     *  WHEN 年龄 >= 40 AND 年龄 < 50 THEN '40 - 49 岁'         WHEN 年龄 >= 50 AND 年龄 < 60 THEN '50 - 59 岁'     END;
                     */

                    ccs = cs.trim().split("@@@");
                    // 避免year( aa ) 这种也被split的，展示只考虑有as的情况
                } else {
                    cs = cc.replace("as", " ").replace("AS", " ");
                    ccs = cs.trim().split(" ");
                }

                log.info("split colon:{}", Arrays.toString(ccs));
                if (ccs.length == 2) {
                    if (ccs[0].contains("sum(") || ccs[0].contains("count(")) {
                        nameOrValueLevel.put(ccs[0], 10);
                        nameOrValueLevel.put(ccs[1], 10);

                    } else {
                        nameOrValueLevel.put(ccs[0], 1);
                        nameOrValueLevel.put(ccs[1], 1);

                    }
                }
//                else if (ccs.length == 1) {
//                    nameOrValueLevel.put(ccs[0], 1);
//                    nameOrValueLevel.put(ccs[0], 1);
//                }

                boolean isWeidu = false;
                String column = ccs[0].trim();
                for (String c : ccs) {
                    if (StringUtils.isEmpty(c.trim())) continue;
                    if (group.contains(c.trim())) {
                        isWeidu = true;
                        if (cc.contains(" as ") || cc.contains(" AS ")) {
                            // 补充字段
                            groupByName2AsName.add(ccs[ccs.length - 1].trim().toLowerCase());
                        }
                    }
                    column = c.trim();
                }
                if (!isWeidu) {
                    // 处理一下这种特殊情况
                    for (String c : ccs) {
                        if (StringUtils.isEmpty(c.trim())) continue;
                        if (c.trim().contains(group)) {
                            isWeidu = true;
                            if (cc.contains(" as ") || cc.contains(" AS ")) {
                                // 补充字段
                                groupByName2AsName.add(ccs[ccs.length - 1].trim().toLowerCase());
                            }
                        }
                        column = c.trim();
                    }
                }
                if (isWeidu) {
                    groupBys.add(column.toLowerCase().replace("'", "").replace("\"", ""));
                }
            }
        } else {
            //todo 智能体生成chart图表
        }

        if (ls != null && ls.size() > 0) {
//            String constantKey = t.getConstantKey();
            // todo 根据表结构获取到对应的表字段列表
            String constantKey = null;
            TableInfo table = null;
            if (StringUtils.isNotEmpty(constantKey)) {
//                String content = constantCacheClient.getConstant(constantKey);
//                log.info(constantKey + ": " + content);
//                table = parseObject(content, TableInfo.class);

            }

            Map<String, String> fieldMap = new HashMap<>();
            Map<String, Map<String, String>> fieldValueMap = new HashMap<>();
            if (table != null && table.getColumns() != null) {
                List<TableColumn> columns = table.getColumns();
                for (TableColumn cc : columns) {
                    fieldMap.put(cc.getColumn(), cc.getComment());
                    String remarks = cc.getRemarks();
                    if (StringUtils.isNotEmpty(remarks)) {
                        String[] values = remarks.split(";");
                        Map<String, String> vMap = new HashMap<>();
                        for (String v : values) {
                            String[] tmpK = v.split(":");
                            if (tmpK.length >= 2) {
                                vMap.put(tmpK[0].trim(), tmpK[1].trim());
                            }
                        }
                        fieldValueMap.put(cc.getColumn(), vMap);
                    }
                }
            }

            Object o = ls.get(0);


            JSONObject j = parseObject(toJSONString(o), Feature.OrderedField);
            if (groupBys.isEmpty() && isUnionAll) {
                // 需要处理一下，把字符串类型的作为group by的把
                // 可供参考
                for (String key : j.keySet()) {
                    Object v = j.get(key);
                    if (v instanceof String) {
                        groupBys.add(key);
                        break;
                    }
                }
            }
            if (!groupBys.isEmpty()) {
                log.info("groupBys:" + toJSONString(groupBys));
                log.info("groupByName2AsName:" + toJSONString(groupByName2AsName));

            }
            // 先判断是否指定了非柱状图
            boolean regFormatBar = false;
            if (StringUtils.isNotBlank(checkHasEchartType(userInput))) {
                regFormatBar = true;
            }
            if (groupBys.size() == 2 && StringUtils.isNotBlank(checkHasEchartType(userInput))
                    && !"bar".equals(checkHasEchartType(userInput))) {
                regFormatBar = false;// 如果不一样，不做自动解析
                // 指定了非柱状图，但是又多个group by，无法展示，优先按照用户意图去识别，如果不行，就处理一下了
                // 判断里面是不是被包含，
                int countNum = 0;
                String lastGroupByStr = null;
                for (String groupBy : groupBys) {
                    if (userInput.contains(groupBy)) {
                        countNum += 1;
                        lastGroupByStr = groupBy;
                    }
                }
                if (countNum == 1) {
                    // 都没包含，让ai自行决定
                    groupBys.clear();
                    groupBys.add(lastGroupByStr);
                    log.info("groupBys :" + toJSONString(groupBys));

                } else {
                    // 其他情况忽略
                }
            }

            if (groupBys.size() == 1 && StringUtils.isNotBlank(checkHasEchartType(userInput)) && rLs.size() > 1) {
                String[] charts = {"pie", "bar", "line"};
                category = findCharType(userInput, charts);

                Map<String, String> fieldRefMap = new HashMap<>();
                Map<String, String> refLabelMap = new HashMap<>();
                //先判断几个有label
                String nameKey = "";
                for (String key : j.keySet()) {
                    String label = fieldMap.get(key);
                    Object v = j.get(key);
                    boolean hasNameKey = (v instanceof String || groupBys.contains(key.toLowerCase())
                            || StringUtils.isNotBlank(key) && groupByName2AsName.contains(key.toLowerCase())
                            || StringUtils.isNotBlank(label) && groupByName2AsName.contains(label.toLowerCase()));
                    if (hasNameKey) {
                        nameKey = key;
                        break;
                    }
                }

                for (String key : j.keySet()) {
                    String label = fieldMap.get(key);
                    Object v = j.get(key);
                    boolean f = StringUtils.isNotEmpty(label);
                    String ref = "";
                    if (StringUtils.isNotBlank(nameKey)) {
                        //说明已经有了
                        if (nameKey.equals(key)) {
                            ref = "name";
                        } else {
                            ref = "value";
                        }
                    } else {
                        ref = (f || v instanceof String || groupBys.contains(key.toLowerCase()) || StringUtils.isNotBlank(key) && groupByName2AsName.contains(key.toLowerCase()) || StringUtils.isNotBlank(label) && groupByName2AsName.contains(label.toLowerCase())) ? "name" : "value";
                    }
                    if ("value".equals(ref) && refLabelMap.containsKey(ref)) {
                        // 已经有value了,根据优先级判断
                        Integer lastLevel = nameOrValueLevel.getOrDefault(refLabelMap.get(ref), 1);
                        Integer currentLevel = nameOrValueLevel.getOrDefault(f ? label : key, 1);
                        // 优先用后面的
                        boolean currentValueIsNumber = !(v instanceof String);

                        if (currentLevel > lastLevel || (currentLevel == lastLevel && currentValueIsNumber)) {
                            // 需要替换
                            if (refLabelMap.containsKey("name")) {
                                // 只需要替换value
                                refLabelMap.put("value", refLabelMap.get(ref));
                            } else {
                                refLabelMap.put("name", refLabelMap.get(ref));
                                AtomicReference<String> tmpkey = new AtomicReference<>();
                                fieldRefMap.forEach((k, r) -> {
                                    if ("value".equals(r)) {
                                        tmpkey.set(k);
                                    }
                                });
                                fieldRefMap.put(tmpkey.get(), "name");
                            }

                        }
                    }

                    refLabelMap.put(ref, f ? label : key);
                    fieldRefMap.put(key, ref);


                    log.info(refLabelMap.toString());

                }
                String xName = refLabelMap.get("name");
                String yName = refLabelMap.get("value");

                List<String> labels = new ArrayList<>();
                List<JSONObject> list = new ArrayList<>();
                for (Object obj : ls) {
                    JSONObject jj = parseObject(toJSONString(obj), Feature.OrderedField);
                    JSONObject r = new JSONObject();
                    for (String key : jj.keySet()) {
                        String n = fieldRefMap.get(key);
                        if (StringUtils.isEmpty(n)) {
                            continue;
                        }

                        Object v = jj.get(key);
                        Object vv = v;
                        Map<String, String> m = fieldValueMap.get(key);
                        if (m != null) {
                            String s = m.get(v + "");
                            vv = StringUtils.isNotEmpty(s) ? s : v;
                        }

                        if ("name".equals(n)) {
                            labels.add(vv + "");
                        }

                        r.put(n, vv);
                    }

                    list.add(r);
                }

                JSONObject jo = prepareChartData1(xName, labels, yName, list, category);
                output.setChartData(jo);
            } else if (groupBys.size() == 2 && regFormatBar) {
                // ai生成吧
                category = "chart2";
                category = "bar";

                String c1 = groupBys.get(0).replace("`", "");
                String c2 = groupBys.get(1).replace("`", "");

                String label = fieldMap.get(c1);
                String xName = StringUtils.isNotEmpty(label) ? label : c1;
                String yName = "";
                for (String key : j.keySet()) {
                    if (c1.equals(key) || c2.equals(key)) {
                        continue;
                    }
                    yName = key;
                }

                Set<String> keySet2 = new HashSet<>();
                Map<String, Map<String, Object>> dataMap = new HashMap<>();
                for (Object obj : ls) {
                    JSONObject jj = parseObject(toJSONString(obj), Feature.OrderedField);

                    String c1Key = jj.getString(c1);
                    Map<String, String> m = fieldValueMap.get(c1);
                    if (m != null) {
                        String s = m.get(c1Key + "");
                        c1Key = StringUtils.isNotEmpty(s) ? s : c1Key;
                    }
                    Map<String, Object> c2Map = dataMap.computeIfAbsent(c1Key, k -> new HashMap<>());

                    String c2Key = jj.getString(c2);
                    m = fieldValueMap.get(c2);
                    if (m != null) {
                        String s = m.get(c2Key + "");
                        c2Key = StringUtils.isNotEmpty(s) ? s : c2Key;
                    }
                    keySet2.add(c2Key);

                    Object v = jj.get(yName);
                    c2Map.put(c2Key, v);
                }

                List<String> xLabels = new ArrayList<>(dataMap.keySet());

                Map<String, List<Object>> seriesData = new HashMap<>();
                for (String key1 : xLabels) {
                    Map<String, Object> c2Map = dataMap.get(key1);
                    for (String key2 : keySet2) {
                        List<Object> data = seriesData.computeIfAbsent(key2, k -> new ArrayList<>());
                        Object cc = c2Map.get(key2);
                        if (cc == null) {
                            cc = 0;
                        }
                        data.add(cc);
                    }
                }

                JSONObject jo = prepareChartData2(xName, yName, xLabels, seriesData, userInput);
                output.setChartData(jo);
            } else {
                List<OutputColumn> columns = new ArrayList<>();
                category = "table";
                for (String key : j.keySet()) {
                    OutputColumn c = new OutputColumn();
                    c.setProp(key);
                    String label = fieldMap.get(key);
                    boolean f = StringUtils.isNotEmpty(label);
                    c.setLabel(f ? label : key);
                    columns.add(c);
                }

                List<Object> list = new ArrayList<>();
                for (Object obj : ls) {
                    JSONObject jj = parseObject(toJSONString(obj), Feature.OrderedField);
                    for (String key : jj.keySet()) {
                        Object v = jj.get(key);
                        Object vv = v;
                        Map<String, String> m = fieldValueMap.get(key);
                        if (m != null) {
                            String s = m.get(v + "");
                            vv = StringUtils.isNotEmpty(s) ? s : v;
                        }
                        jj.put(key, vv);
                    }
                    list.add(jj);
                }
                output.setList(list);

                output.setColumns(columns);
            }
        }

        JSONObject extraParams = prepareExtraParams(type, category);
        output.setExtraParams(extraParams);
        return;
    }

    private static DatabaseUtil getDefaultSqlConnect(Map<String, String> sqlConfigs) throws Exception {
//        DatabaseHelper.build(t.getDbType(), t.getHost(), t.getPort(), t.getSchema(), t.getUsername(), t.getPassword());
        if (sqlConfigs == null) {
            sqlConfigs = new HashMap<>();
        }
        String version = sqlConfigs.getOrDefault("version", "8");
        String host = sqlConfigs.getOrDefault("host", "***********");
        String port = sqlConfigs.getOrDefault("port", "3306");
        String schema = sqlConfigs.getOrDefault("schema", "yj_data");
        String username = sqlConfigs.getOrDefault("username", "root");
        String password = sqlConfigs.getOrDefault("password", "Yjjy_fykj_7890");

        return DatabaseHelper.build("mysql", version,
                host,
                port,
                schema,
                username,
                password);
    }

    private static JSONObject prepareChartData1(String xName, List<String> xLabels, String yName, List<JSONObject> datas, String type) {
        JSONObject jo = new JSONObject();
        if (!"pie".equals(type)) {
            JSONObject xAxis = new JSONObject();
            xAxis.put("name", xName);
            xAxis.put("type", "category");
            xAxis.put("data", xLabels);
            jo.put("xAxis", xAxis);

            JSONObject yAxis = new JSONObject();
            yAxis.put("name", yName);
            yAxis.put("type", "value");
            jo.put("yAxis", yAxis);
        }

        JSONObject series = new JSONObject();
        series.put("type", type);
        series.put("data", datas);
        if ("pie".equals(type)) {
            series.put("radius", "50%");
        }
        jo.put("series", series);

        return jo;
    }

    private static JSONObject prepareChartData2(String xName, String yName, List<String> xLabels, Map<String, List<Object>> seriesData, String userInput) {
        JSONObject jo = new JSONObject();

        JSONObject legend = new JSONObject();
        legend.put("data", seriesData.keySet());
        jo.put("legend", legend);

        JSONObject yAxis = new JSONObject();
        yAxis.put("name", yName);
        yAxis.put("type", "value");
        jo.put("yAxis", yAxis);

        JSONObject xAxis = new JSONObject();
        xAxis.put("name", xName);
        xAxis.put("type", "category");
        xAxis.put("data", xLabels);
        jo.put("xAxis", xAxis);

        String[] charts = {"bar", "line"};
        String type = findCharType(userInput, charts);

        List<JSONObject> series = new ArrayList<>();
        for (String key : seriesData.keySet()) {
            JSONObject s = new JSONObject();
            s.put("name", key);
            s.put("type", type);
            s.put("data", seriesData.get(key));
            series.add(s);
        }
        jo.put("series", series);

        return jo;
    }

    private static JSONObject prepareExtraParams(String type, String category) {
        JSONObject o = new JSONObject();
        o.put("type", type);
        o.put("category", category);
        return o;
    }

    private static String findCharType(String userInput, String[] charts) {
        String type = "";
        if (userInput.contains("柱状") || userInput.contains("条状") || userInput.contains("柱形")) {
            type = "bar";
        } else if (userInput.contains("折线")) {
            type = "line";
        } else if (userInput.contains("饼图") || userInput.contains("饼状图")) {
            type = "pie";
        }

        if (StringUtils.isEmpty(type) || !Arrays.asList(charts).contains(type)) {
            /*Random random = new Random();
            int idx = random.nextInt(charts.length);
            type = charts[idx];*/
            type = "bar";
        }

        return type;
    }

    public static void main(String[] args) throws Exception {
        HashMap<String, String> sqlConfigs = new HashMap<>();
        String version = sqlConfigs.getOrDefault("version", "8");
        String host = sqlConfigs.getOrDefault("host", "localhost");
        String port = sqlConfigs.getOrDefault("port", "3306");
        String schema = sqlConfigs.getOrDefault("schema", "demo");
        String username = sqlConfigs.getOrDefault("username", "root");
        String password = sqlConfigs.getOrDefault("password", "123456");
        sqlConfigs.put("version", version);
        sqlConfigs.put("host", host);
        sqlConfigs.put("port", port);
        sqlConfigs.put("schema", schema);
        sqlConfigs.put("username", username);
        sqlConfigs.put("password", password);
//        DatabaseUtil d = getDefaultSqlConnect(sqlConfigs);
        String sqlStr = "SELECT\n" +
                "姓名 AS '学生姓名'\n" +
                "FROM exam_scores\n" +
                "WHERE 批次 IN ('零模', '一模', '二模') AND G1线 = true\n" +
                "GROUP BY 姓名\n" +
                "HAVING COUNT(1) = 3;";
        IHandlerContext context = new IHandlerContext();
        context.setSqlConfigs(sqlConfigs);
//        Output output1 = execSqlQuery(context, sqlStr, -1);
////        List<Map<String, Object>> maps = d.executeQuery(sqlStr.toLowerCase(), new HashMap<>());
//        System.out.println(output1);

//        ArrayList<Map<String, Object>> rLs = new ArrayList<>();
//        {
//            HashMap<String, Object> e = new HashMap<>();
//            e.put("教师数量", 2);
//            rLs.add(e);
//        }
//        {
//            HashMap<String, Object> e = new HashMap<>();
//            e.put("任教学科", "b");
//            e.put("教师数量", 1);
//            rLs.add(e);
//        }
//        // 多个需要核对一下key和value，0、1一起算一下
//        Set<String> keys = new HashSet<>();
//        for (Map<String, Object> l : rLs) {
//            Set<String> strings = l.keySet();
//            keys.addAll(strings);
//        }
//        System.out.println("query table colums len:" + keys.size());
//        for (Map<String, Object> l : rLs) {
////                        System.out.println("key len:" + l.keySet().size());
//            for (String key : keys) {
//                if (!l.containsKey(key)) {
//                    System.out.println("补充字段:" + key);
//                    l.put(key, "-");
//                }
//            }
//        }
//        String regx = "((SELECT|select).*?)";
//        Pattern p = Pattern.compile(regx);
//        String rawSql = "SELECT COUNT(*) AS '教师数量'\n" +
//                "FROM v_teacher_baseinfo\n" +
//                "WHERE data_status = 0\n" +
//                "  AND yrxs = '教职员工'\n" +
//                "  AND organizeschool_id NOT IN ('693c83b0-2c10-46ea-b7a6-392c32d26ea7', '70ce7ca7-4f45-4752-a4b2-7b7b89da74ff', 'd51ee675-f446-4bdf-9c93-c9743de9b5cd', 'd0ede7f0-0e9c-4907-bfce-938a93db2a14', '5c085e6d-dd46-4239-b304-a0211e17b63a', '2e94e658-be0e-4c22-903f-8ff6637d2497', '41813A097C244B3E9CF50F7CB2EF6D1B')\n" +
//                "  AND organizeschool_name NOT LIKE '%演示%'\n" +
//                "  AND school_id IN (SELECT school_id FROM v_school_baseinfo WHERE school_jc LIKE '%景城学校%')";
//        Matcher m = p.matcher(rawSql.toLowerCase().trim().replace("\n", " "));
//        if (m.find()) {
//            String sqlGroups = m.group();
//            int debug = 1;
//        } else {
//            int debug = 2;
//        }
        Output output = new Output();
        ArrayList<Map<String, Object>> rLs = new ArrayList<>();
//        {
//            HashMap<String, Object> e = new HashMap<>();
//            e.put("任务数量", 1);
//            e.put("year", 2024);
////            e.put("性别", "男");
//
//            rLs.add(e);
//        }
        {
            HashMap<String, Object> e = new HashMap<>();
            e.put("year", "2021");
            e.put("本科录取率",0.976);
//            e.put("总任务数量 ", 2);
            rLs.add(e);
        }
//        {
//            HashMap<String, Object> e = new HashMap<>();
//            e.put("任务编号", "2022");
//            e.put("任务名称", "2");
//            e.put("总任务数量 ", 3);
//            rLs.add(e);
//        }
//        output.setList(rLs);
        String sqlChange = "select      rjkc as '任教学科',     count(*) as '教师人数' from      v_teacher_baseinfo where      yrxs != '临聘员工'      and data_status = '0' group by      rjkc;";
        sqlChange = "SELECT year, (SELECT COUNT(*) FROM v_teacher_baseinfo WHERE rjkc = '信息科技' AND STR_TO_DATE(ryqzgsj, '%Y年%m月%d日') < CONCAT(year, '-01-01') AND yrxs != '临聘员工') + (SELECT COUNT(*) FROM v_teacher_baseinfo WHERE rjkc = '信息科技' AND update_time >= CONCAT(year, '-01-01') AND data_status = '9' AND STR_TO_DATE(ryqzgsj, '%Y年%m月%d日') < CONCAT(year, '-01-01')) AS '考虑退休的老师人数', (SELECT COUNT(*) FROM v_teacher_baseinfo WHERE rjkc = '信息科技' AND STR_TO_DATE(ryqzgsj, '%Y年%m月%d日') < CONCAT(year, '-01-01') AND yrxs != '临聘员工' AND data_status = '0') AS '不考虑退休的老师人数' FROM (SELECT 2023 AS year UNION ALL SELECT 2024 UNION ALL SELECT 2025) years ORDER BY year";
        sqlChange = "select live_status, count(*) as 数量 from population where address like '%娄葑街道%' group by live_status;\n";
        sqlChange = "select      case          when age < 30 then '30岁以下'         when age between 30 and 35 then '30-35岁'         else '35岁以上'     end as '年龄分组',     xb as '性别',     count(*) as '教师人数' from v_teacher_baseinfo inner join v_school_baseinfo on v_teacher_baseinfo.organizeschool_id = v_school_baseinfo.school_id where v_school_baseinfo.school_jbz in ('公办', '直属', '街道')   and v_school_baseinfo.school_lb in ('小学', '普通初中', '普通高中')   and v_teacher_baseinfo.data_status = '0'   and v_teacher_baseinfo.yrxs = '教职员工'   and v_school_baseinfo.school_name not like '%苏州工业园区教育局%' group by      case          when age < 30 then '30岁以下'         when age between 30 and 35 then '30-35岁'         else '35岁以上'     end,     xb order by       '年龄分组',     xb";
        sqlChange = "select concat(floor(datediff(current_date, birth_date) / 365.25 / 10) * 10, ' - ', floor(datediff(current_date, birth_date) / 365.25 / 10) * 10 + 9) as '年龄段', count(*) as '社工数量' from sw_basic_info where is_quit = 0 group by floor(datediff(current_date, birth_date) / 365.25 / 10);";
        sqlChange = "select count(t.id) as '任务数量', t.year from tt_task t where t.is_deleted = 0 and t.year >= 2021 group by t.year;";
        sqlChange = "select     '语文' as '科目',     avg(语文) as '平均分' from     exam_scores where     类别 = '物' union all select     '数学' as '科目',     avg(数学) as '平均分' from     exam_scores where     类别 = '物' union all select     '英语' as '科目',     avg(英语) as '平均分' from     exam_scores where     类别 = '物' union all select     '物理' as '科目',     avg(物理) as '平均分' from     exam_scores where     类别 = '物' union all select     '化学' as '科目',     avg(化学) as '平均分' from     exam_scores where     类别 = '物' union all select     '生物' as '科目',     avg(生物) as '平均分' from     exam_scores where     类别 = '物';";
        sqlChange = "select      `科别` as '科目',     avg(语文) as '语文平均分',     avg(数学) as '数学平均分',     avg(英语) as '英语平均分',     avg(物理) as '物理平均分',     avg(化学) as '化学平均分',     avg(生物) as '生物平均分' from      exam_scores where      `类别` = '物' group by      `科别`;";
        sqlChange = "SELECT \n" +
                "    t1.njmc AS '年级名称',\n" +
                "    COUNT(t2.uuid) AS '学生总数'\n" +
                "FROM \n" +
                "    v_school_class t1\n" +
                "JOIN \n" +
                "    v_student_baseinfo t2 ON t1.school_id = t2.school_id AND t1.bjwzmc_xn LIKE CONCAT('%', t2.class_name, '%')\n" +
                "WHERE \n" +
                "    t1.data_status = '0' \n" +
                "    AND t2.data_status = '0' \n" +
                "    AND t1.njmc IN ('一年级', '二年级', '三年级', '四年级', '五年级', '六年级', '七年级', '八年级', '九年级')\n" +
                "GROUP BY \n" +
                "    t1.njmc\n" +
                "ORDER BY \n" +
                "    t1.njmc;";
        sqlChange = "\n\nSELECT YEAR(issue_date) AS 年度, QUARTER(issue_date) AS 季度, COUNT(DISTINCT task_no) AS 任务数量 FROM task_info GROUP BY YEAR(issue_date), QUARTER(issue_date) ORDER BY 年度 ASC, 季度 ASC;";
        sqlChange = "select      year,     bk2plql as '本科录取率' from      v_area_bklq where      year = '2024' order by      year;";
        buildEchartJsonByResults(output, "请查询2024年园区高考本科的录取率，用柱状图展示", sqlChange, rLs);
        System.out.println(JSON.toJSONString(output));
        System.out.println(JSON.toJSONString(rLs));

//        String json = "```json\n" +
//                "{\n" +
//                "  \"chartData\": {\n" +
//                "    \"yAxis\": {\n" +
//                "      \"name\": \"数量\",\n" +
//                "      \"type\": \"value\"\n" +
//                "    },\n" +
//                "    \"xAxis\": {\n" +
//                "      \"data\": [\"市学科带头人\", \"区学科带头人\"],\n" +
//                "      \"name\": \"类型\",\n" +
//                "      \"type\": \"category\"\n" +
//                "    },\n" +
//                "    \"series\": {\n" +
//                "      \"data\": [\n" +
//                "        {\n" +
//                "          \"name\": \"市学科带头人\",\n" +
//                "          \"value\": 10\n" +
//                "        },\n" +
//                "        {\n" +
//                "          \"name\": \"区学科带头人\",\n" +
//                "          \"value\": 24\n" +
//                "        }\n" +
//                "      ],\n" +
//                "      \"type\": \"bar\"\n" +
//                "    }\n" +
//                "  },\n" +
//                "  \"extraParams\": {\n" +
//                "    \"type\": \"chart\",\n" +
//                "    \"category\": \"bar\"\n" +
//                "  }\n" +
//                "}\n" +
//                "```";
//        String outputstr = json.replaceAll("```", "").replace("json", "").trim();
//
//        JSONObject jsonObject = parseObject(outputstr);
//        System.out.println(jsonObject);


    }
}
