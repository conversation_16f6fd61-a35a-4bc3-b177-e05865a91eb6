package com.fytec.controller.open.agent.flow.utils.edu;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fytec.controller.open.agent.flow.protocol.IHandlerContext;
import com.fytec.controller.open.agent.flow.protocol.ISyncPreConstruct;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.Map;

//@Component
@Component("jiaoyu")
@Slf4j
public class QueryUserInfo implements ISyncPreConstruct {
    public static void main(String[] args) throws JsonProcessingException, UnirestException {

    }

    public static UserInfo getUserInfo(String token) throws Exception {
        Map<String, String> headers = GatewayUtils.buildDefaultHeader();
        JSONObject body = new JSONObject();
        body.put("accessToken", token);

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonBody = objectMapper.writeValueAsString(body);

        String turl = "http://portapigw.sipedu.cn/portal/app-api/sys/oauth/user_info";
        System.out.println(headers);
        HttpResponse<String> response = Unirest.post(turl)
                .header("Content-Type", org.springframework.http.MediaType.APPLICATION_JSON_VALUE)
                .headers(headers)
                .body(jsonBody)
                .asString();
        JSONObject parseObj = JSON.parseObject(response.getBody());
//        System.out.println(parseObj);
        if (parseObj != null && parseObj.containsKey("data")) {
            UserInfo data = parseObj.getObject("data", UserInfo.class);
            return data;
        }
        return null;
    }

    public static void syncUserInfo(IHandlerContext context) {
        String thirdUserToken = context.getThirdUserToken();
        if (StringUtils.isNotBlank(thirdUserToken)) {
            try {
                UserInfo userInfo = getUserInfo(thirdUserToken);
                if (userInfo != null) {
                    context.getConstants().putIfAbsent("sys_info_username", userInfo.getTruename());
                    context.getConstants().putIfAbsent("sys_info_tel", userInfo.getMobile());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void init(IHandlerContext context) {
        log.info("sync user info");
        syncUserInfo(context);
    }

    //    {"code":0,"data":{"id":"96FFB996B44E42FB91887F48FFA927CB","username":"xueyang123","truename":"徐雪阳",
//            "mobile":"13914014940","teacherId":"96FFB996B44E42FB91887F48FFA927CB","parentId":""},"msg":""}

    @Data
    public static class UserInfo {
        private String id;
        private String username;
        private String truename;
        private String mobile;
        private String teacherId;
        private String parentId;
    }
}
