package com.fytec.controller.open.agent.flow.utils.edu;

import cn.hutool.core.exceptions.ValidateException;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fytec.controller.open.agent.flow.handler.edu.EduTopicDataQueryAgent;

import com.fytec.controller.open.agent.flow.protocol.IHandlerContext;
import com.fytec.controller.open.agent.flow.utils.functioncall.FCallResultDTO;
import com.fytec.handler.IResponseHandlerProxy;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.util.DigestUtils;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;


/**
 * 教育
 * 网关需要的验证等
 * 流程：
 * 先通过appId获取到apiToken和平台上申请订阅
 * <p>
 * 然后：把appId、appSecret、apiToken、和timestamp以及md5后的，放在header里，即可调用
 * sign=md5(appId +appSecret+ timestamp + apiToken)
 * 时间戳，长度为10位，如: 1474610314
 */
@Slf4j
public final class GatewayUtils {

    public static final String appId = "aeca6faaf36f40bb984c830bc194ee5d";
    private static final String appSecret = "ef81c39a2dfb4213aaf522ef8ba8383f";
    // 获取到apiToken，需要缓存时间
    private static String apiToken = null;
    private static ZonedDateTime apiTokenExpireTimestamp = ZonedDateTime.now().minusDays(1);

    private static final ReadWriteLock apiTokenLock = new ReentrantReadWriteLock();

    private static final String authTokenUrl = "https://portapigw.sipedu.cn/aep/gateway/api_token";
    private static final String authRefreshTokenUrl = "https://portapigw.sipedu.cn/aep/gateway/refresh_api_token";

    public static Map<String,String>  buildDefaultHeader() {
//        if (apiTokenExpireTimestamp.isBefore(ZonedDateTime.now())) {
//            apiTokenLock.writeLock().lock();
//            try {
//                if (apiTokenExpireTimestamp.isBefore(ZonedDateTime.now())) {
//                    //刷新token
//                    apiToken = refreshApiToken();
//                    apiTokenExpireTimestamp = ZonedDateTime.now().plusHours(1); //一小时刷新一次
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//                throw new ValidateException("授权出错");
//            } finally {
//                apiTokenLock.writeLock().unlock();
//            }
//        }
        EduHeaderBuilder builder = new EduHeaderBuilder(appId, appSecret, apiToken);
        return builder.buildDefaultHeader();
    }

    private static String refreshApiToken() throws UnirestException {
//        {
//            "data": {
//            "apiToken": " C106AD0D5A161C88097A4B7D1DD56C00"
//        },
//            "retCode": "000000",
//                "retDesc": "请求成功"
//        }
        String url = null;
        if (apiToken == null) {
            //获取
            url = authTokenUrl;
        } else {
            // 刷新
            url = authRefreshTokenUrl;
        }

        EduHeaderBuilder builder = new EduHeaderBuilder(appId, appSecret, null);
        long timestamp = new Date().getTime() / 1000;
        String sign = DigestUtils.md5DigestAsHex((appId + appSecret + timestamp).getBytes());

        HttpResponse<String> response = Unirest.get(url)
                .header("timestamp", String.valueOf(timestamp))
                .header("appId", appId)
                .header("sign", sign)
                .header("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .asString();
        JSONObject parseObj = JSON.parseObject(response.getBody());
        JSONObject data = parseObj.getJSONObject("data");
        if(data==null||!data.containsKey("apiToken")){
            log.error("refreshApiToken error:" + response.getBody());
        }
        return data.getString("apiToken");
    }
    // 测试数据权限接口
    public static void main(String[] args) throws Exception {
        IHandlerContext context = new IHandlerContext();
        context.setThirdUserToken("962771f1-cf28-4fe3-a29f-44fc9e20d431");
        Map<String, String> headers = buildDefaultHeader();
//        EduTableMetaQueryAgent.DataPermission permission = EduTableMetaQueryAgent.getPermission(headers,context, "老师,学校,家长教师学生");
//        permisson data:{"studentIdList":"","classIdList":"","teacherIdList":"","schoolIdList":"","isAreaAdmin":false,"subjectList":"语文,数学","xdList":"小学,初中"}
//        {appId=aeca6faaf36f40bb984c830bc194ee5d, sign=b73949d97f0bd18f2ada0aca2dd50a22, timestamp=1750125432}
//        System.out.println(permission);
//        refreshApiToken();

        EduTopicDataQueryAgent topicAgent = new EduTopicDataQueryAgent();
        FCallResultDTO info = topicAgent.getEduSchoolInfo(context, "2023", "2");
//        FCallResultDTO info = topicAgent.getEduResourceInfo(context);

        System.out.println(info);
    }


    //测试流接口
    public static void main1(String[] args) throws Exception {
//        System.out.println(buildDefaultHeader());
        String urlText = "http://portapigw.sipedu.cn/agent-api/api/agent/open/stream/execute";

        Map<String,String>  headers = buildDefaultHeader();

        String API_KEY = "96qROXNPAZqdTMtLLiiZHIvxuc4esa9dAuDgIgvAPvjeEsvcwweAYMCd4gtM";
        // 创建请求体
        JSONObject body = new JSONObject();
        // todo body
        body.put("conversationId", "xxy_20250417_1");
        body.put("agentId", 5);
        body.put("userInput", "园区近3年信息技术老师的每年总人数变化趋势");
        body.put("thirdUserId", "xxy");


        ObjectMapper objectMapper = new ObjectMapper();
        String jsonBody = objectMapper.writeValueAsString(body);
        System.out.println(jsonBody);


        URL url = new URL(urlText);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("Authorization", "Bearer " + API_KEY);
        headers.forEach((key, value) -> {
            connection.setRequestProperty(key, value == null ? null : value.toString());
        });

        connection.setDoOutput(true);
        try (DataOutputStream wr = new DataOutputStream(connection.getOutputStream())) {
            byte[] input = jsonBody.getBytes(StandardCharsets.UTF_8);
            wr.write(input, 0, input.length);
        }

        int responseCode = connection.getResponseCode();
        if (responseCode == HttpURLConnection.HTTP_OK) {
            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                System.out.println(line);
            }
        }
    }

}
