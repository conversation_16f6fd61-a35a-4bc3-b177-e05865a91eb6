package com.fytec.controller.open.agent.flow.protocol.errors;

import com.fytec.controller.open.agent.flow.protocol.AgentHandlerError;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class AgentSqlQueryExecException extends AgentSqlQueryException {

    public AgentSqlQueryExecException(String msg) {
        super(msg);
        this.msg = msg;
        // Unknown column 'v_student_baseinfo.djrmc1' in 'field list'
        // 这种具体执行的错误应该改写
        if (msg != null && msg.contains("Unknown column ")) {
            String columnName = parseColumnName(msg);
            if (columnName != null) {
                String[] split = columnName.split("\\.");
                this.msg += "\n字段名[" + split[split.length - 1] + "]不存在";
            }
        }
    }
    public static String parseColumnName(String errorMessage) {
        // 正则表达式：匹配单引号内的任意非引号字符
        Pattern pattern = Pattern.compile("'([^']+)'");
        Matcher matcher = pattern.matcher(errorMessage);

        // 查找第一个匹配的单引号内容（通常是字段名）
        if (matcher.find()) {
            return matcher.group(1); // 返回第一个捕获组的内容
        }
        return null; // 未找到匹配内容
    }
}
