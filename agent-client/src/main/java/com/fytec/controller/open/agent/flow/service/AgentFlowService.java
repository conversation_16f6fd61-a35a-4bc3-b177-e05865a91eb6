package com.fytec.controller.open.agent.flow.service;

import cn.hutool.core.collection.ConcurrentHashSet;
import cn.hutool.core.exceptions.ValidateException;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.constant.AgentFlowConstants;
import com.fytec.controller.open.agent.flow.handler.common.GenericAgentHandler;
import com.fytec.controller.open.agent.flow.protocol.errors.AgentDataNotPermissionException;
import com.fytec.controller.open.agent.flow.utils.edu.PermissonSqlChangeUtil;
import com.fytec.controller.open.agent.flow.handler.prod.ProdEduExamTableQuerySqlAgent;
import com.fytec.controller.open.agent.flow.handler.prod.RenwuProdIntentIAgent;
import com.fytec.controller.open.agent.flow.handler.prod.RenwuProdTableQuerySqlAgent;
import com.fytec.controller.open.agent.flow.handler.prod.RenwuProdTaiZhangQuerySqlAgent;
import com.fytec.controller.open.agent.flow.handler.renwu.RenwuIntentIAgent;
import com.fytec.controller.open.agent.flow.handler.renwu.RenwuTableQuerySqlAgent;
import com.fytec.controller.open.agent.flow.handler.renwu.RenwuTaiZhangQuerySqlAgent;
import com.fytec.controller.open.agent.flow.handler.wjedu.WjEduTableQuerySqlAgent;
import com.fytec.controller.open.agent.flow.protocol.errors.AgentDataNotPermissionException;
import com.fytec.controller.open.agent.flow.protocol.ISyncPreConstruct;
import com.fytec.controller.open.agent.flow.protocol.errors.AgentRunBreakError;
import com.fytec.controller.open.agent.flow.protocol.errors.AgentSqlQueryExecException;
import com.fytec.controller.open.agent.flow.utils.edu.QueryUserInfo;
import com.fytec.dto.agent.AgentHandlerConfigQueryDTO;
import com.fytec.dto.agent.AgentFlowHistoryDTO;
import com.fytec.dto.agent.feedback.AgentHistoryFeedbackDTO;
import com.fytec.dto.knowledge.KnowledgeConfigDTO;
import com.fytec.entity.agent.AgentHandlerConfig;
import com.fytec.dto.open.EchartFormatDTO;
import com.fytec.mapper.agent.AgentHistoryFeedbackMapper;
import com.fytec.satoken.StpClientUserUtil;
import com.fytec.constant.Constants;
import com.fytec.controller.open.agent.flow.handler.edu.*;
import com.fytec.controller.open.agent.flow.protocol.AgentHandlerError;
import com.fytec.controller.open.agent.flow.protocol.IAgentHandler;
import com.fytec.controller.open.agent.flow.protocol.IHandlerContext;
import com.fytec.controller.open.agent.flow.protocol.errors.AgentJsonFormatException;
import com.fytec.controller.open.agent.flow.protocol.errors.AgentSqlQueryException;
import com.fytec.controller.open.agent.flow.utils.SqlQueryUtils;
import com.fytec.controller.open.agent.flow.utils.dataquery.dto.Output;
import com.fytec.controller.open.agent.flow.utils.sft.SftKnowledgeDto;
import com.fytec.controller.open.agent.dto.CallAgentDTO;
import com.fytec.dto.agent.AgentExecuteDTO;
import com.fytec.dto.agent.AgentPublishHistoryDTO;
import com.fytec.dto.agent.AgentSftDto;
import com.fytec.dto.knowledge.KnowledgeDocHistoryDTO;
import com.fytec.entity.agent.AgentPublish;
import com.fytec.entity.agent.AgentPublishHistory;
import com.fytec.handler.IResponseHandlerProxy;
import com.fytec.mapper.agent.AgentPublishHistoryMapper;
import com.fytec.mapper.agent.AgentPublishMapper;
import com.fytec.service.agent.AgentHandlerConfigService;
import com.fytec.service.agent.AgentHistoryService;
import com.fytec.service.agent.AgentService;
import com.fytec.service.agent.AgentSftService;
import com.fytec.service.flow.FunctionCallService;
import com.fytec.util.UUIDGenerator;
import com.fytec.utils.AiFormatJsonUtil;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
public class AgentFlowService {
    @Value("${env.type}")
    private String envType;
    @Value("${env.level.sql.print}")
    private boolean sqlPrint = false;
    @Value("${agent.utils.echart.format}")
    private Long echartFormatAgentId;

    @Autowired
    private AgentService agentService;
    @Autowired
    private AgentPublishMapper agentPublishMapper;
    @Autowired
    private AgentPublishHistoryMapper agentPublishHistoryMapper;
    @Autowired
    private AgentSftService agentSftService;
    @Autowired
    private AgentFlowInitService agentFlowInitService;
    @Autowired
    private AgentHistoryService agentHistoryService;
    @Autowired
    private FunctionCallService functionCallService;

    private Map<String, ISyncPreConstruct> preConstructMap;

    @Autowired
    private AgentHistoryFeedbackMapper feedbackMapper;

    @Autowired
    public AgentFlowService(Map<String, ISyncPreConstruct> preConstructMap) {
        this.preConstructMap = preConstructMap;
    }

    public ConcurrentHashMap<Long, IAgentHandler> globalAgentHandlerMap = new ConcurrentHashMap<>();

    private static final Set<String> streamAgentIds = new ConcurrentHashSet<>();
    private static final Set<String> sqlAgentIds = new ConcurrentHashSet<>();

    /***通用处理器配置***/
    @Autowired
    private AgentHandlerConfigService agentHandlerConfigService;
    @Value("${spring.profiles.active:''}")
    private String env;

    @PostConstruct
    public void init() {
        //将不同的处理智能体注册上去
        agentFlowInitService.init(this, envType, streamAgentIds, sqlAgentIds);

        log.info("load AgentFlowService,envType:" + envType);

        // 最后从库里 新增追加配置
        try {
            AgentHandlerConfigQueryDTO queryDTO = new AgentHandlerConfigQueryDTO();
            queryDTO.setEnvType(env);
            List<AgentHandlerConfig> list = agentHandlerConfigService.list(queryDTO);
            if (list != null && list.isEmpty()) {
                list.forEach(agentHandlerConfig -> {
                    if (agentHandlerConfig.getAgentId() != null) {
                        globalAgentHandlerMap.put(agentHandlerConfig.getAgentId(), new GenericAgentHandler(agentHandlerConfig));
                        sqlAgentIds.add(agentHandlerConfig.getAgentId().toString());
                        log.info("加载 AgentHandlerConfig,环境:{},agentId:{}", agentHandlerConfig.getEnvType(), agentHandlerConfig.getAgentId());
                    }
                });
            }
        } catch (Exception e) {
            log.error("智能体配置处理器注册失败：", e);
        }


    }

    public void execAgentChangeNodeById(Long agentId, IHandlerContext context) throws AgentHandlerError {
        AgentExecuteDTO dto = new AgentExecuteDTO();
        dto.setUserInput(context.getInput());
        // 拿最新版本
        AgentPublish agentPublishDTO = agentService.getAgentPublishByAgentId("" + agentId);
        dto.setAgentPublishId(agentPublishDTO.getId());
        dto.setConversationId(context.getSessionId());
        dto.setFlowId(context.getFlowId());
        dto.setThirdUserId(context.getThirdUserId());
        dto.setAgentId(agentId);
        dto.setObjectId(context.getObjectId());
        // 判断一下
        log.debug(String.format(" change before: %s", dto.getUserInput()));
        AgentPublishHistoryDTO history = agentService.executePublishedAgent(dto);
        // 通过智能体，output作为input
        log.debug(String.format(" change after: %s", history.getAnswer()));
        context.setInput(history.getAnswer());

    }

    public String execAgentNodeById(AgentFlowConstants.AGENT_RUN_TYPE runType, Long agentId, IHandlerContext context, String userInput) throws AgentHandlerError {
        AgentExecuteDTO dto = new AgentExecuteDTO();
        dto.setUserInput(userInput);
        // 拿最新版本
        AgentPublish agentPublishDTO = agentService.getAgentPublishByAgentId("" + agentId);
        if (agentPublishDTO == null) {
            log.error("execAgentNodeById agentPublishDTO null,agentId:{}",agentId);
            return null;
        }
        dto.setAgentRunType(runType.name());// 标注这个单节点类型
        dto.setAgentPublishId(agentPublishDTO.getId());
        dto.setConversationId(context.getSessionId());
        dto.setFlowId(context.getFlowId());
        dto.setObjectId(context.getObjectId());
        dto.setThirdUserId(context.getThirdUserId());
        dto.setAgentId(agentId);
        // 判断一下
        log.debug(String.format(" change before: %s", dto.getUserInput()));
        AgentPublishHistoryDTO history = agentService.executePublishedAgent(dto);
        // 通过智能体，output作为input
        log.debug(String.format(" change after: %s", history.getAnswer()));
        return history.getAnswer();
    }

    private void initContextSysConstants(IHandlerContext context) {
        if (context.getConstants() == null) {
            context.setConstants(new HashMap<>());
        }
        if (preConstructMap != null && preConstructMap.containsKey(envType)) {
            // 找出这个环境的方法
            preConstructMap.get(envType).init(context);
        }
        //今天是{{sys_curdate}}
        //我的姓名是：{{sys_info_username}}
        //我的手机号是：{{sys_info_tel}}
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        context.getConstants().putIfAbsent("sys_curdate", dateFormat.format(new Date()));
        context.getConstants().putIfAbsent("sys_info_username", "");
        context.getConstants().putIfAbsent("sys_info_tel", "");
        context.setFunctionCallService(functionCallService);
    }

    public AgentPublishHistoryDTO executePublishedAgent(HttpServletResponse response, CallAgentDTO callAgentDTO) throws AgentHandlerError {

        //
        Long historyKnowledgeId = null;
        String historyKnowledgeConfigStr = null;
        callAgentDTO.setFlowId(UUIDGenerator.getUUID());
        if (callAgentDTO.getAgentPublishId() == null) {
            // 通过agentId，获取到最新的发布版本
            AgentPublish agentPublishDTO = agentService.getAgentPublishByAgentId(callAgentDTO.getAgentId());
            if (agentPublishDTO == null) {
                throw new ValidateException(callAgentDTO.getAgentId() + "号智能体未发布，无法使用，请核对");
            }
            historyKnowledgeConfigStr = agentPublishDTO.getHistoryKnowledge();
            callAgentDTO.setAgentPublishId(agentPublishDTO.getId());
            String model = agentPublishDTO.getModel();
            callAgentDTO.setCurrentModel(StringUtils.isNotBlank(model) ? JSON.parseObject(model) : null);

        } else if (callAgentDTO.getAgentId() == null) {
            // 设置一下
            AgentPublish agentPublish = agentPublishMapper.selectById(callAgentDTO.getAgentPublishId());
            callAgentDTO.setAgentId("" + agentPublish.getAgentId());
            historyKnowledgeConfigStr = agentPublish.getHistoryKnowledge();
            String model = agentPublish.getModel();
            callAgentDTO.setCurrentModel(StringUtils.isNotBlank(model) ? JSON.parseObject(model) : null);

        }
        // 如果有历史知识库，需要做记录
        if (StringUtils.isNotBlank(historyKnowledgeConfigStr)) {
            KnowledgeConfigDTO knowledgeConfigDTO = JSON.parseObject(historyKnowledgeConfigStr, KnowledgeConfigDTO.class);
            if (knowledgeConfigDTO.getKnowledgeList() != null && !knowledgeConfigDTO.getKnowledgeList().isEmpty()) {
                historyKnowledgeId = knowledgeConfigDTO.getKnowledgeList().getFirst().getId();
            }
        }
        // 需要循环判断
        AgentPublishHistoryDTO history = null;
        final List<Map<String, Object>> extraOutputs = new LinkedList<>();
        List<Thread> parallelThreads = new LinkedList<>();

        if (globalAgentHandlerMap.containsKey(Long.parseLong(callAgentDTO.getAgentId()))) {
            //需要循环调用了可能
            // 这时候只处理流接口

            IHandlerContext context = new IHandlerContext();
            context.setEnvType(envType);
            context.setSessionId(callAgentDTO.getConversationId());
            context.setInput(callAgentDTO.getUserInput());
            context.setRawInput(callAgentDTO.getRawInput());
            context.setAgentId("" + callAgentDTO.getAgentId());
            context.setThirdUserToken(callAgentDTO.getThirdUserToken());
            context.setAgentPublishId(callAgentDTO.getAgentPublishId());
            context.setRawInput(callAgentDTO.getRawInput());
            context.setAgentFlowService(this);
            context.setFlowId(callAgentDTO.getFlowId());
            context.setResponse(response);
            context.setThirdUserId(callAgentDTO.getThirdUserId());
            context.setObjectId(callAgentDTO.getObjectId());
            context.setCurrentNodeIndex(1);
            context.setCurrentModel(callAgentDTO.getCurrentModel());
//            context.setSysPrompt(callAgentDTO.getSysPrompt()); // 不允许这样调用
            // 设置一些常见字段
            context.setHistoryKnowledgeId(historyKnowledgeId);
            context.setConfigs(callAgentDTO.getConfigs());
            initContextSysConstants(context);

            Set<String> hasRunId = new HashSet<>();
            boolean isFirstNode = true;
            do {
                // 改写
                IAgentHandler beforeHandler = globalAgentHandlerMap.getOrDefault(Long.parseLong(context.getAgentId()), null);
                if (beforeHandler != null) {
                    beforeHandler.beforeHandle(context);
                }
                AgentExecuteDTO dto = new AgentExecuteDTO();
                dto.setResponseHandlerProxy(null);
                dto.setAgentPublishId(context.getAgentPublishId());
                dto.setUserInput(context.getInput());
                dto.setSysPrompt(context.getSysPrompt());
                dto.setRawInput(context.getRawInput());
                if (StringUtils.isNotBlank(context.getAgentId())) {
                    dto.setAgentId(Long.parseLong(context.getAgentId()));
                }
                dto.setConversationId(context.getSessionId());
                dto.setConstants(context.getConstants());
                dto.setEnableCurrentDate(false);
                buildAgentExecuteConfig(dto, context.getConfigs());

                dto.setThirdUserId(context.getThirdUserId());

                dto.setFlowId(context.getFlowId());
                dto.setObjectId(context.getObjectId());
                dto.setHistoryKnowledgeId(context.getHistoryKnowledgeId());
                dto.setAgentRunType(AgentFlowConstants.AGENT_RUN_TYPE.customer.name());
                if (isFirstNode) {
                    isFirstNode = false;
                    dto.setAgentRunType(AgentFlowConstants.AGENT_RUN_TYPE.first.name());
                }

                context.setNextAgentId(null);
                context.setAgentPublishId(null);
                context.setHistoryDTO(null);//清空历史记录
                history = null;
                context.setCurrentHandler(beforeHandler);

                // 目前只有客服是流的
                if (streamAgentIds.contains(context.getAgentId())) {
                    dto.setAgentRunType(AgentFlowConstants.AGENT_RUN_TYPE.last.name());
                    try {
                        history = runLastCustomerAgent(context, dto, parallelThreads, extraOutputs);
                    } catch (AgentRunBreakError e) {
                        updateAgentsRunCompleteByFlowId(context, history);
                    }
                    break;
                } else {
                    Thread thinkThread = null;
                    final AgentPublishHistoryDTO[] thinkHistoryDto = {null};
                    {
                        log.info(String.format("run agent: %s,think agent: %s", context.getAgentId(), context.getThinkAgentId()));
                        //思考过程的
                        if (response != null && context.getThinkAgentId() != null) {
                            // 需要针对这个sql/其他input做做思考过程
                            // 需要线程处理
                            thinkThread = startThinkThread(context, dto.getUserInput(), thinkHistoryDto);
                        }
                    }
                    // 如果是那种，阻塞但是需要流输出思考过程的

                    if (response != null) {
                        checkAndRunParallelAgents(context, parallelThreads, extraOutputs, beforeHandler);
                    }

                    if (response != null && context.getCurrentHandler() != null && context.getCurrentHandler().getBlockButPrintThink(context)) {
                        // 流调用，但是拦截改写输出
                        dto.setResponseHandlerProxy(context.getCurrentHandler().getResponseHandlerProxy(IAgentHandler.RESPONSE_TYPE.common, context));
                        history = agentService.executePublishedAgent(response, dto);
                    } else {
                        history = agentService.executePublishedAgent(dto);
                    }

//                    context.setLastInput(history.getUserInput());
                    context.setOutput(history.getAnswer());
                    context.setHistoryDTO(history);

                    Class flowErrorType = null;
                    IAgentHandler afterHandlerNode = globalAgentHandlerMap.getOrDefault(Long.parseLong(context.getAgentId()), null);
                    if (afterHandlerNode != null) {
                        try {
                            afterHandlerNode.handle(context);
                        } catch (AgentJsonFormatException | AgentDataNotPermissionException e) {
//                            flowErrorType = AgentJsonFormatException.class;
                            flowErrorType = e.getClass();
                            e.printStackTrace();
                            history.setError(e.getErrorMsg());
                            updateAgentsRunCompleteByFlowId(context, history);
                            if (response != null) {
                                context.initResponse();
                                try {
                                    String errorcontent = null;
                                    if (e instanceof AgentDataNotPermissionException) {
                                        errorcontent = ((AgentDataNotPermissionException) e).getMsg();
                                        if (e instanceof AgentDataNotPermissionException) {
                                            // 中断流程了
                                            context.setNextAgentId(null);
                                            history.setAnswer(errorcontent);
                                            updateHistoryAnswer(errorcontent, history.getId());
                                        }
                                    } else {
                                        errorcontent = context.getOutput();
                                        errorcontent = errorcontent.replaceAll("\n", "\\n").replaceAll("\"", "\\\"");
                                    }
//                                    response.getWriter().write("event:message\ndata:{\"message\":{\"type\":\"content\",\"content\":\"" + errorcontent + "\"}}\n\n");
//                                    response.getWriter().flush();
                                    writeToResponse(response, null, "message", createErrorJson(errorcontent, history));
                                } catch (IOException e2) {
                                    throw new RuntimeException(e2);
                                }
                            }
                        }
                    }
                    context.setCurrentHandler(afterHandlerNode);

                    boolean isSameRunId = hasRunId.contains("" + context.getNextAgentId());
                    if(isSameRunId){
                        log.warn("id重复执行:" + context.getNextAgentId());
                    }
                    if (context.getNextAgentId() != null && context.getNextAgentId() > 0L && !isSameRunId) {
                        // 并行的结果也呈现一下
//                        if (hasRunId.contains("" + context.getNextAgentId())) {
//                            //  理论上不会重复，一个流程里有相同智能体处理
//                            break;
//                        }
                        // 执行下一个节点前，处理一下当前节点可能的cache数据，进行缓存
                        List<Map<String, Object>> currentNodeCahce = new ArrayList<>();
                        int writenum = writeExtraInfo(context, history.getId(), history.getAgentId(), currentNodeCahce, history.getRuntime());
                        if (writenum > 0) {
                            extraOutputs.add(createExtraOuput("", history.getAnswer(), history));
                            agentPublishHistoryMapper.updateExtraInfoById(JSON.toJSONString(extraOutputs), history.getId());
                        }

                        hasRunId.add("" + context.getNextAgentId());
                        context.setAgentId("" + context.getNextAgentId());
                        // 获取发布的版本
                        log.warn("执行下一个智能体，id:" + context.getNextAgentId());
                        AgentPublish agentPublishDTO = agentService.getAgentPublishByAgentId(context.getAgentId());
                        context.setAgentPublishId(agentPublishDTO.getId());
                        context.setHistoryKnowledgeId(null);
                        if (StringUtils.isNotBlank(agentPublishDTO.getHistoryKnowledge())) {
                            KnowledgeConfigDTO knowledgeConfigDTO = JSON.parseObject(agentPublishDTO.getHistoryKnowledge(), KnowledgeConfigDTO.class);
                            if (knowledgeConfigDTO.getKnowledgeList() != null && !knowledgeConfigDTO.getKnowledgeList().isEmpty()) {
                                context.setHistoryKnowledgeId(knowledgeConfigDTO.getKnowledgeList().getFirst().getId());
                            }
                        }
                        context.setNextAgentId(null);
                        context.setCurrentModel(null);
                        // 继续执行
                        context.setCurrentNodeIndex(context.getCurrentNodeIndex() + 1);
                        if (StringUtils.isNotBlank(agentPublishDTO.getModel())) {
                            context.setCurrentModel(JSON.parseObject(agentPublishDTO.getModel()));
                        }

                    } else {
                        // 修改节点状态为最后一个了。
                        updateAgentRunNodeEndType(history.getId());
                        // 直执行完了
//                        log.warn("下一个id为空，结束，输出结果:");
                        context.initResponse();
                        try {
                            String anserwer = history.getAnswer();
                            if (sqlAgentIds.contains(context.getAgentId())) {
                                anserwer = runSqlAgentBuildChartJsonFlow(context, history, parallelThreads);
                            } else {
                                // 需要加上前缀
                                anserwer = "\"" + anserwer + "\"";
                            }
                            if (response != null) {
                                // 流接口，这边回存储运算结果
                                if (flowErrorType == null) {
                                    // 如果不存在流程，默认简单处理，如果涉及流程了，那么久一起处理
                                    boolean addHistoryAnswer = false;
                                    if (!extraOutputs.isEmpty()) {
                                        // 先存储，然后一起输出
                                        addHistoryAnswer = true;
                                        extraOutputs.add(createExtraOuput("", anserwer, history));
                                    } else {
//                                        anserwer = anserwer.replaceAll("\\\\n", "\n");
//                                        anserwer = anserwer.replaceAll("\\\\\"", "\"");
                                        anserwer = anserwer.replaceAll("\n", "\\n");//.replace("\"", "\\\"");
                                        writeToResponse(response, null, "message", createMessageJson(history, anserwer));
                                    }


                                    // 涉及多个了，就一起输出一下
                                    waitParallelAgentsResult(response, context, history, parallelThreads, extraOutputs);
                                    if (!extraOutputs.isEmpty()) {
                                        //多个记录的
                                        if (!addHistoryAnswer) {
                                            extraOutputs.add(createExtraOuput("", anserwer, history));
                                        }
                                        history.setExtraInfo(JSON.toJSONString(extraOutputs));
                                        agentPublishHistoryMapper.updateExtraInfoById(history.getExtraInfo(), history.getId());
                                    }
                                } else {
                                    // 不用等待了，但是可以把已经处理好的存起来
                                    extraOutputs.add(createExtraOuput("", history.getError(), history));
                                    // 处理一下，其他节点也加进去的流程点.异常其实这里不一定需要加上去了
//                                    int writenum = writeExtraInfo(context, history.getId(), history.getAgentId(), extraOutputs, history.getRuntime());
//                                    history.setExtraInfo(JSON.toJSONString(extraOutputs));
                                    agentPublishHistoryMapper.updateExtraInfoById(history.getExtraInfo(), history.getId());
                                }


                                writeToResponse(response, null, "message", createHistoryIdJson(history));

                                if (thinkThread != null) {
                                    try {
                                        thinkThread.join();
                                        if (thinkHistoryDto[0] != null) {
                                            //  把这个回答作为思考过程
                                            agentPublishHistoryMapper.updateReasoningById(thinkHistoryDto[0].getAnswer(), history.getId());
                                        }

                                    } catch (InterruptedException e) {
                                        log.error("thinkThread error:" + e.getMessage());
                                        e.printStackTrace();
                                    }
                                }
                                writeToResponse(response, null, "done", "");

                                // 把思维链结果插入进去
                                if (!context.getThinkStrList().isEmpty()) {
                                    agentPublishHistoryMapper.updateReasoningChainById(JSON.toJSONString(context.getThinkStrList()), history.getId());
                                }
                            }

                        } catch (IOException e) {
                            history.setError(e.getMessage());
                            updateAgentsRunCompleteByFlowId(context, history);
                            throw new RuntimeException(e);
                        }
                        break;
                    }
                }

            } while (context.getAgentPublishId() != null);
        } else {
            history = commonExecutePublishedAgentOneNode(response, callAgentDTO, historyKnowledgeId);
        }
        return history;
    }

    private void updateAgentRunNodeEndType(Long historyId) {
        agentPublishHistoryMapper.updateAgentType(historyId, AgentFlowConstants.AGENT_RUN_TYPE.last.name());
    }

    private Map<String, Object> createExtraOuput(String type, String content, AgentPublishHistoryDTO history) {
        return createExtraOuput(type, content, history.getRuntime());
    }

    private Map<String, Object> createExtraOuput(String type, String content, Long runtime) {
        HashMap<String, Object> objectMap = new HashMap<>();
        objectMap.put("content", content);
        objectMap.put("type", type);
        objectMap.put("runtime", runtime);
        return objectMap;
    }

    public void buildAgentExecuteConfig(AgentExecuteDTO dto, Map<String, Object> configs) {
        if (configs == null) {
            return;
        }
        if (configs.containsKey("" + dto.getAgentId())) {
            // 说明有这个配置
            Object o = configs.get("" + dto.getAgentId());
            if (o != null && o instanceof Map) {
                if (((Map) o).containsKey("constants")) {
                    Map constants = (Map) ((Map) o).get("constants");
                    // 把value变成从常量里查询
                    Map<String, String> map;
                    if (dto.getConstants() == null) {
                        map = new HashMap<>();
                    } else {
                        // 避免污染全局的
                        map = JSON.parseObject(JSON.toJSONString(dto.getConstants()), Map.class);
                    }
                    for (Object key : constants.keySet()) {
                        // 输入的强制要求是常量，后续再替换为常量
                        map.put(key.toString(), String.format("${%s}", constants.get(key).toString()));
                    }
                    dto.setConstants(map);
                }
            }
        }
    }

    /**
     * 没有流程，单智能体的执行
     *
     * @return
     */
    public AgentPublishHistoryDTO commonExecutePublishedAgentOneNode(HttpServletResponse response,
                                                                     CallAgentDTO callAgentDTO, Long historyKnowledgeId) {
        AgentPublishHistoryDTO history;

        // 调用平台智能体

        AgentExecuteDTO dto = new AgentExecuteDTO();
        BeanUtils.copyProperties(callAgentDTO, dto);
        dto.setAgentId(Long.valueOf(callAgentDTO.getAgentId()));
        dto.setHistoryKnowledgeId(historyKnowledgeId);
        dto.setAgentRunType(AgentFlowConstants.AGENT_RUN_TYPE.last.name());
        dto.setEnableCurrentDate(false);
        //  单节点，设置常量即可

        buildAgentExecuteConfig(dto, callAgentDTO.getConfigs());
        if (response == null) {
            history = agentService.executePublishedAgent(dto);
        } else {
            // 可以拦截
//                history = agentService.executePublishedAgent(response, dto);
            // 这边也有代理，处理一下
            dto.setResponseHandlerProxy(new IResponseHandlerProxy() {
                @Override
                public String handler(String responseStr) {
                    if (responseStr.startsWith("event:done")) {
                        // 忽略流处理的
                        return null;
                    }
                    return responseStr;
                }
            });
            // 拦截住over，最后返回
            history = agentService.executePublishedAgent(response, dto);
            try {
                writeToResponse(response, null, "message", createHistoryIdJson(history));
                writeToResponse(response, null, "event", "");
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return history;
    }


    public AgentPublishHistoryDTO executePublishedAgentReflush(HttpServletResponse response,
                                                               String thirdUserToken,
                                                               Long historyId) throws AgentHandlerError {

        CallAgentDTO callAgentDTO = new CallAgentDTO();
        AgentPublishHistory history = agentPublishHistoryMapper.selectById(historyId);
        if (history == null) {
            throw new AgentHandlerError("历史记录不存在");
        }
        callAgentDTO.setAgentPublishId(history.getAgentPublishId());
        callAgentDTO.setConversationId(history.getConversationId());
        callAgentDTO.setUserInput(history.getUserInput());
        callAgentDTO.setAgentId("" + history.getAgentId());
        callAgentDTO.setThirdUserId(history.getThirdUserId());
        callAgentDTO.setThirdUserToken(thirdUserToken);
        callAgentDTO.setObjectId(history.getObjectId());

        if (StringUtils.isNotBlank(history.getFlowId())) {
            agentPublishHistoryMapper.delete(new LambdaQueryWrapper<AgentPublishHistory>().eq(AgentPublishHistory::getFlowId, history.getFlowId()));
        }
        // 重新调用
        AgentPublishHistoryDTO historyNew = executePublishedAgent(response, callAgentDTO);
        return historyNew;
    }

    public void clearPublishHistory(String conversationId, Long agentId, String userId) {
        LambdaQueryWrapper<AgentPublishHistory> query = new LambdaQueryWrapper<AgentPublishHistory>()
                .eq(AgentPublishHistory::getThirdUserId, userId)
                .eq(AgentPublishHistory::getAgentId, agentId);
        if (StringUtils.isNotBlank(conversationId)) {
            query = query.eq(AgentPublishHistory::getConversationId, conversationId);
        }
//                .eq(AgentPublishHistory::getClientId, StpClientUserUtil.getClientId());
        agentPublishHistoryMapper.delete(query);
    }

    public AgentPublishHistoryDTO queryPublishedAgentHistoryDetail(Long historyId) {
        AgentPublishHistory history = agentPublishHistoryMapper.selectById(historyId);
        if (history == null) {
            throw new ValidateException("历史记录不存在");
        }
        AgentPublishHistoryDTO agentPublishHistoryDTO = mapAgentPublishDetail(history);

        // 查询反馈id
        List<Long> feedbackIds = feedbackMapper.selectByHistoryId(agentPublishHistoryDTO.getId());
        agentPublishHistoryDTO.setFeedbackId(feedbackIds);
        return agentPublishHistoryDTO;
    }

    public AgentPublishHistoryDTO mapAgentPublishDetail(AgentPublishHistory history) {
        AgentPublishHistory historyReal = history;
        // 根据："answer": "{\"id\":\"9\"}",处理到实际到返回结果,不能上面转换是因为根据问题不同，处理的智能体也不同
        if (StringUtils.isNotBlank(history.getFlowId())) {
            // 需要流处理删除吧
            Long nextAgentId = history.getAgentId();
            while (nextAgentId != null) {
                if (globalAgentHandlerMap.containsKey(nextAgentId)) {
                    IHandlerContext context = new IHandlerContext();
                    context.setInput(historyReal.getUserInput());
                    context.setRawInput(historyReal.getRawInput());
                    context.setOutput(historyReal.getAnswer());
                    nextAgentId = globalAgentHandlerMap.get(nextAgentId).getNextAgentId(context);
                    //找到这个回答对应的id
                    if (nextAgentId != null) {
                        AgentPublishHistory tmpHistory = agentPublishHistoryMapper
                                .selectOne(new LambdaQueryWrapper<AgentPublishHistory>().eq(AgentPublishHistory::getFlowId,
                                        history.getFlowId()).eq(AgentPublishHistory::getAgentId, nextAgentId));
                        if (tmpHistory != null) {
                            historyReal = tmpHistory;
                        } else {
                            break;
                        }
                    }
                } else {
                    break;
                }
            }
//                if (historyReal.getAgentId().longValue() != history.getAgentId().longValue()) {
//                    log.info(String.format("改写历史查询内容 从%s，定位到 %s", history.getAgentId(), historyReal.getAgentId()));
//                }

            if (sqlAgentIds.contains("" + historyReal.getAgentId())) {
                //去掉sql
                if (!sqlPrint && !StringUtils.isBlank(historyReal.getExtraInfo())) {
                    historyReal.setAnswer("");
                }
            }
        }
        AgentPublishHistoryDTO agentPublishHistoryDTO = new AgentPublishHistoryDTO();
        BeanUtils.copyProperties(historyReal, agentPublishHistoryDTO);
        agentPublishHistoryDTO.setKnowledgeDocInfo(JSON.parseArray(historyReal.getKnowledgeDocInfo(), KnowledgeDocHistoryDTO.class));
        if (StringUtils.isNotBlank(history.getRawInput())) {
            agentPublishHistoryDTO.setUserInput(history.getRawInput());// 不展示改写后的用户输入
        } else {
            agentPublishHistoryDTO.setUserInput(history.getUserInput());// 不展示改写后的用户输入
        }
        if ("null".equals(agentPublishHistoryDTO.getReasoning())) {
            // 历史数据问题
            agentPublishHistoryDTO.setReasoning(null);
        }
        return agentPublishHistoryDTO;
    }

    public Page<AgentPublishHistoryDTO> queryAgentHistory(Page<AgentPublishHistory> page,
                                                          String conversationId, Long agentId,
                                                          String thirdUserId,
                                                          String objectId,
                                                          String keyword
    ) {

        LambdaQueryWrapper<AgentPublishHistory> query = new LambdaQueryWrapper<AgentPublishHistory>()
                .eq(AgentPublishHistory::getConversationId, conversationId)
                .eq(AgentPublishHistory::getUserId, StpClientUserUtil.getLoginIdAsLong());
        query.eq(AgentPublishHistory::getAgentId, agentId);
        query.eq(AgentPublishHistory::getThirdUserId, thirdUserId);
        String clientId = StpClientUserUtil.getClientId();
        if (StringUtils.isNotBlank(clientId)) {
            query.eq(AgentPublishHistory::getClientId, clientId);
        }
        if (StringUtils.isNotBlank(objectId)) {
            query.eq(AgentPublishHistory::getObjectId, objectId);
        }
        if (StringUtils.isNotBlank(keyword)) {
            query.like(AgentPublishHistory::getRawInput, keyword);
        }
        query.orderByDesc(AgentPublishHistory::getCreateTime);
        page = agentPublishHistoryMapper.selectPage(page, query);
        List<AgentPublishHistoryDTO> list = new ArrayList<>(page.getRecords().size());


        for (AgentPublishHistory history : page.getRecords()) {
            AgentPublishHistoryDTO agentPublishHistoryDTO = mapAgentPublishDetail(history);
            // 查询一下有没有历史记录 todo 还是优化一下吧
            List<Long> feedbackIds = feedbackMapper.selectByHistoryId(history.getId());
            agentPublishHistoryDTO.setFeedbackId(feedbackIds);

            list.add(agentPublishHistoryDTO);
        }
        Page<AgentPublishHistoryDTO> result = new Page<AgentPublishHistoryDTO>();
        result.setRecords(list);
        result.setSize(page.getSize());
        result.setTotal(page.getTotal());
        result.setCurrent(page.getCurrent());
        return result;
    }

    public List<Map<String, Object>> queryPublishedAgentHistoryGroup(String thirdUserId,
                                                                     Long agentId,
                                                                     String startDate,
                                                                     String endDate,
                                                                     String keyword,
                                                                     String objectId) {
        // 查询历史记录，group by conversationId，并且取出第一个作为名字
        //  根据创建时间排序
        List<Map<String, Object>> maps = agentPublishHistoryMapper
                .queryPublishedAgentHistoryGroup(thirdUserId
                        , StpClientUserUtil.getClientId(), agentId, startDate, endDate, keyword, objectId);
        maps.sort(new Comparator<Map<String, Object>>() {
            @Override
            public int compare(Map<String, Object> map1, Map<String, Object> map2) {
                // 获取两个 map 中的 createTime 字段的值
                String createTime1 = map1 == null ? "" : map1.getOrDefault("createTime", "").toString();
                String createTime2 = map2 == null ? "" : map2.getOrDefault("createTime", "").toString();
                // 调用 String 类的 compareTo 方法进行比较
                return createTime1.compareTo(createTime2);
            }
        });
        return maps;
    }


    /**
     * 记录这次回答为正确的
     * 1. 先通过id，获取到要记录的结果，sql工具这种其实只需要记录核心的sql节点，。。不过好像也就是最后一个，没啥问题。获取最后一个节点结果
     * 2. 根据agentId获取到 绑定的知识库类型，根据这个类型，进行解析支撑，把内容丢上去
     *
     * @param dto
     */
    public void sftLogKnowledgePositiveOrNegative(SftKnowledgeDto dto) {
        if (!Constants.KNOWLEDGE_BASE_TYPE.negative.name().equals(dto.getType())
                && Constants.KNOWLEDGE_BASE_TYPE.positive.name().equals(dto.getType())) {
            throw new ValidateException("标注类型错误");
        }
        if (Constants.KNOWLEDGE_BASE_TYPE.negative.name().equals(dto.getType()) && StringUtils.isBlank(dto.getType())) {
            throw new ValidateException("错误回答未标注原因");
        }
//        dto.getConversationId()
        // 先查询到最后一个节点
        // 需要流处理删除吧
        LambdaQueryWrapper<AgentPublishHistory> query = new LambdaQueryWrapper<AgentPublishHistory>()
                .eq(AgentPublishHistory::getConversationId, dto.getConversationId());
        query.eq(AgentPublishHistory::getAgentId, dto.getAgentId());
        query.eq(AgentPublishHistory::getThirdUserId, dto.getThirdUserId());
        String clientId = StpClientUserUtil.getClientId();
        if (StringUtils.isNotBlank(clientId)) {
            query.eq(AgentPublishHistory::getClientId, clientId);
        }
        query.eq(AgentPublishHistory::getFlowId, dto.getFlowId());
        AgentPublishHistory history = agentPublishHistoryMapper.selectOne(query);

        AgentPublishHistory historyReal = history;

        Long nextAgentId = dto.getAgentId();
        while (nextAgentId != null) {
            if (globalAgentHandlerMap.containsKey(nextAgentId)) {
                IHandlerContext context = new IHandlerContext();
                context.setInput(historyReal.getUserInput());
                context.setRawInput(historyReal.getRawInput());
                context.setOutput(historyReal.getAnswer());
                nextAgentId = globalAgentHandlerMap.get(nextAgentId).getNextAgentId(context);
                //找到这个回答对应的id
                if (nextAgentId != null) {
                    historyReal = agentPublishHistoryMapper
                            .selectOne(new LambdaQueryWrapper<AgentPublishHistory>().eq(AgentPublishHistory::getFlowId,
                                    history.getFlowId()).eq(AgentPublishHistory::getAgentId, nextAgentId));
                    nextAgentId = historyReal.getAgentId();
                }
            } else {
                break;
            }
        }
        // 实际的最后的id
        AgentSftDto agentSftDto = new AgentSftDto();
        agentSftDto.setAgentId(dto.getAgentId());
        agentSftDto.setContent(dto.getContent());
        agentSftDto.setType(dto.getType());
        agentSftDto.setSource(AgentFlowConstants.AGENT_FEEDBACK_SOURCE.user.name());

        //得到问题结果

        agentSftDto.setUserInput(historyReal.getUserInput());
        if (Constants.KNOWLEDGE_BASE_TYPE.negative.name().equals(dto.getType())) {
            if (StringUtils.isNotBlank(historyReal.getRawInput()) && !historyReal.getRawInput().equals(historyReal.getUserInput())) {
                agentSftDto.setUserInput(String.format("用户输入:%s,\n改写后输入: %s\n", historyReal.getRawInput(), historyReal.getUserInput()));
            } else {
                agentSftDto.setUserInput(historyReal.getUserInput());
            }
        } else {
            agentSftDto.setUserInput(historyReal.getUserInput());
        }

        agentSftDto.setRawInput(historyReal.getRawInput());
        agentSftDto.setAnswer(historyReal.getAnswer());
        agentSftDto.setThirdUserId(historyReal.getThirdUserId());
        agentSftDto.setHistoryId(historyReal.getId());

        agentSftService.openStfLogKnowledgePositive(agentSftDto);

    }

    public void updateHistoryAnswer(String output, Long id) {
        agentPublishHistoryMapper.updateAnswerById(output, id);
    }

    /**
     * 根据历史id，查询记录sql，然后查询
     *
     * @param thirdUserId
     * @param historyId
     * @return
     */
    public List queryMoreData(String thirdUserId, Long historyId, Integer index) {
        // 提示更多时才会查询，去掉limit限制即可
        // todo 权限判断
        AgentPublishHistory history = agentPublishHistoryMapper.selectById(historyId);
        if (!thirdUserId.equals(history.getThirdUserId())) {
            throw new ValidateException("无权查询历史");
        }
        String sql = history.getAnswer();
        try {
            sql = AiFormatJsonUtil.formatJson(sql);
            sql = AiFormatJsonUtil.formatSql(sql);
            if (sql == null) {
                log.info("query more null sql:" + history.getAnswer());
                throw new ValidateException("数据集为空");
            }

            String[] sqlArr = sql.split(";");
            if (sqlArr.length >= 2 && index >= 1 && index < sqlArr.length) {
                sql = sqlArr[index];
            } else {
                sql = sqlArr[0];
            }


            int maxlen = 500;
            if (sql.toLowerCase().replaceAll("\n", " ").contains(" limit ")) {
                // 替换
                int limitIndex = sql.toLowerCase().replaceAll("\n", " ").lastIndexOf(" limit ");
                sql = sql.substring(0, limitIndex) + " limit " + maxlen;
            }
            int selectIndex = sql.toLowerCase().indexOf("select ");
            if (selectIndex != -1) {
                sql = sql.substring(selectIndex);
            }

            log.info("more sql:" + sql);
            Output output = SqlQueryUtils.execSqlQuery(null, sql, maxlen);
            return output.getList();
        } catch (Exception e) {
            e.printStackTrace();
            throw new ValidateException("查询更多失败");
        }
    }

    public List<Map<String, Object>> queryDetailData(String thirdUserId,
                                                     Long historyId,
                                                     String xLabel,
                                                     String yLabel,
                                                     String value) {
        // 使用sql智能体，查询出具体的值，并生成echart，output表格
        // id为5的
        AgentPublishHistory history = agentPublishHistoryMapper.selectById(historyId);
        if (!thirdUserId.equals(history.getThirdUserId())) {
            throw new ValidateException("无权查询历史");
        }
        String sql = history.getAnswer();
        Long agentId = history.getAgentId();
        // 继续提问 ？ 先不考虑吧
        //todo
        return null;
    }

    public Output echartsFormatBySql(String userInput, String sql, Map<String, String> configs) throws Exception {
        IHandlerContext context = new IHandlerContext();
        context.setInput(userInput);
        context.setRawInput(userInput);
        context.setSqlConfigs(configs);
        context.setEnvType(envType);

        EduTableQuerySqlAgent currentHandler = new EduTableQuerySqlAgent();
//        if ("jiaoyu".equals(envType)) {
//            currentHandler.setFormatAgentId(24L);
//        } else {
//            currentHandler.setFormatAgentId(136L);
//        }
        currentHandler.setFormatAgentId(echartFormatAgentId);
        context.setCurrentHandler(currentHandler);
        context.setAgentFlowService(this);
        Output output = SqlQueryUtils.execSqlQuery(context, sql, 0);
        return output;
    }

    public void checkAndRunParallelAgents(IHandlerContext context, List<Thread> parallelThreads,
                                          List<Map<String, Object>> extraOutputs, IAgentHandler beforeHandler) {
        if (beforeHandler != null && beforeHandler.getParallelAgentId() != null && !beforeHandler.getParallelAgentId().isEmpty()) {
            //并行阻塞
            for (final Long parallelAgent : beforeHandler.getParallelAgentId()) {
                Runnable thinkRunnable = new Runnable() {
                    @Override
                    public void run() {
                        log.info("run parallel before agent id:" + parallelAgent);
                        AgentExecuteDTO parallelDto = new AgentExecuteDTO();
                        AgentPublish parallelAgentPublish = agentService.getAgentPublishByAgentId("" + parallelAgent);
                        parallelDto.setAgentPublishId(parallelAgentPublish.getId());
                        parallelDto.setAgentId(parallelAgent);
                        parallelDto.setConversationId(context.getSessionId());
                        parallelDto.setConstants(context.getConstants());
                        parallelDto.setThirdUserId(context.getThirdUserId());
                        parallelDto.setFlowId(context.getFlowId());
                        parallelDto.setObjectId(context.getObjectId());
                        parallelDto.setAgentRunType(AgentFlowConstants.AGENT_RUN_TYPE.parallel.name());

                        IAgentHandler afterHandler = globalAgentHandlerMap.getOrDefault(Long.parseLong(context.getAgentId()), null);
                        parallelDto.setResponseHandlerProxy(new IResponseHandlerProxy() {
                            @Override
                            public String handler(String responseStr) {
                                if (responseStr.startsWith("event:done")) {
                                    // 忽略流处理的
                                    return null;
                                }
                                return responseStr;
                            }
                        });
                        parallelDto.setUserInput(context.getInput());
                        AgentPublishHistoryDTO historyDTO = null;
                        try {
                            historyDTO = agentService.executePublishedAgent(parallelDto);
                        } catch (Exception e) {
                            log.error("run parallel error msg:{}",e.getMessage());
                            e.printStackTrace();
                        }
                        if (historyDTO != null && afterHandler != null) {
                            String output = afterHandler.formatJson(historyDTO.getAnswer());
                            // 处理到实际历史里吧
                            if (StringUtils.isNotBlank(output)) {
                                log.info("run parallel answer :{}",output);
                                HashMap<String, Object> objectHashMap = new HashMap<>();
                                objectHashMap.put("content", output);
                                objectHashMap.put("runtime", historyDTO.getRuntime());
                                extraOutputs.add(objectHashMap);
                            }else{
                                log.error("run parallel null answer error msg,id:{}",parallelAgent);
                            }
                        }
                    }
                };
                Thread parallelThread = new Thread(thinkRunnable);
                parallelThread.setDaemon(true);
                parallelThread.start();
                parallelThreads.add(parallelThread);
            }
        }


    }

    private void waitParallelAgentsResult(HttpServletResponse response, IHandlerContext context, AgentPublishHistoryDTO historyDto,
                                          List<Thread> parallelThreads, List<Map<String, Object>> extraOutputs) throws IOException {
        if (!parallelThreads.isEmpty()) {
            for (Thread parallelThread : parallelThreads) {
                try {
                    parallelThread.join();
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }

        if (!extraOutputs.isEmpty()) {
            for (Map<String, Object> extraOutput : extraOutputs) {
                writeToResponse(response, null, "message", createContentJson("json", extraOutput));
            }
        }
        // 补充其他节点，这里写入。因为不需要输出
        writeExtraInfo(context, historyDto.getId(), historyDto.getAgentId(), extraOutputs, historyDto.getRuntime());
    }

    protected void writeToResponse(HttpServletResponse response, IResponseHandlerProxy responseHandlerProxy,
                                   String event, Object data) throws IOException {
        String s = "event:" + event + "\ndata:" + data + "\n\n";
        if (responseHandlerProxy != null) {
            s = responseHandlerProxy.handler(s);
        }
        if (s != null) {
            response.getWriter().write(s);
            response.getWriter().flush();
        }
    }

    protected String createMessageJson(AgentPublishHistoryDTO historyDTO, Object content) {
        JSONObject contentObj = new JSONObject();
        contentObj.put("type", "json");
        contentObj.put("content", content);
        contentObj.put("runtime", historyDTO == null ? null : historyDTO.getRuntime());

        JSONObject messageObj = new JSONObject();
        messageObj.put("message", contentObj);
        return messageObj.toJSONString();
    }

    protected String createContentJson(String type, Map<String, Object> extraOutput) {
//        String content = extraOutput.getOrDefault("content", "").toString().replace("\n", "\\n");//.replace("\"", "\\\"");
//                response.getWriter().write("event:message\ndata:{\"message\":{\"type\":\"json\",\"content\":" + extraOutput + "}}\n\n");
//                response.getWriter().flush();

        JSONObject contentObj = new JSONObject();
        contentObj.put("type", type);
//        contentObj.put("content", extraOutput.getOrDefault("content", "").toString()
//                .replace("\\\\n", "\n").replace("\n", "\\n"));
        contentObj.put("content", extraOutput.getOrDefault("content", "").toString());
        contentObj.put("runtime", extraOutput.get("runtime"));
//        return contentObj.toJSONString();

        JSONObject messageObj = new JSONObject();
        messageObj.put("message", contentObj);
        return messageObj.toJSONString();
    }

    protected String createHistoryIdJson(AgentPublishHistoryDTO historyDTO) {
        JSONObject contentObj = new JSONObject();
        contentObj.put("type", "historyId");
        contentObj.put("agentId", historyDTO == null ? null : historyDTO.getAgentId());
        contentObj.put("content", historyDTO == null ? null : historyDTO.getId());
        contentObj.put("runtime", historyDTO == null ? null : historyDTO.getRuntime());

        JSONObject messageObj = new JSONObject();
        messageObj.put("message", contentObj);
        return messageObj.toJSONString();
    }

    protected String createErrorJson(String errormsg, AgentPublishHistoryDTO historyDTO) {
        JSONObject contentObj = new JSONObject();
        contentObj.put("type", "content");
        contentObj.put("content", errormsg);
        contentObj.put("runtime", historyDTO == null ? null : historyDTO.getRuntime());
        return contentObj.toJSONString();
    }

    private Thread startThinkThread(IHandlerContext context, String userInput, AgentPublishHistoryDTO[] thinkHistoryDto) {
        final String tmpAnswer = userInput;
        Runnable thinkRunnable = new Runnable() {
            @Override
            public void run() {
                AgentExecuteDTO thinkDto = new AgentExecuteDTO();
                AgentPublish thinkPublishDto = agentService.getAgentPublishByAgentId("" + context.getThinkAgentId());
                thinkDto.setAgentPublishId(thinkPublishDto.getId());
                thinkDto.setAgentId(context.getThinkAgentId());
                thinkDto.setConversationId(context.getSessionId());
                thinkDto.setConstants(context.getConstants());
                thinkDto.setEnableCurrentDate(false);
                thinkDto.setThirdUserId(context.getThirdUserId());
                thinkDto.setFlowId(context.getFlowId());
                thinkDto.setObjectId(context.getObjectId());
                thinkDto.setAgentRunType(AgentFlowConstants.AGENT_RUN_TYPE.think.name());

                IAgentHandler orDefault = globalAgentHandlerMap.getOrDefault(Long.parseLong(context.getAgentId()), null);
                if (orDefault != null) {
                    thinkDto.setResponseHandlerProxy(orDefault.getResponseHandlerProxy(
                            IAgentHandler.RESPONSE_TYPE.think, context));
                }

//                                    thinkDto.setUserInput(afterHandlerNode.getThinkAgentInput(context, tmpAnswer));
                thinkDto.setUserInput(tmpAnswer);
                log.info("exec think agent input:" + tmpAnswer);
                try {
                    thinkHistoryDto[0] = agentService.executePublishedAgent(context.getResponse(), thinkDto);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        };
        Thread thinkThread = new Thread(thinkRunnable);
        thinkThread.setDaemon(true);
        thinkThread.start();
//                                executorService.execute(thinkRunnable);
        return thinkThread;
    }

    private void updateAgentsRunCompleteByFlowId(IHandlerContext context, AgentPublishHistoryDTO historyDTO) {
        // 将运行成功的修改为完成的
        agentPublishHistoryMapper.updateAgentsRunCompleteByFlowId(context.getFlowId(), AgentFlowConstants.AGENT_RUN_STATUS.complete.name());
        if (historyDTO != null && StringUtils.isNotBlank(historyDTO.getError())) {
            agentPublishHistoryMapper.updateAgentStatusById(historyDTO.getId(), AgentFlowConstants.AGENT_RUN_STATUS.failed.name(),
                    historyDTO.getError());
            updateAgentRunNodeEndType(historyDTO.getId());
        }
    }

    private int writeExtraInfo(IHandlerContext context, Long historyId, Long agentId, List<Map<String, Object>> extraOutputs, Long runtime) {
        //如果是并行
        // 判断是否需要写入的，这个就是最后节点了因为
        int writenum = 0;
        if (context.getCacheExtraInfoByAgentId().containsKey(agentId)) {
            List<AgentFlowConstants.CacheExtraInfo> cacheExtraInfos = context.getCacheExtraInfoByAgentId().get(agentId);
            if (!cacheExtraInfos.isEmpty()) {
                if (context.getCacheExtraInfoByAgentId().containsKey(agentId)) {
                    for (AgentFlowConstants.CacheExtraInfo cacheExtraInfo : context.getCacheExtraInfoByAgentId().get(agentId)) {
                        if (!cacheExtraInfo.isWrite()) {
                            cacheExtraInfo.setWrite(true);
                            extraOutputs.add(createExtraOuput(cacheExtraInfo.getProtocol(), cacheExtraInfo.getValue(), runtime));
                            writenum += 1;
                        }
                    }
                }
            }
        }
        return writenum;
    }

    private final AgentPublishHistoryDTO runLastCustomerAgent(IHandlerContext context, AgentExecuteDTO dto,
                                                              List<Thread> parallelThreads, List<Map<String, Object>> extraOutputs)
            throws AgentRunBreakError {
        HttpServletResponse response = context.getResponse();
        AgentPublishHistoryDTO history = null;
        // 中间节点不支持流
        if (response == null) {
            history = agentService.executePublishedAgent(dto);
            int writenum = writeExtraInfo(context, history.getId(), history.getAgentId(), extraOutputs, history.getRuntime());
            if (!extraOutputs.isEmpty()) {
                extraOutputs.add(createExtraOuput("", history.getAnswer(), history));
                agentPublishHistoryMapper.updateExtraInfoById(JSON.toJSONString(extraOutputs), history.getId());
            }
        } else {
            // 这边也有代理，处理一下
            dto.setResponseHandlerProxy(new IResponseHandlerProxy() {
                @Override
                public String handler(String responseStr) {
                    if (responseStr.startsWith("event:done")) {
                        // 忽略流处理的
                        return null;
                    }
                    return responseStr;
                }
            });


            checkAndRunParallelAgents(context, parallelThreads, extraOutputs, context.getCurrentHandler());

            // 拦截住over，最后返回
            // 客服的，开启文档引用
            if (context.getCurrentHandler() != null && context.getCurrentHandler().isEnableCitation()) {
                dto.setEnableCitation(true);
            }


            history = agentService.executePublishedAgent(response, dto);
            try {
                writeToResponse(response, null, "message", createHistoryIdJson(history));
                // 等待内容都返回了
                waitParallelAgentsResult(response, context, history, parallelThreads, extraOutputs);
                // 写入主回答
                if (history != null && StringUtils.isNotBlank(history.getAnswer())) {
                    JSONObject contentJson = new JSONObject();
                    contentJson.put("type", "content");
                    contentJson.put("runtime", history.getRuntime());
                    contentJson.put("content", history.getAnswer());
                    extraOutputs.add(contentJson);
                }
                writeToResponse(response, null, "done", "");
            } catch (Exception e) {
                e.printStackTrace();
                throw new AgentRunBreakError(e.getMessage());
            }
            if (history != null && !extraOutputs.isEmpty()) {
                // 说明 有多个,有并行任务，history已经写进去了，这里就不用了
                agentPublishHistoryMapper.updateExtraInfoById(JSON.toJSONString(extraOutputs), history.getId());
            }
        }
        return history;
    }

    private String runSqlAgentBuildChartJsonFlow(IHandlerContext context, AgentPublishHistoryDTO history,
                                                 List<Thread> parallelThreads) throws IOException {
        //需要对结果进行处理
        String anserwer = history.getAnswer();
        anserwer = AiFormatJsonUtil.formatSql(anserwer);

        List<Output> resultsList = new ArrayList<>();
        try {
            String[] sqlArr = anserwer.split(";");
            log.info("exec sql query:" + anserwer);
            if (sqlArr.length >= 2) {
                log.info("exec sql query real len:" + sqlArr.length);
            }
            // 可能多个sql
            Output results = SqlQueryUtils.execSqlQuery(context, sqlArr[0], 20);
            resultsList.add(results);
            if (sqlArr.length >= 2) {
                try {
                    Output results_2 = SqlQueryUtils.execSqlQuery(context, sqlArr[1], 20);
                    resultsList.add(results_2);
                    // 第二个sql结果集
                } catch (Exception e) {
                    e.printStackTrace();
                    if (e instanceof AgentSqlQueryExecException) {
                        history.setError(((AgentSqlQueryException) e).getErrorMsg());
                    } else {
                        history.setError(e.getMessage());
                    }
                    log.error("runSqlAgentBuildChartJsonFlow:{}", e.getMessage());
                }
            }

        } catch (Exception e) {
            log.error("runSqlAgentBuildChartJsonFlow error:{}", e.getMessage());
            if (e instanceof AgentSqlQueryException) {
                history.setError(((AgentSqlQueryException) e).getErrorMsg());
                if (e instanceof AgentSqlQueryExecException) {
                    // 具体执行的错误
                    // 可以输出具体错误

                }
            } else {
                e.printStackTrace();
                history.setError(e.getMessage());
            }


        }
        if (!resultsList.isEmpty()) {
            // 正常查询到结果集了
        } else {
            log.error("runSqlAgentBuildChartJsonFlow result empty,answer:{}", history.getAnswer());
            Output results = new Output();
            resultsList.add(results);
            results.setLog(history.getError());
            results.setAnswer(history.getAnswer());
            if (StringUtils.isNotBlank(history.getError())) {
                // 需要返回友好信息
                if (context.getResponse() != null) {
                    context.initResponse();
                    context.getResponse().getWriter().write("event:message\ndata:{\"message\":{\"type\":\"content\",\"content\":\"" + context.getCurrentHandler().getErrorMsg(context) + "\"}}\n\n");
                    context.getResponse().getWriter().flush();
                } else {
                    log.error("error:" + history.getError());
                }

                //todo 需要记录一下日志，判断这个智能体，是否有负样本库，有的话，可以直接记录一下错误情况
                AgentSftDto sftDto = new AgentSftDto();
                sftDto.setSource(AgentFlowConstants.AGENT_FEEDBACK_SOURCE.sys.name());
                try {
                    sftDto.setContent(history.getError());
                    sftDto.setUserInput(context.getInput());
                    sftDto.setRawInput(context.getRawInput());
                    sftDto.setAnswer(anserwer);
                    sftDto.setAgentId(Long.parseLong(context.getAgentId()));
                    sftDto.setThirdUserId(context.getThirdUserId());
                    sftDto.setHistoryId(history.getId());
                    agentSftService.sftLogKnowledgeNegative(sftDto);
                } catch (Exception e) {
                    System.out.println("sft eror:" + e.getMessage());
                }
            }
        }
//                                results.setConstants(context.getConstants());
        String finalAnserwer = anserwer;
        resultsList.forEach(entity -> {
            entity.setSql(finalAnserwer);
        });
        // 关闭sql输出,记录，但是输出时候不需要
//                                String rawAnserwer = JSON.toJSONString(results);
        if (!sqlPrint) {
            resultsList.forEach(entity -> {
                entity.clearSqlOutput();
            });
        }

        anserwer = JSON.toJSONString(resultsList.size() >= 2 ? resultsList : resultsList.getFirst());
        //默认一个结果集，如果涉及多个的话，就返回数组了

//                                history.setAnswer(anserwer);
        if (context.getResponse() == null) {
            history.setExtraInfo(anserwer);
        }
        // 需要做更新，20250328 不做更新，不然反馈那边就不好做了
        if (!parallelThreads.isEmpty()) {
            // 后续会更新
        } else {
            history.setExtraInfo(anserwer);
            agentPublishHistoryMapper.updateExtraInfoById(anserwer, history.getId());
        }
        if (StringUtils.isNotBlank(history.getError())) {
            updateAgentsRunCompleteByFlowId(context, history);
        }
        return anserwer;
    }

    public Output echartsFormat(EchartFormatDTO dto) {
        if (StringUtils.isBlank(dto.getType())) {
            throw new ValidateException("图表类型未指定");
        }
        List<AgentFlowHistoryDTO> agentFlowHistoryDTOS = agentHistoryService
                .queryFlowAgentHistoryDetailList(dto.getHistoryId());
        // 提取出最后一个sql节点
        AgentFlowHistoryDTO sqlNode = null;
        for (int i = agentFlowHistoryDTOS.size() - 1; i >= 0; i--) {
            if (sqlAgentIds.contains(agentFlowHistoryDTOS.get(i).getAgentId().toString())) {
                sqlNode = agentFlowHistoryDTOS.get(i);
                break;
            }
        }
        if (sqlNode == null) {
            throw new ValidateException("查询的数据不存在");
        }
        // 得到结果集合，根据结果集生成新的模板
        String typeChart = null;
        switch (dto.getType()) {
            case "pie":
                typeChart = AgentFlowConstants.EchartsType.pie.getDesc()[0];
                break;
            case "line":
                typeChart = AgentFlowConstants.EchartsType.line.getDesc()[0];
                break;
            case "bar":
                typeChart = AgentFlowConstants.EchartsType.bar.getDesc()[0];
                break;
            case "table":
                typeChart = AgentFlowConstants.EchartsType.table.getDesc()[0];
                break;
            default:
                throw new ValidateException("图表类型错误");
        }

        Map<String, String> configs = dto.getConfigs();
        if (configs == null || configs.isEmpty()) {
            for (long agentIdKey : globalAgentHandlerMap.keySet()) {
                if (sqlNode.getAgentId() == agentIdKey) {
                    IHandlerContext context = new IHandlerContext();
                    try {
                        globalAgentHandlerMap.get(agentIdKey).beforeHandle(context);
                    } catch (AgentHandlerError e) {
                        throw new ValidateException(e.getMessage());
                    }
                    configs = context.getSqlConfigs();
                    break;
                }
            }
        }

        try {
            String rawInput = sqlNode.getRawInput();
            // 去掉图形的描述
            for (AgentFlowConstants.EchartsType value : AgentFlowConstants.EchartsType.values()) {
                String[] desc = value.getDesc();
                for (String typeStr : desc) {
                    if (rawInput.contains(typeStr)) {
                        rawInput = rawInput.replace(typeStr, typeChart);
                    }
                }
            }
            Output output = echartsFormatBySql(String.format("用户问题：%s，最终用%s展示数据", rawInput, typeChart), sqlNode.getAnswer(), configs);
            return output;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ValidateException(e.getMessage());
        }
    }


}
