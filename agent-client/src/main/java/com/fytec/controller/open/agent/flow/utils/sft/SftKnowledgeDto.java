package com.fytec.controller.open.agent.flow.utils.sft;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class SftKnowledgeDto {

    @Schema(description = "需要做好问题改写，得有上下文，才比较合适")
    @NotBlank(message = "agentId不能为空")
    private Long agentId;

    @Schema(description = "第三方用户ID")
    @NotBlank(message = "第三方用户id不能为空")
    private String thirdUserId;

    @Schema(description = "会话ID")
    @NotBlank(message = "会话ID不能为空")
    private String conversationId;
    private String thirdUserToken;

    @Schema(description = "flowId")
    @NotBlank(message = "flowId不能为空")
    private String flowId;

    @Schema(description = "备注信息，标注为什么是正确还是错误的,可选，如果是错误的，必须选择")
    private String content;

    @Schema(description = "negative，positive")
    private String type;//知识库类型,KNOWLEDGE_BASE_TYPE
    //    common("默认知识库"),
//    positive("标注正确的知识库"),
//    rule("规则知识库"),
//    negative("错误知识库");
    @Schema(hidden = true)
    private String userInput;
    @Schema(hidden = true)
    private String answer;
}
