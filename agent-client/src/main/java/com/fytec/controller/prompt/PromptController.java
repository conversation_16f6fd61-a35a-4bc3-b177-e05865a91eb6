package com.fytec.controller.prompt;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.satoken.StpClientUserUtil;
import com.fytec.dto.prompt.*;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.prompt.PromptMgmtService;
import com.fytec.service.prompt.PromptService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.util.List;

@Slf4j
@Validated
@Tag(name = "提示词用户")
@RequestMapping("/api/prompt")
@RestController
@RequiredArgsConstructor
public class PromptController {
    private final PromptMgmtService promptMgmtService;
    private final PromptService promptService;

    //#################################提示词推荐################################
    @GetMapping(value = "/recommended/category")
    @Operation(summary = "分类列表")
    @SaCheckPermission4FytecClient(value = "prompt:recommended:category", orRole = "admin")
    public R<List<PromptCategoryDTO>> queryPromptCategory(PromptCategoryQueryDTO dto) {
        return R.ok(promptMgmtService.queryPromptCategory(dto));
    }

    @GetMapping(value = "/recommended/page")
    @Operation(summary = "推荐提示词列表")
    @SaCheckPermission4FytecClient(value = "prompt:recommended:page", orRole = "admin")
    public R<Page<PromptContentListDTO>> queryRecommendedPromptContent(Page<PromptContentListDTO> page, PromptContentQueryDTO dto) {
        return R.ok(promptMgmtService.queryPromptContent(page, dto));
    }

    @GetMapping(value = "/recommended/detail")
    @Operation(summary = "推荐提示词详情")
    @SaCheckPermission4FytecClient(value = "prompt:recommended:detail", orRole = "admin")
    public R<PromptContentDetailDTO> getRecommendedPromptContentDetail(Long id) {
        return R.ok(promptMgmtService.getPromptContentDetail(id));
    }


    //#################################我的提示词################################
    @PostMapping(value = "/content/auto")
    @Operation(summary = "优化提示词")
    @SaCheckPermission4FytecClient(value = "prompt:content:auto", orRole = "admin")
    public void autoPromptContent(HttpServletResponse response, @Validated @RequestBody PromptContentAutoDTO dto) {
        promptService.autoPromptContent(response, dto);
    }


    @PostMapping(value = "/content/add")
    @Operation(summary = "添加提示词")
    @SaCheckPermission4FytecClient(value = "prompt:content:add", orRole = "admin")
    public R<Void> addPromptContent(@Validated @RequestBody PromptContentCreateDTO dto) {
        promptService.addPromptContent(dto);
        return R.ok();
    }

    @PostMapping(value = "/content/edit")
    @Operation(summary = "编辑提示词")
    @SaCheckPermission4FytecClient(value = "prompt:content:edit", orRole = "admin")
    public R<Void> editPromptContent(@Validated @RequestBody PromptContentUpdateDTO dto) {
        promptService.editPromptContent(dto);
        return R.ok();
    }

    @GetMapping(value = "/content/delete")
    @Operation(summary = "删除提示词")
    @SaCheckPermission4FytecClient(value = "prompt:content:delete", orRole = "admin")
    public R<Void> deletePromptContent(Long id) {
        promptService.deletePromptContent(id);
        return R.ok();
    }

    @GetMapping(value = "/content/detail")
    @Operation(summary = "提示词详情")
    @SaCheckPermission4FytecClient(value = "prompt:content:detail", orRole = "admin")
    public R<PromptContentDetailDTO> getPromptContentDetail(Long id) {
        return R.ok(promptService.getPromptContentDetail(id));
    }

    @GetMapping(value = "/content/page")
    @Operation(summary = "提示词列表")
    @SaCheckPermission4FytecClient(value = "prompt:content:page", orRole = "admin")
    public R<Page<PromptContentListDTO>> queryPromptContent(Page<PromptContentListDTO> page, PromptContentQueryDTO dto) {
        dto.setCreateBy(StpClientUserUtil.getLoginIdAsLong());
        return R.ok(promptService.queryPromptContent(page, dto));
    }
}
