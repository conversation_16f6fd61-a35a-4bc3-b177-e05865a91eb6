package com.fytec.controller.prompt;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.prompt.*;
import com.fytec.entity.prompt.PromptCategory;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.prompt.PromptMgmtService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Validated
@Tag(name = "提示词管理")
@RequestMapping("/api/prompt-mgmt")
@RestController
@RequiredArgsConstructor
public class PromptMgmtController {
    private final PromptMgmtService promptMgmtService;

    //#################################提示词分类################################
    @PostMapping(value = "/category/add")
    @Operation(summary = "添加分类")
    @SaCheckPermission4FytecClient(value = "prompt-mgmt:category:add", orRole = "admin")
    public R<Void> addPromptCategory(@Validated @RequestBody PromptCategoryCreateDTO dto) {
        promptMgmtService.addPromptCategory(dto);
        return R.ok();
    }

    @PostMapping(value = "/category/edit")
    @Operation(summary = "编辑分类")
    @SaCheckPermission4FytecClient(value = "prompt-mgmt:category:edit", orRole = "admin")
    public R<Void> editPromptCategory(@Validated @RequestBody PromptCategoryUpdateDTO dto) {
        promptMgmtService.editPromptCategory(dto);
        return R.ok();
    }

    @GetMapping(value = "/category/delete")
    @Operation(summary = "删除分类")
    @SaCheckPermission4FytecClient(value = "prompt-mgmt:category:delete", orRole = "admin")
    public R<Void> deletePromptCategory(Long id) {
        promptMgmtService.deletePromptCategory(id);
        return R.ok();
    }

    @GetMapping(value = "/category/detail")
    @Operation(summary = "分类详情")
    @SaCheckPermission4FytecClient(value = "prompt-mgmt:category:detail", orRole = "admin")
    public R<PromptCategory> getPromptCategoryDetail(Long id) {
        return R.ok(promptMgmtService.getPromptCategoryDetail(id));
    }

    @GetMapping(value = "/category/list")
    @Operation(summary = "分类列表")
    @SaCheckPermission4FytecClient(value = "prompt-mgmt:category:list", orRole = "admin")
    public R<List<PromptCategoryDTO>> queryPromptCategory(PromptCategoryQueryDTO dto) {
        return R.ok(promptMgmtService.queryPromptCategory(dto));
    }

    //#################################提示词内容################################

    @PostMapping(value = "/content/add")
    @Operation(summary = "添加提示词")
    @SaCheckPermission4FytecClient(value = "prompt-mgmt:content:add", orRole = "admin")
    public R<Void> addPromptContent(@Validated @RequestBody PromptContentCreateDTO dto) {
        promptMgmtService.addPromptContent(dto);
        return R.ok();
    }

    @PostMapping(value = "/content/edit")
    @Operation(summary = "编辑提示词")
    @SaCheckPermission4FytecClient(value = "prompt-mgmt:content:edit", orRole = "admin")
    public R<Void> editPromptContent(@Validated @RequestBody PromptContentUpdateDTO dto) {
        promptMgmtService.editPromptContent(dto);
        return R.ok();
    }

    @GetMapping(value = "/content/delete")
    @Operation(summary = "删除提示词")
    @SaCheckPermission4FytecClient(value = "prompt-mgmt:content:delete", orRole = "admin")
    public R<Void> deletePromptContent(Long id) {
        promptMgmtService.deletePromptContent(id);
        return R.ok();
    }

    @GetMapping(value = "/content/detail")
    @Operation(summary = "提示词详情")
    @SaCheckPermission4FytecClient(value = "prompt-mgmt:content:detail", orRole = "admin")
    public R<PromptContentDetailDTO> getPromptContentDetail(Long id) {
        return R.ok(promptMgmtService.getPromptContentDetail(id));
    }

    @GetMapping(value = "/content/page")
    @Operation(summary = "提示词列表")
    @SaCheckPermission4FytecClient(value = "prompt-mgmt:content:page", orRole = "admin")
    public R<Page<PromptContentListDTO>> queryPromptContent(Page<PromptContentListDTO> page, PromptContentQueryDTO dto) {
        return R.ok(promptMgmtService.queryPromptContent(page, dto));
    }



    @GetMapping(value = "/content/out-of-category")
    @Operation(summary = "不在当前分类下提示词列表")
    @SaCheckPermission4FytecClient(value = "prompt-mgmt:content:page", orRole = "admin")
    public R<List<PromptContentListDTO>> queryOutOfCategoryPromptContent(PromptContentQueryDTO dto) {
        return R.ok(promptMgmtService.queryOutOfCategoryPromptContent(dto));
    }

    //#################################提示词内容分类关联################################

    @PostMapping(value = "/relation/save")
    @Operation(summary = "提示词分类内容关联")
    @SaCheckPermission4FytecClient(value = "prompt-mgmt:relation:save", orRole = "admin")
    public R<Void> savePromptContentCategory(@Validated @RequestBody PromptContentCategoryDTO dto) {
        promptMgmtService.savePromptContentCategory(dto);
        return R.ok();
    }
}
