package com.fytec.controller.wechat;

import cn.dev33.satoken.stp.SaTokenInfo;
import com.fytec.dto.wechat.WechatWorkUser;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.LoginService;
import com.fytec.service.wechat.WechatWorkService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpSession;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;

@Slf4j
@Validated
@Tag(name = "企业微信登录")
@RequestMapping("/api/wechat-work")
@RestController
@RequiredArgsConstructor
public class WechatWorkController {

    private final LoginService loginService;
    private final WechatWorkService wechatWorkService;


    @GetMapping("/qrcode")
    public String wechatWorkLogin() {
        // 生成随机state用于CSRF防护
        String state = UUID.randomUUID().toString();
        // 重定向到企业微信授权页面
        return wechatWorkService.getAuthorizationUrl(state);
    }


    @GetMapping("/login")
    public R<SaTokenInfo> wechatWorkCallback(@RequestParam String code) {
        // 获取用户信息
        WechatWorkUser user = wechatWorkService.getUserInfoByCode(code);
        log.info("用户信息: {}", user);
        if (user == null) {
            throw new RuntimeException("获取用户信息失败");
        }
        return R.ok(loginService.generateWechatWorkLoginAccessToken(user));
    }


    @GetMapping("/department/list")
    @SaCheckPermission4FytecClient(value = "wechat:work:departments", orRole = "admin")
    public R<?> queryWechatWorkDepartments() {
        return R.ok(wechatWorkService.queryWechatWorkDepartments());
    }


    @GetMapping("/department/user/list")
    @SaCheckPermission4FytecClient(value = "wechat:work:users", orRole = "admin")
    public R<?> queryWechatWorkDepartmentUsers(String departmentId) {
        return R.ok(wechatWorkService.queryWechatWorkDepartmentUsers(departmentId));
    }

}
