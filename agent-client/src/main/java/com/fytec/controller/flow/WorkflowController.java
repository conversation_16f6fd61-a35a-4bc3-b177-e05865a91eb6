package com.fytec.controller.flow;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.flow.*;
import com.fytec.satoken.StpClientUserUtil;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.flow.WorkflowService;
import com.fytec.util.R;
import com.fytec.util.UUIDGenerator;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.ClientServiceProvider;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Validated
@Tag(name = "工作流")
@RequestMapping("/api/workflow")
@RestController
@AllArgsConstructor
public class WorkflowController {
    private final WorkflowService workflowService;


    @GetMapping(value = "/list")
    @Operation(summary = "查询工作流")
    @SaCheckPermission4FytecClient(value = "workflow:detail", orRole = "admin")
    public R<Page<WorkflowDTO>> queryWorkflow(QueryWorkflowDTO dto, Page<WorkflowDTO> page) {
        dto.setCreateBy(StpClientUserUtil.getLoginIdAsLong());
        return R.ok(workflowService.queryWorkflow(dto, page));
    }

    @GetMapping(value = "/develop/detail")
    @Operation(summary = "查看开发工作流")
    @SaCheckPermission4FytecClient(value = "workflow:detail", orRole = "admin")
    public R<WorkflowDevelopDetailDTO> getDetail(Long resourceId) {
        return R.ok(workflowService.getDetail(resourceId));
    }

    @PostMapping(value = "/develop")
    @Operation(summary = "开发工作流")
    @SaCheckPermission4FytecClient(value = "workflow:develop", orRole = "admin")
    public R<LocalDateTime> developWorkflow(@Validated @RequestBody DevelopWorkflowDTO dto) {
        return R.ok(workflowService.developWorkflow(dto));
    }

    @PostMapping(value = "/develop/publish")
    @Operation(summary = "发布工作流")
    @SaCheckPermission4FytecClient(value = "workflow:publish", orRole = "admin")
    public R<Void> publishDevelop(@Validated @RequestBody PublishWorkflowDTO dto) {
        workflowService.publishDevelop(dto);
        return R.ok();
    }

    @PostMapping(value = "/develop/debug")
    @Operation(summary = "调试工作流")
    @SaCheckPermission4FytecClient(value = "workflow:debug", orRole = "admin")
    public R<String> debugDevelopWorkflow(@Validated @RequestBody WorkflowExecuteDTO dto) {
        dto.setDebug(true);
        return R.ok(workflowService.executeWorkflow(dto));
    }

    @GetMapping(value = "/develop/execute/history")
    @Operation(summary = "轮询工作流执行记录")
    @SaCheckPermission4FytecClient(value = "workflow:execute", orRole = "admin")
    public R<JSONArray> queryDevelopWorkflowHistory(String threadId) {
        return R.ok(workflowService.queryDevelopWorkflowHistory(threadId));
    }

    @PostMapping(value = "/publish/execute")
    @Operation(summary = "执行发布工作流")
    @SaCheckPermission4FytecClient(value = "workflow:execute", orRole = "admin")
    public R<String> executePublishedWorkflow(@Validated @RequestBody WorkflowExecuteDTO dto) {
        return R.ok(workflowService.executeWorkflow(dto));
    }

    @SneakyThrows
    @PostMapping(value = "/publish/chat/execute")
    @Operation(summary = "执行发布对话流")
    @SaCheckPermission4FytecClient(value = "workflow:execute", orRole = "admin")
    public SseEmitter executePublishedChatFlow(@Validated @RequestBody WorkflowExecuteDTO dto) {
        String runId = UUIDGenerator.getUUID();
        dto.setRunId(runId);
        workflowService.executeChatFlow(dto);

        String clientId = IdUtil.nanoId();
        final ClientServiceProvider provider = ClientServiceProvider.loadService();
        final SseEmitter sseEmitter = workflowService.getConn(clientId);
        CompletableFuture.runAsync(() -> workflowService.listenExecuteChatFlow(provider, clientId, runId));
        return sseEmitter;
    }
}
