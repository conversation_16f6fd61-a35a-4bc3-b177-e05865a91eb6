package com.fytec.controller.flow;

import cn.hutool.core.exceptions.ValidateException;
import com.alibaba.druid.util.StringUtils;
import com.fytec.dto.flow.WorkflowExecuteDTO;
import com.fytec.service.flow.WorkflowService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

import static cn.dev33.satoken.sign.SaSignUtil.checkSign;

@Slf4j
@Validated
@Tag(name = "工作流-function-call")
@RequestMapping("/api/workflow/function")
@RestController
@RequiredArgsConstructor
public class WorkflowFunctionCallController {

    private final WorkflowService workflowService;

    @PostMapping(value = "/call")
    @Operation(summary = "执行工作流")
    public R<String> executePublishedWorkflow(@Validated @RequestBody WorkflowExecuteDTO dto, HttpServletRequest request) {
        String sign = request.getHeader("sign");
        if (StringUtils.isEmpty(sign)) {
            throw new ValidateException("签名不能为空");
        }
        dto.setDebug(false);
        Map<String, Object> params = new HashMap<>();
        params.put("resourceId", dto.getResourceId());
        params.put("params", dto.getParams());
        params.put("id", dto.getId());
        checkSign(params, sign);
        return R.ok(workflowService.executeWorkflow(dto));
    }
}
