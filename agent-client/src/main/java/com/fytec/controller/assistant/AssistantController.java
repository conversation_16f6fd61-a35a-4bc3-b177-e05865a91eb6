package com.fytec.controller.assistant;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.agent.AgentBasicDTO;
import com.fytec.dto.agent.AgentQueryDTO;
import com.fytec.dto.assistant.AssistantDTO;
import com.fytec.dto.assistant.AssistantQueryDTO;
import com.fytec.satoken.StpClientUserUtil;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.assistant.AssistantService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@Validated
@Tag(name = "智能体助理管理")
@RequestMapping("/api/assistant")
@RestController
@AllArgsConstructor
public class AssistantController {

    private final AssistantService assistantService;

    @PostMapping(value = "/add")
    @Operation(summary = "添加我的助理")
    @SaCheckPermission4FytecClient(value = "assistant:add", orRole = "admin")
    public R<Long> addAssistant(@Validated @RequestBody AssistantDTO dto) {
        assistantService.addAssistant(dto);
        return R.ok();
    }

    @PostMapping(value = "/delete")
    @Operation(summary = "移除我的助理")
    @SaCheckPermission4FytecClient(value = "assistant:delete", orRole = "admin")
    public R<Long> removeAssistant(@Validated @RequestBody AssistantDTO dto) {
        assistantService.removeAssistant(dto);
        return R.ok();
    }

    @GetMapping(value = "/list")
    @Operation(summary = "查询我的助理")
    @SaCheckPermission4FytecClient(value = "assistant:list", orRole = "admin")
    public R<List<Map<String, Object>>> queryAssistantList(AssistantQueryDTO dto) {
        return R.ok(assistantService.queryAssistantList(dto));
    }

}
