package com.fytec.controller.plugin;

import cn.hutool.core.exceptions.ValidateException;
import com.alibaba.druid.util.StringUtils;
import com.fytec.dto.plugin.PluginExecuteDTO;
import com.fytec.service.plugin.PluginService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

import static cn.dev33.satoken.sign.SaSignUtil.checkSign;

@Slf4j
@Validated
@Tag(name = "工作流-function-call")
@RequestMapping("/api/plugin/function")
@RestController
@RequiredArgsConstructor
public class PluginFunctionCallController {

    private final PluginService pluginService;

    @PostMapping(value = "/call")
    @Operation(summary = "执行插件")
    public R<Map<String, Object>> executePublishedWorkflow(@Validated @RequestBody PluginExecuteDTO dto, HttpServletRequest request) {
        String sign = request.getHeader("sign");
        if (StringUtils.isEmpty(sign)) {
            throw new ValidateException("签名不能为空");
        }
        dto.setDebug(false);
        Map<String, Object> params = new HashMap<>();
        params.put("parameters", dto.getParameters());
        params.put("id", dto.getId());
        checkSign(params, sign);
        return R.ok(pluginService.executePluginApi(dto));
    }
}
