package com.fytec.controller.plugin;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.plugin.*;
import com.fytec.satoken.StpClientUserUtil;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.plugin.PluginService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@Validated
@Tag(name = "插件")
@RequestMapping("/api/plugin")
@RestController
@AllArgsConstructor
public class PluginController {
    private final PluginService pluginService;

    //##############################Plugin Basic##############################
    @GetMapping(value = "/info")
    @Operation(summary = "插件信息")
    @SaCheckPermission4FytecClient(value = "plugin:info", orRole = "admin")
    public R<JSONObject> getPluginInfo(Long resourceId) {
        return R.ok(pluginService.getPluginInfo(resourceId));
    }

    @PostMapping(value = "/publish")
    @Operation(summary = "插件发布")
    @SaCheckPermission4FytecClient(value = "plugin:publish", orRole = "admin")
    public R<Void> publishPlugin(@Validated @RequestBody PluginPublishDTO dto) {
        pluginService.publishPlugin(dto);
        return R.ok();
    }

    @GetMapping(value = "/publish/list")
    @Operation(summary = "发布插件列表")
    @SaCheckPermission4FytecClient(value = "plugin:list", orRole = "admin")
    public R<Page<PluginPublishListDTO>> queryPublishPluginList(PluginQueryDTO dto, Page<PluginPublishListDTO> page) {
        dto.setCreateBy(StpClientUserUtil.getLoginIdAsLong());
        return R.ok(pluginService.queryPublishPluginList(dto, page));
    }

    @GetMapping(value = "/publish/official-list")
    @Operation(summary = "官方发布插件列表")
    @SaCheckPermission4FytecClient(value = "plugin:list", orRole = "admin")
    public R<Page<PluginPublishListDTO>> queryOfficialPublishPluginList(PluginQueryDTO dto, Page<PluginPublishListDTO> page) {
        return R.ok(pluginService.queryPublishPluginList(dto, page));
    }


    //##############################Plugin Api##############################
    @PostMapping(value = "/api/create")
    @Operation(summary = "创建api")
    @SaCheckPermission4FytecClient(value = "plugin:api:create", orRole = "admin")
    public R<Long> createPluginApi(@Validated @RequestBody PluginApiDTO dto) {
        return R.ok(pluginService.createPluginApi(dto));
    }

    @PostMapping(value = "/api/update_basic")
    @Operation(summary = "更新api基本信息")
    @SaCheckPermission4FytecClient(value = "plugin:api:update", orRole = "admin")
    public R<Void> updatePluginApiBasic(@Validated @RequestBody PluginApiDTO dto) {
        pluginService.updatePluginApiBasic(dto);
        return R.ok();
    }

    @PostMapping(value = "/api/update_extend")
    @Operation(summary = "更新api地址、请求方式等")
    @SaCheckPermission4FytecClient(value = "plugin:api:update", orRole = "admin")
    public R<Void> updatePluginApiExtend(@Validated @RequestBody PluginApiDTO dto) {
        pluginService.updatePluginApiExtend(dto);
        return R.ok();
    }

    @PostMapping(value = "/api/update_request_params")
    @Operation(summary = "更新api请求参数")
    @SaCheckPermission4FytecClient(value = "plugin:api:update", orRole = "admin")
    public R<Void> updatePluginApiRequestParams(@Validated @RequestBody PluginApiDTO dto) {
        pluginService.updatePluginApiRequestParams(dto);
        return R.ok();
    }

    @PostMapping(value = "/api/update_response_params")
    @Operation(summary = "更新api响应参数")
    @SaCheckPermission4FytecClient(value = "plugin:api:update", orRole = "admin")
    public R<Void> updatePluginApiResponseParams(@Validated @RequestBody PluginApiDTO dto) {
        pluginService.updatePluginApiResponseParams(dto);
        return R.ok();
    }

    @GetMapping(value = "/api/list")
    @Operation(summary = "api插件列表")
    @SaCheckPermission4FytecClient(value = "plugin:api:query", orRole = "admin")
    public R<List<PluginApiListDTO>> queryPluginApiList(Long resourceId) {
        return R.ok(pluginService.queryPluginApiList(resourceId));
    }

    @GetMapping(value = "/api/info")
    @Operation(summary = "api插件详情")
    @SaCheckPermission4FytecClient(value = "plugin:api:query", orRole = "admin")
    public R<PluginApiDTO> getPluginApiDetail(Long id) {
        return R.ok(pluginService.getPluginApiDetail(id));
    }



    @GetMapping(value = "/api/publish/info")
    @Operation(summary = "api插件详情")
    @SaCheckPermission4FytecClient(value = "plugin:api:query", orRole = "admin")
    public R<PluginApiDTO> getPublishPluginApiDetail(Long id) {
        return R.ok(pluginService.getPublishPluginApiDetail(id));
    }

    @GetMapping(value = "/api/enable")
    @Operation(summary = "api插件启用")
    @SaCheckPermission4FytecClient(value = "plugin:api:update", orRole = "admin")
    public R<Void> enablePluginApi(Long id) {
        pluginService.enablePluginApi(id);
        return R.ok();
    }

    @GetMapping(value = "/api/disable")
    @Operation(summary = "api插件启用")
    @SaCheckPermission4FytecClient(value = "plugin:api:update", orRole = "admin")
    public R<Void> disablePluginApi(Long id) {
        pluginService.disablePluginApi(id);
        return R.ok();
    }

    @GetMapping(value = "/api/delete")
    @Operation(summary = "api插件详情")
    @SaCheckPermission4FytecClient(value = "plugin:api:delete", orRole = "admin")
    public R<Void> deletePluginApi(Long id) {
        pluginService.deletePluginApi(id);
        return R.ok();
    }

    @PostMapping(value = "/api/execute")
    @Operation(summary = "api插件调试/执行")
    @SaCheckPermission4FytecClient(value = "plugin:api:execute", orRole = "admin")
    public R<Map<String, Object>> executePluginApi(@Validated @RequestBody PluginExecuteDTO dto) {
        return R.ok(pluginService.executePluginApi(dto));
    }

    //##############################Plugin mcp##############################
    @PostMapping(value = "/mcp/save")
    @Operation(summary = "保存mcp")
    @SaCheckPermission4FytecClient(value = "plugin:mcp:create", orRole = "admin")
    public R<Void> savePluginMcp(@Validated @RequestBody PluginMcpDTO dto) {
        pluginService.savePluginMcp(dto);
        return R.ok();
    }

    @PostMapping(value = "/mcp/execute")
    @Operation(summary = "mcp插件调试/执行")
    @SaCheckPermission4FytecClient(value = "plugin:mcp:execute", orRole = "admin")
    public R<?> executePluginMcp(@Validated @RequestBody PluginExecuteDTO dto) {
        return R.ok(pluginService.executePluginMcp(dto));
    }
}
