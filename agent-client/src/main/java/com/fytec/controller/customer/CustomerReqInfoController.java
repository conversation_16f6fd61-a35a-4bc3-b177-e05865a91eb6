package com.fytec.controller.customer;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.customer.CustomerReqInfoDTO;
import com.fytec.dto.customer.CustomerReqInfoQueryDTO;
import com.fytec.entity.customer.CustomerReqInfo;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.customer.CustomerReqInfoService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Validated
@Tag(name = "客户管理")
@RequestMapping("/api/customer")
@RestController
@RequiredArgsConstructor
public class CustomerReqInfoController {

    private final CustomerReqInfoService customerReqInfoService;

    @PostMapping("/req-info/add")
    @Operation(summary = "新增用户需求")
//    @SaCheckPermission4FytecClient(value = "customer:reqInfo:add", orRole = {"admin"})
    public R<Void> addCustomerReqInfo(@RequestBody @Validated CustomerReqInfoDTO dto) {
        customerReqInfoService.addCustomerReqInfo(dto);
        return R.ok();
    }

    @GetMapping("/req-info/status")
    @Operation(summary = "更新用户需求")
    @SaCheckPermission4FytecClient(value = "customer:reqInfo:delete", orRole = {"admin"})
    public R<Void> updateCustomerReqInfoStatus(Long id) {
        customerReqInfoService.updateCustomerReqInfoStatus(id);
        return R.ok();
    }

    @GetMapping("/req-info/delete")
    @Operation(summary = "删除用户需求")
    @SaCheckPermission4FytecClient(value = "customer:reqInfo:delete", orRole = {"admin"})
    public R<Void> deleteCustomerReqInfo(Long id) {
        customerReqInfoService.deleteCustomerReqInfo(id);
        return R.ok();
    }

    @GetMapping("/req-info/page")
    @Operation(summary = "列表用户需求")
    @SaCheckPermission4FytecClient(value = "customer:reqInfo:page", orRole = {"admin"})
    public R<Page<CustomerReqInfo>> queryCustomerReqInfoPage(CustomerReqInfoQueryDTO dto, Page<CustomerReqInfo> page) {
        return R.ok(customerReqInfoService.queryCustomerReqInfoPage(dto, page));
    }
}
