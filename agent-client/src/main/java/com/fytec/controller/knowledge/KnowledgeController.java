package com.fytec.controller.knowledge;


import cn.dev33.satoken.oauth2.annotation.SaCheckClientIdSecret;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.constant.Constants;
import com.fytec.dto.knowledge.*;
import com.fytec.dto.knowledge.deprecated.AddKnowledgeDocDTO;
import com.fytec.dto.knowledge.deprecated.DocSegmentDTO;
import com.fytec.dto.knowledge.deprecated.SegmentConfigDTO;
import com.fytec.entity.knowledge.KnowledgeDoc;
import com.fytec.satoken.StpClientUserUtil;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.knowledge.KnowledgeService;
import com.fytec.service.knowledge.KnowledgeWithoutTranService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Validated
@Tag(name = "知识库管理")
@RequestMapping("/api/knowledge")
@RestController
@AllArgsConstructor
public class KnowledgeController {
    private final KnowledgeService knowledgeService;
    private final KnowledgeWithoutTranService knowledgeWithoutTranService;

    //##################################知识库查询#################################
    @GetMapping(value = "/list")
    @Operation(summary = "知识库查询")
    @SaCheckPermission4FytecClient(value = "knowledge:list", orRole = "admin")
    public R<Page<KnowledgeDetailDTO>> queryKnowledge(KnowledgeQueryDTO dto, Page<KnowledgeDetailDTO> page) {
//        dto.setUserId(StpClientUserUtil.getLoginIdAsLong());
//        dto.setKnowledgeType(Constants.KNOWLEDGE_TYPE.team.name());
        // 这样就查询是null的了，其实就代表团队了
        return R.ok(knowledgeService.queryKnowledge(dto, page));
    }

    @GetMapping(value = "/detail")
    @Operation(summary = "知识库详情")
    @SaCheckPermission4FytecClient(value = "knowledge:preview", orRole = "admin")
    public R<KnowledgeDetailDTO> getKnowledgeDetail(Long resourceId) {
        return R.ok(knowledgeService.getKnowledgeDetail(resourceId));
    }

    //##################################知识库文档查询#################################
    @GetMapping(value = "/doc/list")
    @Operation(summary = "知识库文档列表")
    @SaCheckPermission4FytecClient(value = "knowledge:doc:list", orRole = "admin")
    public R<List<KnowledgeDocDTO>> getKnowledgeDocs(KnowledgeDocQueryDTO dto) {
        dto.setCreateBy(StpClientUserUtil.getLoginIdAsLong());
        return R.ok(knowledgeService.getKnowledgeDocs(dto));
    }


    @GetMapping(value = "/doc/list/all-in-one")
    @Operation(summary = "知识库文档列表")
    @SaCheckPermission4FytecClient(value = "knowledge:doc:list", orRole = "admin")
    public R<List<KnowledgeAllInOneDTO>> queryKnowledgeAllInOne(KnowledgeAllInOneQueryDTO dto) {
        dto.setCreateBy(StpClientUserUtil.getLoginIdAsLong());
        return R.ok(knowledgeService.queryKnowledgeAllInOne(dto));
    }


    @GetMapping(value = "/doc/detail")
    @Operation(summary = "知识库文档详情")
    @SaCheckPermission4FytecClient(value = "knowledge:doc:detail", orRole = "admin")
    public R<KnowledgeDocDetailDTO> getKnowledgeDocDetail(Long docId) {
        return R.ok(knowledgeService.getKnowledgeDocDetail(docId));
    }

    @PostMapping(value = "/doc/batch-detail")
    @Operation(summary = "知识库文档详情")
    @SaCheckPermission4FytecClient(value = "knowledge:doc:detail", orRole = "admin")
    public R<List<KnowledgeDoc>> batchGetKnowledgeDocDetail(@RequestBody KnowledgeDocBatchDTO dto) {
        return R.ok(knowledgeService.batchGetKnowledgeDocDetail(dto));
    }

    @PostMapping(value = "/doc/batch-tree")
    @Operation(summary = "知识库文档详情")
    @SaCheckPermission4FytecClient(value = "knowledge:doc:detail", orRole = "admin")
    public R<List<Map<String, Object>>> batchGetKnowledgeDocTree(@RequestBody KnowledgeDocBatchDTO dto) {
        return R.ok(knowledgeService.batchGetKnowledgeDocTree(dto));
    }

    @PostMapping(value = "/doc/update-name")
    @Operation(summary = "更新文档名称")
    @SaCheckPermission4FytecClient(value = "knowledge:doc:update", orRole = "admin")
    public R<Void> updateDocName(@RequestBody KnowledgeDocUpdateDTO dto) {
        knowledgeService.updateDocName(dto);
        return R.ok();
    }

    @PostMapping(value = "/doc/move-group")
    @Operation(summary = "文档移动")
    @SaCheckPermission4FytecClient(value = "knowledge:doc:update", orRole = "admin")
    public R<Void> updateDocGroup(@RequestBody KnowledgeDocMoveDTO dto) {
        knowledgeService.updateDocGroup(dto);
        return R.ok();
    }

    @GetMapping(value = "/doc/delete")
    @Operation(summary = "删除文档")
    @SaCheckPermission4FytecClient(value = "knowledge:doc:delete", orRole = "admin")
    public R<Void> deleteDoc(String docId) {
        knowledgeService.deleteDoc(docId);
        return R.ok();
    }

    @PostMapping(value = "/doc/batch-delete")
    @Operation(summary = "删除文档")
    @SaCheckPermission4FytecClient(value = "knowledge:doc:delete", orRole = "admin")
    public R<Void> batchDeleteDoc(@RequestBody KnowledgeDocBatchDTO dto) {
        knowledgeService.batchDeleteDoc(dto);
        return R.ok();
    }

    //##################################知识库文档导入#################################
    @PostMapping(value = "/doc/batch-import")
    @Operation(summary = "知识库导入到知识库")
    @SaCheckPermission4FytecClient(value = "knowledge:doc:import", orRole = "admin")
    public R<List<Long>> aiworkBatchImportFromAnotherKnowledge(@Validated @RequestBody AiWorkKnowledgeDocBatchImportDTO dto) {
        List<Map<String, Object>> resultMaps = knowledgeService.aiworkBatchImportFromAnotherKnowledge(dto);
        List<Long> targetDocIds = new ArrayList<>();
        for (Map<String, Object> resultMap : resultMaps) {
            targetDocIds.add((Long) resultMap.get("targetDocId"));
            knowledgeService.copySegmentAndVector(dto.getTargetKnowledgeId(),
                    (String) resultMap.get("sourceCollectionName"),
                    (String) resultMap.get("targetCollectionName"),
                    (KnowledgeDoc) resultMap.get("sourceDoc"),
                    (KnowledgeDoc) resultMap.get("targetDoc"));
        }
        return R.ok(targetDocIds);
    }


    //##################################默认智能模式##################################
    @PostMapping(value = "/doc/auto")
    @Operation(summary = "添加内容-智能模式")
    @SaCheckPermission4FytecClient(value = "knowledge:doc:add", orRole = "admin")
    public R<List<KnowledgeDoc>> autoProcessData(@Validated @RequestBody KnowledgeDocAutoDTO dto) {
        dto.setDefaultSegment(true);
        dto.setAutoSegment(true);
        //Async方法获取不到用户信息，需要当成参数往后传递
        dto.setUserId(StpClientUserUtil.getLoginIdAsString());
        List<KnowledgeDoc> docs = knowledgeService.autoProcessData(dto);
        for (KnowledgeFileDTO file : dto.getFiles()) {
            knowledgeWithoutTranService.autoProcessDocV2(file, dto);
        }
        return R.ok(docs);
    }

    @PostMapping(value = "/doc/callBack")
    @Operation(summary = "添加内容-智能模式")
    @SaCheckClientIdSecret()
    public R<List<KnowledgeDoc>> processDocCallBack(@Validated @RequestBody KnowledgeDocCallBackDTO dto) {
        knowledgeWithoutTranService.processDocCallBack(dto);
        return R.ok();
    }

    //##################################高级分步模式#################################
    @PostMapping(value = "/doc/advanced/preview")
    @Operation(summary = "添加内容-高级模式-预览")
    @SaCheckPermission4FytecClient(value = "knowledge:doc:add", orRole = "admin")
    public R<List<KnowledgeDoc>> advancedProcessData(@Validated @RequestBody KnowledgeDocAutoDTO dto) {
        dto.setDefaultSegment(false);
        dto.setPreview(true);
        //Async方法获取不到用户信息，需要当成参数往后传递
        dto.setUserId(StpClientUserUtil.getLoginIdAsString());
        List<KnowledgeDoc> docs = knowledgeService.autoProcessData(dto);
        for (KnowledgeFileDTO file : dto.getFiles()) {
            knowledgeWithoutTranService.autoProcessDocV2(file, dto);
        }
        return R.ok(docs);
    }

    @PostMapping(value = "/doc/advanced/vector")
    @Operation(summary = "添加内容-高级模式-数据处理")
    @SaCheckPermission4FytecClient(value = "knowledge:doc:add", orRole = "admin")
    public R<List<Long>> advancedVectorData(@Validated @RequestBody KnowledgeDocVectorDTO dto) {
        List<KnowledgeDoc> docs = knowledgeService.updateDocVisible(dto.getDocIds());
        for (KnowledgeDoc doc : docs) {
            knowledgeWithoutTranService.advancedVectorData(doc.getId(), StpClientUserUtil.getLoginIdAsString());
        }
        return R.ok(docs.stream().map(KnowledgeDoc::getId).toList());
    }

    //##################################知识库文档导入#################################
    @PostMapping(value = "/doc/base/batch-import")
    @Operation(summary = "把知识库整个或者部分文档，拷贝到新的知识库中（如果未选择文档id就把正常全部的都拷贝过去）")
    @SaCheckPermission4FytecClient(value = "knowledge:doc:import", orRole = "admin")
    public R<List<Long>> baseBatchImportFromAnotherKnowledge(@Validated @RequestBody KnowledgeDocBatchImportDTO dto) {
        knowledgeService.batchImportFromAnotherKnowledge(dto);
//        List<Long> targetDocIds =
        return R.ok();
    }

    @GetMapping(value = "/doc/batch-clear-failed")
    @Operation(summary = "清除失败的文件记录")
    @SaCheckPermission4FytecClient(value = "knowledge:doc:clear:failed", orRole = "admin")
    public R<Void> batchClearFailedDoc(Long knowledgeId) {
        knowledgeService.batchClearFailedDoc(knowledgeId);
        return R.ok();
    }

}
