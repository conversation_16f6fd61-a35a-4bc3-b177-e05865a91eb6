package com.fytec.controller.knowledge;


import cn.dev33.satoken.oauth2.annotation.SaCheckClientToken;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.constant.Constants;
import com.fytec.dto.knowledge.*;
import com.fytec.dto.knowledge.group.KnowledgeGroupDTO;
import com.fytec.dto.resource.AddResourceDTO;
import com.fytec.dto.resource.UpdateResourceDTO;
import com.fytec.entity.knowledge.KnowledgeDoc;
import com.fytec.satoken.StpClientUserUtil;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.knowledge.KnowledgeService;
import com.fytec.service.resource.ResourceService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Validated
@Tag(name = "个人知识库")
@RequestMapping("/api/knowledge/personal")
@RestController
@RequiredArgsConstructor
public class KnowledgePersonalController {

    private final KnowledgeService knowledgeService;

    private final ResourceService resourceService;

    @PostMapping(value = "/add")
    @Operation(summary = "知识库-添加")
    @SaCheckPermission4FytecClient(value = "knowledge:personal:add", orRole = "admin")
    public R<Long> addPersonalKnowledge(@Validated @RequestBody KnowledgeInitDTO dto) {
        dto.setKnowledgeType(Constants.KNOWLEDGE_TYPE.personal.name());
        Long knowledgeId = knowledgeService.addPersonalKnowledge(dto.getName(), dto.getDescription(), dto.getLogo(), dto.getKnowledgeType(),
                dto.getExpireOption(), dto.getStartTime(), dto.getEndTime(), dto.getUserId());
        return R.ok(knowledgeId);
    }

    @GetMapping(value = "/list")
    @Operation(summary = "知识库查询")
    @SaCheckPermission4FytecClient(value = "knowledge:list", orRole = "admin")
    public R<Page<KnowledgeDetailDTO>> queryKnowledge(KnowledgeQueryDTO dto, Page<KnowledgeDetailDTO> page) {
        dto.setUserId(StpClientUserUtil.getLoginIdAsLong());
        dto.setKnowledgeType(Constants.KNOWLEDGE_TYPE.personal.name());
        return R.ok(knowledgeService.queryKnowledge(dto, page));
    }
}
