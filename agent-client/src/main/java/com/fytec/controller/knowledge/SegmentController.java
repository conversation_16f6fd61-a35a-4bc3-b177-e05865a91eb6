package com.fytec.controller.knowledge;


import com.fytec.dto.knowledge.*;
import com.fytec.dto.prompt.PromptContentAutoDTO;
import com.fytec.entity.knowledge.KnowledgeSegment;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.knowledge.SegmentService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Validated
@Tag(name = "知识库段落管理")
@RequestMapping("/api/knowledge/segment")
@RestController
@AllArgsConstructor
public class SegmentController {

    private final SegmentService segmentService;


    @GetMapping(value = "/list")
    @Operation(summary = "文档分段列表")
    @SaCheckPermission4FytecClient(value = "knowledge:segment:list", orRole = "admin")
    public R<List<KnowledgeSegment>> list(SegmentQueryDTO dto) {
        return R.ok(segmentService.list(dto));
    }


    @PostMapping(value = "/add")
    @Operation(summary = "新增片段")
    @SaCheckPermission4FytecClient(value = "knowledge:segment:add", orRole = "admin")
    public R<KnowledgeSegment> add(@RequestBody AddKnowledgeSegmentDTO dto) {
        return R.ok(segmentService.add(dto));
    }

    @PostMapping(value = "/update")
    @Operation(summary = "更新片段")
    @SaCheckPermission4FytecClient(value = "knowledge:segment:update", orRole = "admin")
    public R<KnowledgeSegment> update(@RequestBody UpdateKnowledgeSegmentDTO dto) {
        return R.ok(segmentService.update(dto));
    }


    @GetMapping(value = "/detail")
    @Operation(summary = "片段详情")
    @SaCheckPermission4FytecClient(value = "knowledge:segment:detail", orRole = "admin")
    public R<KnowledgeSegment> detail(Long id) {
        return R.ok(segmentService.detail(id));
    }

    @GetMapping(value = "/delete")
    @Operation(summary = "删除片段")
    @SaCheckPermission4FytecClient(value = "knowledge:segment:delete", orRole = "admin")
    public R<Void> delete(Long id) {
        segmentService.delete(id);
        return R.ok();
    }

    @PostMapping(value = "/overview/auto")
    @Operation(summary = "生成概要")
    @SaCheckPermission4FytecClient(value = "knowledge:segment:overview:auto", orRole = "admin")
    public void autoOverviewContent(HttpServletResponse response, @Validated @RequestBody SegmentOverviewAutoDTO dto) {
        segmentService.autoOverviewContent(response, dto);
    }


}
