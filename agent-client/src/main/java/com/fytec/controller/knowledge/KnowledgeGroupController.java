package com.fytec.controller.knowledge;


import cn.hutool.core.util.StrUtil;
import com.fytec.dto.knowledge.group.KnowledgeGroupDTO;
import com.fytec.dto.knowledge.group.KnowledgeGroupTreeDTO;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.knowledge.KnowledgeGroupService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.fytec.constant.Constants.KNOWLEDGE_TYPE.personal;

@Slf4j
@Validated
@Tag(name = "知识库组管理")
@RequestMapping("/api/knowledge/group")
@RestController
@RequiredArgsConstructor
public class KnowledgeGroupController {
    private final KnowledgeGroupService knowledgeGroupService;

    @PostMapping(value = "/add")
    @Operation(summary = "知识库组添加")
    @SaCheckPermission4FytecClient(value = "knowledge:group:add", orRole = "admin")
    public R<Long> addKnowledgeGroup(@Validated @RequestBody KnowledgeGroupDTO dto) {
        return R.ok(knowledgeGroupService.addKnowledgeGroup(dto));
    }

    @PostMapping(value = "/edit")
    @Operation(summary = "知识库组编辑")
    @SaCheckPermission4FytecClient(value = "knowledge:group:edit", orRole = "admin")
    public R<Long> editKnowledgeGroup(@Validated @RequestBody KnowledgeGroupDTO dto) {
        knowledgeGroupService.editKnowledgeGroup(dto);
        return R.ok();
    }

    @GetMapping(value = "/delete")
    @Operation(summary = "知识库组删除")
    @SaCheckPermission4FytecClient(value = "knowledge:group:delete", orRole = "admin")
    public R<Long> deleteKnowledgeGroup(Long id) {
        knowledgeGroupService.deleteKnowledgeGroup(id);
        return R.ok();
    }


    @GetMapping(value = "/detail")
    @Operation(summary = "知识库组详情")
    @SaCheckPermission4FytecClient(value = "knowledge:group:detail", orRole = "admin")
    public R<?> getKnowledgeGroupDetail(Long id) {
        return R.ok(knowledgeGroupService.getKnowledgeGroupDetail(id));
    }


    @GetMapping(value = "/tree")
    @Operation(summary = "知识库组列表")
    @SaCheckPermission4FytecClient(value = "knowledge:group:list", orRole = "admin")
    public R<List<KnowledgeGroupTreeDTO>> queryKnowledgeGroup(String type) {
        if (StrUtil.isBlank(type)) {
            type = personal.name();
        }
        return R.ok(knowledgeGroupService.queryKnowledgeGroup(type));
    }

    @GetMapping(value = "/detailtree")
    @Operation(summary = "知识库详情带子节点")
    @SaCheckPermission4FytecClient(value = "knowledge:group:list", orRole = "admin")
    public R<KnowledgeGroupTreeDTO> detailTree(@Validated @NotNull(message = "id不能为空") Long id) {
        return R.ok(knowledgeGroupService.detailTree(id));
    }
}
