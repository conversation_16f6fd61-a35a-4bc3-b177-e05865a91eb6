package com.fytec.controller.knowledge;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.knowledge.KnowledgeDetailDTO;
import com.fytec.dto.knowledge.KnowledgeQueryDTO;
import com.fytec.dto.knowledge.group.KnowledgeGroupTreeDTO;
import com.fytec.dto.knowledge.team.TeamKnowledgeMemberDTO;
import com.fytec.dto.knowledge.team.TeamKnowledgeMemberEditRoleDTO;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.knowledge.KnowledgeTeamService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Validated
@Tag(name = "团队知识库管理")
@RequestMapping("/api/knowledge/team")
@RestController
@AllArgsConstructor
public class KnowledgeTeamController {
    private final KnowledgeTeamService knowledgeTeamService;


    @GetMapping(value = "/mine/list")
    @Operation(summary = "我创建的团队知识库列表")
    @SaCheckPermission4FytecClient(value = "knowledge:list", orRole = "admin")
    public R<List<KnowledgeGroupTreeDTO>> queryKnowledgeTeamCreatedByMine(KnowledgeQueryDTO dto) {
        return R.ok(knowledgeTeamService.queryKnowledgeTeamCreatedByMine(dto));
    }


    @GetMapping(value = "/join/list")
    @Operation(summary = "我加入的团队知识库列表")
    @SaCheckPermission4FytecClient(value = "knowledge:list", orRole = "admin")
    public R<List<KnowledgeGroupTreeDTO>> queryKnowledgeTeamJoined(KnowledgeQueryDTO dto) {
        return R.ok(knowledgeTeamService.queryKnowledgeTeamJoined(dto));
    }


    @GetMapping(value = "/member/list")
    @Operation(summary = "团队知识库成员列表")
    @SaCheckPermission4FytecClient(value = "knowledge:member:list", orRole = "admin")
    public R<?> queryKnowledgeTeamMembers(Long knowledgeId) {
        return R.ok(knowledgeTeamService.queryKnowledgeTeamMembers(knowledgeId));
    }


    @GetMapping(value = "/member/quit")
    @Operation(summary = "团队知识库成员退出")
    @SaCheckPermission4FytecClient(value = "knowledge:member:quit", orRole = "admin")
    public R<Void> quitKnowledgeTeamMembers(Long knowledgeId) {
        knowledgeTeamService.quitKnowledgeTeamMembers(knowledgeId);
        return R.ok();
    }


    @PostMapping(value = "/member/add")
    @Operation(summary = "团队知识库成员添加")
    @SaCheckPermission4FytecClient(value = "knowledge:member:add", orRole = "admin")
    public R<Void> knowledgeTeamAddMember(@RequestBody TeamKnowledgeMemberDTO dto) {
        knowledgeTeamService.knowledgeTeamAddMember(dto);
        return R.ok();
    }

    @PostMapping(value = "/member/edit-role")
    @Operation(summary = "团队知识库成员添加")
    @SaCheckPermission4FytecClient(value = "knowledge:member:edit-role", orRole = "admin")
    public R<Void> knowledgeTeamMemberEditRole(@RequestBody TeamKnowledgeMemberEditRoleDTO dto) {
        knowledgeTeamService.knowledgeTeamMemberEditRole(dto);
        return R.ok();
    }

    @PostMapping(value = "/member/remove")
    @Operation(summary = "团队知识库成员已出")
    @SaCheckPermission4FytecClient(value = "knowledge:member:add", orRole = "admin")
    public R<Void> knowledgeTeamRemoveMember(@RequestBody TeamKnowledgeMemberDTO dto) {
        knowledgeTeamService.knowledgeTeamRemoveMember(dto);
        return R.ok();
    }
}
