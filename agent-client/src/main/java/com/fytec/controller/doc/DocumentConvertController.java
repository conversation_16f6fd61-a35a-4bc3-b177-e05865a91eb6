package com.fytec.controller.doc;


import com.fytec.dto.open.Markdown2wordDTO;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.utils.Markdown2docxUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

@Slf4j
@Validated
@Tag(name = "文档转换")
@RequestMapping("/api/document")
@RestController
@AllArgsConstructor
public class DocumentConvertController {
    @PostMapping(value = "/markdown2word")
    @Operation(summary = "把markdown格式的数据转为word")
    @SaCheckPermission4FytecClient(value = "document:markdown2word", orRole = "admin")
    public ResponseEntity<byte[]> parseDoc(@Validated @RequestBody Markdown2wordDTO dto) {
        byte[] documentBytes = Markdown2docxUtil.buildDocFileBytes(dto.getFileContent());
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", URLEncoder.encode(dto.getFileName(), StandardCharsets.UTF_8));
        headers.setContentLength(documentBytes.length);
        headers.set("filename", URLEncoder.encode(dto.getFileName(), StandardCharsets.UTF_8));
        headers.set("Access-Control-Expose-Headers", "filename");

        return ResponseEntity.ok()
                .headers(headers)
                .body(documentBytes);
    }
}
