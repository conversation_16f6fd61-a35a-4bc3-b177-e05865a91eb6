package com.fytec.controller.doc;


import cn.dev33.satoken.oauth2.annotation.SaCheckClientToken;
import com.fytec.dto.doc.DocParseDTO;
import com.fytec.file.FileParseUtils;
import com.fytec.service.knowledge.ImageReplacerService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.fytec.file.FileParseUtils.FILE_TYPE;

@Slf4j
@Validated
@Tag(name = "文档解析服务")
@RequestMapping("/api/document/parse")
@RestController
@AllArgsConstructor
public class DocumentParseController {


    private final ImageReplacerService imageReplacerService;

    @PostMapping(value = "/local")
    @Operation(summary = "本地文档解析")
    @SaCheckClientToken(scope = "agent:document:parse")
    public R<String> parseDoc(@Validated @RequestBody DocParseDTO dto) {
        if (dto.getFileBytes() == null) {
            return R.failed("文件字节不能为空");
        }
        if (StringUtils.isEmpty(dto.getFileType())) {
            return R.failed("文件类型不能为空");
        }
        if (!FILE_TYPE.contains(dto.getFileType())) {
            return R.failed("文件类型不支持");
        }

        return R.ok(FileParseUtils.parseDoc(dto.getFileBytes(), dto.getFileType()));
    }



    @PostMapping(value = "/paper")
    @Operation(summary = "测试试卷解析")
    public R<Map> parsePaper(@Validated @RequestBody DocParseDTO dto) {
        Map<Integer, String> map = imageReplacerService.parseDocument(dto.getFileUrl(), dto.getFileType());
        return R.ok(map);
    }

}
