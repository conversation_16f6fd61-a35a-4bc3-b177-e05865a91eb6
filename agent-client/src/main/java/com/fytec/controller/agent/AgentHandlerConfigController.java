package com.fytec.controller.agent;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.entity.agent.AgentHandlerConfig;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.agent.AgentHandlerConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.fytec.util.R;
/**
 * Copyright (C), 2025-2099
 * Agent处理器配置管理
 *
 * <AUTHOR> lix
 * @date :   2025/6/9 18:06
 * Version: V1.0.0
 */
@RestController
@RequestMapping("/agent-handler-config")
@Tag(name = "Agent处理程序配置管理")
@AllArgsConstructor
public class AgentHandlerConfigController {
    private final AgentHandlerConfigService agentHandlerConfigService;
    @PostMapping
    @Operation(summary = "新增配置")
    @SaCheckPermission4FytecClient(value = "agentConfig:add", orRole = "admin")
    public R<Boolean> save(@RequestBody AgentHandlerConfig config) {
        return R.ok(agentHandlerConfigService.save(config));
    }
    @PutMapping
    @Operation(summary = "修改配置")
    @SaCheckPermission4FytecClient(value = "agentConfig:update", orRole = "admin")
    public R<Boolean> update(@RequestBody AgentHandlerConfig config) {
        return R.ok(agentHandlerConfigService.updateById(config));
    }
    @DeleteMapping("/{id}")
    @Operation(summary = "删除配置(逻辑删除)")
    @SaCheckPermission4FytecClient(value = "agentConfig:delete", orRole = "admin")
    public R<Boolean> delete(@PathVariable Long id) {
        return R.ok(agentHandlerConfigService.removeById(id));
    }
    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询配置")
    @SaCheckPermission4FytecClient(value = "agentConfig:info", orRole = "admin")
    public R<AgentHandlerConfig> getById(@PathVariable Long id) {
        return R.ok(agentHandlerConfigService.getById(id));
    }
    @GetMapping("/page")
    @Operation(summary = "分页查询配置")
    @SaCheckPermission4FytecClient(value = "agentConfig:page", orRole = "admin")
    public R<Page<AgentHandlerConfig>> page(Page<AgentHandlerConfig> page) {
        return R.ok(agentHandlerConfigService.page(page));
    }
}
