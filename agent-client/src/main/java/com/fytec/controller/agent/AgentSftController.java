package com.fytec.controller.agent;

import cn.hutool.core.exceptions.ValidateException;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.constant.AgentFlowConstants;
import com.fytec.constant.Constants;
import com.fytec.dto.agent.*;
import com.fytec.dto.agent.feedback.AgentFeedbackHandleDTO;
import com.fytec.dto.agent.feedback.AgentFeedbackQueryDTO;
import com.fytec.dto.agent.feedback.AgentHistoryFeedbackDTO;
import com.fytec.entity.agent.AgentHistoryFeedback;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.agent.AgentSftService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Validated
@Tag(name = "智能体-知识库强化学习")
@RequestMapping("/api/agent")
@RestController
@AllArgsConstructor
public class AgentSftController {
    @Autowired
    private AgentSftService agentSftService;

    @PostMapping(value = "/develop/sft/positive")
    @Operation(summary = "监督强化学习-正样本")
    @SaCheckPermission4FytecClient(value = "knowledge:doc:update", orRole = "admin")

    public R<Page<AgentPublishHistoryDTO>> sftLogKnowledgePositive(@Validated @RequestBody AgentSftDto dto) {
        dto.setType(Constants.KNOWLEDGE_BASE_TYPE.positive.name());
        agentSftService.sftLogKnowledgePositive(dto);

        return R.ok();
    }

    @PostMapping(value = "/develop/sft/negative")
    @Operation(summary = "监督强化学习-负样本")
    @SaCheckPermission4FytecClient(value = "knowledge:doc:update", orRole = "admin")
    public R<Page<AgentPublishHistoryDTO>> sftLogKnowledgeNegative(@Validated @RequestBody AgentSftDto dto) {
        dto.setType(Constants.KNOWLEDGE_BASE_TYPE.negative.name());
        agentSftService.sftLogKnowledgeNegative(dto);
        return R.ok();
    }

    // 查询反馈列表
    @GetMapping(value = "/feedback/page")
    @Operation(summary = "管理平台-获取智能体反馈记录")
    @SaCheckPermission4FytecClient(value = "agent:feedback:page", orRole = "admin")
    public R<Page<AgentHistoryFeedbackDTO>> queryAgentFeedbackPage(AgentFeedbackQueryDTO dto, Page<AgentHistoryFeedback> page) {
        return R.ok(agentSftService.queryAgentFeedbackPage(page, dto));
    }

    // todo 对应几种处理状态的设置。包括不限于：目前可以先弄个正样本的。之后比如修改提示词就展示某个流程列表吧。酌情看看需要修改哪个流程节点
//    other("其他"),
//    positive("设置为正样本"),
//    prompt("修改提示词"),
//    data("修改数据源"),
//    defined("补充定义/解释");

    @PostMapping(value = "/feedback/handle")
    @Operation(summary = "管理平台-修改反馈状态")
    @SaCheckPermission4FytecClient(value = "agent:feedback:handle", orRole = "admin")
    public R handleFeedback(@RequestBody AgentFeedbackHandleDTO dto) {
        if (AgentFlowConstants.AGENT_FEEDBACK_RESULT.positive.name().equals(dto.getResult())) {
//            throw new ValidateException("修改并处理为正样本，此操作请调用其他处理接口");
            return handleFeedbackAsPositive(dto);
        }
        if (dto.getId() != null && dto.getFeedbackId() == null) {
            dto.setFeedbackId(dto.getId());
        }
        agentSftService.handleFeedback(dto);
        return R.ok();
    }

    @PostMapping(value = "/feedback/handle/positive")
    @Operation(summary = "管理平台-修改并处理为正样本")
    @SaCheckPermission4FytecClient(value = "agent:feedback:handle:positive", orRole = "admin")
    public R handleFeedbackAsPositive(@RequestBody AgentFeedbackHandleDTO dto) {
        if (dto.getId() != null && dto.getFeedbackId() == null) {
            dto.setFeedbackId(dto.getId());
        }
        agentSftService.handleFeedbackAsPositive(dto);
        return R.ok();
    }
}
