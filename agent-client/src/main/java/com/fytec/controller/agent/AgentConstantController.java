package com.fytec.controller.agent;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.agent.AddConstantDTO;
import com.fytec.dto.agent.UpdateConstantDTO;
import com.fytec.entity.agent.AgentConstant;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.agent.ConstantService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

@Slf4j
@Validated
@Tag(name = "智能体-常量管理")
@RequestMapping("/api/agent/constant")
@RestController
@AllArgsConstructor
public class AgentConstantController {
    private final ConstantService constantService;

    @PostMapping(value = "/add")
    @Operation(summary = "添加")
    @SaCheckPermission4FytecClient(value = "constant:add", orRole = "admin")
    public R<Long> add(@Validated @RequestBody AddConstantDTO dto) {
        return R.ok(constantService.add(dto));
    }

    @PostMapping(value = "/update")
    @Operation(summary = "修改")
    @SaCheckPermission4FytecClient(value = "constant:update", orRole = "admin")
    public R<Long> update(@Validated @RequestBody UpdateConstantDTO dto) {
        return R.ok(constantService.update(dto));
    }


    @PostMapping(value = "/delete")
    @Operation(summary = "删除")
    @SaCheckPermission4FytecClient(value = "constant:delete", orRole = "admin")
    public R<?> delete(@NotNull(message = "ID不能为空") Long id) {
        constantService.delete(id);
        return R.ok();
    }

    @GetMapping(value = "/list")
    @Operation(summary = "查询")
    @SaCheckPermission4FytecClient(value = "constant:page", orRole = "admin")
    public R<List<AgentConstant>> queryList(String name) {
        return R.ok(constantService.queryList(name));
    }



    @GetMapping(value = "/page")
    @Operation(summary = "查询-分页")
    @SaCheckPermission4FytecClient(value = "constant:page", orRole = "admin")
    public R<Page<AgentConstant>> queryPage(String name, Page page) {
        return R.ok(constantService.queryPage(name, page));
    }


}
