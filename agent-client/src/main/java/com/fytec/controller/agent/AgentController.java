package com.fytec.controller.agent;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.agent.*;
import com.fytec.dto.application.ApplicationAgentDTO;
import com.fytec.entity.agent.AgentDevelopHistory;
import com.fytec.entity.agent.AgentPublishHistory;
import com.fytec.satoken.StpClientUserUtil;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.agent.AgentHistoryService;
import com.fytec.service.agent.AgentService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Slf4j
@Validated
@Tag(name = "智能体管理")
@RequestMapping("/api/agent")
@RestController
@AllArgsConstructor
public class AgentController {
    private final AgentService agentService;
    private final AgentHistoryService agentHistoryService;

    @PostMapping(value = "/basic/add")
    @Operation(summary = "添加智能体")
    @SaCheckPermission4FytecClient(value = "agent:add", orRole = "admin")
    public R<Long> addAgent(@Validated @RequestBody AddAgentDTO dto) {
        return R.ok(agentService.addAgent(dto));
    }

    @PostMapping(value = "/basic/update")
    @Operation(summary = "修改智能体")
    @SaCheckPermission4FytecClient(value = "agent:update", orRole = "admin")
    public R<Long> updateAgent(@Validated @RequestBody UpdateAgentDTO dto) {
        return R.ok(agentService.updateAgent(dto));
    }

    @PostMapping(value = "/basic/delete")
    @Operation(summary = "删除智能体")
    @SaCheckPermission4FytecClient(value = "agent:delete", orRole = "admin")
    public R<Long> deleteAgent(@NotNull(message = "不能为空") Long id) {
        agentService.deleteAgent(id);
        return R.ok();
    }

    @GetMapping(value = "/basic/copy")
    @Operation(summary = "复制智能体")
    @SaCheckPermission4FytecClient(value = "agent:copy", orRole = "admin")
    public R<Long> copyAgent(@NotNull(message = "不能为空") Long id) {
        return R.ok(agentService.copyAgent(id));
    }

    @PostMapping(value = "/develop")
    @Operation(summary = "开发智能体")
    @SaCheckPermission4FytecClient(value = "agent:develop", orRole = "admin")
    public R<LocalDateTime> developAgent(@Validated @RequestBody DevelopAgentDTO dto) {
        return R.ok(agentService.developAgent(dto));
    }

    @GetMapping(value = "/develop/page")
    @Operation(summary = "查询智能体")
    @SaCheckPermission4FytecClient(value = "agent:develop:page", orRole = "admin")
    public R<Page<AgentBasicDTO>> developAgentPage(AgentQueryDTO dto, Page<AgentBasicDTO> page) {
        List<String> roleList = StpClientUserUtil.getRoleList();
        if (dto.isOnlySelf() || !roleList.contains("admin")) {
            dto.setCreateBy(StpClientUserUtil.getLoginIdAsLong());
        }
        return R.ok(agentService.developAgentPage(dto, page));
    }

    @GetMapping(value = "/develop/detail")
    @Operation(summary = "开发智能体详情")
    @SaCheckPermission4FytecClient(value = "agent:develop:detail", orRole = "admin")
    public R<DevelopAgentDetailDTO> developAgentDetail(Long id) {
        return R.ok(agentService.developAgentDetail(id));
    }

    @PostMapping(value = "/develop/debug")
    @Operation(summary = "智能体调试")
    @SaCheckPermission4FytecClient(value = "agent:develop:debug", orRole = "admin")
    public void debug(HttpServletResponse response, @Validated @RequestBody AgentDebugDTO dto) {
        agentService.debug(response, dto);
    }

    @PostMapping(value = "/develop/history")
    @Operation(summary = "智能体调试历史")
    @SaCheckPermission4FytecClient(value = "agent:develop:history", orRole = "admin")
    public R<List<Map<String, Object>>> findDevelopHistory(@NotNull(message = "智能体ID不能为空") Long agentId,
                                                           Long messageId,
                                                           Page<AgentDevelopHistory> page) {
        return R.ok(agentService.findDevelopHistory(page, agentId, messageId));
    }

    @PostMapping(value = "/develop/history/clear")
    @Operation(summary = "清除智能体调试历史")
    @SaCheckPermission4FytecClient(value = "agent:develop:clear", orRole = "admin")
    public R<Page<AgentDevelopHistory>> clearDevelopHistory(@NotNull(message = "智能体ID不能为空") Long agentId) {
        agentService.clearDevelopHistory(agentId);
        return R.ok();
    }

    @PostMapping(value = "/develop/publish")
    @Operation(summary = "发布智能体")
    @SaCheckPermission4FytecClient(value = "agent:develop:publish", orRole = "admin")
    public R<?> publishAgent(@Validated @RequestBody PublishAgentDTO dto) {
        agentService.publishAgent(dto);
        return R.ok();
    }

    @PostMapping(value = "/publish/execute")
    @Operation(summary = "执行发布智能体")
    @SaCheckPermission4FytecClient(value = "agent:execute", orRole = "admin")
    public void executePublishedAgent(HttpServletResponse response, @Validated @RequestBody AgentExecuteDTO dto) {
        agentService.executePublishedAgent(response, dto);
    }

    @GetMapping(value = "/publish/detail")
    @Operation(summary = "发布智能体详情")
    @SaCheckPermission4FytecClient(value = "agent:publish:detail", orRole = "admin")
    public R<PublishAgentDetailDTO> publishAgentDetail(Long agentPublishId, Long agentId) {
        return R.ok(agentService.publishAgentDetail(agentPublishId, agentId));
    }

    @GetMapping(value = "/publish/list")
    @Operation(summary = "发布智能体列表")
    @SaCheckPermission4FytecClient(value = "agent:execute:list", orRole = "admin")
    public R<Page<ApplicationAgentDTO>> queryPublishedAgentList(AgentQueryDTO dto, Page<ApplicationAgentDTO> page) {
        dto.setCreateBy(StpClientUserUtil.getLoginIdAsLong());
        return R.ok(agentService.queryPublishedAgentList(dto, page));
    }

    @GetMapping(value = "/publish/history")
    @Operation(summary = "智能体对话历史")
    @SaCheckPermission4FytecClient(value = "agent:execute:history", orRole = "admin")
    public R<List<Map<String, Object>>> queryPublishedAgentHistory(@NotNull(message = "智能体会话ID不能为空") String conversationId,
                                                                   Long agentPublishId,
                                                                   Long messageId,
                                                                   Page<AgentPublishHistory> page) {
        return R.ok(agentService.queryPublishedAgentHistory(page, conversationId, agentPublishId, messageId, StpClientUserUtil.getLoginIdAsString()));
    }

    @GetMapping(value = "/publish/history/clear")
    @Operation(summary = "清除智能体历史")
    @SaCheckPermission4FytecClient(value = "agent:execute:clear", orRole = "admin")
    public R<Void> clearPublishHistory(@NotNull(message = "智能体会话ID不能为空") String conversationId,
                                       Long agentPublishId) {
        agentService.clearPublishHistory(conversationId, agentPublishId, StpClientUserUtil.getLoginIdAsString());
        return R.ok();
    }

    // 智能体流程节点的执行历史
    @GetMapping(value = "/flow/history/page")
    @Operation(summary = "管理平台-获取智能体历史执行情况记录")
    @SaCheckPermission4FytecClient(value = "agent:flow:history:page", orRole = "admin")
    public R<Page<AgentFlowHistoryDTO>> queryFlowAgentHistory(AgentFlowHistoryQueryDTO dto, Page<AgentPublishHistory> page) {
        return R.ok(agentHistoryService.queryFlowAgentHistory(page, dto));
    }

    @GetMapping(value = "/flow/history/detail")
    @Operation(summary = "管理平台-获取智能体流程的")
    @SaCheckPermission4FytecClient(value = "agent:flow:history:detail", orRole = "admin")
    public R<List<AgentFlowHistoryDTO>> queryFlowAgentHistoryDetailList(@NotBlank(message = "历史id不能为空") Long historyId) {
        return R.ok(agentHistoryService.queryFlowAgentHistoryDetailList(historyId));
    }
}
