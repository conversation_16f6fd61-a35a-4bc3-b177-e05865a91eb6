package com.fytec.controller.quartz;


import cn.hutool.core.exceptions.ValidateException;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.QuartzBean;
import com.fytec.entity.quartz.QuartzJob;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.quartz.QuartzService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Scheduler;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

@Slf4j
@Validated
@Tag(name = "定时调度管理")
@RequestMapping("/api/quartz/")
@RestController
@RequiredArgsConstructor
public class QuartzController {

    private final Scheduler scheduler;

    private final QuartzService quartzService;


    //新增执行定时任务
    @PostMapping(value = "add")
    @Operation(summary = "新增执行定时任务")
    @SaCheckPermission4FytecClient(value = "quartz:add", orRole = "admin")
    public R<?> add(@Validated @RequestBody QuartzBean quartzBean) {
        quartzService.createScheduleJob(scheduler,quartzBean);
        return R.ok();
    }

    //修改执行定时任务
    @PostMapping(value = "update")
    @Operation(summary = "修改执行定时任务")
    @SaCheckPermission4FytecClient(value = "quartz:update", orRole = "admin")
    public R<?> update(@Validated @RequestBody QuartzBean quartzBean) {
        if (quartzBean.getId() == null || quartzBean.getId() == 0) {
            throw new ValidateException("id不能为空");
        }
        quartzService.updateScheduleJob(scheduler,quartzBean);
        return R.ok();
    }

    //暂停执行定时任务
    @GetMapping(value = "pause")
    @Operation(summary = "暂停执行定时任务")
    @SaCheckPermission4FytecClient(value = "quartz:pause", orRole = "admin")
    public R<?> pause(@NotNull(message = "id不能为空") Integer id) {
        quartzService.pauseScheduleJob(scheduler,id);
        return R.ok();
    }

    //恢复执行定时任务
    @GetMapping(value = "resume")
    @Operation(summary = "恢复执行定时任务")
    @SaCheckPermission4FytecClient(value = "quartz:resume", orRole = "admin")
    public R<?> resume(@NotNull(message = "id不能为空") Integer id) {
        quartzService.resumeScheduleJob(scheduler,id);
        return R.ok();
    }

    //删除执行定时任务
    @GetMapping(value = "delete")
    @Operation(summary = "删除执行定时任务")
    @SaCheckPermission4FytecClient(value = "quartz:delete", orRole = "admin")
    public R<?> delete(@NotNull(message = "id不能为空") Integer id) {
        quartzService.deleteScheduleJob(scheduler,id);
        return R.ok();
    }

    //立即执行定时任务
    @GetMapping(value = "run")
    @Operation(summary = "立即执行定时任务")
    @SaCheckPermission4FytecClient(value = "quartz:run", orRole = "admin")
    public R<?> run(@NotNull(message = "id不能为空") Integer id) {
        quartzService.runScheduleJob(scheduler,id);
        return R.ok();
    }

    //获取所有定时任务
    @GetMapping(value = "query")
    @Operation(summary = "获取所有定时任务")
    @SaCheckPermission4FytecClient(value = "quartz:query", orRole = "admin")
    public R<Page<QuartzJob>> queryAll(String name, Page page) {
        // 获取所有定时任务列表
        return R.ok(quartzService.query(name,page));
    }

}
