package com.fytec.controller.external;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.external.ExternalBillingDailyStatsQueryDTO;
import com.fytec.dto.external.ExternalBillingDailyStatsResultDTO;
import com.fytec.service.external.ExternalBillingDailyStatsService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

/**
 * 能力服务每日使用统计表 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/external/billing-stats")
@Tag(name = "外部计费统计", description = "能力服务每日使用统计相关接口")
@RequiredArgsConstructor
public class ExternalBillingDailyStatsController {

    private final ExternalBillingDailyStatsService externalBillingDailyStatsService;

    @PostMapping("/generate")
    @Operation(summary = "生成每日统计数据", description = "根据指定日期生成外部服务使用统计数据")
    public R<Void> generateDailyStats(
            @Parameter(description = "统计日期，格式：yyyy-MM-dd")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate statsDate) {
        externalBillingDailyStatsService.generateDailyStats(statsDate);
        return R.ok();
    }

    @GetMapping("/page")
    @Operation(summary = "分页查询外部计费每日统计数据", description = "根据日期范围查询统计数据，按服务类型分组汇总")
    public R<Page<ExternalBillingDailyStatsResultDTO>> queryBillingStatsPage(
            Page<ExternalBillingDailyStatsResultDTO> page,
            ExternalBillingDailyStatsQueryDTO queryDTO) {
        Page<ExternalBillingDailyStatsResultDTO> result = externalBillingDailyStatsService.queryBillingStatsPage(page, queryDTO);
        return R.ok(result);
    }
}
