package com.fytec.controller.external;

import com.fytec.service.external.StreamIseService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.Map;

/**
 * 流式语音评测控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/ise-stream/")
@Tag(name = "讯飞语音评测", description = "讯飞语音评测相关接口")
@RequiredArgsConstructor
public class XfyunStreamIseController {
    
    private final StreamIseService streamIseService;
    
    @PostMapping("/evaluate")
    @Operation(summary = "音频文件评测", description = "上传音频文件和评测文本进行流式评测，返回SSE流")
    public SseEmitter evaluateAudioFile(
            @Parameter(description = "评测文本") @RequestParam("text") String text,
            @Parameter(description = "音频文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "超时时间（毫秒）") @RequestParam(value = "timeout", defaultValue = "600000") Long timeout) {
        return streamIseService.evaluateAudioFile(text, file, timeout);
    }
    
    @PostMapping("/evaluate-sync")
    @Operation(summary = "音频文件评测（同步）", description = "上传音频文件和评测文本进行评测，一次性返回完整结果列表")
    public R<Object> evaluateAudioFileSync(
            @Parameter(description = "评测文本") @RequestParam("text") String text,
            @Parameter(description = "音频文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "超时时间（毫秒）") @RequestParam(value = "timeout", defaultValue = "600000") Long timeout) {
        try {
            Object result = streamIseService.evaluateAudioFileSync(text, file, timeout);
            return R.ok(result, "语音评测成功");
        } catch (Exception e) {
            log.error("语音评测失败: {}", e.getMessage(), e);
            return R.failed("语音评测失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/status")
    @Operation(summary = "检查服务状态", description = "检查语音评测服务的可用性")
    public Map<String, Object> checkServiceStatus() {
        return Map.of(
            "provider", streamIseService.getProviderName(),
            "available", streamIseService.isAvailable(),
            "timestamp", System.currentTimeMillis()
        );
    }
}