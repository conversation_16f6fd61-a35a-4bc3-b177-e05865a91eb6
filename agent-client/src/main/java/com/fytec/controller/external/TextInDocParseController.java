package com.fytec.controller.external;

import com.fytec.aspect.annotation.ExternalServiceLog;
import com.fytec.constant.ExternalConstants;
import com.fytec.dto.external.TextInParseRequestDTO;
import com.fytec.service.external.DocParseService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * 文档解析控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/doc-parse/")
@Tag(name = "文档解析", description = "文档解析相关接口")
@RequiredArgsConstructor
public class TextInDocParseController {

    private final DocParseService<TextInParseRequestDTO> docParseService;

    @PostMapping("/parse-file")
    @Operation(summary = "解析文档（本地文件）", description = "上传本地文件进行文档解析")
    @ExternalServiceLog(
            serviceType = ExternalConstants.ServiceType.OCR_GENERAL,
            contents = @ExternalServiceLog.Path2Type(
                    contentPath = "$.data.result.pages[*].content[*].text"),
            dataMode = ExternalServiceLog.DataMode.RESPONSE
    )
    public R<Object> parseDocument(@RequestParam("file") MultipartFile file, TextInParseRequestDTO requestDTO) {
        try {
            Object result = docParseService.parseDocument(file, requestDTO);
            return R.ok(result, "文档解析成功");
        } catch (Exception e) {
            log.error("文档解析失败: {}", e.getMessage(), e);
            return R.failed("文档解析失败: " + e.getMessage());
        }
    }

    @PostMapping("/parse-url")
    @Operation(summary = "解析文档（URL文件）", description = "通过URL解析在线文件")
    @ExternalServiceLog(
            serviceType = ExternalConstants.ServiceType.OCR_GENERAL,
            contents = @ExternalServiceLog.Path2Type(
                    contentPath = "$.data.result.pages[*].content[*].text"),
            dataMode = ExternalServiceLog.DataMode.RESPONSE
    )
    public R<Object> parseDocumentByUrl(TextInParseRequestDTO requestDTO) {
        try {
            String url = requestDTO.getFileUrl();
            Object result = docParseService.parseDocumentByUrl(url, requestDTO);
            return R.ok(result, "文档解析成功");
        } catch (Exception e) {
            log.error("文档解析失败: {}", e.getMessage(), e);
            return R.failed("文档解析失败: " + e.getMessage());
        }
    }

    @GetMapping("/status")
    @Operation(summary = "检查服务状态", description = "检查文档解析服务的可用性")
    public R<Map<String, Object>> checkServiceStatus() {
        Map<String, Object> status = Map.of(
                "provider", docParseService.getProviderName(),
                "available", docParseService.isAvailable(),
                "timestamp", System.currentTimeMillis()
        );
        return R.ok(status, "服务状态查询成功");
    }
}