package com.fytec.controller.upload;

import com.fytec.dto.UploadResultDTO;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.util.MinioUtil;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.validation.ValidationException;
import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.UUID;

@Slf4j
@Validated
@Tag(name = "minio上传下载管理")
@RequestMapping("/api/minio")
@RestController
@AllArgsConstructor
public class MinioController {
    @Autowired
    private MinioUtil minioUtil;

    /**
     * 上传文件
     */
    @PostMapping("/upload")
    @ResponseBody
    @Operation(summary = "上传")
    @SaCheckPermission4FytecClient(value = "file:upload", orRole = "admin")
    public R uploadFile(final String _fileName, HttpServletRequest request) throws Exception {
        List<UploadResultDTO> result = new ArrayList<UploadResultDTO>();

        MultipartHttpServletRequest mRequest = (MultipartHttpServletRequest) request;
        Iterator<String> iter = mRequest.getFileNames();
        while (iter.hasNext()) {
            MultipartFile file = mRequest.getFile(iter.next());
            String fileName = null;
            if (StringUtils.isNotEmpty(_fileName)) {
                fileName = _fileName;
            } else {
                fileName = file.getOriginalFilename();
            }
            //获取文件类型
            String extName;
            try {
                extName = fileName.substring(fileName.lastIndexOf("."))
                        .toLowerCase();
                extName = extName.replace(".", "");
            } catch (Exception e) {
                extName = "";
            }
            String fileId = UUID.randomUUID().toString();
            fileId = fileId+"."+extName;
            minioUtil.upload(file, fileId);
            String url = minioUtil.getUrl(fileId);

            UploadResultDTO uploadResult = new UploadResultDTO();
            uploadResult.setUrl(url);
            uploadResult.setType(extName);
            uploadResult.setSize(file.getSize());
            uploadResult.setFileId(fileId);
            uploadResult.setFileName(fileName);
            result.add(uploadResult);
        }
        if (result.isEmpty()) {
            throw new ValidationException("文件为空");
        }
        return R.ok(result);
    }

    /**
     * 下载文件
     */
    @GetMapping("/download")
    public void download(@NotBlank(message = "文件ID不能为空")String fileId,
                         @NotBlank(message = "文件名称不能为空")String fileName,
                         HttpServletResponse response) {
        minioUtil.download(response, fileId, fileName);
    }
}
