package com.fytec.controller.upload;


import com.fytec.config.UploadProperties;
import com.fytec.dto.UploadResultDTO;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.util.FileUtil;
import com.fytec.util.MinioUtil;
import com.fytec.util.R;
import com.fytec.util.UUIDGenerator;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.validation.ValidationException;
import java.awt.image.BufferedImage;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;

@Slf4j
@Validated
@Tag(name = "上传下载管理")
@RequestMapping("/api/upload")
@RestController
@AllArgsConstructor
public class FileUploadController {

    public static final List<String> fileType = Arrays.asList("md", "txt", "zip", "jpg", "png", "gif", "jpeg", "pdf", "doc", "docx",
            "xls", "xlsx", "ppt", "pptx", "mp4", "wmv", "rmvb", "mpg", "mpeg", "3gp", "mov", "m4v", "avi", "flv", "ofd");

    private final UploadProperties uploadProperties;

    private final MinioUtil minioUtil;

    @PostMapping("/file")
    @Operation(summary = "上传")
    @SaCheckPermission4FytecClient(value = "file:upload", orRole = "admin")
    public R uploadFile(final String _fileName, HttpServletRequest request) throws Exception {
        List<UploadResultDTO> result = new ArrayList<UploadResultDTO>();

        MultipartHttpServletRequest mRequest = (MultipartHttpServletRequest) request;
        Iterator<String> iter = mRequest.getFileNames();
        while (iter.hasNext()) {
            MultipartFile file = mRequest.getFile(iter.next());
            String fileName = null;
            if (StringUtils.isNotEmpty(_fileName)) {
                fileName = _fileName;
            } else {
                fileName = file.getOriginalFilename();
            }
            //获取文件类型
            String extName;
            try {
                extName = fileName.substring(fileName.lastIndexOf("."))
                        .toLowerCase();
                extName = extName.replace(".", "");
            } catch (Exception e) {
                extName = "";
            }
            if (!fileType.contains(extName.toLowerCase())) {
                throw new ValidationException("文件类型格式不正确");
            }

            UploadResultDTO uploadResult = new UploadResultDTO();
            if ("jpg".equals(extName)
                    || "png".equals(extName)
                    || "gif".equals(extName)
                    || "jpeg".equals(extName)) {
                BufferedImage image = Thumbnails.of(file.getInputStream()).scale(1).asBufferedImage();

                // 获取图片的宽度和高度
                int width = image.getWidth();
                int height = image.getHeight();

                if (width < 14 || height < 14) {
                    throw new ValidationException("图片尺寸太小，请上传大于14x14的图片");
                }

                uploadResult.setHeight(String.valueOf(height));
                uploadResult.setWidth(String.valueOf(width));

            }

            byte[] bytes = file.getBytes();
            uploadResult.setFileName(fileName);
            fileName = UUIDGenerator.getUUID() + FileUtil.getFileEndfix(fileName);
            String url = "";
            String filePath = "";
            if("minio".equalsIgnoreCase(uploadProperties.getStorage())){
                minioUtil.upload(file, fileName);
                url = minioUtil.getUrl(fileName);
                filePath = minioUtil.getPath(fileName);
            }else{
                String path = uploadProperties.getLocalServerPath();
                filePath = FileUtil.saveFile(path, bytes, fileName, uploadProperties.getFileGroup());
                url = uploadProperties.getFileServerPath() + filePath;
            }
            uploadResult.setUrl(url);
            uploadResult.setPath(filePath);
            uploadResult.setFileId(fileName);
            uploadResult.setType(extName);
            uploadResult.setSize(file.getSize());
            result.add(uploadResult);
        }
        if (result.isEmpty()) {
            throw new ValidationException("文件为空");
        }
        return R.ok(result);
    }
}
