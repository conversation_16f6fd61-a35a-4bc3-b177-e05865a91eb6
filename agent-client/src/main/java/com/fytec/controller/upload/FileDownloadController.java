package com.fytec.controller.upload;

import cn.hutool.core.util.StrUtil;
import com.fytec.dto.file.FileConvertDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import net.coobird.thumbnailator.geometry.Positions;
import org.jodconverter.core.DocumentConverter;
import org.jodconverter.core.document.DefaultDocumentFormatRegistry;
import org.jodconverter.core.document.DocumentFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.imageio.ImageIO;
import javax.validation.ValidationException;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URI;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

@Slf4j
@Validated
@Tag(name = "上传下载管理")
@RequestMapping("/api/download")
@RestController
@RequiredArgsConstructor
public class FileDownloadController {

    @Resource
    private DocumentConverter documentConverter;

    @GetMapping("/thumbnail")
    @Operation(summary = "缩略图")
    @SneakyThrows
    public void thumbnailFile(HttpServletResponse response, final String filePath,
                              final int width, final int height) {
        URI uri = new URI(filePath);
        URL url = uri.toURL();

        BufferedImage resizedImage = Thumbnails.of(url)
                .size(width, height)
                .crop(Positions.CENTER)
                .asBufferedImage();

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        ImageIO.write(resizedImage, "png", byteArrayOutputStream);

//        response.reset();
        response.setContentType("application/octet-stream");
        try (OutputStream out = new BufferedOutputStream(response.getOutputStream())) {
            byteArrayOutputStream.writeTo(out);
            out.flush();
        }
    }

    @GetMapping("/preview")
    @Operation(summary = "预览")
    @SneakyThrows
    public void previewFile(HttpServletResponse response, final String filePath, String outputFormat) {
        URI uri = new URI(filePath);
        URL url = uri.toURL();

        String sourceFilename = getFilenameFromUrl(filePath);
        DocumentFormat sourceFormat = DefaultDocumentFormatRegistry.getFormatByExtension(
                getExtension(sourceFilename));

        if (StrUtil.isBlank(outputFormat)) {
            outputFormat = "pdf";
        }
        DocumentFormat targetFormat = DefaultDocumentFormatRegistry.getFormatByExtension(outputFormat);
        if (sourceFormat == null || targetFormat == null) {
            throw new ValidationException("不支持的文件格式");
        }

//        response.reset();
        response.setContentType(targetFormat.getMediaType());
//        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment; filename=\"" +
                changeExtension(sourceFilename, targetFormat.getExtension()) + "\"");

        try (InputStream inputStream = url.openStream()) {
            documentConverter.convert(inputStream).as(sourceFormat).to(response.getOutputStream()).as(targetFormat).execute();
            response.getOutputStream().flush();
        }
    }


    @PostMapping("/convert")
    @Operation(summary = "格式转换")
    @SneakyThrows
    public void convertFile(HttpServletResponse response, @Validated @RequestBody FileConvertDTO dto) {
        if (StrUtil.isBlank(dto.getFileContent())) {
            throw new ValidationException("文件内容为空");
        }

        DocumentFormat sourceFormat = DefaultDocumentFormatRegistry.getFormatByExtension(
                getExtension(dto.getFileName()));

        InputStream inputStream = new ByteArrayInputStream(dto.getFileContent().getBytes(StandardCharsets.UTF_8));
        String outputFormat = "pdf";
        if (StrUtil.isNotBlank(dto.getOutputFormat())) {
            outputFormat = dto.getOutputFormat();
        }
        DocumentFormat targetFormat = DefaultDocumentFormatRegistry.getFormatByExtension(outputFormat);
        if (sourceFormat == null || targetFormat == null) {
            throw new ValidationException("不支持的文件格式");
        }

        response.setContentType(targetFormat.getMediaType());
        String fileName = changeExtension(dto.getFileName(), targetFormat.getExtension());
        response.setHeader("Content-Disposition", "attachment; filename=\"" + URLEncoder.encode(fileName, StandardCharsets.UTF_8) + "\"");
        response.addHeader("filename", URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        response.addHeader("Access-Control-Expose-Headers", "filename");

        documentConverter.convert(inputStream).as(sourceFormat).to(response.getOutputStream()).as(targetFormat).execute();
        response.getOutputStream().flush();
    }


    private String getFilenameFromUrl(String url) {
        String[] parts = url.split("/");
        return parts[parts.length - 1];
    }

    private String getExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return filename.substring(lastDotIndex + 1);
        }
        return "";
    }

    private String changeExtension(String filename, String newExtension) {
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return filename.substring(0, lastDotIndex + 1) + newExtension;
        }
        return filename + "." + newExtension;
    }


    @GetMapping("/file")
    @Operation(summary = "下载")
    public void downloadFile(final String fileName, final String filePath, HttpServletResponse response) throws IOException {
        URL url = new URL(filePath);
        URLConnection uc = url.openConnection();
        String contentType = uc.getContentType();
        int contentLength = uc.getContentLength();
        if (contentType.startsWith("text/") || contentLength == -1) {
            throw new ValidationException("文件为空");
        }
        try (InputStream raw = uc.getInputStream()) {
            InputStream in = new BufferedInputStream(raw);
            byte[] data = new byte[contentLength];
            int offset = 0;
            while (offset < contentLength) {
                int bytesRead = in.read(data, offset, data.length - offset);
                if (bytesRead == -1) {
                    break;
                }
                offset += bytesRead;
            }
            if (offset != contentLength) {
                throw new ValidationException("文件为空");
            }
//            response.reset();
            response.addHeader("Content-Disposition", "attachment;filename=\"" + URLEncoder.encode(fileName, "UTF-8") + "\"");
            response.addHeader("Content-Length", "" + contentLength);
            response.setContentType("application/octet-stream");
            try (OutputStream out = new BufferedOutputStream(response.getOutputStream())) {
                out.write(data);
                out.flush();
            }
        }
    }
}
