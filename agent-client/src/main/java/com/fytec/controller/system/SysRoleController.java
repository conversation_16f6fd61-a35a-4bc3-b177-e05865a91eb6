package com.fytec.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.system.*;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.system.SysRoleService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


@Slf4j
@Validated
@Tag(name = "角色管理")
@RequestMapping("/api/role")
@RestController
@RequiredArgsConstructor
public class SysRoleController {

    private final SysRoleService sysRoleService;

    @PostMapping("/add")
    @Operation(summary = "新增角色")
    @SaCheckPermission4FytecClient(value = "role:add", orRole = {"admin"})
    public R<Void> addRole(@RequestBody @Validated SysRoleCreateDTO sysRoleCreateDTO) {
        sysRoleService.addRole(sysRoleCreateDTO);
        return R.ok();
    }

    @PostMapping("/edit")
    @Operation(summary = "编辑角色")
    @SaCheckPermission4FytecClient(value = "role:update", orRole = {"admin"})
    public R<Void> updateRole(@RequestBody @Validated SysRoleUpdateDTO sysRoleUpdateDTO) {
        sysRoleService.updateRole(sysRoleUpdateDTO);
        return R.ok();
    }

    @GetMapping("/delete")
    @Operation(summary = "删除角色")
    @SaCheckPermission4FytecClient(value = "role:delete", orRole = {"admin"})
    public R<Void>  deleteRole(@NotBlank(message = "角色id不能为空") Long id) {
        sysRoleService.deleteRole(id);
        return R.ok();
    }


    @GetMapping("/page")
    @Operation(summary = "查询角色列表")
    @SaCheckPermission4FytecClient(value = "role:search", orRole = {"admin"})
    public R<Page<SysRoleListDTO>> searchRole(SysRoleSearchDTO sysRoleSearchDTO, Page<SysRoleListDTO> page) {
        return R.ok(sysRoleService.searchRole(sysRoleSearchDTO, page));
    }

    @GetMapping("detail")
    @Operation(summary = "查询角色详情")
    @SaCheckPermission4FytecClient(value = "role:detail", orRole = {"admin"})
    public R<SysRoleDetailDTO> getRole(@NotBlank(message = "角色id不能为空") Long id) {
        return R.ok(sysRoleService.getRoleDetail(id));
    }
}
