package com.fytec.controller.system;

import com.fytec.dto.system.*;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.system.SysOrganizationService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@Slf4j
@Validated
@Tag(name = "用户管理")
@RequestMapping("/api/group")
@RestController
@RequiredArgsConstructor
public class SysOrganizationController {

    private final SysOrganizationService sysOrganizationService;

    @PostMapping("/add")
    @Operation(summary = "创建组")
    @SaCheckPermission4FytecClient(value = "org:add", orRole = {"admin"})
    public R<Void> addOrganization(@RequestBody @Validated SysOrganizationCreateDTO dto) {
        sysOrganizationService.addOrganization(dto);
        return R.ok();
    }

    @PostMapping("/edit")
    @Operation(summary = "编辑组")
    @SaCheckPermission4FytecClient(value = "org:edit", orRole = {"admin"})
    public R<Void> updateOrganization(@RequestBody @Validated SysOrganizationUpdateDTO dto) {
        sysOrganizationService.updateOrganization(dto);
        return R.ok();
    }

    @GetMapping("/delete")
    @Operation(summary = "删除组")
    @SaCheckPermission4FytecClient(value = "org:delete", orRole = {"admin"})
    public R<Void> deleteOrganization(@NotBlank(message = "用户id不能为空") Long id) {
        sysOrganizationService.deleteOrganization(id);
        return R.ok();
    }

    @GetMapping("/page")
    @Operation(summary = "组列表")
    @Parameters({
            @Parameter(name = "dto", description = "搜索参数", schema = @Schema(implementation = SysOrganizationQueryDTO.class))
    })
    @SaCheckPermission4FytecClient(value = "org:list", orRole = {"admin"})
    public R<List<SysOrganizationTreeDTO>> queryOrganization(SysOrganizationQueryDTO dto) {
        return R.ok(sysOrganizationService.queryOrganization(dto));
    }

    @GetMapping("/detail")
    @Operation(summary = "组详情")
    @Parameter(name = "id", description = "组ID")
    @SaCheckPermission4FytecClient(value = "org:detail", orRole = {"admin"})
    public R<SysOrganizationDetailDTO> getOrganizationDetail(@NotBlank(message = "用户id不能为空") Long id) {
        return R.ok(sysOrganizationService.getOrganizationDetail(id));
    }
}
