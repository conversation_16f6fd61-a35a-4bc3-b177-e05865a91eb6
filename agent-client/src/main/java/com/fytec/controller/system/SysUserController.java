package com.fytec.controller.system;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.config.validate.Mandatory;
import com.fytec.config.validate.Optional;
import com.fytec.dto.CurrentUserDTO;
import com.fytec.dto.system.*;
import com.fytec.satoken.StpClientUserUtil;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.system.SysUserService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


@Slf4j
@Validated
@Tag(name = "用户管理")
@RequestMapping("/api/user")
@RestController
@RequiredArgsConstructor
public class SysUserController {

    private final SysUserService sysUserService;

    @PostMapping("/add")
    @Operation(summary = "创建用户")
    @SaCheckPermission4FytecClient(value = "user:add", orRole = {"admin"})
    public R<Void> addUser(@RequestBody @Validated({Mandatory.class, Optional.class}) SysUserCreateDTO sysUserCreateDTO) {
        sysUserService.addUser(sysUserCreateDTO);
        return R.ok();
    }


    @PostMapping("/edit")
    @Operation(summary = "编辑用户")
    @SaCheckPermission4FytecClient(value = "user:edit", orRole = {"admin"})
    public R<Void> updateUser(@RequestBody @Validated({Mandatory.class}) SysUserUpdateDTO updateUserDTO) {
        sysUserService.updateUser(updateUserDTO);
        return R.ok();
    }

    @PostMapping("/edit/password")
    @Operation(summary = "编辑用户")
    @SaCheckPermission4FytecClient(value = "user:edit.password", orRole = {"admin"})
    public R<Void> updateUserPassword(@RequestBody @Validated SysUserPasswordUpdateDTO dto) {
        dto.setId(StpClientUserUtil.getLoginIdAsString());
        sysUserService.updateUserPassword(dto);
        return R.ok();
    }

    @PostMapping("/edit/image")
    @Operation(summary = "修改用户头像")
    @SaCheckPermission4FytecClient(value = "user:current:user", orRole = "admin")
    public R<CurrentUserDTO> editUserImage(@RequestBody @Validated SysUserImageUpdateDTO dto) {
        dto.setId(StpClientUserUtil.getLoginIdAsString());
        sysUserService.updateUserImage(dto);
        return R.ok();
    }

    @GetMapping("/delete")
    @Operation(summary = "删除用户")
    @SaCheckPermission4FytecClient(value = "user:delete", orRole = {"admin"})
    public R<Void> deleteUser(@NotBlank(message = "用户id不能为空") Long id) {
        sysUserService.deleteUser(id);
        return R.ok();
    }

    @GetMapping("/page")
    @Operation(summary = "用户列表")
    @Parameters({
            @Parameter(name = "searchSysUserDTO", description = "搜索参数", schema = @Schema(implementation = SysUserQueryDTO.class))
    })
    @SaCheckPermission4FytecClient(value = "user:list", orRole = {"admin"})
    public R<Page<SysUserListDTO>> queryUser(SysUserQueryDTO sysUserQueryDTO, Page<SysUserListDTO> page) {
        return R.ok(sysUserService.queryUser(page, sysUserQueryDTO));
    }

    @GetMapping("/detail")
    @Operation(summary = "用户详情")
    @Parameter(name = "id", description = "用户ID")
    @SaCheckPermission4FytecClient(value = "user:detail", orRole = {"admin"})
    public R<SysUserDetailDTO> getUserDetail(@NotBlank(message = "用户id不能为空") Long id) {
        return R.ok(sysUserService.getUserDetail(id));
    }
}
