package com.fytec.controller.system;


import com.fytec.dto.system.DictValueDTO;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.system.SysDictService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/dict")
@Tag(name = "字典管理")
@Validated
@AllArgsConstructor
public class SysDictController {


    private final SysDictService sysDictService;

    @GetMapping("/value")
    @Operation(summary = "获取字典值")
    @SaCheckPermission4FytecClient(value = "dict:search", orRole = "admin")
    public R<List<DictValueDTO>> searchDictValue(@NotBlank(message = "{error.dictTypeCode.blank}") String typeCode) {
        return R.ok(sysDictService.searchDictValue(typeCode));
    }

    @GetMapping("/dict/value/multiple")
    @Operation(summary = "获取多个字典值")
    @SaCheckPermission4FytecClient(value = "dict:multiple", orRole = "admin")
    public R<Map<String, List<DictValueDTO>>> searchMultipleDictValue(@NotBlank(message = "{error.dictTypeCode.blank}") String typeCode) {
        return R.ok(sysDictService.searchMultipleDictValue(typeCode));
    }
}
