package com.fytec.controller.system;

import com.fytec.dto.system.SysMenuCreateDTO;
import com.fytec.dto.system.SysMenuDetailDTO;
import com.fytec.dto.system.SysMenuTreeDTO;
import com.fytec.dto.system.SysMenuUpdateDTO;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.system.SysMenuService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@Slf4j
@Validated
@Tag(name = "菜单管理")
@RequestMapping("/api/menu")
@RestController
@RequiredArgsConstructor
public class SysMenuController {

    private final SysMenuService sysMenuService;

    @PostMapping("/add")
    @Operation(summary = "创建菜单")
    @SaCheckPermission4FytecClient(value = "menu:add", orRole = {"admin"})
    public R<Void> addMenu(@RequestBody @Validated SysMenuCreateDTO sysMenuCreateDTO) {
        sysMenuService.addMenu(sysMenuCreateDTO);
        return R.ok();
    }

    @PostMapping("/edit")
    @Operation(summary = "编辑菜单")
    @SaCheckPermission4FytecClient(value = "menu:update", orRole = {"admin"})
    public R<Void> updateMenu(@RequestBody @Validated SysMenuUpdateDTO sysMenuUpdateDTO) {
        sysMenuService.updateMenu(sysMenuUpdateDTO);
        return R.ok();
    }

    @GetMapping("/delete")
    @Operation(summary = "编辑菜单")
    @SaCheckPermission4FytecClient(value = "menu:delete", orRole = {"admin"})
    @Parameters({
            @Parameter(name = "id", description = "租户菜单ID", required = true)
    })
    public R<Void> deleteMenu(@NotBlank(message = "租户菜单ID不能为空") Long id) {
        sysMenuService.deleteMenu(id);
        return R.ok();
    }


    @GetMapping("/list")
    @Operation(summary = "查询菜单列表")
    @SaCheckPermission4FytecClient(value = "menu:list", orRole = {"admin"})
    public R<List<SysMenuTreeDTO>> searchMenu() {
        return R.ok(sysMenuService.searchMenu());
    }


    @GetMapping("/detail")
    @Operation(summary = "查询菜单详情")
    @SaCheckPermission4FytecClient(value = "menu:detail", orRole = {"admin"})
    @Parameters({
            @Parameter(name = "id", description = "菜单ID", required = true)
    })
    public R<SysMenuDetailDTO> getMenuDetail(@NotBlank(message = "租户菜单ID不能为空") Long id) {
        return R.ok(sysMenuService.getMenuDetail(id));
    }
}
