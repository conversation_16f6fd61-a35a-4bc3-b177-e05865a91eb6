package com.fytec.controller.model;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.llm.*;
import com.fytec.entity.llm.AiModel;
import com.fytec.entity.llm.AiModelGroup;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.model.ModelMgmtService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Validated
@Tag(name = "大模型管理")
@RequestMapping("/api/llm-model")
@RestController
@RequiredArgsConstructor
public class ModelMgmtController {
    private final ModelMgmtService modelMgmtService;

    @PostMapping(path = "/group/add")
    @Operation(summary = "添加大模型分组")
    @SaCheckPermission4FytecClient(value = "llm:model-group:add", orRole = "admin")
    public R<Long> addAiModelGroup(@RequestBody @Validated AiModelGroupCreateDTO dto) {
        return R.ok(modelMgmtService.addAiModelGroup(dto));
    }

    @PostMapping(path = "/group/update")
    @Operation(summary = "编辑大模型分组")
    @SaCheckPermission4FytecClient(value = "llm:model-group:edit", orRole = "admin")
    public R<Void> editAiModelGroup(@RequestBody @Validated AiModelGroupUpdateDTO dto) {
        modelMgmtService.editAiModelGroup(dto);
        return R.ok();
    }

    @GetMapping(path = "/group/delete")
    @Operation(summary = "删除大模型组")
    @SaCheckPermission4FytecClient(value = "llm:model-group:delete", orRole = "admin")
    @Parameter(name = "id", description = "模型组ID")
    public R<Void> deleteAiModel(Long id) {
        modelMgmtService.deleteAiModel(id);
        return R.ok();
    }


    @GetMapping(path = "/group/page")
    @Operation(summary = "大模型组列表")
    @SaCheckPermission4FytecClient(value = "llm:model-group:page", orRole = "admin")
    public R<Page<AiModelGroupListDTO>> queryAiModelGroupPaging(AiModelGroupQueryDTO dto, Page<AiModelGroupListDTO> page) {
        return R.ok(modelMgmtService.queryAiModelGroupPaging(dto, page));
    }

    @GetMapping(path = "/group/detail")
    @Operation(summary = "大模型列表")
    @SaCheckPermission4FytecClient(value = "llm:model-group:page", orRole = "admin")
    @Parameter(name = "id", description = "模型ID")
    public R<AiModelGroup> queryAiModelGroupDetail(Long id) {
        return R.ok(modelMgmtService.queryAiModelGroupDetail(id));
    }


    @PostMapping(path = "/add")
    @Operation(summary = "添加大模型")
    @SaCheckPermission4FytecClient(value = "llm:model:add", orRole = "admin")
    public R<Void> addAiModel(@RequestBody @Validated AiModelCreateDTO aiModelCreateDTO) {
        modelMgmtService.addAiModel(aiModelCreateDTO);
        return R.ok();
    }

    @PostMapping(path = "/update")
    @Operation(summary = "编辑大模型")
    @SaCheckPermission4FytecClient(value = "llm:model:edit", orRole = "admin")
    public R<Void> editAiModel(@RequestBody @Validated AiModelUpdateDTO aiModelUpdateDTO) {
        modelMgmtService.editAiModel(aiModelUpdateDTO);
        return R.ok();
    }

    @PostMapping(path = "/default")
    @Operation(summary = "默认大模型")
    @SaCheckPermission4FytecClient(value = "llm:model:edit", orRole = "admin")
    public R<Void> setDefaultAiModel(@RequestBody @Validated AiModelUpdateDTO aiModelUpdateDTO) {
        modelMgmtService.setDefaultAiModel(aiModelUpdateDTO);
        return R.ok();
    }

    @GetMapping(path = "/enable")
    @Operation(summary = "启用大模型")
    @SaCheckPermission4FytecClient(value = "llm:model:enable", orRole = "admin")
    @Parameter(name = "id", description = "模型ID")
    public R<Void> enableAiModel(Long id) {
        modelMgmtService.updateAiModelStatus(id, true);
        return R.ok();
    }

    @GetMapping(path = "/disable")
    @Operation(summary = "禁用大模型")
    @SaCheckPermission4FytecClient(value = "llm:model:disable", orRole = "admin")
    @Parameter(name = "id", description = "模型ID")
    public R<Void> disableAiModel(Long id) {
        modelMgmtService.updateAiModelStatus(id, false);
        return R.ok();
    }

    @GetMapping(path = "/page")
    @Operation(summary = "大模型列表")
    @SaCheckPermission4FytecClient(value = "llm:model:page", orRole = "admin")
    public R<Page<AiModelListDTO>> queryAiModelPaging(AiModelQueryDTO aiModelQueryDTO, Page<AiModelListDTO> page) {
        return R.ok(modelMgmtService.queryAiModelPaging(aiModelQueryDTO, page));
    }

    @GetMapping(path = "/detail")
    @Operation(summary = "大模型列表")
    @SaCheckPermission4FytecClient(value = "llm:model:page", orRole = "admin")
    @Parameter(name = "id", description = "模型ID")
    public R<AiModel> queryAiModelDetail(Long id) {
        return R.ok(modelMgmtService.queryAiModelDetail(id));
    }


}
