package com.fytec.controller.model;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.llm.AiModelListDTO;
import com.fytec.dto.llm.AiModelQueryDTO;
import com.fytec.dto.model.ModelDTO;
import com.fytec.dto.model.ModelGroupListDTO;
import com.fytec.dto.model.ModelListDTO;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.model.ModelService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@Validated
@Tag(name = "模型广场")
@RequestMapping("/api/model")
@RestController
@AllArgsConstructor
public class ModelController {
    private final ModelService modelService;

    @GetMapping(value = "/group/list")
    @Operation(summary = "查询模型列表")
    @SaCheckPermission4FytecClient(value = "model:page", orRole = "admin")
    public R<List<ModelGroupListDTO>> findModelGroup() {
        return R.ok(modelService.findModelGroup());
    }

    @GetMapping(value = "/list")
    @Operation(summary = "查询模型列表")
    @SaCheckPermission4FytecClient(value = "model:list", orRole = "admin")
    public R<List<ModelListDTO>> queryModelList(AiModelQueryDTO aiModelQueryDTO) {
        return R.ok(modelService.queryModelList(aiModelQueryDTO));
    }


    @GetMapping(value = "/model/default")
    @Operation(summary = "查询模型列表")
    @SaCheckPermission4FytecClient(value = "model:default", orRole = "admin")
    public R<ModelDTO> findDefaultModel() {
        return R.ok(modelService.findDefaultModel());
    }


}
