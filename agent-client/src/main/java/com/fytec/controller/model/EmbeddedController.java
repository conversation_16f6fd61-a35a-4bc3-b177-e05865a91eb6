package com.fytec.controller.model;

import com.fytec.entity.llm.EmbeddedModel;
import com.fytec.satoken.annotation.SaCheckPermission4FytecClient;
import com.fytec.service.model.ModelService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@Validated
@Tag(name = "向量模型管理")
@RequestMapping("/api/embedded")
@RestController
@AllArgsConstructor
public class EmbeddedController {


    private final ModelService modelService;


    @GetMapping(value = "/list")
    @Operation(summary = "查询向量模型列表")
    @SaCheckPermission4FytecClient(value = "embedded:page", orRole = "admin")
    public R<List<EmbeddedModel>> findModelGroup() {
        return R.ok(modelService.findEmbeddedModel());
    }


}
