# main.py
import json
import os
import uuid
from urllib.parse import quote, urlparse

import redis.asyncio as redis
import uvicorn
from fastapi import FastAP<PERSON>, Depends, HTTPException, BackgroundTasks
from fastapi.security import API<PERSON>eyHeader
from magic_pdf.config.enums import SupportedPdfParseMethod
from magic_pdf.data.data_reader_writer import S3DataReader, S3DataWriter
from magic_pdf.data.dataset import PymuDocDataset
from magic_pdf.model.doc_analyze_by_custom_model import doc_analyze
from pydantic import BaseModel

app = FastAPI()

API_KEY = "49219f34e54e4886a9fe4053c1403a84"
api_key_header = APIKeyHeader(name="X-API-Key", auto_error=True)


def verify_api_key(api_key: str = Depends(api_key_header)):
    if api_key != API_KEY:
        raise HTTPException(status_code=403, detail="Invalid API Key")
    return api_key


REDIS_URL = f"redis://172.18.79.21:6379/11"


# 异步 Redis 连接池
async def get_redis():
    redis_client = await redis.from_url(REDIS_URL, decode_responses=True)
    try:
        yield redis_client
    finally:
        await redis_client.aclose()


class ParseRequest(BaseModel):
    bucket_name: str
    ak: str
    sk: str
    endpoint_url: str
    show_url: str
    filename: str


async def parse_task(task_id: str, req: ParseRequest, redis_client: redis.Redis):
    bucket_name = req.bucket_name
    ak = req.ak
    sk = req.sk
    endpoint_url = req.endpoint_url
    show_url = req.show_url
    parsed_url = urlparse( req.filename)
    filename = os.path.basename(parsed_url.path)

    reader = S3DataReader('', bucket_name, ak, sk, endpoint_url)
    image_writer = S3DataWriter('', bucket_name, ak, sk, endpoint_url)
    md_writer = S3DataWriter('', bucket_name, ak, sk, endpoint_url)

    pdf_file_name = (
        f"s3://{bucket_name}/{filename}"  # replace with the real s3 path
    )

    # prepare env
    name_without_suff = os.path.basename(pdf_file_name).split(".")[0]

    # read bytes
    pdf_bytes = reader.read(pdf_file_name)  # read the pdf content

    # proc
    ## Create Dataset Instance
    ds = PymuDocDataset(pdf_bytes)

    ## inference
    if ds.classify() == SupportedPdfParseMethod.OCR:
        infer_result = ds.apply(doc_analyze, ocr=True)

        ## pipeline
        pipe_result = infer_result.pipe_ocr_mode(image_writer)

    else:
        infer_result = ds.apply(doc_analyze, ocr=False)

        ## pipeline
        pipe_result = infer_result.pipe_txt_mode(image_writer)

    pipe_result.dump_content_list(md_writer, f"{name_without_suff}_content_list.json", f'{show_url}{bucket_name}')
    content_list_content = pipe_result.get_content_list(f'{show_url}{bucket_name}')

    await redis_client.hset(task_id, mapping={"status": "completed", "result": json.dumps(content_list_content, ensure_ascii=False)})


@app.post("/parse/pdf")
async def parse_pdf(req: ParseRequest,
                    background_tasks: BackgroundTasks,
                    redis_client: redis.Redis = Depends(get_redis),
                    api_key: str = Depends(verify_api_key)):
    task_id = str(uuid.uuid4())
    background_tasks.add_task(parse_task, task_id, req, redis_client)
    await redis_client.hset(task_id, mapping={"status": "pending"})
    await redis_client.expire(task_id, 3600)
    return {"task_id": task_id}


@app.get("/parse/result/{task_id}")
async def get_result(task_id: str,
                     redis_client: redis.Redis = Depends(get_redis),
                     api_key: str = Depends(verify_api_key)):
    result = await redis_client.hgetall(task_id)
    if not result:
        raise HTTPException(status_code=404, detail="Task not found")
    return result


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=9003)
