alter table ai_resource
    add parent_id bigint null after id;

alter table ai_resource
    add is_sys tinyint null after status;


alter table ai_knowledge_doc
    add doc_source_type varchar(20) null after doc_type;

alter table ai_knowledge_doc
    add reason varchar(50) null after status;

alter table ai_knowledge_doc
    add default_segment tinyint(1) null comment '是否默认模式' after file_size;

alter table ai_knowledge_doc
    modify file_name varchar(300) collate utf8mb4_bin null comment '知识库文件名';

alter table ai_agent_publish_history
    add model_name varchar(50) null after user_input;

alter table ai_agent_publish_history
    add knowledge_doc_info varchar(300) null after reference;

create table `agent-client`.ai_knowledge_segment_ext
(
    id          int auto_increment
        primary key,
    segment_id  bigint      null comment '段落ID',
    ext_name    varchar(40) null,
    ext_type    varchar(20) null,
    ext_content longtext    null,
    ext_order   int         null comment '排序',
    update_by   bigint      null,
    create_time datetime    null,
    update_time datetime    null,
    deleted     tinyint(1)  null,
    create_by   bigint      null
)
    comment '知识库文档分段信息' charset = utf8mb4;

create table `agent-client`.ai_note
(
    id               bigint auto_increment,
    doc_name         varchar(100) not null comment '笔记名',
    title            varchar(100) not null comment '笔记名',
    content          longtext     null comment '笔记内容',
    knowledge_doc_id bigint       null,
    deleted          tinyint(1)   null comment '是否删除',
    create_by        bigint       null,
    update_by        bigint       null,
    create_time      datetime     null,
    update_time      datetime     null,
    constraint ai_flow_id_IDX
        unique (id)
)
    comment '笔记表' charset = utf8mb4;

alter table ai_model
    add non_stream_method varchar(100) null after non_stream_url;

alter table ai_model
    add stream_method varchar(100) null after stream_url;

alter table ai_chat_history
    add application_related_function varchar(50) null after application_id;

alter table ai_knowledge_doc
    add note_id bigint null after task_id;

alter table ai_knowledge_doc
    add is_visible tinyint null after task_id;


CREATE TABLE `t_quartz_job`
(
    `id`          bigint(20) NOT NULL AUTO_INCREMENT,
    `name`        varchar(100) DEFAULT NULL COMMENT '任务名称',
    `description` varchar(200) DEFAULT NULL COMMENT '任务描述',
    `status`      varchar(100) DEFAULT NULL COMMENT '调度状态',
    `job_id`      varchar(100) DEFAULT NULL COMMENT '任务执行ID',
    `job_class`   varchar(500) DEFAULT NULL COMMENT '任务执行类',
    `cron`        varchar(100) DEFAULT NULL COMMENT '执行表达式',
    `params`      longtext COMMENT '任务参数 json',
    `create_by`   bigint(20)   DEFAULT NULL,
    `create_time` datetime     DEFAULT NULL,
    `update_by`   bigint(20)   DEFAULT NULL,
    `update_time` datetime     DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

## 2024-03-07
create table if not exists `agent-client`.ai_knowledge_group
(
    id           bigint auto_increment primary key,
    parent_id    bigint                           null comment '父节点',
    knowledge_id bigint                           null comment '父节点',
    name         varchar(100) collate utf8mb4_bin null comment '分组名称',
    type         varchar(50) collate utf8mb4_bin  null comment '分组类型',
    seq          int                              null comment '排序',
    is_sys       tinyint                          null comment '是否系统分组',
    create_by    bigint                           null,
    update_by    bigint                           null,
    create_time  datetime                         null,
    update_time  datetime                         null,
    deleted      tinyint(1)                       null
)
    comment '知识库分组' charset = utf8mb4;

alter table `agent-client`.ai_knowledge_doc
    add group_id bigint null after knowledge_id;


ALTER TABLE `agent-client`.ai_agent_publish_history
    ADD client_id varchar(100) NULL COMMENT '客户端ID';
ALTER TABLE `agent-client`.ai_agent_publish_history
    CHANGE client_id client_id varchar(100) NULL COMMENT '客户端ID' AFTER knowledge_doc_info;


ALTER TABLE `agent-client`.ai_agent_publish_history
    ADD third_user_id varchar(100) NULL COMMENT '第三方用户id';

ALTER TABLE `agent-client`.ai_agent_publish_history
    ADD agent_id bigint NULL COMMENT '智能体id';

ALTER TABLE `agent-client`.ai_agent_publish_history
    ADD flow_id varchar(100) NULL COMMENT '单次流程id';

#### 2024-03-18
alter table ai_application_basic
    add models longtext null COMMENT '应用可用模型' after agents;

alter table ai_knowledge_group
    add is_default tinyint null COMMENT '默认分组' after is_sys;

alter table ai_knowledge
    add start_time datetime null COMMENT '共享知识库期限开始' after embedded_id;

alter table ai_knowledge
    add end_time datetime null COMMENT '共享知识库期限结束' after start_time;

alter table ai_knowledge_segment
    add page_number int null COMMENT '文档分段对应页码' after doc_id;

alter table ai_knowledge
    add expire_option varchar(20) null COMMENT '共享知识库期' after embedded_id;


alter table `agent-client`.t_sys_user
    add wechat_work_user_id varchar(50) null after is_sys;

alter table `agent-client`.ai_knowledge_team_member
    add role varchar(50) null after knowledge_id;



alter table `agent-client`.ai_agent_dev
    add positive_knowledge longtext null COMMENT '知识库正反馈';

alter table `agent-client`.ai_agent_dev
    add negative_knowledge longtext null COMMENT '知识库负反馈问题';

alter table `agent-client`.ai_agent_publish
    add positive_knowledge longtext null COMMENT '知识库正反馈';

alter table `agent-client`.ai_agent_publish
    add negative_knowledge longtext null COMMENT '知识库负反馈问题';

alter table `agent-client`.ai_knowledge
    add base_type varchar(20) null COMMENT '知识库类别(默认、规则、标注、错误）';



INSERT INTO `agent-client`.t_code_value (ID, TYPE_CODE, VALUE, LABEL, DESCRIPTION, SEQ, STATUS, IS_VIS, deleted, CREATE_BY, UPDATE_BY, CREATE_TIME, UPDATE_TIME) VALUES (22, 'KNOWLEDGE_BASE_TYPE', 'common', '默认知识库', null, 1, 'A', null, 0, null, null, null, null);
INSERT INTO `agent-client`.t_code_value (ID, TYPE_CODE, VALUE, LABEL, DESCRIPTION, SEQ, STATUS, IS_VIS, deleted, CREATE_BY, UPDATE_BY, CREATE_TIME, UPDATE_TIME) VALUES (23, 'KNOWLEDGE_BASE_TYPE', 'positive', '标注正确的知识库', null, 1, 'A', null, 0, null, null, null, null);
INSERT INTO `agent-client`.t_code_value (ID, TYPE_CODE, VALUE, LABEL, DESCRIPTION, SEQ, STATUS, IS_VIS, deleted, CREATE_BY, UPDATE_BY, CREATE_TIME, UPDATE_TIME) VALUES (24, 'KNOWLEDGE_BASE_TYPE', 'rule', '规则知识库', null, 1, 'A', null, 0, null, null, null, null);
INSERT INTO `agent-client`.t_code_value (ID, TYPE_CODE, VALUE, LABEL, DESCRIPTION, SEQ, STATUS, IS_VIS, deleted, CREATE_BY, UPDATE_BY, CREATE_TIME, UPDATE_TIME) VALUES (25, 'KNOWLEDGE_BASE_TYPE', 'negative', '错误知识库', null, 1, 'A', null, 0, null, null, null, null);

ALTER TABLE `agent-client`.ai_knowledge
    ADD user_id varchar(100) NULL COMMENT '用户ID';

ALTER TABLE `agent-client`.ai_agent_basic
    ADD tags varchar(300) NULL COMMENT '标签';



alter table `agent-client`.ai_agent_publish_history
    add extra_info longtext null COMMENT '额外记录';

INSERT INTO `agent-client`.t_code_value (ID, TYPE_CODE, VALUE, LABEL, DESCRIPTION, SEQ, STATUS, IS_VIS, deleted, CREATE_BY, UPDATE_BY, CREATE_TIME, UPDATE_TIME) VALUES (32, 'RERANKER_TYPE', 'sip', 'sip', null, 2, 'A', null, 0, null, null, null, null);
INSERT INTO `agent-client`.t_code_value (ID, TYPE_CODE, VALUE, LABEL, DESCRIPTION, SEQ, STATUS, IS_VIS, deleted, CREATE_BY, UPDATE_BY, CREATE_TIME, UPDATE_TIME) VALUES (33, 'RERANKER_TYPE', 'gte', 'gte', null, 1, 'A', null, 0, null, null, null, null);


## 2024-04-10
alter table ai_workflow_dev
    add config_tree_json longtext null after config_json;

alter table ai_workflow_publish
    add config_tree_json longtext null after config_json;

ALTER TABLE `agent-client`.ai_agent_publish_history
    ADD object_id varchar(100) NULL COMMENT '第三方传入的id，用于历史记录的自定义搜索';


## 2024-04-11
CREATE TABLE `ai_agent_constant` (
                                     `id` int NOT NULL AUTO_INCREMENT,
                                     `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '名称',
                                     `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '描述',
                                     `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '常量code',
                                     `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '常量值类型（json\\txt\\markdown）',
                                     `value` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '常量值',
                                     `create_by` bigint DEFAULT NULL,
                                     `update_by` bigint DEFAULT NULL,
                                     `create_time` datetime DEFAULT NULL,
                                     `update_time` datetime DEFAULT NULL,
                                     `deleted` tinyint(1) DEFAULT NULL,
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='智能体-常量信息';

alter table `agent-client`.ai_agent_publish
    add tips longtext null COMMENT '默认问题';

alter table t_sys_user
    add third_user_id varchar(50) null after is_sys;

ALTER TABLE `agent-client`.ai_knowledge_segment ADD object_id varchar(100) NULL COMMENT '对象ID';

ALTER TABLE `agent-client`.ai_agent_publish_history
    ADD raw_input longtext NULL COMMENT '用户原始输入';

alter table `agent-client`.ai_agent_dev
    add tips longtext null COMMENT '默认问题';


alter table ai_agent_publish_history
    add raw_input longtext null after user_input;

ALTER TABLE `agent-client`.ai_agent_publish_history
    ADD reasoningchain longtext NULL COMMENT '思维过程';

## 2025-05-19
create table `agent-client`.ai_plugin_basic
(
    id           int auto_increment
        primary key,
    resource_id  bigint                           null comment '资源ID',
    type         varchar(20) collate utf8mb4_bin  null comment '插件类型',
    url          varchar(200) collate utf8mb4_bin null comment '插件URL',
    headers      varchar(400) collate utf8mb4_bin null comment '插件头',
    auth_type    varchar(50) collate utf8mb4_bin  null comment '插件授权类型',
    auth_info    varchar(500) collate utf8mb4_bin null comment '插件授权信息',
    status       varchar(10) collate utf8mb4_bin  null comment '状态',
    version      varchar(10) collate utf8mb4_bin  null comment '发布版本',
    publish_time datetime                         null comment '发布时间',
    pub_record   varchar(500)                     null comment '发布记录',
    create_by    bigint                           null,
    update_by    bigint                           null,
    create_time  datetime                         null,
    update_time  datetime                         null,
    deleted      tinyint(1)                       null
)
    comment '插件基本信息' charset = utf8mb4;


create table `agent-client`.ai_plugin_dev
(
    id              int auto_increment
        primary key,
    plugin_id       bigint                           null comment '插件ID',
    resource_id     bigint                           null comment '资源ID',
    name            varchar(100) collate utf8mb4_bin null comment 'api名称',
    description     varchar(400) collate utf8mb4_bin null comment 'api简介',
    path            varchar(200) collate utf8mb4_bin null comment 'api完整地址',
    suffix_path     varchar(150) collate utf8mb4_bin null comment 'api地址',
    method          varchar(50) collate utf8mb4_bin  null comment 'api请求方式',
    request_params  longtext                         null comment 'api请求参数',
    response_params longtext                         null comment 'api响应参数',
    enable          boolean                          null comment '启用/禁用',
    status          varchar(10) collate utf8mb4_bin  null comment '状态',
    debug_status    varchar(10) collate utf8mb4_bin  null comment '调试状态',
    create_by       bigint                           null,
    update_by       bigint                           null,
    create_time     datetime                         null,
    update_time     datetime                         null,
    deleted         boolean                          null
)
    comment '插件api信息' charset = utf8mb4;

create table `agent-client`.ai_plugin_publish
(
    id                 int auto_increment
        primary key,
    plugin_id          bigint                           null comment '插件ID',
    resource_id        bigint                           null comment '资源ID',
    name               varchar(100) collate utf8mb4_bin null comment 'api名称',
    description        varchar(400) collate utf8mb4_bin null comment 'api简介',
    path               varchar(200) collate utf8mb4_bin null comment 'api完整地址',
    suffix_path        varchar(150) collate utf8mb4_bin null comment 'api地址',
    method             varchar(50) collate utf8mb4_bin  null comment 'api请求方式',
    request_params     longtext                         null comment 'api请求参数',
    response_params    longtext                         null comment 'api响应参数',
    enable             boolean                          null comment '启用/禁用',
    status             varchar(10) collate utf8mb4_bin  null comment '状态',
    debug_status       varchar(10) collate utf8mb4_bin  null comment '调试状态',
    version            varchar(10) collate utf8mb4_bin  null comment '发布版本',
    publish_time       datetime                         null comment '发布时间',
    pub_record         varchar(500)                     null comment '发布记录',
    function_call_tool longtext                         null comment 'function call',
    create_by          bigint                           null,
    update_by          bigint                           null,
    create_time        datetime                         null,
    update_time        datetime                         null,
    deleted            boolean                          null
)
    comment '插件api信息' charset = utf8mb4;


alter table ai_knowledge_segment
    add tag varchar(200) null COMMENT '标签' after text;
alter table ai_knowledge_segment
    add overview longtext null COMMENT '概述' after text;

alter table ai_knowledge_doc
    add overview longtext null after is_visible;


# 2025-05-27

alter table ai_plugin_basic
    add mcp_server_type varchar(50) null after auth_info;

alter table ai_plugin_basic
    add mcp_server_config varchar(1000) null after mcp_server_type;

alter table ai_plugin_dev
    add mcp_input_schema longtext null after enable;


# 20250528 智能体配置
alter table `agent-client`.ai_agent_dev
    add params longtext null COMMENT '智能体超参数';

# 20250529 工作流的执行client_id(未用工作流的忽略)
INSERT INTO `agent-client`.`t_oauth_client_details` (`client_id`, `client_secret`, `client_name`, `scope`,
                                                     `authorized_grant_types`, `allow_redirect_uri`, `status`, `host`,
                                                     `create_time`, `update_time`, `create_by`, `update_by`)
VALUES ('49219f34e54e4886a9fe4053c1403a84', '4d6da556ccc9a974769134d4b61af4b4f7796988922addd9764519d66d381651', 'agent',
        'client:execute', 'client_credentials;refresh_token', '*', 'A', NULL, '2025-04-16 15:19:54',
        '2025-04-16 15:19:54', '1', '1');

##20250530 内网部署的-修改园区实验室内网模型的配置(实际模型地址需要替换成大模型服务器:3000的地址)
update ai_model_embedded
set dimension = 512,
    defaulted=0
where code = 'sipM3e';
INSERT INTO `agent-client`.`ai_model_embedded` (`name`, `code`, `size`, `tags`, `description`, `url`, `defaulted`,
                                                `enable`, `dimension`, `method_name`, `create_by`, `update_by`,
                                                `create_time`, `update_time`)
VALUES ('sipBgeM3', 'sipBgeM3', '32K', '园区AI实验室', '园区AI实验室',
        'https://espace.sipedu.cn/dmxllm/tmp/v1/embeddings', 1, 1, 1024, 'callSysEmbedded', NULL, NULL, NULL, NULL);

##20250603
alter table `agent-client`.ai_agent_publish
    add history_knowledge longtext null COMMENT '历史知识库';
alter table `agent-client`.ai_agent_dev
    add history_knowledge longtext null COMMENT '历史知识库';

alter table `agent-client`.ai_plugin_publish
    add tool_id bigint null COMMENT '';

##20250610
alter table t_sys_user
    add third_user_type varchar(20) null after wechat_work_user_id;

alter table t_sys_user
    add dept_code varchar(50) null after third_user_id;

create table t_sys_org
(
    id          bigint auto_increment
        primary key,
    code        varchar(50)  null,
    parent_code varchar(50)  null,
    name        varchar(100) null,
    source_from varchar(20)  null,
    sync        tinyint      null,
    sync_time   datetime     null,
    create_by   bigint       null,
    update_by   bigint       null,
    create_time datetime     null,
    update_time datetime     null
);

create table t_sys_user_org
(
    id          bigint auto_increment
        primary key,
    user_id     bigint   null,
    org_id      bigint   null,
    create_by   bigint   null,
    update_by   bigint   null,
    create_time datetime null,
    update_time datetime null
);

##20250612
create table ai_database_basic
(
    id          bigint auto_increment
        primary key,
    resource_id bigint                          null comment '资源ID',
    type        varchar(20) collate utf8mb4_bin null,
    config      longtext                        null,
    user_id     varchar(200)                    null,
    create_by   bigint                          null,
    update_by   bigint                          null,
    create_time datetime                        null,
    update_time datetime                        null,
    deleted     tinyint(1)                      null
)
    comment '数据库基础信息' charset = utf8mb4;


##20250613 处理片段和部分upload的文档没有doc字段问题
alter table `agent-client`.ai_knowledge_segment
    add doc longtext null COMMENT '片段标题';
CREATE INDEX idx_ai_knowledge_doc_doc_source_type ON `agent-client`.ai_knowledge_doc (doc_source_type);
##20250613 区分片段是自己手动上传的还是其他情况
alter table `agent-client`.ai_knowledge_segment
    add type varchar(20) null COMMENT '片段来源';
## 思维链
ALTER TABLE `agent-client`.ai_agent_publish_history
    ADD reasoningchain longtext NULL COMMENT '思维过程';

## 20250616 智能体的运行状态与时间，索引
alter table `agent-client`.ai_agent_publish_history
    add runtime bigint null COMMENT '运行时间（毫秒数）';
alter table `agent-client`.ai_agent_publish_history
    add status varchar(20) null COMMENT '运行状态';
alter table `agent-client`.ai_agent_publish_history
    add error longtext null COMMENT '错误原因';

CREATE INDEX idx_ai_agent_publish_history_flow_id ON `agent-client`.ai_agent_publish_history (flow_id);
## 修改插件认证字段过长的问题
ALTER TABLE ai_plugin_basic MODIFY COLUMN headers longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin;
ALTER TABLE ai_plugin_basic MODIFY COLUMN auth_info longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin;

#20250618 添加索引
CREATE INDEX idx_ai_knowledge_doc_knowledge_id ON `agent-client`.ai_knowledge_doc (knowledge_id);
CREATE INDEX idx_ai_knowledge_segment_knowledge_id ON `agent-client`.ai_knowledge_segment (knowledge_id);
CREATE INDEX idx_ai_knowledge_segment_doc_id ON `agent-client`.ai_knowledge_segment (doc_id);

## 20250620反馈记录
CREATE TABLE `agent-client`.`ai_agent_history_feedback` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `type` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '反馈类型',
  `history_id` bigint(20) DEFAULT NULL COMMENT '智能体发布ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `message_id` bigint(20) DEFAULT NULL,
  `result` varchar(55) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '状态',
  `remark` longtext COLLATE utf8mb4_general_ci COMMENT '处理备注',
  `user_remark` longtext COLLATE utf8mb4_general_ci COMMENT '用户提交备注',

  `raw_input` longtext COLLATE utf8mb4_general_ci COMMENT '原始输入',
  `input` longtext COLLATE utf8mb4_general_ci COMMENT '实际输入',
  `answer` longtext COLLATE utf8mb4_general_ci COMMENT '最终结果',

  `seg_id` bigint(20) DEFAULT NULL COMMENT '片段id',
  `knowledge_id` bigint(20) DEFAULT NULL COMMENT '知识库id',
  `agent_id` bigint(20) DEFAULT NULL COMMENT '智能体id',
  `flow_id` varchar(60) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '流程id',
  `client_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_by` bigint(20) DEFAULT NULL,
  `update_by` bigint(20) DEFAULT NULL,
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT NULL COMMENT '删除标记',
  `third_user_id` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '第三方用户id',
  PRIMARY KEY (`id`),
    KEY `idx_history_id` (`history_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_agent_id` (`agent_id`),
    KEY `idx_knowledge_id` (`knowledge_id`),
    KEY `idx_flow_id` (`flow_id`),
    KEY `idx_third_user_id` (`third_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='智能体历史反馈表';


## 20250623 明确记录智能体是不是最后一个节点反馈记录 和 字典值

alter table `agent-client`.ai_agent_publish_history
    add type varchar(20) default 'customer' comment '智能体执行类型，最后一个节点，最开始的节点（入口）、并行的、默认等';
CREATE INDEX idx_ai_agent_publish_history_type ON `agent-client`.ai_agent_publish_history (type);

alter table `agent-client`.ai_agent_history_feedback
    add status varchar(20) default null comment '反馈状态';
CREATE INDEX idx_ai_agent_history_feedback_status ON `agent-client`.ai_agent_history_feedback (status);



INSERT INTO `agent-client`.t_code_value (TYPE_CODE, VALUE, LABEL, DESCRIPTION, SEQ, STATUS, IS_VIS, deleted,
                                         CREATE_BY, UPDATE_BY, CREATE_TIME, UPDATE_TIME)
VALUES ('AGENT_RUN_STATUS', 'complete', '运行完成', null, 1, 'A', null, 0, null, null, null, null);
INSERT INTO `agent-client`.t_code_value (TYPE_CODE, VALUE, LABEL, DESCRIPTION, SEQ, STATUS, IS_VIS, deleted,
                                         CREATE_BY, UPDATE_BY, CREATE_TIME, UPDATE_TIME)
VALUES ( 'AGENT_RUN_STATUS', 'embedded', '运行中', null, 1, 'A', null, 0, null, null, null, null);
INSERT INTO `agent-client`.t_code_value (TYPE_CODE, VALUE, LABEL, DESCRIPTION, SEQ, STATUS, IS_VIS, deleted,
                                         CREATE_BY, UPDATE_BY, CREATE_TIME, UPDATE_TIME)
VALUES ('AGENT_RUN_STATUS', 'success', '运行成功', null, 1, 'A', null, 0, null, null, null, null);
INSERT INTO `agent-client`.t_code_value (TYPE_CODE, VALUE, LABEL, DESCRIPTION, SEQ, STATUS, IS_VIS, deleted,
                                         CREATE_BY, UPDATE_BY, CREATE_TIME, UPDATE_TIME)
VALUES ('AGENT_RUN_STATUS', 'timeout', '运行超时', null, 1, 'A', null, 0, null, null, null, null);
INSERT INTO `agent-client`.t_code_value (TYPE_CODE, VALUE, LABEL, DESCRIPTION, SEQ, STATUS, IS_VIS, deleted,
                                         CREATE_BY, UPDATE_BY, CREATE_TIME, UPDATE_TIME)
VALUES ('AGENT_RUN_STATUS', 'failed', '运行失败', null, 1, 'A', null, 0, null, null, null, null);


INSERT INTO `agent-client`.t_code_value (TYPE_CODE, VALUE, LABEL, DESCRIPTION, SEQ, STATUS, IS_VIS, deleted,
                                         CREATE_BY, UPDATE_BY, CREATE_TIME, UPDATE_TIME)
VALUES ('AGENT_RUN_TYPE', 'customer', '默认节点', null, 1, 'A', null, 0, null, null, null, null);
INSERT INTO `agent-client`.t_code_value (TYPE_CODE, VALUE, LABEL, DESCRIPTION, SEQ, STATUS, IS_VIS, deleted,
                                         CREATE_BY, UPDATE_BY, CREATE_TIME, UPDATE_TIME)
VALUES ('AGENT_RUN_TYPE', 'first', '入口节点', null, 1, 'A', null, 0, null, null, null, null);
INSERT INTO `agent-client`.t_code_value (TYPE_CODE, VALUE, LABEL, DESCRIPTION, SEQ, STATUS, IS_VIS, deleted,
                                         CREATE_BY, UPDATE_BY, CREATE_TIME, UPDATE_TIME)
VALUES ('AGENT_RUN_TYPE', 'parallel', '并行节点', null, 1, 'A', null, 0, null, null, null, null);
INSERT INTO `agent-client`.t_code_value (TYPE_CODE, VALUE, LABEL, DESCRIPTION, SEQ, STATUS, IS_VIS, deleted,
                                         CREATE_BY, UPDATE_BY, CREATE_TIME, UPDATE_TIME)
VALUES ('AGENT_RUN_TYPE', 'think', '思考节点', null, 1, 'A', null, 0, null, null, null, null);
INSERT INTO `agent-client`.t_code_value (TYPE_CODE, VALUE, LABEL, DESCRIPTION, SEQ, STATUS, IS_VIS, deleted,
                                         CREATE_BY, UPDATE_BY, CREATE_TIME, UPDATE_TIME)
VALUES ('AGENT_RUN_TYPE', 'last', '最后节点', null, 1, 'A', null, 0, null, null, null, null);

INSERT INTO `agent-client`.t_code_value (TYPE_CODE, VALUE, LABEL, DESCRIPTION, SEQ, STATUS, IS_VIS, deleted,
                                         CREATE_BY, UPDATE_BY, CREATE_TIME, UPDATE_TIME)
VALUES ('AGENT_FEEDBACK_TYPE', 'positive', '正反馈', null, 1, 'A', null, 0, null, null, null, null);
INSERT INTO `agent-client`.t_code_value (TYPE_CODE, VALUE, LABEL, DESCRIPTION, SEQ, STATUS, IS_VIS, deleted,
                                         CREATE_BY, UPDATE_BY, CREATE_TIME, UPDATE_TIME)
VALUES ('AGENT_FEEDBACK_TYPE', 'negative', '负反馈', null, 1, 'A', null, 0, null, null, null, null);

## 20250624 字典值
INSERT INTO `agent-client`.t_code_value (TYPE_CODE, VALUE, LABEL, DESCRIPTION, SEQ, STATUS, IS_VIS, deleted,
                                         CREATE_BY, UPDATE_BY, CREATE_TIME, UPDATE_TIME)
VALUES ('AGENT_FEEDBACK_STATUS', 'complete', '已处理', null, 1, 'A', null, 0, null, null, null, null);
INSERT INTO `agent-client`.t_code_value (TYPE_CODE, VALUE, LABEL, DESCRIPTION, SEQ, STATUS, IS_VIS, deleted,
                                         CREATE_BY, UPDATE_BY, CREATE_TIME, UPDATE_TIME)
VALUES ('AGENT_FEEDBACK_STATUS', 'uncomplete', '未处理', null, 1, 'A', null, 0, null, null, null, null);

INSERT INTO `agent-client`.t_code_value (TYPE_CODE, VALUE, LABEL, DESCRIPTION, SEQ, STATUS, IS_VIS, deleted,
                                         CREATE_BY, UPDATE_BY, CREATE_TIME, UPDATE_TIME)
VALUES ('AGENT_FEEDBACK_RESULT', 'other', '其他', null, 1, 'A', null, 0, null, null, null, null);
INSERT INTO `agent-client`.t_code_value (TYPE_CODE, VALUE, LABEL, DESCRIPTION, SEQ, STATUS, IS_VIS, deleted,
                                         CREATE_BY, UPDATE_BY, CREATE_TIME, UPDATE_TIME)
VALUES ('AGENT_FEEDBACK_RESULT', 'positive', '修改并设置为正样本', null, 1, 'A', null, 0, null, null, null, null);
INSERT INTO `agent-client`.t_code_value (TYPE_CODE, VALUE, LABEL, DESCRIPTION, SEQ, STATUS, IS_VIS, deleted,
                                         CREATE_BY, UPDATE_BY, CREATE_TIME, UPDATE_TIME)
VALUES ('AGENT_FEEDBACK_RESULT', 'prompt', '修改提示词', null, 1, 'A', null, 0, null, null, null, null);
INSERT INTO `agent-client`.t_code_value (TYPE_CODE, VALUE, LABEL, DESCRIPTION, SEQ, STATUS, IS_VIS, deleted,
                                         CREATE_BY, UPDATE_BY, CREATE_TIME, UPDATE_TIME)
VALUES ('AGENT_FEEDBACK_RESULT', 'data', '修改数据源', null, 1, 'A', null, 0, null, null, null, null);
INSERT INTO `agent-client`.t_code_value (TYPE_CODE, VALUE, LABEL, DESCRIPTION, SEQ, STATUS, IS_VIS, deleted,
                                         CREATE_BY, UPDATE_BY, CREATE_TIME, UPDATE_TIME)
VALUES ('AGENT_FEEDBACK_RESULT', 'defined', '补充定义/解释', null, 1, 'A', null, 0, null, null, null, null);
