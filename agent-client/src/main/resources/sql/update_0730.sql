create table ai_execute_history
(
    id              int auto_increment
        primary key,
    conversation_id varchar(50)  null comment '会话ID',
    third_user_id   varchar(100) null comment '第三方用户id',
    user_input      longtext     null comment '用户输入',
    content         longtext     null comment '历史内容',
    deleted         tinyint(1)   null,
    create_by       bigint       null,
    update_by       bigint       null,
    create_time     datetime     null,
    update_time     datetime     null
)
    comment '历史记录' auto_increment = 1;

INSERT INTO `agent-client`.`t_code_value` (  `TYPE_CODE`, `VALUE`, `LABEL`, `DESCRIPTION`, `SEQ`, `STATUS`, `IS_VIS`, `deleted`, `CREATE_BY`, `UPDATE_BY`, `CREATE_TIME`, `UPDATE_TIME` )
VALUES
	(   'AGENT_FEEDBACK_RESULT', 'close', '关闭/删除', NULL, 1, 'A', NULL, 0, NULL, NULL, NULL, NULL );