CREATE INDEX v_school_baseinfo_school_jc_IDX USING BTREE ON yj_data.v_school_baseinfo (school_jc);
CREATE INDEX v_school_baseinfo_school_id_IDX USING BTREE ON yj_data.v_school_baseinfo (school_id);
CREATE INDEX v_school_baseinfo_data_status_IDX USING BTREE ON yj_data.v_school_baseinfo (data_status);

CREATE INDEX v_teacher_baseinfo_school_name_IDX USING BTREE ON yj_data.v_teacher_baseinfo (school_name);
CREATE INDEX v_teacher_baseinfo_organizeschool_id_IDX USING BTREE ON yj_data.v_teacher_baseinfo (organizeschool_id);
CREATE INDEX v_teacher_baseinfo_data_status_IDX USING BTREE ON yj_data.v_teacher_baseinfo (data_status);
CREATE INDEX v_teacher_baseinfo_rjkc_IDX USING BTREE ON yj_data.v_teacher_baseinfo (rjkc);
CREATE INDEX v_teacher_baseinfo_yrxs_IDX USING BTREE ON yj_data.v_teacher_baseinfo (yrxs);

CREATE INDEX v_teacher_baseinfo_school_id_IDX USING BTREE ON yj_data.v_teacher_baseinfo (school_id);

delete from yj_data.v_teacher_baseinfo where organizeschool_id in ('693c83b0-2c10-46ea-b7a6-392c32d26ea7','70ce7ca7-4f45-4752-a4b2-7b7b89da74ff','d51ee675-f446-4bdf-9c93-c9743de9b5cd','d0ede7f0-0e9c-4907-bfce-938a93db2a14','5c085e6d-dd46-4239-b304-a0211e17b63a','2e94e658-be0e-4c22-903f-8ff6637d2497','41813A097C244B3E9CF50F7CB2EF6D1B');


ALTER TABLE `yj_data`.v_teacher_baseinfo ADD csrq varchar(50) NULL COMMENT '出生年月(yyyy年mm月dd日)';


INSERT INTO `agent-client`.t_code_value (TYPE_CODE,VALUE,LABEL,DESCRIPTION,SEQ,STATUS,IS_VIS,deleted,CREATE_BY,UPDATE_BY,CREATE_TIME,UPDATE_TIME) VALUES
  ('AGENT_TAGS','tag1','AI智伴',NULL,1,'A',NULL,0,NULL,NULL,NULL,NULL);
INSERT INTO `agent-client`.t_code_value (TYPE_CODE,VALUE,LABEL,DESCRIPTION,SEQ,STATUS,IS_VIS,deleted,CREATE_BY,UPDATE_BY,CREATE_TIME,UPDATE_TIME) VALUES
  ('AGENT_TAGS','tag2','AI智囊',NULL,2,'A',NULL,0,NULL,NULL,NULL,NULL);

INSERT INTO `agent-client`.t_code_value (TYPE_CODE,VALUE,LABEL,DESCRIPTION,SEQ,STATUS,IS_VIS,deleted,CREATE_BY,UPDATE_BY,CREATE_TIME,UPDATE_TIME) VALUES
  ('AGENT_TAGS','tag3','AI导师',NULL,3,'A',NULL,0,NULL,NULL,NULL,NULL);

INSERT INTO `agent-client`.t_code_value (TYPE_CODE,VALUE,LABEL,DESCRIPTION,SEQ,STATUS,IS_VIS,deleted,CREATE_BY,UPDATE_BY,CREATE_TIME,UPDATE_TIME) VALUES
  ('AGENT_TAGS','tag4','AI助研',NULL,4,'A',NULL,0,NULL,NULL,NULL,NULL);
INSERT INTO `agent-client`.t_code_value (TYPE_CODE,VALUE,LABEL,DESCRIPTION,SEQ,STATUS,IS_VIS,deleted,CREATE_BY,UPDATE_BY,CREATE_TIME,UPDATE_TIME) VALUES
  ('AGENT_TAGS','tag5','AI助教',NULL,5,'A',NULL,0,NULL,NULL,NULL,NULL);


INSERT INTO `agent-client`.t_oauth_client_details (client_id,client_secret,client_name,`scope`,authorized_grant_types,allow_redirect_uri,status,host,create_time,update_time,create_by,update_by) VALUES
    ('e57187612f354a44a30de43d94e2c90c','b7d9c31e64a44d4ea819bdaa2197cf83','edu_work','client:execute','client_credentials;refresh_token','*','A',NULL,'2025-01-08 10:43:57','2025-01-08 10:43:57','1','1');


--`zj` varchar(100) DEFAULT NULL COMMENT '职级',
--  `jtzw` varchar(100) DEFAULT NULL COMMENT '具体职务',
--  `jszgzzl` varchar(100) DEFAULT NULL COMMENT '教师资格证种类',

ALTER TABLE `yj_data`.v_school_baseinfo ADD school_jbz varchar(50) NULL COMMENT '组织性质(公办、民办、直属、街道)';
ALTER TABLE `yj_data`.v_school_baseinfo ADD rs_school_name varchar(50) NULL COMMENT '合作学校名称(统计学校数量时根据这个字段distinct，因为多个学校会隶属于算一个学校）';
ALTER TABLE `yj_data`.v_school_baseinfo ADD jt_name varchar(50) NULL COMMENT '所属集团';

CREATE INDEX v_school_baseinfo_school_jbz_IDX USING BTREE ON yj_data.v_school_baseinfo (school_jbz);
CREATE INDEX v_school_baseinfo_rs_school_name_IDX USING BTREE ON yj_data.v_school_baseinfo (rs_school_name);
CREATE INDEX v_school_baseinfo_jt_name_IDX USING BTREE ON yj_data.v_school_baseinfo (jt_name);

ALTER TABLE `yj_data`.v_teacher_baseinfo ADD rylb varchar(50) NULL COMMENT '人员类别(专任教师、在编职工)';
CREATE INDEX v_teacher_baseinfo_rylb_IDX USING BTREE ON yj_data.v_teacher_baseinfo (rylb);

-- 同步班级信息表
CREATE TABLE `yj_data`.`v_school_class` (
  `uuid` varchar(50) NOT NULL COMMENT '主键',
  `school_id` varchar(50) COMMENT '学校ID',
  `bh` varchar(50) COMMENT '班号',
  `njh` varchar(50) COMMENT '年级号（年份）',
  `bjmc` varchar(50) COMMENT '班级名称',
  `jbny` varchar(50) COMMENT '建班年月',
  `kssj` date COMMENT '班级开始时间',
  `jssj` date COMMENT '班级结束时间',
  `bzr_id` varchar(50) COMMENT '班主任教师ID',
  `fbzr_id` varchar(50) COMMENT '副班主任教师ID',
  `njmc` varchar(50) COMMENT '年级名称',
  `bjwzmc_xn` varchar(50) COMMENT '班级完整名称（2021级四年级03班格式，查询此名称字段时通过like查询）',
  `data_status` varchar(10) COMMENT '数据状态 0-正常 3-锁定 9-删除',
  `update_time` timestamp(6) COMMENT '数据更新时间',
  PRIMARY KEY (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='学校班级信息';

-- 单列索引
CREATE INDEX idx_xm ON v_student_baseinfo(xm);
CREATE INDEX idx_xb ON v_student_baseinfo(xb);
CREATE INDEX idx_school_id ON v_student_baseinfo(school_id);
CREATE INDEX idx_class_id ON v_student_baseinfo(class_id);
CREATE INDEX idx_data_status ON v_student_baseinfo(data_status);
CREATE INDEX idx_age ON v_student_baseinfo(age);
CREATE INDEX idx_update_time ON v_student_baseinfo(update_time);

-- 组合索引
CREATE INDEX idx_school_class ON v_student_baseinfo(school_id, class_id);
CREATE INDEX idx_guardian ON v_student_baseinfo(dyjhrxm, dyjhrgx);
CREATE INDEX idx_search_combo ON v_student_baseinfo(xm, xb, jg, mz);

-- 特殊场景索引（前缀索引示例，按需启用）
-- CREATE INDEX idx_xzz ON v_student_baseinfo(xzz(20));

-- 单列索引
CREATE INDEX idx_xm ON v_teacher_baseinfo(xm);
CREATE INDEX idx_school_id ON v_teacher_baseinfo(school_id);
CREATE INDEX idx_organizeschool_id ON v_teacher_baseinfo(organizeschool_id);
CREATE INDEX idx_xb ON v_teacher_baseinfo(xb);
CREATE INDEX idx_rjkc ON v_teacher_baseinfo(rjkc);
CREATE INDEX idx_rjxd ON v_teacher_baseinfo(rjxd);
CREATE INDEX idx_data_status ON v_teacher_baseinfo(data_status);
CREATE INDEX idx_update_time ON v_teacher_baseinfo(update_time);
CREATE INDEX idx_age ON v_teacher_baseinfo(age);
CREATE INDEX idx_cylb ON v_teacher_baseinfo(cylb);
CREATE INDEX idx_rylb ON v_teacher_baseinfo(rylb);

-- 组合索引
CREATE INDEX idx_school_teach ON v_teacher_baseinfo(school_id, rjkc, rjxd);
CREATE INDEX idx_personal_info ON v_teacher_baseinfo(xm, xb, age, mz);
CREATE INDEX idx_career_info ON v_teacher_baseinfo(zgxl, zgzc, zgxkry);
CREATE INDEX idx_time_range ON v_teacher_baseinfo(ryqzgsj, cjgzny);

-- 前缀索引（针对长文本字段）
CREATE INDEX idx_jtzw ON v_teacher_baseinfo(jtzw(20));
-- 单列索引
CREATE INDEX idx_school_id ON v_school_class(school_id);
CREATE INDEX idx_njh ON v_school_class(njh);
CREATE INDEX idx_bjmc ON v_school_class(bjmc);
CREATE INDEX idx_bzr_id ON v_school_class(bzr_id);
CREATE INDEX idx_data_status ON v_school_class(data_status);
CREATE INDEX idx_update_time ON v_school_class(update_time);

-- 组合索引
CREATE INDEX idx_school_nj ON v_school_class(school_id, njh);
CREATE INDEX idx_class_time ON v_school_class(kssj, jssj);
CREATE INDEX idx_class_info ON v_school_class(school_id, njh, bjmc);

-- 外键关联优化索引
CREATE INDEX idx_teacher_bzr ON v_school_class(bzr_id);
CREATE INDEX idx_teacher_fbzr ON v_school_class(fbzr_id);

-- 前缀索引（针对长文本字段）
CREATE INDEX idx_bjwzmc ON v_school_class(bjwzmc_xn(30));

-- 单列索引
CREATE INDEX idx_school_name ON v_school_baseinfo(school_name);
CREATE INDEX idx_school_jc ON v_school_baseinfo(school_jc);
CREATE INDEX idx_school_lb ON v_school_baseinfo(school_lb);
CREATE INDEX idx_school_xz ON v_school_baseinfo(school_xz);
CREATE INDEX idx_school_jbz ON v_school_baseinfo(school_jbz);
CREATE INDEX idx_data_status ON v_school_baseinfo(data_status);
CREATE INDEX idx_jt_name ON v_school_baseinfo(jt_name);
CREATE INDEX idx_rs_school_name ON v_school_baseinfo(rs_school_name);

-- 组合索引
CREATE INDEX idx_school_class ON v_school_baseinfo(school_lb, school_xz);
CREATE INDEX idx_student_teacher ON v_school_baseinfo(student_num, teacher_num);
CREATE INDEX idx_school_search ON v_school_baseinfo(school_name, school_jc, school_location);

-- 统计查询索引
CREATE INDEX idx_student_num ON v_school_baseinfo(student_num);
CREATE INDEX idx_teacher_num ON v_school_baseinfo(teacher_num);
CREATE INDEX idx_class_num ON v_school_baseinfo(class_num);

-- 前缀索引（针对长文本字段）
CREATE INDEX idx_school_location ON v_school_baseinfo(school_location(50));