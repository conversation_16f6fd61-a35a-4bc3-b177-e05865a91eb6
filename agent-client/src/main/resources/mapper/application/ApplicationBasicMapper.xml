<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fytec.mapper.application.ApplicationBasicMapper">
    <select id="queryApplicationPage" resultType="com.fytec.dto.application.ApplicationBasicDTO">
        select * from ai_application_basic
        where deleted = 0
        <if test="param.name != null and param.name != ''">
            and name like concat('%', #{param.name}, '%')
        </if>
    </select>
</mapper>