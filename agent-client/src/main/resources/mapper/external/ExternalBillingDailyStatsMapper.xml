<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fytec.mapper.external.ExternalBillingDailyStatsMapper">
    
    <select id="queryBillingStatsPage" resultType="com.fytec.dto.external.ExternalBillingDailyStatsResultDTO">
        SELECT 
            service_code AS serviceCode,
            COALESCE(SUM(service_count), 0) AS serviceCount,
            COALESCE(SUM(usage_duration_minutes), 0) AS usageDurationMinutes,
            COALESCE(SUM(service_word_count), 0) AS serviceWordCount,
            COALESCE(SUM(use_count), 0) AS useCount
        FROM external_billing_daily_stats
        WHERE deleted = 0
        <if test="param.startDate != null">
            AND stats_date >= #{param.startDate}
        </if>
        <if test="param.endDate != null">
            AND stats_date &lt;= #{param.endDate}
        </if>
        GROUP BY service_code
        ORDER BY useCount DESC, service_code
    </select>
    
</mapper>
