<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fytec.mapper.model.ModelGroupMapper">
    <select id="queryAiModelGroupPaging" resultType="com.fytec.dto.llm.AiModelGroupListDTO">
        select id, group_name as groupName, logo, description, create_time as createTime
        from ai_model_group
        <where>
            <if test="param.keyword != null and param.keyword != ''">
                and group_name like concat('%', #{param.keyword}, '%')
            </if>
        </where>
    </select>
    <select id="getMaxSeqNo" resultType="java.lang.Integer">
        select max(seq) from ai_model_group
    </select>
</mapper>