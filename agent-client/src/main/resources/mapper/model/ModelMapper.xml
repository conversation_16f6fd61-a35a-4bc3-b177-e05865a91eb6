<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fytec.mapper.model.ModelMapper">
    <select id="findAll" resultType="com.fytec.dto.model.ModelListDTO">
        select m.*,
        g.id as groupId,
        g.group_name as groupName,
        g.logo as groupLogo,
        g.seq as groupSeq
        from ai_model m
        left join ai_model_group g on g.id = m.group_id
        where m.enable != 0
        and (m.type = 'text' or m.type = 'multimodal')
        <if test="param.keyword != null and param.keyword != ''">
            and m.`name` like concat('%', #{param.keyword}, '%')
        </if>
        <if test="param.groupId != null">
            and m.group_id = #{param.groupId}
        </if>
        <if test="param.open">
            and m.open = 1
        </if>
        order by g.seq, m.update_time desc
    </select>
    <select id="queryAiModelPaging" resultType="com.fytec.dto.llm.AiModelListDTO">
        select id, name, size, code, enable, defaulted
        from ai_model
        <where>
            <if test="param.keyword != null and param.keyword != ''">
                and `name` like concat('%', #{param.keyword}, '%')
            </if>
            <if test="param.groupId != null">
                and group_id = #{param.groupId}
            </if>
        </where>
    </select>
</mapper>