<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fytec.mapper.workflow.WorkflowPublishMapper">
    <select id="queryWorkflow" resultType="com.fytec.dto.flow.WorkflowDTO">
        SELECT
        ar.id as resourceId,
        ar.logo,
        ar.name,
        ar.description,
        awd.id as workFlowId,
        od.id,
        od.id as workFlowPublishId,
        od.version as workFlowPublishVersion
        FROM ai_resource ar
        INNER JOIN ai_workflow_dev awd ON ar.id = awd.resource_id
        INNER JOIN (
        SELECT * FROM ai_workflow_publish awp
        WHERE awp.deleted =0
        and awp.id =
        (
        SELECT max(id) FROM ai_workflow_publish
        WHERE deleted = 0 and resource_id = awp.resource_id
        group by resource_id
        )
        ORDER BY awp.publish_time desc
        ) od ON ar.id = od.resource_id
        where ar.`type` ='workflow'
        and ar.deleted =0
        and ar.create_by = #{dto.createBy}
        <if test="dto.filterId!= null">
            and ar.id != #{dto.filterId}
        </if>
        <if test="dto.name!= null and dto.name!= ''">
            and ar.name like concat('%',#{dto.name},'%')
        </if>
        order by ar.create_time desc
    </select>
</mapper>