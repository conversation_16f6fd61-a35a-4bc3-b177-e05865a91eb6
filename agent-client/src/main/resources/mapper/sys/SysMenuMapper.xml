<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fytec.mapper.system.SysMenuMapper">
    <select id="selectListOrderBySeq" resultType="com.fytec.dto.system.SysMenuTreeDTO">
        select id        as id,
               parent_id as parentid,
               name      as menuname,
               type      as menutype,
               icon      as menuicon,
               component as menucomponent,
               href      as menuhref,
               seq       as menusort
        from t_sys_menu
        where status != 'D'
          and is_visible = 'Y'
        order by seq
    </select>
    <select id="selectMenuListByRoles" resultType="com.fytec.dto.system.SysMenuTreeDTO">
        select t_sys_menu.id as id,
        parent_id as parentid,
        name as menuname,
        type as menutype,
        icon as menuicon,
        component as menucomponent,
        href as menuhref,
        seq as menusort
        from t_sys_menu
        <if test="roles != null and roles.size() > 0">
            inner join t_sys_role_menu on t_sys_menu.id = t_sys_role_menu.menu_id
        </if>
        where status != 'D'
        and is_visible = 'Y'
        and type = 'MENU'
        <if test="roles != null and roles.size() > 0">
            and t_sys_role_menu.role_id in
            <foreach collection="roles" item="roleId" open="(" separator="," close=")">
                #{roleId}
            </foreach>
        </if>
        order by seq
    </select>
    <select id="selectPermissionListByRoles" resultType="java.lang.String">
        select distinct permission
        from t_sys_menu
        inner join t_sys_role_menu on t_sys_menu.id = t_sys_role_menu.menu_id
        where status != 'D'
        and is_visible = 'Y'
        and type = 'BUTTON'
        and t_sys_role_menu.role_id in
        <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </select>
</mapper>