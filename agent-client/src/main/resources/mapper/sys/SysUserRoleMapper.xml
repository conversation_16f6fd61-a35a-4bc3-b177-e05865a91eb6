<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fytec.mapper.system.SysUserRoleMapper">
    <select id="selectRoleListByUserId" resultType="java.lang.String">
        select r.role
        from t_sys_user_role ur
                 inner join t_sys_role r on ur.role_id = r.id
        where ur.user_id = #{loginId}
    </select>
    <select id="selectRoleIdByUserId" resultType="java.lang.Long">
        select r.id
        from t_sys_user_role ur
                 inner join t_sys_role r on ur.role_id = r.id
        where ur.user_id = #{loginId}

    </select>
</mapper>