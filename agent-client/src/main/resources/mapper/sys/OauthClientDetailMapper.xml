<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fytec.mapper.system.OauthClientDetailMapper">
    <select id="selectClientNamesByClientIds" resultType="com.fytec.entity.system.OauthClientDetail">
        select  client_id as clientId,client_name as name
        from t_oauth_client_details
        where client_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>