<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fytec.mapper.system.SysDictValueMapper">
    <select id="selectByTypeCode" resultType="com.fytec.dto.system.DictValueDTO">
        SELECT TYPE_CODE AS typeCode, `VALUE` AS value, LABEL AS label, DESCRIPTION AS description
        FROM t_code_value
        WHERE TYPE_CODE = #{typeCode}
        ORDER BY SEQ ASC
    </select>

    <select id="selectByMultipleTypeCode" resultType="com.fytec.dto.system.DictValueDTO">
        SELECT TYPE_CODE AS typeCode, `VALUE` AS value, LABEL AS label, DESCRIPTION AS description
        FROM t_code_value
        <where>
            <if test="types != null and types.size > 0">
                and TYPE_CODE in
                <foreach collection="types" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY TYPE_CODE, SEQ ASC
    </select>

</mapper>