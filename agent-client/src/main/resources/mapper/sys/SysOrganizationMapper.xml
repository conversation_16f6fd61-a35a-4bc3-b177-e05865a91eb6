<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fytec.mapper.system.SysOrganizationMapper" >
    <select id="queryOrganization" resultType="com.fytec.dto.system.SysOrganizationTreeDTO">
        select id        as id,
               parent_id as parentid,
               name      as name,
               code      as code
        from t_sys_org
        <where>
            <if test="param.name != null and param.name != ''">
                and name like concat('%', #{param.name}, '%')
            </if>
        </where>
        order by create_time;
    </select>
</mapper>