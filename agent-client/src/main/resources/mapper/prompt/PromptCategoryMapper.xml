<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fytec.mapper.prompt.PromptCategoryMapper">
    <select id="queryPromptCategory" resultType="com.fytec.dto.prompt.PromptCategoryDTO">
        select pc.id, pc.name, pc.seq, pc.create_time as createTime, u.name as createBy
        from ai_prompt_category pc
        left join t_sys_user u on pc.create_by = u.id
        <where>
            <if test="param.name != null and param.name != ''">
                and pc.`name` like concat('%', #{param.name}, '%')
            </if>
        </where>
        order by pc.seq, pc.create_time
    </select>
</mapper>