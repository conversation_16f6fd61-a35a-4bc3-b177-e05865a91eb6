<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fytec.mapper.prompt.PromptContentMapper">
    <select id="queryPromptContent" resultType="com.fytec.dto.prompt.PromptContentListDTO">
        select c.id, c.name, c.type, c.content, c.description,
               c.create_time as createTime, u.name as createBy,
        GROUP_CONCAT(pc.name) AS categories
        from ai_prompt_content c
        left join ai_prompt_content_category cc on cc.content_id = c.id
        left join ai_prompt_category pc on cc.category_id = pc.id
        left join t_sys_user u on c.create_by = u.id
        <where>
            <if test="param.categoryId != null ">
                cc.category_id = #{param.categoryId}
            </if>
            <if test="param.name != null and param.name != ''">
                and c.`name` like concat('%', #{param.name}, '%')
            </if>
        </where>
        group by c.id
    </select>
    <select id="queryOutOfCategoryPromptContent" resultType="com.fytec.dto.prompt.PromptContentListDTO">
        select c.id, c.name, c.type, c.content, c.description, c.create_time as createTime
        from ai_prompt_content c
        where not exists (select v.id
                          from ai_prompt_content_category v
                          where v.content_id = c.id
                            and v.category_id = #{param.categoryId})
    </select>
</mapper>