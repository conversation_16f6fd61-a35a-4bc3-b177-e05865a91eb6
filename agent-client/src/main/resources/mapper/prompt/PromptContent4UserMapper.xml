<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fytec.mapper.prompt.PromptContent4UserMapper">
    <select id="queryPromptContent" resultType="com.fytec.dto.prompt.PromptContentListDTO">
        select c.id, c.name, c.type, c.content, c.description, c.create_time as createTime
        from ai_prompt_content c
        where c.create_by = #{param.createBy}
        <if test="param.name != null and param.name != ''">
            and c.`name` like concat('%', #{param.name}, '%')
        </if>
    </select>
</mapper>