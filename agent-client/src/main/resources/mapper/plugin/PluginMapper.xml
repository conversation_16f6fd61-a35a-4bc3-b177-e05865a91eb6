<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fytec.mapper.plugin.PluginMapper">
    <select id="queryPublishPluginList" resultType="com.fytec.dto.plugin.PluginPublishListDTO">
        select r.name as name, r.description as description, r.id as resouceId, r.logo as logo,
               pb.id as pluginId, pb.version as version, pb.type as type
        from ai_resource r
        inner join ai_plugin_basic pb on r.id = pb.resource_id
        where r.deleted = 0
        and pb.deleted = 0
        and r.type = 'plugin'
        and pb.status = '1'
        <if test="param.keywords != null and param.keywords != ''">
            and r.name like CONCAT('%',#{param.keywords},'%')
        </if>
        <choose>
            <when test="param.createBy != null">
                and r.create_by = #{param.createBy}
            </when>
            <otherwise>
                and pb.official = 1
            </otherwise>
        </choose>
        order by r.create_time desc
    </select>
</mapper>