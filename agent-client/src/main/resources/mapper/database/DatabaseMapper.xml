<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fytec.mapper.database.DatabaseMapper">
    <select id="queryDatabase" resultType="com.alibaba.fastjson2.JSONObject">
        SELECT
        ar.id as resourceId,
        ar.logo,
        ar.name,
        ar.description,
        db.id
        from
        ai_resource ar
        inner join ai_database_basic db on ar.id = db.resource_id
        WHERE
        ar.`type` = 'database'
        and ar.deleted = 0 and db.deleted =0
        <if test="dto.userId!= null and dto.userId!= ''">
            and (db.create_by = #{dto.userId} or db.user_id = #{dto.userId})
        </if>
        <if test="dto.name != null and dto.name != ''">
            and ar.name like concat('%',#{dto.name},'%')
        </if>
        <if test="dto.ids!=null and dto.ids!=''">
            and db.id in
            <foreach collection="dto.ids.split(',')" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by ar.create_time desc
    </select>
</mapper>