<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fytec.mapper.resource.ResourceMapper">

    <select id="queryPage" resultType="com.fytec.dto.resource.ResourceDTO">
        select r.*, pb.type as pluginType
        from ai_resource r
        left join ai_plugin_basic pb on r.id = pb.resource_id
        <if test="dto.types != null and dto.types == 'knowledge'">
            inner join ai_knowledge k on r.id = k.resource_id
        </if>
        where r.deleted = 0
        <if test="dto.createBy != null and dto.createBy != ''">
            and r.create_by = #{dto.createBy}
        </if>
        <if test="dto.name != null and dto.name != ''">
            and r.name like concat('%',#{dto.name},'%')
        </if>
        <if test="dto.types != null and dto.types == 'knowledge'">
            and k.type is null
        </if>
        <if test="dto.types != null and dto.types == 'knowledge' and dto.knowledgeBaseType != null and dto.knowledgeBaseType != ''">
            and k.base_type = #{dto.knowledgeBaseType}
        </if>
        <if test="dto.types!= null and dto.types != ''">
            and r.type in
            <foreach collection="dto.types.split(',')" item="type" index="index" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        <if test="dto.status!= null and dto.status!=''">
            and r.status = #{dto.status}
        </if>
        order by r.update_time desc
    </select>
</mapper>