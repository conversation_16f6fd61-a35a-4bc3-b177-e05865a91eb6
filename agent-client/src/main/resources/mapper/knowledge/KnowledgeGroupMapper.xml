<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fytec.mapper.knowledge.KnowledgeGroupMapper">
    <select id="queryKnowledgeTeamCreatedByMine"
            resultType="com.fytec.dto.knowledge.group.KnowledgeGroupTreeDTO">
        select id,
               parent_id    AS parentId,
               name,
               type,
               knowledge_id AS knowledgeId,
               is_sys       AS systemGroup
        from ai_knowledge_group
        where deleted = 0
          and type = #{param.knowledgeType}
    </select>
    <select id="queryKnowledgeTeamJoined" resultType="com.fytec.dto.knowledge.group.KnowledgeGroupTreeDTO">
        select akg.id,
               akg.parent_id    AS parentId,
               akg.name,
               akg.type,
               akg.knowledge_id AS knowledgeId,
               akg.is_sys       AS systemGroup
        from ai_knowledge_group akg
        where akg.deleted = 0
          and akg.type = #{param.knowledgeType}
          and exists (select user_id
                      from ai_knowledge_team_member aktm
                      where aktm.knowledge_id = akg.knowledge_id
                        and aktm.role != 'owner'
                        and aktm.user_id = #{param.userId})
    </select>
    <select id="queryKnowledgeRootTeamCreatedByMine"
            resultType="com.fytec.dto.knowledge.group.KnowledgeGroupTreeDTO">
        select id,
               parent_id    AS parentId,
               name,
               type,
               knowledge_id AS knowledgeId,
               is_sys       AS systemGroup
        from ai_knowledge_group
        where deleted = 0
          and type = #{param.knowledgeType}
          and create_by = #{param.userId}
          and parent_id is null
    </select>
</mapper>