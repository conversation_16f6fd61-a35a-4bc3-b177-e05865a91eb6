<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fytec.mapper.knowledge.KnowledgeMapper">
    <select id="queryKnowledge" resultType="com.fytec.dto.knowledge.KnowledgeDetailDTO">
        SELECT
        ar.id as resourceId,
        ar.logo,
        ar.name,
        ar.description,
        ar.parent_id as parentId,
        ar.is_sys as systemResource,
        ak.id,
        ak.type as knowledgeType
        from
        ai_resource ar
        inner join ai_knowledge ak on ar.id = ak.resource_id
        WHERE
        ar.`type` = 'knowledge'
        and ar.deleted = 0 and ak.deleted =0
        <if test="dto.userId!= null and dto.userId!= ''">
            and (ak.create_by = #{dto.userId} or ak.user_id = #{dto.userId})
        </if>
        <choose>
            <when test="dto.knowledgeType != null and dto.knowledgeType != ''">
                and ak.type in
                <foreach collection="dto.knowledgeType.split(',')" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                and ak.type is null
            </otherwise>
        </choose>
        <if test="dto.knowledgeBaseType != null and dto.knowledgeBaseType != ''">
            and ak.base_type = #{dto.knowledgeBaseType}
        </if>
        <if test="dto.name != null and dto.name != ''">
            and ar.name like concat('%',#{dto.name},'%')
        </if>
        <if test="dto.ids!=null and dto.ids!=''">
            and ak.id in
            <foreach collection="dto.ids.split(',')" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by ar.create_time desc
    </select>
    <select id="queryTeamKnowledgeJoined" resultType="com.fytec.dto.knowledge.KnowledgeDetailDTO">
        SELECT
        ar.id as resourceId,
        ar.logo,
        ar.name,
        ar.description,
        ar.parent_id as parentId,
        ar.is_sys as systemResource,
        ak.id,
        ak.type as knowledgeType
        from
        ai_resource ar
        inner join ai_knowledge ak on ar.id = ak.resource_id
        WHERE
        ar.`type` = 'knowledge'
        and ar.deleted = 0 and ak.deleted =0
        and exists (select user_id from ai_knowledge_team_member aktm where aktm.knowledge_id = ak.id and aktm.user_id =
        #{dto.userId})
        <if test="dto.knowledgeType != null and dto.knowledgeType != ''">
            and ak.type = #{dto.knowledgeType}
        </if>
        <if test="dto.name != null and dto.name != ''">
            and ar.name like concat('%',#{dto.name},'%')
        </if>
        <if test="dto.ids!=null and dto.ids!=''">
            and ak.id in
            <foreach collection="dto.ids.split(',')" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        order by ar.create_time desc
    </select>
    <update id="updateBaseTypeByResourceId">
        update ai_knowledge set base_type = #{baseType}
        where resource_id = #{resourceId}
    </update>
</mapper>