<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fytec.mapper.knowledge.KnowledgeTeamMemberMapper">
    <select id="queryKnowledgeTeamMembers" resultType="com.fytec.dto.knowledge.team.TeamKnowledgeMemberListDTO">
        (select user.id,
                user.image_url,
                user.wechat_work_user_id as wechatWorkUserId,
                user.name,
                aktm.create_time         as createTime,
                aktm.role                as role
         from ai_knowledge_team_member aktm
                  inner join t_sys_user user on user.id = aktm.user_id
         where aktm.knowledge_id = #{knowledgeId}
           and role = 'owner')
        UNION all
        (select user.id,
                user.image_url,
                user.wechat_work_user_id as wechat<PERSON><PERSON><PERSON><PERSON><PERSON>Id,
                user.name,
                aktm.create_time         as createTime,
                aktm.role                as role
         from ai_knowledge_team_member aktm
                  inner join t_sys_user user on user.id = aktm.user_id
         where aktm.knowledge_id = #{knowledgeId}
           and role != 'owner')
    </select>
</mapper>