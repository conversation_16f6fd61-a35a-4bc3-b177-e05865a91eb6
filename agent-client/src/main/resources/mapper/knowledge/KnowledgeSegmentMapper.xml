<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fytec.mapper.knowledge.KnowledgeSegmentMapper">


    <insert id="insertBatch">
        insert into ai_knowledge_segment (
        knowledge_id,
        doc_id,
        page_number,
        text,
        vector_id,
        vector_index,
        create_time,
        update_time,
        create_by,
        update_by,
        deleted
        ) values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.knowledgeId},
            #{item.docId},
            #{item.pageNumber},
            #{item.text},
            #{item.vectorId},
            #{item.vectorIndex},
            now(),
            now(),
            #{item.createBy},
            #{item.updateBy},
            #{item.deleted}
            )
        </foreach>
    </insert>

    <select id="selectDocIdsByContent" resultType="java.lang.Long">
        select distinct doc_id from ai_knowledge_segment where knowledge_id = #{knowledgeId} and text like
        concat('%',#{filterContent},'%')
    </select>
    <update id="updateVectorIdById">
        update ai_knowledge_segment set vector_id = #{vectorId} where id = #{segId}
    </update>
    <update id="updateFileNameByDocId">
        update ai_knowledge_segment set doc = #{fileName} where id = #{id}
    </update>
</mapper>