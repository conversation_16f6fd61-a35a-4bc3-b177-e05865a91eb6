<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fytec.mapper.knowledge.KnowledgeDocMapper">


    <select id="selectDocCountByKnowledgeId" resultType="com.fytec.dto.knowledge.KnowledgeDocCountDTO">
        select t.id as id,
        t.file_size as fileSize,
        s.id as segmentId
        FROM ai_knowledge_doc t
        left join ai_knowledge_segment s on
        s.doc_id = t.id and s.knowledge_id = t.knowledge_id
        where t.deleted = 0
        and t.is_visible = 1
        and t.knowledge_id = #{knowledgeId}
    </select>
    <select id="queryKnowledgeAllInOne" resultType="com.fytec.dto.knowledge.KnowledgeAllInOneDTO">
        # 个人知识库文件
        select if(doc.note_id is not null, doc.note_id, doc.id) as id, doc.file_name as name,
        if(doc.note_id is not null, 'note', 'doc') as type, 'team' as groupType, false as joined
        FROM ai_knowledge_doc doc
        inner join ai_knowledge ak on doc.knowledge_id = ak.id
        where doc.deleted = 0
        and ak.deleted = 0
        and ak.create_by = #{param.createBy}
        and ak.type = 'personal'
        <if test="param.keywords != null and param.keywords != ''">
            and doc.file_name like concat('%', #{param.keywords}, '%')
        </if>

        union all

        # 团队知识库文件
        # 我创建的共享知识库
        select if(doc.note_id is not null, doc.note_id, doc.id) as id, doc.file_name as name,
        if(doc.note_id is not null, 'note', 'doc') as type, 'team' as groupType, false as joined
        FROM ai_knowledge_doc doc
        inner join ai_knowledge ak on doc.knowledge_id = ak.id
        where doc.deleted = 0
        and ak.deleted = 0
        and ak.create_by = #{param.createBy}
        and ak.type = 'team'
        <if test="param.keywords != null and param.keywords != ''">
            and doc.file_name like concat('%', #{param.keywords}, '%')
        </if>

        union all

        # 团队知识库文件
        # 我加入的共享知识库
        select if(doc.note_id is not null, doc.note_id, doc.id) as id, doc.file_name as name,
        if(doc.note_id is not null, 'note', 'doc') as type, 'team' as groupType, false as joined
        FROM ai_knowledge_doc doc
        inner join ai_knowledge ak on doc.knowledge_id = ak.id
        where doc.deleted = 0
        and ak.deleted = 0
        and ak.type = 'team'
        and exists (select user_id from ai_knowledge_team_member aktm where aktm.knowledge_id = ak.id
        and aktm.user_id = #{param.createBy})
        <if test="param.keywords != null and param.keywords != ''">
            and doc.file_name like concat('%', #{param.keywords}, '%')
        </if>

        union all

        # 个人知识库文件夹（组）
        select akg.id, akg.name as name, 'group' as type, 'personal' as groupType, false as joined
        from ai_knowledge_group akg
        inner join ai_knowledge ak on akg.knowledge_id = ak.id
        where akg.deleted = 0
        and ak.deleted = 0
        and ak.create_by = #{param.createBy}
        and ak.type = 'personal'
        <if test="param.keywords != null and param.keywords != ''">
            and akg.name like concat('%', #{param.keywords}, '%')
        </if>

        union all

        # 团队知识库知识库文件夹（组）
        # 我创建的共享知识库
        select akg.id, akg.name as name, 'group' as type, 'team' as groupType, false as joined
        from ai_knowledge_group akg
        inner join ai_knowledge ak on akg.knowledge_id = ak.id
        where akg.deleted = 0
        and ak.deleted = 0
        and ak.create_by = #{param.createBy}
        and ak.type = 'team'
        <if test="param.keywords != null and param.keywords != ''">
            and akg.name like concat('%', #{param.keywords}, '%')
        </if>

        union all

        # 团队知识库知识库文件夹（组）
        # 我加入的共享知识库
        select akg.id, akg.name as name, 'group' as type, 'team' as groupType, true as joined
        from ai_knowledge_group akg
        inner join ai_knowledge ak on akg.knowledge_id = ak.id
        where akg.deleted = 0
        and ak.deleted = 0
        and ak.type = 'team'
        and exists (select user_id from ai_knowledge_team_member aktm where aktm.knowledge_id = ak.id
        and aktm.user_id = #{param.createBy})
        <if test="param.keywords != null and param.keywords != ''">
            and akg.name like concat('%', #{param.keywords}, '%')
        </if>

    </select>

    <select id="docSearchNameByIds" resultType="com.fytec.dto.knowledge.KnowledgeDocDTO">
        select id, file_name as fileName from ai_knowledge_doc where id in
        <foreach collection="docIds" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="selectDocNameByDocId" resultType="java.lang.String">
        select file_name from ai_knowledge_doc where id = #{docId}
    </select>

    <select id="selectDocBaseInfoByIds" resultType="com.fytec.dto.knowledge.KnowledgeDocDTO">
        select id, file_name as fileName, file_url as fileUrl from ai_knowledge_doc where id in
        <foreach collection="docIds" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and status = 'success'
        <if test="docSourceType!=null and docSourceType != ''">
            and doc_source_type = #{docSourceType}
        </if>
    </select>
</mapper>