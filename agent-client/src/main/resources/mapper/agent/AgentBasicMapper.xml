<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fytec.mapper.agent.AgentBasicMapper">

    <select id="developAgentPage" resultType="com.fytec.dto.agent.AgentBasicDTO">
        SELECT
        t.id,
        t.name,
        t.description,
        t.logo,
        t.status,
        t.update_time as updateTime,
        u.name as updateName,
        u.image_url as imageUrl
        from
        ai_agent_basic t
        left join t_sys_user u on t.update_by = u.id and t.deleted = 0
        where deleted = 0
        <if test="param.tags != null and param.tags != ''">
            and
            <foreach collection="param.tags.split(',')" item="item" index="index" open="(" close=")" separator="or">
                t.tags like CONCAT('%',#{item},'%')
            </foreach>
        </if>

        <if test="param.agentIds != null and param.agentIds.size > 0">
            and t.id in
            <foreach collection="param.agentIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.createBy != null">
            and t.create_by = #{param.createBy}
        </if>
        <if test="param.name != null and param.name != ''">
            and t.name like CONCAT('%',#{param.name},'%')
        </if>
        <if test="param.status != null and param.status != ''">
            and t.status = #{param.status}
        </if>
        order by t.update_time desc
    </select>
    <select id="queryPublishedAgentList" resultType="com.fytec.dto.application.ApplicationAgentDTO">
        select p.id as agentPublishId,
        t.id as agentId,
        t.name,
        t.description,
        t.logo,
        t.tags,
        p.publish_time as publishTime,
        p.version,
        u.name as publisher,
        if(a.id is null, false, true) as assistant,
        u.image_url as imageUrl
        from ai_agent_publish p
        inner join ai_agent_basic t on p.agent_id = t.id and t.deleted = 0
        left join t_sys_user u on p.create_by = u.id
        left join ai_assistant a on p.agent_id = a.agent_id and a.user_id = #{param.createBy}
        where t.deleted = 0
        and p.id = (select id from ai_agent_publish where agent_id = t.id order by create_time desc limit 1)
        <if test="param.name != null and param.name != ''">
            and (t.name like CONCAT('%',#{param.name},'%') or t.description like CONCAT('%',#{param.name},'%'))
        </if>
        <if test="param.tags != null and param.tags != ''">
           and
            <foreach collection="param.tags.split(',')" item="item" index="index" open="(" close=")" separator="or">
                t.tags like CONCAT('%',#{item},'%')
            </foreach>
        </if>
        <if test="param.agentIds != null and param.agentIds.size > 0">
            and t.id in
            <foreach collection="param.agentIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <choose>
            <when test="param.onlySelf">
                and t.create_by = #{param.createBy}
            </when>
            <otherwise>
                and (t.create_by = #{param.createBy} or t.`open` = 1)
            </otherwise>
        </choose>
    </select>

    <select id="queryPublishedAgentListByTags" resultType="com.fytec.dto.application.ApplicationAgentDTO">
        select p.id as agentPublishId,
        t.id as agentId,
        t.name,
        t.description,
        t.logo,
        t.tags,
        p.publish_time as publishTime,
        p.version,
        p.prologue as prologue,
        p.tips as tips,
        u.name as publisher
        from ai_agent_publish p
        inner join ai_agent_basic t on p.agent_id = t.id and t.deleted = 0
        left join t_sys_user u on p.create_by = u.id
        where t.deleted = 0
        and p.id = (select id from ai_agent_publish where agent_id = t.id order by create_time desc limit 1)
        <if test="param.name != null and param.name != ''">
            and (t.name like CONCAT('%',#{param.name},'%') or t.description like CONCAT('%',#{param.name},'%'))
        </if>
        and t.tags is not null and t.tags != ''
        <if test="param.tags != null and param.tags != ''">
            and
            <foreach collection="param.tags.split(',')" item="item" index="index" open="(" close=")" separator="or">
                t.tags like CONCAT('%',#{item},'%')
            </foreach>
        </if>
        <if test="param.agentIds != null and param.agentIds.size > 0">
            and t.id in
            <foreach collection="param.agentIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.createBy != null">
            and t.create_by = #{param.createBy}
        </if>
    </select>




</mapper>