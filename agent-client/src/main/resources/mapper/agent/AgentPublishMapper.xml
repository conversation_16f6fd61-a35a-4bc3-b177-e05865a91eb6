<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fytec.mapper.agent.AgentPublishMapper">
    <select id="selectLatestPublishesByAgentIds" resultType="com.fytec.entity.agent.AgentPublish">
        SELECT t1.* FROM ai_agent_publish t1
        INNER JOIN (
        SELECT agent_id, MAX(create_time) as max_create_time
        FROM ai_agent_publish
        WHERE agent_id IN
        <foreach item='item' collection='agentIds' open='(' separator=',' close=')'>
            #{item}
        </foreach>
        GROUP BY agent_id
        ) t2 ON t1.agent_id = t2.agent_id AND t1.create_time = t2.max_create_time
    </select>
</mapper>