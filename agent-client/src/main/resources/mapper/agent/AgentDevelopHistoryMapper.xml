<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fytec.mapper.agent.AgentDevelopHistoryMapper">
    <select id="getMaxMessageId" resultType="java.lang.Long">
        select COALESCE(MAX(message_id), 0) as next_message_id
        from ai_agent_dev_history
        where agent_id = #{agentId}
        and debug_id = #{userId}
    </select>
</mapper>