<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fytec.mapper.agent.AgentPublishHistoryMapper">
    <select id="getMaxMessageId" resultType="java.lang.Long">
        select COALESCE(MAX(message_id), 0) as next_message_id
        from ai_agent_publish_history
        where agent_publish_id = #{agentId}
        and user_id = #{userId}
    </select>
    <select id="queryPublishedAgentHistoryGroup" resultType="java.util.HashMap">
        select r.conversation_id as conversationId,
        (SELECT raw_input
        FROM ai_agent_publish_history
        WHERE third_user_id = #{thirdUserId}
        and agent_id = #{agentId}
        AND conversation_id = r.conversation_id
        and raw_input is not null
        ORDER BY id ASC
        LIMIT 1) AS name,
        (SELECT create_time
        FROM ai_agent_publish_history
        WHERE agent_id = #{agentId} and third_user_id = #{thirdUserId}
        AND conversation_id = r.conversation_id
        ORDER BY id ASC
        LIMIT 1) AS createTime
        from ai_agent_publish_history r
        where r.deleted = 0 and r.third_user_id = #{thirdUserId} and r.conversation_id is not null
        and r.agent_id = #{agentId}
        <if test="objectId != null and objectId != ''">
            and r.object_id = #{objectId}
        </if>
        <if test="objectId == null or objectId == '' ">
            and r.create_time >= DATE_SUB(CURDATE(), INTERVAL 90 DAY)
        </if>
        <if test="clientId != null and clientId != ''">
            and r.client_id = #{clientId}
        </if>

        <if test="startDate != null and startDate != ''">
            and r.create_time >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            and #{endDate} >= r.create_time
        </if>
        <if test="keyword != null and keyword != ''">
            and r.raw_input like concat('%',#{keyword},'%')
        </if>

        GROUP BY r.conversation_id;

    </select>
    <select id="getMaxMessageIdByThirdUserId" resultType="java.lang.Long">
        select COALESCE(MAX(message_id), 0) as next_message_id
        from ai_agent_publish_history
        where agent_publish_id = #{agentId}
        and third_user_id = #{thirdUserId}
    </select>
    <update id="updateReasoningById">
        update ai_agent_publish_history set reasoning = #{reason} where id = #{id}
    </update>
    <update id="updateAnswerById">
        update ai_agent_publish_history set answer = #{answer} where id = #{id}
    </update>
    <update id="updateExtraInfoById">
        update ai_agent_publish_history set extra_info = #{extraInfo} where id = #{id}
    </update>
    <update id="updateReasoningChainById">
        update ai_agent_publish_history set reasoningchain = #{reasoningchain} where id = #{id}
    </update>
    <update id="updateAgentsRunCompleteByFlowId">
        update ai_agent_publish_history set status = #{status} where flow_id = #{flowId} and status = 'success'
    </update>
    <update id="updateAgentStatusById">
        update ai_agent_publish_history set status = #{status},error = #{error} where id = #{id}
    </update>
    <update id="updateAgentType">
        update ai_agent_publish_history set type = #{type}  where id = #{id}
    </update>

</mapper>