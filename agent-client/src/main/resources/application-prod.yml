#server:
#  servlet:
#    context-path: /agent-client
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: **********************************************************************************************************************
    username: root
    password: 7890@uiop
    druid:
      initial-size: 5
      max-active: 20
      min-idle: 5
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      validation-query: SELECT 1 FROM DUAL
      test-on-borrow: false
      test-on-return: false
      test-while-idle: true
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      filters: config,stat
      remove-abandoned: true
      remove-abandoned-timeout: 180
      web-stat-filter:
        enabled: true
        url-pattern: /api/*
        exclusions:
        session-stat-enable: true
        session-stat-max-count: 1000
        profile-enable: true
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: true
        login-username: admin
        login-password: admin
        allow:
        deny:
  data:
    redis:
      host: ************
      password:
      port: 6379
      database: 2

  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

flow:
  type: langGraph
  dag:
    path: /root/airflow/dags/
  conda-env:
    condaPath: /opt/miniconda3/envs
    envName: fytec
  airflow:
    basicAuth: Basic YWlyZmxvdzphaXJmbG93
    run-url: http://************:8080/api/v1/dags/%s/dagRuns
    state-url: http://************:8080/api/v1/dags/%s/dagRuns/%s
    unpause-url: http://************:8080/api/v1/dags/%s?update_mask=is_paused
    xcom-entry-url: http://************:8080/api/v1/dags/%s/dagRuns/%s/taskInstances/%s/xcomEntries/%s
  langGraph:
    check-pointer:
      host: ************
      port: 16379
      user: #root
      password: #7890@uiop
      database: #langgraph
  api:
    prefix:
      client-url: http://************:10010
      server-url: http://************:10086


client:
  clientName: 风云智能体平台客户端
  clientId: 90bcdc3c-a481-4cc2-ae29-4abfd8bba1b6
  clientSecret: 4gg9NvpsP2JMOVPYZfpEDL3VYz9TJrWtzGc/SK8ljGM=
  tokenUrl: http://************:10086/oauth2/client_token?grant_type=client_credentials&scope=fytec:llm
  tokenPyUrl: http://************:10000/oauth/token
  doc:
    enabled-advance: true
    submitUrl: http://************:9003/parse/pdf
    taskUrl: http://************:9003/parse/result/{}
    submitCallBackUrl:  http://************:9003/parse/pdf/callback
    callBackUrl: http://************:10010/api/knowledge/doc/callBack
  image:
    process-type: vision # 默认为vision，包含文字识别和图片解释；当为ocr时，包含文字识别
    vision-model: doubao_1_5_vision_pro_250115
    orc-normal-url: http://************:10086/api/volcengine/ocr/normal
  text:
    rerank-model: gte
    rerank-url: http://************:10086/api/ali/text/rerank
    max-length: 2000
  deer:
    enabled: true
    url-prefix: http://************:10000
    file-path: https://www.fyunai.cn/deer-history



upload:
  storage: minio
  fileServerPath: https://www.fyunai.cn/files
  fileGroup: /group1
  localServerPath: /home/<USER>
minio:
  endpoint: ************:9000
  url: http://************:9000
  show-url: https://www.fyunai.cn/file/
#  access-key: dZK0msQU4sFS33OCwtea
#  secret-key: stzx818OOIh0W1kqPewBa1OV6ubLDSsDMer6KsI1
#  bucket-name: llm-public-file
  access-key: 5fBkvRyCOucLidMg
  secret-key: 35WRY30roTAcNZXsgYVDJdESGYQP41Tc
  bucket-name: llm-public-file


milvus:
  config:
    host: ************
    port: 19530
    dbName: default

jodconverter:
  enabled: true
  remote:
    enabled: true
    url: http://************:8902
    ssl:
      enabled: false

rocketmq:
  endpoint: ************:8081
  topic: LlmProdTopic


scheduler:
  oa:
    enabled-sync: true

env:
  type: prod
  loginType: oa