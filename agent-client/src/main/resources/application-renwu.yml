spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: *******************************************************************************************************************
    username: root
    password: yfb@2020@API
    druid:
      initial-size: 5
      max-active: 20
      min-idle: 5
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      validation-query: SELECT 1 FROM DUAL
      test-on-borrow: false
      test-on-return: false
      test-while-idle: true
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      filters: config,stat
      remove-abandoned: true
      remove-abandoned-timeout: 180
      web-stat-filter:
        enabled: true
        url-pattern: /api/*
        exclusions:
        session-stat-enable: true
        session-stat-max-count: 1000
        profile-enable: true
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: true
        login-username: admin
        login-password: admin
        allow:
        deny:
  data:
    redis:
      host: 127.0.0.1
#      password: fengyun123456
      port: 9701
      database: 2
#  quartz:
#    # 使用数据库存储
#    job-store-type: jdbc
#    #    jdbc:
#    #      initialize-schema: always
#    # 初始化完成后自动启动调度程序
#    autoStartup: true
#    properties:
#      org:
#        quartz:
#          # 调度器配置
#          scheduler:
#            instanceName: fyQuartzScheduler
#            instanceId: AUTO
#          # 存储配置
#          jobStore:
#            class: org.springframework.scheduling.quartz.LocalDataSourceJobStore
#            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
#            tablePrefix: QRTZ_
#            isClustered: false
#            misfireThreshold: 12000
#            clusterCheckinInterval: 15000
#            useProperties: false
#          # 线程池配置
#          threadPool:
#            threadNamePrefix: Job_Pool
#            threadPriority: 5
#            threadCount: 10
#            class: org.quartz.simpl.SimpleThreadPool

  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

#airflow:
#  basicAuth: Basic YWlyZmxvdzphaXJmbG93
#  dag:
#    path: /data/airflow/dags/
#    run-url: http://***********:8080/api/v1/dags/%s/dagRuns
#    state-url: http://***********:8080/api/v1/dags/%s/dagRuns/%s
#    unpause-url: http://***********:8080/api/v1/dags/%s?update_mask=is_paused
#    xcom-entry-url: http://***********:8080/api/v1/dags/%s/dagRuns/%s/taskInstances/%s/xcomEntries/%s
#  api:
#    prefix:
#      client-url: http://***********:10010
#      server-url: http://***********:10086

flow:
  type: prefect
  dag:
    path: /data/airflow/dags/
  airflow:
    basicAuth: Basic YWlyZmxvdzphaXJmbG93
    run-url: http://************:8080/api/v1/dags/%s/dagRuns
    state-url: http://************:8080/api/v1/dags/%s/dagRuns/%s
    unpause-url: http://************:8080/api/v1/dags/%s?update_mask=is_paused
    xcom-entry-url: http://************:8080/api/v1/dags/%s/dagRuns/%s/taskInstances/%s/xcomEntries/%s
  prefect:
    condaPath: /opt/anaconda3/bin/conda
    envName: prefect-env
  api:
    prefix:
      client-url: http://************:10010
      server-url: http://************:10086

#  rw_sys
client:
  clientName: 风云智能体平台客户端
  clientId: 8a1463ae3d8043c49e93f5937c0429d1
  clientSecret: 284b4c90e2890283e3cb3e47a0cd34a6f351b91c56bd9fa63ef0374e42f5b294
  tokenUrl: http://localhost:10010/oauth2/client_token?grant_type=client_credentials&scope=client:execute
  doc:
    submitUrl: http://localhost:10086/api/llm/baidu/task/submit
    taskUrl: http://localhost:10086/api/llm/baidu/task
  text:
    rerank-model: sip
    rerank-url: http://************:3000/v1/rerank
    max-length: 800
  image:
    process-type: vision # 默认为vision，包含文字识别和图片解释；当为ocr时，包含文字识别
    vision-model: doubao_1_5_vision_pro_250115
    orc-normal-url: http://************:10086/api/volcengine/ocr/normal
upload:
  storage: minio
  fileServerPath: http://************:9000/files
  fileGroup: /group1
  localServerPath: /data/files
minio:
  url: http://127.0.0.1:9000
  show-url: http://************:9000/
  access-key: IRftDWPQDAd3M67j
  secret-key: jIJcLtjLRvS9smQiCMxqqyBnIVimtHJW
  bucket-name: llm-public-file
  endpoint: 127.0.0.1:9000

milvus:
  config:
    host: 127.0.0.1
    port: 19530
    dbName: default

jodconverter:
  enabled: false
  remote:
    enabled: true
    url: http://127.0.0.1:8902
    ssl:
      enabled: false

env:
  type: renwu
## 默认配置
rocketmq:
  endpoint: 127.0.0.1:8081
  topic: TestTopic