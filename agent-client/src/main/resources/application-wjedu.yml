server:
  servlet:
    context-path: /agent-api
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: **************************************************************************************************************************************************
    username: aiagent
    password: IA-BBQ-670)1a
    druid:
      initial-size: 5
      max-active: 20
      min-idle: 5
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      validation-query: SELECT 1 FROM DUAL
      test-on-borrow: false
      test-on-return: false
      test-while-idle: true
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      filters: config,stat
      remove-abandoned: true
      remove-abandoned-timeout: 180
      web-stat-filter:
        enabled: true
        url-pattern: /api/*
        exclusions:
        session-stat-enable: true
        session-stat-max-count: 1000
        profile-enable: true
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: true
        login-username: admin
        login-password: admin
        allow:
        deny:
  data:
    redis:
      host: ***********
      password: FyKjb!789
      port: 6379
      database: 2

  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

flow:
  type: prefect
  dag:
    path: /root/airflow/dags/
  airflow:
    basicAuth: Basic YWlyZmxvdzphaXJmbG93
    run-url: http://localhost:8080/api/v1/dags/%s/dagRuns
    state-url: http://localhost:8080/api/v1/dags/%s/dagRuns/%s
    unpause-url: http://localhost:8080/api/v1/dags/%s?update_mask=is_paused
    xcom-entry-url: http://localhost:8080/api/v1/dags/%s/dagRuns/%s/taskInstances/%s/xcomEntries/%s
  prefect:
    condaPath: /opt/miniconda3/envs
    envName: fytec
  api:
    prefix:
      client-url: http://***********:10010
      server-url: http://***********:10086


client:
  clientName: 风云智能体平台客户端
  clientId: 5d1b1a6fab1e4c3cb8c082f0c756bb1b
  clientSecret: 39b3f2db991d4b79f0eeae90fb8a794738e8c7e9bdd15c711e59ee574f174bdf
  tokenUrl: https://www.fyunai.com/agent-mgmt/oauth2/client_token?grant_type=client_credentials&scope=fytec:llm
  doc:
    submitUrl: https://localhost/mineru/parse/pdf
    taskUrl: https://localhost/mineru/parse/result/{}
    enabled-advance: false
  image:
    process-type: vision # 默认为vision，包含文字识别和图片解释；当为ocr时，包含文字识别
    vision-model: olmOcr
  text:
    rerank-model: wj_edu
    rerank-url: http://localhost:10086/api/ali/text/rerank
    max-length: 500



upload:
  storage: minio
  fileServerPath: http://127.0.0.1/files
  fileGroup: /group1
  localServerPath: /data/files
minio:
  endpoint: ***********:9000
  url: http://***********:9000
  show-url: http://zhjy.jswjedu.com/
  access-key: wohQTb3jTgEuhHrW
  secret-key: N4ohR03yCNUxfqJyzgINJRSIVvULfdoG
  bucket-name: llm-public-file

milvus:
  config:
    host: ***********
    port: 19530
    dbName: doubao

jodconverter:
  enabled: false
  remote:
    enabled: true
    url: http://***********:8902
    ssl:
      enabled: false

rocketmq:
  endpoint: 127.0.0.1:8081
  topic: TestTopic

sso:
  auth-host: fyedu.cnsaas.com
  clientId: 2da20021-059b-4fa3-acd8-3f7d5b1f934f
  clientSecret: 7529ab71-6610-4a1c-89b0-350efe1801d2
  redirect_uri: https://llm.cnsaas.com/test/agent-client/api/sso/login/wj-edu
  final_redirect_uri: https://llm.cnsaas.com/test/agent-web/
