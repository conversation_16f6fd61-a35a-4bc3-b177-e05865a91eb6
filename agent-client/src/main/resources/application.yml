server:
  port: 10010


env:
  type: default
  loginType: default
  level:
    sql:
      print: true # 日志级别，是否sql智能体输出相关日志

spring:
  application:
    name: agent-client
  config:
    import:
      - classpath:config/external/xfyun.yml
      - classpath:config/external/text-in.yml

springdoc:
  api-docs:
    enabled: true # 开启OpenApi接口
    path: /doc-client/api-client-docs  # 自定义路径，默认为 "/v3/api-docs"
  swagger-ui:
    enabled: true # 开启swagger界面，依赖OpenApi，需要OpenApi同时开启
    path: /doc-client/api-client-swagger # 自定义路径，默认为"/swagger-ui/index.html"

logging:
  level:
    ROOT: INFO
    com.fytec: DEBUG
    org.jodconverter: DEBUG

mybatis-plus:
  mapper-locations: classpath:/mapper/*Mapper.xml,classpath:/mapper/**/*Mapper.xml
  global-config:
    banner: false
    db-config:
      updateStrategy: IGNORED

sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: Authorization
  token-prefix: Bearer
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 2592000
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: true
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: uuid
  # 是否输出操作日志
  is-log: true
  is-read-cookie: false

self:
  client:
    clientId: 49219f34e54e4886a9fe4053c1403a84
    clientSecret: 4d6da556ccc9a974769134d4b61af4b4f7796988922addd9764519d66d381651
  rsa:
    public-key: MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA5AKhhFg97XWeR1tB/yDKa98QJhNwBuhsgK7s/Md11hpRIAahqLO0LE+Bs8XN74IcbDb9klGTDzmagFwS2s9luK5INT4/yiLfO9OEdU2hkzt841TfQYLSrRoBoE2fRFUi6yCK1YpatWm8dK+ubHyICJ+CDScEAgut5Hviu1URLoXWaEbw1psKgf15y99/FQHyxMYwjDIhPGlKHX/1yWdpqdKH/RxGgxfgvSM8RfxryMCn9nzGw+O2j/2bGq/Np1xhpcQZHux0sIv+1vaW/E3EvQjLOjfrdO8dRSaO3bRHIWfLI169Tj1Tx/1oyMVRtK6N95PVuATVLOI9L3WQZCrbtcl8xL9lR43YhCHyt58mGVNok+k/bR6tbMaB9X1v/S4LG+XrgMzUmyE3qwpLgYAIP1dgIN3XlCsaFfo1/q2bNFtHOSIPcDiPQ1RqdnH9NtdCC7NJZj7WVGaHvXSSfaoEmvobyX6j2e+A/KEd3Dcg7qmRF4ZD20uRxQJQoNDCaJQ7L+gWSpJ/Wl/XSJztN0CvRVaafHwfpvcFmrYSO/s8e79bsZeMjRLkR+dyeIyMmZuiOdy6Wn+L7EdbJjWDp8+SUroCDM/R7idd9zcAMmvicM0hlFzLFmi8VJ9FdGf3ys9pVORl5FzTleumDT6AHYXUAnlRnV24CLnKntyo8j/+4/0CAwEAAQ==
    private-key: 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

scheduler:
  oa:
    enabled-sync: false

wechat:
  work:
    #    corp-id: wx056e083634ab6bc7
    #    agent-id: 1000055
    #    corp-secret: 0IucwbA_BxO-wZWxkcpsPdUnadvCywMbNE4x_8aCZQo
    corp-id: ww9667234a77b69ac4
    agent-id: 1000003
    corp-secret: OhA22wpeCNrTgeFQuNyFlk0n6O1ZpsVda08wILonEtk
    redirect-uri: https://llm.cnsaas.com/agent-client/api/wechat-work/login

sso:
  auth-host: portapigw.sipedu.cn

## 默认配置
rocketmq:
  endpoint: 127.0.0.1:8081
  topic: TestTopic


agent:
  utils:
    echart:
      format: 136
