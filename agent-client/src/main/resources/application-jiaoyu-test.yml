server:
  port: 10020
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: **************************************************************************************************************************
    username: root
    password: Yjjy_fykj_54!
    druid:
      initial-size: 5
      max-active: 20
      min-idle: 5
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      validation-query: SELECT 1 FROM DUAL
      test-on-borrow: false
      test-on-return: false
      test-while-idle: true
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      filters: config,stat
      remove-abandoned: true
      remove-abandoned-timeout: 180
      web-stat-filter:
        enabled: true
        url-pattern: /api/*
        exclusions:
        session-stat-enable: true
        session-stat-max-count: 1000
        profile-enable: true
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: true
        login-username: admin
        login-password: admin
        allow:
        deny:
  data:
    redis:
      host: ***********
      password: fyAI7890!
      port: 6379
      database: 3
  quartz:
    # 使用数据库存储
    job-store-type: jdbc
    #    jdbc:
    #      initialize-schema: always
    # 初始化完成后自动启动调度程序
    autoStartup: false
    properties:
      org:
        quartz:
          # 调度器配置
          scheduler:
            instanceName: fyQuartzScheduler-test
            instanceId: AUTO
          # 存储配置
          jobStore:
            class: org.springframework.scheduling.quartz.LocalDataSourceJobStore
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
            tablePrefix: QRTZ_
            isClustered: true                    # 启用集群
            misfireThreshold: 12000
            clusterCheckinInterval: 15000
            acquireTriggersWithinLock: true      # 避免任务被多个节点重复获取
            useProperties: false
          # 线程池配置
          threadPool:
            threadNamePrefix: Job_Pool
            threadPriority: 5
            threadCount: 10
            class: org.quartz.simpl.SimpleThreadPool

  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

#airflow:
#  basicAuth: Basic YWlyZmxvdzphaXJmbG93
#  dag:
#    path: /data/airflow/dags/
#    run-url: http://***********:8080/api/v1/dags/%s/dagRuns
#    state-url: http://***********:8080/api/v1/dags/%s/dagRuns/%s
#    unpause-url: http://***********:8080/api/v1/dags/%s?update_mask=is_paused
#    xcom-entry-url: http://***********:8080/api/v1/dags/%s/dagRuns/%s/taskInstances/%s/xcomEntries/%s
#  api:
#    prefix:
#      client-url: http://***********:10020
#      server-url: http://***********:10086

flow:
  type: prefect
  dag:
    path: /data/airflow/dags/
  airflow:
    basicAuth: Basic YWlyZmxvdzphaXJmbG93
    run-url: http://***********:8080/api/v1/dags/%s/dagRuns
    state-url: http://***********:8080/api/v1/dags/%s/dagRuns/%s
    unpause-url: http://***********:8080/api/v1/dags/%s?update_mask=is_paused
    xcom-entry-url: http://***********:8080/api/v1/dags/%s/dagRuns/%s/taskInstances/%s/xcomEntries/%s
  prefect:
    condaPath: /root/miniconda3/envs
    envName: prefect-env
  api:
    prefix:
      client-url: http://***********:10020
      server-url: http://***********:10086
  conda-env:
    condaPath: /root/miniconda3/envs
    envName: prefect-env

client:
  clientName: 风云智能体平台客户端
  clientId: 90bcdc3c-a481-4cc2-ae29-4abfd8bba1b6
  clientSecret: 4gg9NvpsP2JMOVPYZfpEDL3VYz9TJrWtzGc/SK8ljGM=
  tokenUrl: https://llm.cnsaas.com/test/agent-mgmt/oauth2/client_token?grant_type=client_credentials&scope=fytec:llm
  doc:
    submitUrl: http://localhost:10086/api/llm/baidu/task/submit
    taskUrl: http://localhost:10086/api/llm/baidu/task
  text:
    rerank-model: sip
    rerank-url: https://espace.sipedu.org/dmxllm/tmp/v1/rerank
    max-length: 800
  image:
    process-type: vision # 默认为vision，包含文字识别和图片解释；当为ocr时，包含文字识别
    vision-model: doubao_1_5_vision_pro_250115
    orc-normal-url: http://************:10086/api/volcengine/ocr/normal
upload:
  storage: minio
  fileServerPath: https://ai.sipedu.cn/files
  fileGroup: /group1
  localServerPath: /data/files
minio:
  url: http://***********:9000
  show-url: https://ai.sipedu.cn/
  access-key: nj9gqM4zyzLVMWQ5
  secret-key: x24awcaXDdjkdhKjGZwEOGKYdPcm3V4g
  bucket-name: llm-public-file
  endpoint: ***********:9000

milvus:
  config:
    host: ***********
    port: 19530
    dbName: default

jodconverter:
  enabled: false
  remote:
    enabled: true
    url: http://***********:8902
    ssl:
      enabled: false

env:
  type: jiaoyu
  code: test

sso:
  auth-host: portapigw.sipedu.cn

rocketmq:
  endpoint: 127.0.0.1:8081
  topic: TestTopic
agent:
  utils:
    echart:
      format: 24