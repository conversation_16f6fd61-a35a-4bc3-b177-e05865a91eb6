<?xml version="1.0" encoding="UTF-8"?>

<configuration scan="true">
    <include resource="org/springframework/boot/logging/logback/base.xml"/>
    <property name="APP_NAME" value="agent-client"/>
    <property name="LOG_HOME" value="./var/log/${APP_NAME}"/>

    <appender name="FILE_SYS" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/${APP_NAME}.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>15</maxHistory>
        </rollingPolicy>
        <encoder>
            <charset>utf-8</charset>
            <Pattern>%d{yyyy/MM/dd-HH:mm:ss} [%thread] %-5level %logger - %msg%n</Pattern>
        </encoder>
    </appender>

    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 错误日志记录规则 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <encoder>
            <charset>utf-8</charset>
            <Pattern>%d{yyyy/MM/dd-HH:mm:ss} [%thread] %-5level %logger - %msg%n</Pattern>
        </encoder>
        <!-- 滚动策略 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/${APP_NAME}-error.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>15</maxHistory>
        </rollingPolicy>
    </appender>

    <logger name="com.fytec" level="#logback.loglevel#"/>
    <logger name="springfox" level="WARN"/>
    <logger name="javax.xml.bind" level="WARN"/>
    <logger name="javax.xml.bind" level="WARN"/>
    <logger name="c.n.d.shared" level="WARN"/>
    <logger name="org.xnio" level="WARN"/>
    <logger name="com.netflix.discovery" level="WARN"/>
    <logger name="com.sun" level="WARN"/>
    <logger name="com.zaxxer" level="WARN"/>
    <logger name="org.apache" level="WARN"/>
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.springframework.web" level="WARN"/>

    <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator">
        <resetJUL>true</resetJUL>
    </contextListener>

    <root level="#logback.loglevel#">
        <appender-ref ref="FILE_SYS"/>
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ERROR_FILE" />
    </root>
</configuration>
