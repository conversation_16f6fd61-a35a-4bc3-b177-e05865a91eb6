#server:
#  servlet:
#    context-path: /agent-client
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    #    url: ************************************************************************************************************************************************
    #    username: root
    #    password: 7890@uiop
    url: jdbc:mysql://**************:3306/agent-client?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=GMT%2b8&allowPublicKeyRetrieval=true
    username: root
    password: 7890@uiop
    druid:
      initial-size: 5
      max-active: 20
      min-idle: 5
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      validation-query: SELECT 1 FROM DUAL
      test-on-borrow: false
      test-on-return: false
      test-while-idle: true
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      filters: config,stat
      remove-abandoned: true
      remove-abandoned-timeout: 180
      web-stat-filter:
        enabled: true
        url-pattern: /api/*
        exclusions:
        session-stat-enable: true
        session-stat-max-count: 1000
        profile-enable: true
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: true
        login-username: admin
        login-password: admin
        allow:
        deny:
  data:
    redis:
      #      host: 127.0.0.1
      host: **************
      password: 7890@uiop
      port: 6379
      database: 2

  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

flow:
  type: langGraph
  dag:
    path: /Users/<USER>/airflow/dags
  conda-env:
    condaPath: /Users/<USER>/opt/anaconda3/envs
    envName: prefect-env
  airflow:
    basicAuth: Basic YWlyZmxvdzphaXJmbG93
    run-url: http://**************:8080/api/v1/dags/%s/dagRuns
    state-url: http://**************:8080/api/v1/dags/%s/dagRuns/%s
    unpause-url: http://**************:8080/api/v1/dags/%s?update_mask=is_paused
    xcom-entry-url: http://**************:8080/api/v1/dags/%s/dagRuns/%s/taskInstances/%s/xcomEntries/%s
  langGraph:
    check-pointer:
      host: **************
      port: 3306
      user: root
      password: 7890@uiop
      database: langgraph
  api:
    prefix:
      client-url: http://localhost:10010
      server-url: http://localhost:10086


client:
  clientName: 风云智能体平台客户端
  clientId: 90bcdc3c-a481-4cc2-ae29-4abfd8bba1b6
  clientSecret: 4gg9NvpsP2JMOVPYZfpEDL3VYz9TJrWtzGc/SK8ljGM=
  tokenUrl: http://localhost:10086/oauth2/client_token?grant_type=client_credentials&scope=fytec:llm
  tokenPyUrl: http://localhost:10000/oauth/token
  doc:
    enabled-advance: true
    submitUrl: https://llm.cnsaas.com/mineru/parse/pdf
    taskUrl: https://llm.cnsaas.com/mineru/parse/result/{}
    submitCallBackUrl: http://localhost:10000/parse/pdf/callback
    callBackUrl: http://localhost:10010/api/knowledge/doc/callBack
  image:
    process-type: vision # 默认为vision，包含文字识别和图片解释；当为ocr时，包含文字识别
    vision-model: doubao_1_5_vision_pro_250115
    orc-normal-url: http://localhost:10086/api/volcengine/ocr/normal
  text:
    rerank-model: gte
    rerank-url: http://localhost:10086/api/ali/text/rerank
    max-length: 2000
  deer:
    enabled: true
    url-prefix: http://localhost:10000
    file-path: http://localhost:10000/deer-history



upload:
  storage: minio
  fileServerPath: http://127.0.0.1/files
  fileGroup: /group1
  localServerPath: /Users/<USER>/files
minio:
  #  endpoint: localhost:9000
  #  url: http://localhost:9000
  #  show-url: http://localhost:9000/
  #  access-key: NVIHBK84yZHy8AVJ
  #  secret-key: F4S7KGuaBc9hgcWxrBcK1Lq3AZkiYIhD
  #  bucket-name: llm-public-file

  endpoint: **************:9000
  url: http://**************:9000
  show-url: http://**************:9000/
  access-key: vAxrwsgUoZhAWYtm
  secret-key: U8k0Nr681DIvx8AFy77CFc5B6Sd09I7a
  bucket-name: llm-public-file

milvus:
  config:
    #    host: 127.0.0.1
    host: **************
    port: 19530
    dbName: default

jodconverter:
  enabled: true
  remote:
    enabled: true
    url: http://**************:8902
    ssl:
      enabled: false

rocketmq:
  endpoint: 127.0.0.1:8081
  topic: LlmTestTopic

scheduler:
  oa:
    enabled-sync: false

env:
  type: local
  loginType: oa