spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: ***********************************************************************************************************************
    username: root
    password: 7890@uiop
    druid:
      initial-size: 5
      max-active: 20
      min-idle: 5
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      validation-query: SELECT 1 FROM DUAL
      test-on-borrow: false
      test-on-return: false
      test-while-idle: true
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      filters: config,stat
      remove-abandoned: true
      remove-abandoned-timeout: 180
      web-stat-filter:
        enabled: true
        url-pattern: /api/*
        exclusions:
        session-stat-enable: true
        session-stat-max-count: 1000
        profile-enable: true
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: true
        login-username: admin
        login-password: admin
        allow:
        deny:
  data:
    redis:
      host: *************
      password: 7890@uiop
      port: 6379
      database: 2

  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

flow:
  type: prefect
  dag:
    path: /root/airflow/dags/


client:
  clientName: 风云智能体平台客户端
  clientId: 90bcdc3c-a481-4cc2-ae29-4abfd8bba1b6
  clientSecret: 4gg9NvpsP2JMOVPYZfpEDL3VYz9TJrWtzGc/SK8ljGM=
  doc:
    submitUrl: http://localhost:9003/parse/pdf
    taskUrl: http://localhost:9003/parse/result/{}
    enabled-advance: false
  image:
    process-type: vision # 默认为vision，包含文字识别和图片解释；当为ocr时，包含文字识别
    vision-model: olmOCR-7B-0225-preview
  text:
    rerank-model: ning_xia
    max-length: 500

upload:
  storage: minio
  fileServerPath: http://*************
  fileGroup: /group1
  localServerPath: /data/files
minio:
  url: http://*************:9000
  show-url: http://*************:9000/
  access-key: d52elZoGIZyrKjTq
  secret-key: 7BVX26z4Blbu0YF33ciTHy6PNQRLacXf
  bucket-name: llm-public-file

milvus:
  config:
    host: *************
    port: 19530
    dbName: default

jodconverter:
  enabled: true
  remote:
    enabled: true
    url: http://*************:8902
    ssl:
      enabled: false

rocketmq:
  endpoint: 127.0.0.1:8081
  topic: TestTopic
