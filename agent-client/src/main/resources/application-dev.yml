spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: ************************************************************************************************************************
    username: root
    password: 7890@uiop
    druid:
      initial-size: 5
      max-active: 20
      min-idle: 5
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      validation-query: SELECT 1 FROM DUAL
      test-on-borrow: false
      test-on-return: false
      test-while-idle: true
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      filters: config,stat
      remove-abandoned: true
      remove-abandoned-timeout: 180
      web-stat-filter:
        enabled: true
        url-pattern: /api/*
        exclusions:
        session-stat-enable: true
        session-stat-max-count: 1000
        profile-enable: true
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: true
        login-username: admin
        login-password: admin
        allow:
        deny:
  data:
    redis:
      host: **************
      password: 7890@uiop
      port: 6379
      database: 2
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

flow:
  type: prefect
  dag:
    path: /root/airflow/dags/
  airflow:
    basicAuth: Basic YWlyZmxvdzphaXJmbG93
    run-url: http://**************:8080/api/v1/dags/%s/dagRuns
    state-url: http://**************:8080/api/v1/dags/%s/dagRuns/%s
    unpause-url: http://**************:8080/api/v1/dags/%s?update_mask=is_paused
    xcom-entry-url: http://**************:8080/api/v1/dags/%s/dagRuns/%s/taskInstances/%s/xcomEntries/%s
  prefect:
    condaPath: /opt/miniconda3/envs
    envName: prefect-env
  api:
    prefix:
      client-url: http://localhost:10010
      server-url: http://localhost:10086


client:
  clientName: 风云智能体平台客户端
  clientId: 90bcdc3c-a481-4cc2-ae29-4abfd8bba1b6
  clientSecret: 4gg9NvpsP2JMOVPYZfpEDL3VYz9TJrWtzGc/SK8ljGM=
  tokenUrl: http://**************:10086/oauth2/client_token?grant_type=client_credentials&scope=fytec:llm
  doc:
    submitUrl: https://llm.cnsaas.com/mineru/parse/pdf
    taskUrl: https://llm.cnsaas.com/mineru/parse/result/{}
    enabled-advance: false
  image:
    vision-model: doubao_1_5_vision_pro_250115
    orc-normal-url: http://**************:10086/api/volcengine/ocr/normal
  text:
    rerank-model: gte
    rerank-url: http://**************:10086/api/ali/text/rerank
    max-length: 800


upload:
  storage: minio
  fileServerPath: http://**************/files
  fileGroup: /group1
  localServerPath: /home/<USER>
minio:
  url: http://**************:9000
  show-url: http://**************:9000/
  access-key: vAxrwsgUoZhAWYtm
  secret-key: U8k0Nr681DIvx8AFy77CFc5B6Sd09I7a
  bucket-name: llm-public-file

milvus:
  config:
    host: **************
    port: 19530
    dbName: default


jodconverter:
  enabled: true
  remote:
    enabled: true
    url: http://**************:8902
    ssl:
      enabled: false

rocketmq:
  endpoint: 127.0.0.1:8081
  topic: TestTopic

scheduler:
  oa:
    enabled-sync: true