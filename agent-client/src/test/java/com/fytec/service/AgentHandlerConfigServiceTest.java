package com.fytec.service;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.fytec.dto.agent.AgentHandlerConfigQueryDTO;
import com.fytec.entity.agent.AgentHandlerConfig;
import com.fytec.service.agent.AgentHandlerConfigService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.core.env.Environment;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Random;

/**
 * Copyright (C), 2025-2099
 * AgentHandlerConfigService test
 *
 * <AUTHOR> lix
 * @date :   2025/6/10 10:24
 * Version: V1.0.0
 */
@SpringBootTest(properties = "spring.profiles.active=local-lx")
public class AgentHandlerConfigServiceTest {

    @Value("${spring.profiles.active:''}")
    private String env;

    @BeforeEach
    public void setUp() {
        // 打印当前激活的配置文件
        System.out.println("Active profiles: " +env);
    }

    @Autowired
    AgentHandlerConfigService agentHandlerConfigService;

    @Test
    public void testList() {
        List<AgentHandlerConfig> list = agentHandlerConfigService.list();
        System.out.println(list);
    }
    @Test
    public void testSave() {
        String type = null;
        agentHandlerConfigService.save(createAgentHandlerConfig(type));
        AgentHandlerConfigQueryDTO queryDTO = new AgentHandlerConfigQueryDTO();
        queryDTO.setEnvType(env);
        List<AgentHandlerConfig> list = agentHandlerConfigService.list(queryDTO);
        System.out.println(list);
    }

     public AgentHandlerConfig createAgentHandlerConfig(String type) {
         AgentHandlerConfig testConfig = new AgentHandlerConfig();
         testConfig.setEnvType(StringUtils.isNotBlank(type)?type:env);
         // 设置一个随机整数数字作为agentId
         testConfig.setAgentId(new Random().nextLong(0,1001));
         testConfig.setDescription("Test description");
         testConfig.setThinkAgentId(new Random().nextLong(0,1001));
         testConfig.setFormatAgentId(new Random().nextLong(0,1001));
         testConfig.setErrorMsg("No error");
         HashMap<String, String> sqlConfigs = new HashMap<>();
         sqlConfigs.put("host", "**********");
         sqlConfigs.put("port", "3306");
         sqlConfigs.put("schema", "foundationlib");
         sqlConfigs.put("username", "wjai");
         sqlConfigs.put("password", "Wjai@202505");
         testConfig.setSqlConfigs(sqlConfigs);
         testConfig.setCreateTime(LocalDateTime.now());
         testConfig.setUpdateTime(LocalDateTime.now());
         return testConfig;
     }
}
