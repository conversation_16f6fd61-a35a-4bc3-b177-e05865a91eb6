package com.fytec.embedded;

import com.fytec.dto.QuartzBean;
import com.fytec.service.quartz.QuartzService;
import com.fytec.util.UUIDGenerator;
import org.junit.jupiter.api.Test;
import org.quartz.Scheduler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

@SpringBootTest(properties = "spring.profiles.active=yj")
public class QuartzTest {

    @Autowired
    private QuartzService quartzService;

    @Autowired
    private Scheduler scheduler;

    @Test
    public void testAddQuartz() {
        QuartzBean quartzBean = new QuartzBean();
        quartzBean.setName("test");
        quartzBean.setJobClass("com.fytec.task.SimpleTask");
        quartzBean.setCron("0 * * * * ?");
        quartzService.createScheduleJob(scheduler,quartzBean);
    }

    @Test
    public void testUpdateQuartz() {
        QuartzBean quartzBean = new QuartzBean();
        quartzBean.setId(1);
        quartzBean.setName("test");
        quartzBean.setJobClass("com.fytec.task.SimpleTask");
        quartzBean.setCron("0 * * * * ?");
        Map<String,Object> params = new HashMap<>();
        params.put("data", LocalDate.now().toString());
        quartzBean.setParams(params);
        quartzService.updateScheduleJob(scheduler,quartzBean);
    }

    @Test
    public void testDeleteQuartz() {
        quartzService.deleteScheduleJob(scheduler,1);
    }
}