package com.fytec.embedded;

import com.fytec.dto.llm.EmbeddedCallDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(properties = "spring.profiles.active=local")
public class EmbeddedTest {

    @Autowired
    private DynamicEmbeddedCallService dynamicEmbeddedCallService;

    @Test
    public void testEmbedded() {
        EmbeddedCallDTO dto = new EmbeddedCallDTO();
        dto.setModelType("");
        dto.setSegments(null);
        dto.setUrl("111");
        dto.setMethodName("callDouBaoEmbedded");
        dynamicEmbeddedCallService.callEmbedded(dto);
    }
}