<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.fytec</groupId>
        <artifactId>agent-dependencies</artifactId>
        <version>1.0-SNAPSHOT</version>
        <relativePath>agent-dependencies/pom.xml</relativePath>
    </parent>


    <artifactId>agent</artifactId>
    <name>${project.artifactId}</name>
    <packaging>pom</packaging>

    <properties>
        <java.version>21</java.version>
        <resource.delimiter>@</resource.delimiter>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    </properties>

    <modules>
        <module>agent-dependencies</module>
        <module>agent-common</module>
        <module>agent-flow</module>
        <module>agent-management</module>
        <module>agent-client</module>
        <module>agent-model</module>
        <module>agent-quartz</module>
    </modules>

    <!-- 环境 -->
    <profiles>
        <!-- 开发 -->
        <profile>
            <id>local</id>
            <activation>
                <!--默认激活配置-->
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <!--当前环境-->
                <profile>local</profile>
                <!--跳过构建源代码包-->
                <skip.build.source.package>false</skip.build.source.package>
                <!--不执行git commit 构建-->
                <skip.build.git.commit.info>true</skip.build.git.commit.info>
            </properties>
        </profile>
        <!-- 生产 -->
        <profile>
            <id>prod</id>
            <properties>
                <!--当前环境-->
                <profile>prod</profile>
                <!--跳过构建源代码包-->
                <skip.build.source.package>false</skip.build.source.package>
                <!--不执行git commit 构建-->
                <skip.build.git.commit.info>false</skip.build.git.commit.info>
            </properties>
        </profile>
    </profiles>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>io.github.git-commit-id</groupId>
                    <artifactId>git-commit-id-maven-plugin</artifactId>
                    <configuration>
                        <skip>${skip.build.git.commit.info}</skip>
                        <!-- 检查的仓库根目录，${project.basedir}：项目根目录，即包含pom.xml文件的目录 -->
                        <dotGitDirectory>${project.basedir}/.git</dotGitDirectory>
                        <!-- 生成git属性文件，默认false：不生成 -->
                        <generateGitPropertiesFile>true</generateGitPropertiesFile>
                        <dateFormat>yyyy-MM-dd HH:mm:ss</dateFormat>
                        <gitDescribe>
                            <skip>false</skip>
                            <always>false</always>
                            <dirty>-dirty</dirty>
                        </gitDescribe>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <configuration>
                    <skipSource>${skip.build.source.package}</skipSource>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>