package com.fytec.embedded;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fytec.dto.llm.EmbeddedCallDTO;
import com.fytec.dto.llm.EmbeddedProcessDTO;
import com.fytec.embedded.local.NingXiaEmbeddedService;
import com.fytec.embedded.local.WjEduEmbeddedService;
import com.fytec.token.ClientTokenService;
import com.mashape.unirest.http.Unirest;
import com.volcengine.ark.runtime.model.embeddings.Embedding;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.fytec.constant.ProjectApiKey.SIP_API_KEY;


@Slf4j
@Component
public class EmbeddedService {

    private final WebClient webClient;

    private final ClientTokenService clientTokenService;

    public EmbeddedService(ClientTokenService clientTokenService) {
        this.clientTokenService = clientTokenService;
        this.webClient = WebClient.create();
    }

    /**
     * 调用豆包大模型的embedding接口
     *
     * @param dto
     * @return
     */
    public List<List<Float>> callDouBaoEmbedded(EmbeddedCallDTO dto) {
        List<List<Float>> vectors = new ArrayList<>();
        EmbeddedProcessDTO embeddedProcessDTO = new EmbeddedProcessDTO();
        embeddedProcessDTO.setInputs(dto.getSegments());
        embeddedProcessDTO.setModelKey(dto.getModelType());

        Mono<List<Embedding>> mono = webClient.post()
                .uri(dto.getUrl())
                .contentType(MediaType.APPLICATION_JSON)
                .headers(httpHeaders -> {
                    httpHeaders.setBearerAuth(clientTokenService.getToken());
                })
                .body(BodyInserters.fromValue(embeddedProcessDTO))
                .retrieve()
                .bodyToFlux(Embedding.class)
                .collectList();
        if (mono != null) {
            List<Embedding> embeddings = mono.block();
//            log.info("embeddings:{}",embeddings);
            embeddings.forEach(embedding -> {
                vectors.add(convert(embedding.getEmbedding()));
            });
        }
        return vectors;
    }

    private List<Float> convert(List<Double> doubleList) {
        List<Float> floatList = new ArrayList<>();
        for (Double value : doubleList) {
            floatList.add(value.floatValue());
        }
        return floatList;
    }


    /**
     * 调用实验室的embedding接口
     *
     * @param dto
     * @return
     */
    @SneakyThrows
    public List<List<Float>> callSysEmbedded(EmbeddedCallDTO dto) {
        List<List<Float>> vectors = new ArrayList<>();
//        System.out.println("调用实验室的embedding接口");

        // 创建请求体
        List<String> segments = dto.getSegments();
        if (segments == null) {
            log.error("向量化片段为空");
            return vectors;
        }
        // 每10个片段为一组
        List<List<String>> segmentGroups = new ArrayList<>();
        for (String segment : segments) {
            if (segmentGroups.isEmpty() || segmentGroups.get(segmentGroups.size() - 1).size() >= 10) {
                segmentGroups.add(new ArrayList<>());
            }
            segmentGroups.get(segmentGroups.size() - 1).add(segment);
        }
        log.info("调用实验室embedded接口，总片段数量：" + segments.size());
        for (List<String> inputList : segmentGroups) {
            log.info("调用实验室embedded接口：" + inputList.size());
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", "m3e");
            if ("sipM3e".equals(dto.getModelType())) {
                requestBody.put("model", "m3e");
            } else if ("sipBgeM3".equals(dto.getModelType())) {
                requestBody.put("model", "bge-m3");
            }
            requestBody.put("input", inputList);

            ObjectMapper objectMapper = new ObjectMapper();
            String jsonBody = objectMapper.writeValueAsString(requestBody);

            com.mashape.unirest.http.HttpResponse<String> response = Unirest.post(dto.getUrl())
                    .header("Authorization", "Bearer " + SIP_API_KEY)
                    .header("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .body(jsonBody)
                    .asString();
            JSONObject parseObj = JSON.parseObject(response.getBody());
//            log.info("调用实验室embedded接口返回数据："+JSON.toJSONString(parseObj));
            JSONArray dataList = parseObj.getJSONArray("data");
            if (dataList == null) {
                log.info("调用实验室embedded接口：" + response.getStatus());
                log.info("调用实验室embedded接口：" + response.getBody());
                log.info("调用实验室embedded接口：" + segments.size());
            }
            for (Object o : dataList) {
                JSONArray embList = ((JSONObject) o).getJSONArray("embedding");
                List<Float> floatList = new ArrayList<>();
                for (Object value : embList) {
                    if (value instanceof BigDecimal) {
                        floatList.add(((BigDecimal) value).floatValue());
                    } else if (value instanceof Double) {
                        floatList.add(((Double) value).floatValue());
                    } else {
                        floatList.add(((Float) value));
                    }
                }
                vectors.add(floatList);
            }
        }
        return vectors;
    }

    public List<List<Float>> callNxEmbedded(EmbeddedCallDTO dto) {
        NingXiaEmbeddedService service = new NingXiaEmbeddedService();
        return service.processEmbedded(dto);
    }

    public List<List<Float>> callWjEduEmbedded(EmbeddedCallDTO dto) {
        WjEduEmbeddedService service = new WjEduEmbeddedService();
        return service.processEmbedded(dto);
    }
}
