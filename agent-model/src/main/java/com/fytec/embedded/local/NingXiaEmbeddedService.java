package com.fytec.embedded.local;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fytec.dto.llm.EmbeddedCallDTO;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
public class NingXiaEmbeddedService {

    //政务内网 实验室的
    public static void main(String[] args) throws Exception {
        NingXiaEmbeddedService service = new NingXiaEmbeddedService();
        EmbeddedCallDTO dto = new EmbeddedCallDTO();
        dto.setUrl("http://111.51.197.134:8081/embed");

        ArrayList<String> segments = new ArrayList<>();
        segments.add("你好1");
        segments.add("你好2");
        dto.setSegments(segments);
        List<List<Float>> vectors = service.processEmbedded(dto);
        System.out.println(vectors);
    }

    @SneakyThrows
    public List<List<Float>> processEmbedded(EmbeddedCallDTO dto) {
        List<List<Float>> vectors = new ArrayList<>();

        // 创建请求体
        List<String> segments = dto.getSegments();
        if (segments == null) {
            log.error("向量化片段为空");
            return vectors;
        }
        // 每10个片段为一组

        log.info("调用宁夏embedded接口，总片段数量：{}", segments.size());
        for (String segment : segments) {
            List<String> inputList = new ArrayList<>();
            inputList.add(segment);
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("inputs", inputList);

            ObjectMapper objectMapper = new ObjectMapper();
            String jsonBody = objectMapper.writeValueAsString(requestBody);

            HttpResponse<String> response = Unirest.post(dto.getUrl())
//                    .header("Authorization", "Bearer " + sipApiKey)
                    .header("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                    .body(jsonBody)
                    .asString();
            JSONArray dataList = JSON.parseArray(response.getBody());
            if (dataList == null) {
                log.info("调用宁夏embedded接口处理状态：{}", response.getStatus());
                log.info("调用宁夏embedded接口处理返回：{}", response.getBody());
                continue;
            }
            for (Object o : dataList) {
                JSONArray embList = (JSONArray) o;
                List<Float> floatList = new ArrayList<>();
                for (Object value : embList) {
                    if (value instanceof BigDecimal) {
                        floatList.add(((BigDecimal) value).floatValue());
                    } else if (value instanceof Double) {
                        floatList.add(((Double) value).floatValue());
                    } else {
                        floatList.add(((Float) value));
                    }
                }
                vectors.add(floatList);
            }
        }
        return vectors;
    }
}
