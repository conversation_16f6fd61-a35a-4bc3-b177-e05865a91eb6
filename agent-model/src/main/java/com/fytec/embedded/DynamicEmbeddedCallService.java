package com.fytec.embedded;

import com.fytec.dto.llm.EmbeddedCallDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class DynamicEmbeddedCallService {


    private final EmbeddedService embeddedService;

    public DynamicEmbeddedCallService(EmbeddedService embeddedService) {
        this.embeddedService = embeddedService;
    }


    /**
     * 调用大模型的embedding接口
     * @param dto
     * @return
     */
    public List<List<Float>> callEmbedded(EmbeddedCallDTO dto) {
        if(dto.getMethodName() == null){
            return null;
        }
        List<List<Float>> vectors = new ArrayList<>();
        try {
            // 获取方法参数类型
            Class<?>[] parameterTypes = new Class[]{EmbeddedCallDTO.class};
            // 获取方法对象
            Method method = embeddedService.getClass().getMethod(dto.getMethodName(), parameterTypes);
            // 调用方法
            Object result = method.invoke(embeddedService, dto);
            // 处理方法返回值
            if (result != null) {
                vectors = (List<List<Float>>) result;
            }
            return vectors;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("调用大模型的embedding接口失败", e);
            return null;
        }
    }
}
