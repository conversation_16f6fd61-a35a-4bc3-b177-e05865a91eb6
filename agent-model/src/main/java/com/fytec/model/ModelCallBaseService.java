package com.fytec.model;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.fytec.dto.llm.ModelCallDTO;
import com.fytec.dto.llm.ReferenceDocDTO;
import com.fytec.dto.llm.VectorResultDTO;
import com.fytec.handler.IResponseHandlerProxy;
import jakarta.servlet.http.HttpServletResponse;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;

import java.util.*;

@Slf4j
public class ModelCallBaseService {

    protected void configureResponseHeaders(HttpServletResponse response) {
        response.setContentType("text/event-stream");
        response.setCharacterEncoding("utf-8");
        response.setDateHeader("Expires", 0);
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("X-Accel-Buffering", "no");
    }

    protected Map<String, Object> processDocsListIfPresent(ModelCallDTO dto, HttpServletResponse response) {
        Map<String, Object> metaInfo = new LinkedHashMap<>();
        int index = 0;

        if (CollUtil.isNotEmpty(dto.getWebSearchResult())) {
            for (Object object : dto.getWebSearchResult()) {
                index += 1;
                metaInfo.put(String.valueOf(index), object);
                writeToResponse(response, dto.getResponseHandlerProxy(), "message", createMessageJson("referenceContent", object));
            }
        }

        if (CollUtil.isNotEmpty(dto.getDocsList())) {
            for (VectorResultDTO vectorResultDTO : dto.getDocsList()) {
                index += 1;
                ReferenceDocDTO referenceDocDTO = new ReferenceDocDTO();
                BeanUtil.copyProperties(vectorResultDTO, referenceDocDTO);
                referenceDocDTO.setType("knowledge");
                metaInfo.put(String.valueOf(index), referenceDocDTO);
                writeToResponse(response, dto.getResponseHandlerProxy(), "message", createMessageJson("referenceDoc", referenceDocDTO));
            }
        }
        return metaInfo;
    }

    @SneakyThrows
    protected void writeToResponse(HttpServletResponse response, IResponseHandlerProxy responseHandlerProxy, String event, Object data) {
        String s = "event:" + event + "\ndata:" + data + "\n\n";
        if (responseHandlerProxy != null) {
            s = responseHandlerProxy.handler(s);
        }
        if (s != null) {
            response.getWriter().write(s);
            response.getWriter().flush();
        }
    }

    protected String createMessageJson(String type, Object content) {
        JSONObject contentObj = new JSONObject();
        contentObj.put("type", type);
        contentObj.put("content", content);

        JSONObject messageObj = new JSONObject();
        messageObj.put("message", contentObj);
        return messageObj.toJSONString();
    }

    protected String createContentJson(String type, Object content) {
        JSONObject contentObj = new JSONObject();
        contentObj.put("type", type);
        contentObj.put("content", content);
        return contentObj.toJSONString();
    }

    protected WebClient getClient(String url) {
        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(
                        HttpClient.create()
                ))
                .defaultHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .baseUrl(url) //请求地址
                .build();
    }

    protected JSONObject buildBasicProcessDTO(String basicProcessDTO) {
        JSONObject body = new JSONObject();
        JSONObject dto = JSON.parseObject(basicProcessDTO);
        if (dto.containsKey("temperature")) {
            body.put("temperature", dto.getDouble("temperature"));
        }
        if (dto.containsKey("topP")) {
            body.put("top_p", dto.getDouble("topP"));
        }
        if (dto.containsKey("maxTokens")) {
            body.put("max_tokens", dto.getInteger("maxTokens"));
        }
        if (dto.containsKey("frequencyPenalty")) {
            body.put("frequencyPenalty", dto.getDouble("frequencyPenalty"));
        }
        if (dto.containsKey("presencePenalty")) {
            body.put("presencePenalty", dto.getDouble("presencePenalty"));
        }

        body.put("model", dto.getString("modelType"));

        ArrayList<Object> msgList = new ArrayList<>();
        //系统人设
        if (StringUtils.isNotBlank(dto.getString("systemMessage"))) {
            HashMap<String, String> userInputMap = new HashMap<>();
            userInputMap.put("role", "system");
            userInputMap.put("content", dto.getString("systemMessage"));
            msgList.add(userInputMap);
        }
        //历史
        if (dto.containsKey("histories")) {
            JSONArray histories = dto.getJSONArray("histories");
            for (Object history : histories) {
                Set<String> strings = ((JSONObject) history).keySet();
                if (strings.contains("role") && strings.contains("content")) {
                    msgList.add(history);
                } else if (strings.size() == 1) {
                    //说明输入和输出，作为key和value的
                    HashMap<String, String> e = new HashMap<>();
                    e.put("role", "user");
                    String userInput = strings.iterator().next();
                    e.put("content", userInput);
                    msgList.add(e);
                    e = new HashMap<>();
                    e.put("role", "assistant");
                    e.put("content", ((JSONObject) history).getString(userInput));
                    msgList.add(e);

                }
            }
        }

        HashMap<String, Object> userInputMap = new HashMap<>();
        userInputMap.put("role", "user");
        userInputMap.put("content", dto.get("userMessage"));
        // 需要拼接userInputMap内容
        msgList.add(userInputMap);
        body.put("messages", msgList);
        return body;
    }
}
