package com.fytec.model;

import com.fytec.dto.llm.ModelCallDTO;
import com.fytec.dto.llm.Txt2ImgCallDTO;
import com.fytec.dto.response.Txt2ImgDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
public class DynamicModelCallService {


    private final ModelCallService modelCallService;
    private final Txt2ImgCallService txt2ImgCallService;


    /**
     * 调用大模型的接口
     *
     * @param dto
     * @return
     */
    public void callModelStream(ModelCallDTO dto) {
        if (dto.getMethodName() == null) {
            return;
        }
        try {

            Class<?>[] parameterTypes = new Class[]{ModelCallDTO.class};
            Method method = modelCallService.getClass().getMethod(dto.getMethodName(), parameterTypes);
            // 调用方法
            dto.setStartTime(System.currentTimeMillis());
            method.invoke(modelCallService, dto);
            dto.setEndTime(System.currentTimeMillis());
        } catch (Exception e) {
            log.error("调用大模型接口失败", e);
        }
    }


    /**
     * 调用大模型的embedding接口
     * <p>
     * status
     * data
     *
     * @param dto
     * @return
     */
    public Map<String, Object> callModelNonStream(ModelCallDTO dto) {
        Map<String, Object> response = null;
        if (dto.getMethodName() == null) {
            return null;
        }
        try {
            Class<?>[] parameterTypes = new Class[]{ModelCallDTO.class};
            Method method = modelCallService.getClass().getMethod(dto.getMethodName(), parameterTypes);
            // 调用方法
            dto.setStartTime(System.currentTimeMillis());
            Object result = method.invoke(modelCallService, dto);
            dto.setEndTime(System.currentTimeMillis());
            if (result != null) {
                response = (Map<String, Object>) result;
            }
        } catch (Exception e) {
            log.error("调用大模型的接口失败,模型MethodName：{}", dto.getMethodName());
        }
        return response;
    }


    public Flux<ServerSentEvent<String>> callModelStreamWithFluxRes(ModelCallDTO dto) {
        if (dto.getMethodName() == null) {
            return Flux.empty();
        }
        try {
            Class<?>[] parameterTypes = new Class[]{ModelCallDTO.class};
            Method method = modelCallService.getClass().getMethod(dto.getMethodName(), parameterTypes);
            // 调用方法
            return (Flux<ServerSentEvent<String>>) method.invoke(modelCallService, dto);
        } catch (Exception e) {
            log.error("调用大模型接口失败", e);
            return Flux.empty();
        }
    }

    public List<Txt2ImgDTO> callModelTxt2Img(Txt2ImgCallDTO dto) {
        List<Txt2ImgDTO> response = null;
        if (dto.getMethodName() == null) {
            return null;
        }
        try {
            Class<?>[] parameterTypes = new Class[]{Txt2ImgCallDTO.class};
            Method method = txt2ImgCallService.getClass().getMethod(dto.getMethodName(), parameterTypes);
            Object result = method.invoke(txt2ImgCallService, dto);
            if (result != null) {
                response = (List<Txt2ImgDTO>) result;
            }
        } catch (Exception e) {
            log.error("调用文生图的接口失败,模型MethodName：{}", dto.getMethodName());
        }
        return response;
    }
}
