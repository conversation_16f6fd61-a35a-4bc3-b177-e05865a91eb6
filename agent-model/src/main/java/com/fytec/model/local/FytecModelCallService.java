package com.fytec.model.local;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.fytec.dto.llm.*;
import com.fytec.dto.response.Txt2ImgDTO;
import com.fytec.model.ModelCallBaseService;
import com.fytec.token.ClientTokenService;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.reactive.function.BodyInserters;
import reactor.core.publisher.Flux;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class FytecModelCallService extends ModelCallBaseService {

    @SneakyThrows
    public Flux<ServerSentEvent<String>> processStreamWithFluxRes(ModelCallDTO dto, ClientTokenService clientTokenService) {
        String token = clientTokenService.getToken();
        List<Object> referenceHistories = new ArrayList<>();

        List<ServerSentEvent<String>> refers = new ArrayList<>();
        Map<String, Object> metaInfo;
        if (dto.isEnableCitation()) {
            metaInfo = processDocsListIfPresentNew(dto, refers);
        } else {
            metaInfo = new HashMap<>();
        }
        Flux<ServerSentEvent<String>> sseStream = Flux.fromIterable(refers);

        final StringBuilder contentCache = new StringBuilder();
        final Pattern pattern = Pattern.compile("<\\$\\d+\\$>");

        Flux<ServerSentEvent<String>> flux = getClient(dto.getUrl()).post()
                .accept(MediaType.TEXT_EVENT_STREAM) //接收text/event-stream流的数据
                .header("Authorization", "Bearer " + token)
                .body(BodyInserters.fromValue(dto.getParamBody())) //参数
                .retrieve()
                .bodyToFlux(String.class)
                .flatMap(s -> {
                    List<ServerSentEvent<String>> events = new ArrayList<>();

                    String anserwer = s.replace("[end]", "");
                    JSONObject jsonObject = JSON.parseObject(anserwer);
                    if (ObjectUtil.isEmpty(jsonObject)) {
                        return Flux.empty();
                    }

                    String messageType = jsonObject.getString("type");
                    anserwer = jsonObject.getString("content");

                    //去掉最前面和最后面的双引号
                    if (anserwer.startsWith("\"") && anserwer.endsWith("\"") && anserwer.length() > 2) {
                        anserwer = anserwer.substring(1, anserwer.length() - 1);
                    }
                    anserwer = anserwer.replaceAll("\\\\n", "\n");
                    anserwer = anserwer.replaceAll("\\\\\"", "\"");
                    log.info(anserwer);

                    if ("reasoningContent".equals(messageType)) {
                        dto.getReasonHistory().append(anserwer);
                    } else if ("content".equals(messageType)) {
                        dto.getHistory().append(anserwer);
                        if (dto.isEnableCitation() && metaInfo != null) {
                            contentCache.append(anserwer);
                            if (!contentCache.toString().contains("<")) {
                                s = createContentJson("content", contentCache);
                                contentCache.setLength(0);
                            } else {
                                Matcher matcher = pattern.matcher(contentCache.toString());
                                if (matcher.find()) {
                                    if (StrUtil.isNotBlank(contentCache.substring(0, matcher.start()))) {
                                        //输出匹配到内容前面的字符
                                        s = contentCache.substring(0, matcher.start());
                                        events.add(event("message", createMessageJson("content", s)));
                                    }
                                    String numberStr = matcher.group().replace("<$", "").replace("$>", "");
                                    System.out.println("--------拿到结构中的数字输出引用: " + numberStr);
                                    if (metaInfo.containsKey(numberStr)) {
                                        Object o = metaInfo.get(numberStr);
                                        referenceHistories.add(o);
                                        events.add(event("message", createMessageJson("refer", o)));
                                    }
                                    contentCache.delete(0, matcher.end());
                                    s = null;
                                } else {
                                    int firstIndex = contentCache.indexOf("<");
                                    int lengthAfterSub = contentCache.length() - firstIndex;
                                    if (lengthAfterSub > 10) {
                                        s = createContentJson("content", contentCache.substring(0, firstIndex));
                                        contentCache.delete(0, firstIndex + 1);
                                    } else {
                                        s = null;
                                    }

                                }
                            }

                        }
                    } else if ("referenceContent".equals(messageType)) {
                        referenceHistories.add(jsonObject.get("content"));
                    }


                    if (StrUtil.isNotEmpty(s)) {
                        if ("[end]".equals(s)) {
                            for (Object o : referenceHistories) {
                                events.add(event("message", createMessageJson("referenceRelDoc", o)));
                            }
                            events.add(event("done", ""));
                        } else {
                            s = "{\"message\":" + s + "}";
                            events.add(event("message", s));
                        }

                    }
                    return Flux.fromIterable(events);
                });

        dto.getReferenceHistory().append(JSON.toJSONString(referenceHistories));
        return sseStream.concatWith(flux);
    }

    @SneakyThrows
    public void processStream(ModelCallDTO dto, ClientTokenService clientTokenService) {
        HttpServletResponse response = dto.getResponse();
        configureResponseHeaders(response);

        String token = clientTokenService.getToken();
        Set<Object> referenceDocHistories = new HashSet<>();
        Set<Object> referenceHistories = new HashSet<>();

        Map<String, Object> metaInfo;
        if (dto.isEnableCitation()) {
            metaInfo = processDocsListIfPresentWithHistory(dto, response, referenceHistories);
        } else {
            metaInfo = new HashMap<>();
        }


        final StringBuilder contentCache = new StringBuilder();
        final Pattern pattern = Pattern.compile("<\\$\\d+\\$>");

        Flux<String> flux = getClient(dto.getUrl()).post()
                .accept(MediaType.TEXT_EVENT_STREAM) //接收text/event-stream流的数据
                .header("Authorization", "Bearer " + token)
                .body(BodyInserters.fromValue(dto.getParamBody())) //参数
                .retrieve()
                .bodyToFlux(String.class)
                .map(s -> {
                    String rawS = s;
                    String anserwer = s.replace("[end]", "");

                    log.info("原始返回内容: {}", anserwer);
                    JSONObject jsonObject = JSON.parseObject(anserwer);
                    if (ObjectUtil.isNotEmpty(jsonObject)) {
                        String messageType = jsonObject.getString("type");
                        anserwer = jsonObject.getString("content");

                        //去掉最前面和最后面的双引号
                        if (anserwer.startsWith("\"") && anserwer.endsWith("\"") && anserwer.length() > 2) {
                            anserwer = anserwer.substring(1, anserwer.length() - 1);
                        }
                        anserwer = anserwer.replaceAll("\\\\n", "\n");
                        anserwer = anserwer.replaceAll("\\\\\"", "\"");
                        log.info(anserwer);

                        if ("reasoningContent".equals(messageType)) {
                            dto.getReasonHistory().append(anserwer);
                        } else if ("content".equals(messageType)) {
                            dto.getHistory().append(anserwer);
                            if (dto.isEnableCitation() && !metaInfo.isEmpty()) {
                                contentCache.append(anserwer);
                                if (!contentCache.toString().contains("<")) {
                                    s = createContentJson("content", contentCache);
                                    contentCache.setLength(0);
                                } else {
                                    Matcher matcher = pattern.matcher(contentCache.toString());
                                    if (matcher.find()) {
                                        if (StrUtil.isNotBlank(contentCache.substring(0, matcher.start()))) {
                                            //输出匹配到内容前面的字符
                                            s = contentCache.substring(0, matcher.start());
                                            writeToResponse(response, dto.getResponseHandlerProxy(), "message", createMessageJson("content", s));
                                        }
                                        String numberStr = matcher.group().replace("<$", "").replace("$>", "");
                                        System.out.println("--------拿到结构中的数字输出引用: " + numberStr);
                                        if (metaInfo.containsKey(numberStr)) {
                                            Object o = metaInfo.get(numberStr);
                                            JSONObject referObject = JSON.parseObject(JSON.toJSONString(o));
                                            if (StrUtil.equals(referObject.getString("type"), "knowledge")) {
                                                referenceDocHistories.add(o);
                                            }
                                            writeToResponse(response, dto.getResponseHandlerProxy(), "message", createMessageJson("refer", o));
                                        }
                                        contentCache.delete(0, matcher.end());
                                        s = null;
                                    } else {
                                        int firstIndex = contentCache.indexOf("<");
                                        int lengthAfterSub = contentCache.length() - firstIndex;
                                        if (lengthAfterSub > 10) {
                                            s = createContentJson("content", contentCache.substring(0, firstIndex));
                                            contentCache.delete(0, firstIndex + 1);
                                        } else {
                                            s = null;
                                        }

                                    }
                                }

                            }
                        } else if ("referenceContent".equals(messageType)) {
                            referenceHistories.add(jsonObject.get("content"));
                        }
                    }

                    if (StrUtil.isNotEmpty(s)) {
                        if ("[end]".equals(s)) {
                            for (Object o : referenceDocHistories) {
                                writeToResponse(response, dto.getResponseHandlerProxy(), "message", createMessageJson("referenceRelDoc", o));
                            }
                            writeToResponse(response, dto.getResponseHandlerProxy(), "done", "");
                        } else {
                            s = "{\"message\":" + s + "}";
                            writeToResponse(response, dto.getResponseHandlerProxy(), "message", s);
                        }

                    }
                    return rawS;
                });
        flux.blockLast();
        if (CollUtil.isNotEmpty(referenceHistories)) {
            dto.getReferenceHistory().append(JSON.toJSONString(referenceHistories));
        } else {
            dto.getReferenceHistory().append(JSON.toJSONString(referenceDocHistories));
        }
    }

    @SneakyThrows
    public Map<String, Object> processNonStream(ModelCallDTO dto, ClientTokenService clientTokenService) {
        String token = clientTokenService.getToken();
        Unirest.setTimeouts(600000, 600000);
        HttpResponse<String> content = Unirest.post(dto.getUrl())
                .header("Authorization", "Bearer " + token)
                .header("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .body(JSON.toJSONString(dto.getParamBody()))
                .asString();
        HashMap<String, Object> res = new HashMap<>();
        res.put("data", "");
        if (content.getStatus() != 200) {
            return res;
        }

        JSONObject jsonObject = JSONObject.parseObject(content.getBody());
        if (jsonObject != null) {
            JSONObject data = jsonObject.getJSONObject("data");
            if (data != null) {
                String result = data.getString("content");
                res.put("data", result);
                if (dto.getHistory() != null) {
                    dto.getHistory().append(result);
                }
            }
        }
        return res;
    }

    private Map<String, Object> processDocsListIfPresentWithHistory(ModelCallDTO dto,
                                                                    HttpServletResponse response,
                                                                    Set<Object> referenceHistories) {
        Map<String, Object> metaInfo = new LinkedHashMap<>();
        int index = 0;

        if (CollUtil.isNotEmpty(dto.getWebSearchResult())) {
            for (Object object : dto.getWebSearchResult()) {
                JSONObject referObject = JSON.parseObject(JSON.toJSONString(object));
                index = referObject.getInteger("index");
                metaInfo.put(String.valueOf(index), object);
                writeToResponse(response, dto.getResponseHandlerProxy(), "message", createMessageJson("referenceContent", object));

                referenceHistories.add(object);
            }
        }

        if (CollUtil.isNotEmpty(dto.getDocsList())) {
            for (VectorResultDTO vectorResultDTO : dto.getDocsList()) {
                index += 1;
                ReferenceDocDTO referenceDocDTO = new ReferenceDocDTO();
                BeanUtil.copyProperties(vectorResultDTO, referenceDocDTO);
                referenceDocDTO.setType("knowledge");
                metaInfo.put(String.valueOf(index), referenceDocDTO);
                writeToResponse(response, dto.getResponseHandlerProxy(), "message", createMessageJson("referenceDoc", referenceDocDTO));
            }
        }
        return metaInfo;
    }

    private Map<String, Object> processDocsListIfPresentNew(ModelCallDTO dto, List<ServerSentEvent<String>> events) {
        if (dto.getDocsList() == null || dto.getDocsList().isEmpty()) {
            return null;
        }

        Map<String, Object> metaInfo = new LinkedHashMap<>();
        int index = 0;

        for (VectorResultDTO vectorResultDTO : dto.getDocsList()) {
            index += 1;
            ReferenceDocDTO referenceDocDTO = new ReferenceDocDTO();
            BeanUtil.copyProperties(vectorResultDTO, referenceDocDTO);
            metaInfo.put(String.valueOf(index), referenceDocDTO);

            events.add(event("message", createMessageJson("referenceDoc", referenceDocDTO)));
        }

        return metaInfo;
    }

    private ServerSentEvent<String> event(String eventType, String data) {
        return ServerSentEvent.<String>builder()
                .event(eventType)
                .data(data)
                .build();
    }

    @SneakyThrows
    public List<Txt2ImgDTO> processTxt2Img(Txt2ImgCallDTO dto, ClientTokenService clientTokenService) {
        String token = clientTokenService.getToken();
        Txt2ImgProcessDTO txt2ImgProcessDTO = new Txt2ImgProcessDTO();
        txt2ImgProcessDTO.setInputs(dto.getInputs());
        txt2ImgProcessDTO.setModelKey(dto.getModelCode());

        Unirest.setTimeouts(600000, 600000);
        HttpResponse<String> content = Unirest.post(dto.getUrl())
                .header("Authorization", "Bearer " + token)
                .header("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .body(JSON.toJSONString(txt2ImgProcessDTO))
                .asString();
        List<Txt2ImgDTO> data = null;
        if (content.getStatus() != 200) {
            return data;
        }

        JSONObject jsonObject = JSONObject.parseObject(content.getBody());
        if (jsonObject != null) {
            data = jsonObject.getList("data", Txt2ImgDTO.class);
        }
        return data;
    }
}
