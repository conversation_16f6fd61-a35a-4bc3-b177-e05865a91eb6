package com.fytec.model.local;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fytec.dto.llm.ModelCallDTO;
import com.fytec.model.ModelCallBaseService;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.util.StringUtil;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.BodyInserters;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.fytec.constant.ProjectApiKey.WJ_EDU_API_KEY;

@Slf4j
public class WjEduModelCallService extends ModelCallBaseService {

    @SneakyThrows
    public void processStream(ModelCallDTO dto) {
        HttpServletResponse response = dto.getResponse();
        configureResponseHeaders(response);
        // 创建请求体
        JSONObject body = buildBasicProcessDTO(JSON.toJSONString(dto.getParamBody()));
        body.put("stream", true);
        List<Object> referenceHistories = new ArrayList<>();
        AtomicReference<Integer> currentInThink = new AtomicReference<>(0);

        Map<String, Object> metaInfo;
        if (dto.isEnableCitation()) {
            metaInfo = processDocsListIfPresent(dto, response);
        } else {
            metaInfo = new HashMap<>();
        }

        final StringBuilder contentCache = new StringBuilder();
        final Pattern pattern = Pattern.compile("<\\$\\d+\\$>");

        Flux<String> flux = getClient(dto.getUrl()).post()
                .accept(MediaType.TEXT_EVENT_STREAM) //接收text/event-stream流的数据
                .header("Authorization", "Bearer " + WJ_EDU_API_KEY)
                .body(BodyInserters.fromValue(body)) //参数
                .retrieve()
                .bodyToFlux(String.class)
                .map(s -> {
                    String anserwer = s.replace("[end]", "");
                    //去掉最前面和最后面的双引号
                    if (anserwer.startsWith("\"") && anserwer.endsWith("\"") && anserwer.length() > 2) {
                        anserwer = anserwer.substring(1, anserwer.length() - 1);
                    }
                    anserwer = anserwer.replaceAll("\\\\n", "\n");
                    anserwer = anserwer.replaceAll("\\\\\"", "\"");
                    log.info(anserwer);

                    if ("[DONE]".equals(s)) {
                        for (Object o : referenceHistories) {
                            writeToResponse(response, dto.getResponseHandlerProxy(), "message", createMessageJson("referenceRelDoc", o));
                        }

                        writeToResponse(response, dto.getResponseHandlerProxy(), "done", "");
                    } else {
                        JSONObject chatCompletionChunk = JSON.parseObject(s);
                        if (ObjectUtil.isNotEmpty(chatCompletionChunk) && chatCompletionChunk.containsKey("choices")) {
                            JSONArray choices = chatCompletionChunk.getJSONArray("choices");
                            if (choices.isEmpty()) {
                                return s;
                            }

                            JSONObject choice = chatCompletionChunk.getJSONArray("choices").getJSONObject(0);
                            JSONObject message;
                            if (choice.containsKey("delta")) {
                                message = choice.getJSONObject("delta");
                            } else {
                                message = choice.getJSONObject("message");
                            }
                            String content = message.getString("content").strip();
                            if ("<think>".equals(content)) {
                                if (currentInThink.get() == 0) {
                                    currentInThink.set(1);
                                }
                            } else if (currentInThink.get() == 1) {
                                if ("</think>".equals(content)) {
                                    currentInThink.set(2);
                                } else {
                                    content = message.getString("content");
                                    dto.getReasonHistory().append(content);
//                                    content = content.replace("\n", "\\n").replace("\"", "\\\"");
                                    writeToResponse(response, dto.getResponseHandlerProxy(), "message", createMessageJson("reasoningContent", content));
                                }
                            } else {
                                content = message.getString("content");
                                dto.getHistory().append(content);
//                                content = content.replace("\n", "\\n").replace("\"", "\\\"");
                                if (dto.isEnableCitation() && metaInfo != null) {
                                    contentCache.append(content);
                                    if (!contentCache.toString().contains("<")) {
                                        contentCache.setLength(0);
                                    } else {
                                        Matcher matcher = pattern.matcher(contentCache.toString());
                                        if (matcher.find()) {
                                            if (StringUtil.isNotBlank(contentCache.substring(0, matcher.start()))) {
                                                //输出匹配到内容前面的字符
                                                content = contentCache.substring(0, matcher.start());
                                                writeToResponse(response, dto.getResponseHandlerProxy(), "message", createMessageJson("content", content));
                                            }
                                            String numberStr = matcher.group().replace("<$", "").replace("$>", "");
                                            System.out.println("--------拿到结构中的数字输出引用: " + numberStr);
                                            if (metaInfo.containsKey(numberStr)) {
                                                Object o = metaInfo.get(numberStr);
                                                referenceHistories.add(o);
                                                writeToResponse(response, dto.getResponseHandlerProxy(), "message", createMessageJson("refer", o));
                                            }
                                            contentCache.delete(0, matcher.end());
                                            content = null;
                                        } else {
                                            int firstIndex = contentCache.indexOf("<");
                                            int lengthAfterSub = contentCache.length() - firstIndex;
                                            if (lengthAfterSub > 10) {
                                                content = contentCache.substring(0, firstIndex);
                                                contentCache.delete(0, firstIndex + 1);
                                            } else {
                                                content = null;
                                            }

                                        }
                                    }
                                }
                                if (StrUtil.isNotEmpty(content)) {
                                    writeToResponse(response, dto.getResponseHandlerProxy(), "message", createMessageJson("content", content));
                                }
                            }
                        }
                    }
                    return s;
                });
        flux.blockLast();
        dto.getReferenceHistory().append(JSON.toJSONString(referenceHistories));
    }

    @SneakyThrows
    public Map<String, Object> processNonStream(ModelCallDTO dto) {
        JSONObject body = buildBasicProcessDTO(JSON.toJSONString(dto.getParamBody()));
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonBody = objectMapper.writeValueAsString(body);

        Unirest.setTimeouts(600000, 600000);
        //http://***********:29000/publishaddress/inference/c47bff27
        HttpResponse<String> response = Unirest.post(dto.getUrl())
                .header("Authorization", "Bearer " + WJ_EDU_API_KEY)
                .header("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .body(jsonBody)
                .asString();
        JSONObject parseObj = JSON.parseObject(response.getBody());
//        log.info("noStream response: {}", parseObj.toJSONString());
        HashMap<String, Object> res = new HashMap<>();
        res.put("status", response.getStatus());
        JSONObject msg = parseObj.getJSONArray("choices")
                .getJSONObject(0)
                .getJSONObject("message");
        String content = msg.getString("content").strip();
        // 出现过，思考字段没内容，但是思考内容在content的
        int startIndex = content.indexOf("<think>");
        int endIndex = content.indexOf("</think>");
        if (startIndex != -1 && endIndex != -1 && endIndex > startIndex) {
            String thinkContent = content.substring(startIndex + "<think>".length(), endIndex);
            if (StringUtils.isNotBlank(thinkContent.strip())) {
                res.put("reasoning_content", thinkContent.strip());
                dto.getReasonHistory().append(thinkContent.strip());
            }
            String data = content.substring(endIndex + "</think>".length()).trim();
            res.put("data", data);
            dto.getHistory().append(data);

        } else {
            //判断是否有思考字段
            if (msg.containsKey("reasoning_content")) {
                res.put("reasoning_content", msg.getString("reasoning_content"));
                dto.getReasonHistory().append(msg.getString("reasoning_content"));
            }
            res.put("data", msg.getString("content"));
            dto.getHistory().append(msg.getString("content"));
        }
        return res;
    }
}
