package com.fytec.model.local;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fytec.dto.llm.ModelCallDTO;
import com.fytec.dto.llm.ModelProcessDTO;
import com.fytec.model.ModelCallBaseService;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.util.StringUtil;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.BodyInserters;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.fytec.constant.ProjectApiKey.NING_XIA_API_KEY;

@Slf4j
public class NingXiaModelCallService extends ModelCallBaseService {

    public static void main(String[] args) {
        NingXiaModelCallService ningXiaModelCallService = new NingXiaModelCallService();
        ModelProcessDTO req = new ModelProcessDTO();
        req.setSystemMessage("你是AI小助手");
//        List<ChatCompletionContentPart> multiParts = new ArrayList<>();
//        multiParts.add(
//                ChatCompletionContentPart.builder().type("image_url").imageUrl(
//                        new ChatCompletionContentPart.ChatCompletionContentPartImageURL("http://fastdfs-dev.cnsaas.com/group1/M00/00/13/rBMAAWTHbpGAXasVAA2oFTmfVTE929.png")
//                ).build()
//        );
//        multiParts.add(ChatCompletionContentPart.builder().type("text").text("这个图片描述了啥").build());
        req.setUserMessage("常见的热带水果有哪些？");
        req.setMaxTokens(8192);
        req.setModelType("ds-r1");
        Map<String, Object> paramBody = JSON.parseObject(JSON.toJSONString(req), Map.class);
        ModelCallDTO dto = new ModelCallDTO();
        dto.setUrl("http://**************:8000/v1/chat/completions");
//        dto.setUrl("http://***********:29000/publishaddress/inference/c47bff27");
        dto.setParamBody(paramBody);
        System.out.println(ningXiaModelCallService.processNonStream(dto));
    }

    @SneakyThrows
    public void processStream(ModelCallDTO dto) {
        HttpServletResponse response = dto.getResponse();
        configureResponseHeaders(response);
        // 创建请求体
        JSONObject body = buildBasicProcessDTO(JSON.toJSONString(dto.getParamBody()));
        body.put("stream", true);
        List<Object> referenceHistories = new ArrayList<>();

        Map<String, Object> metaInfo;
        if (dto.isEnableCitation()) {
            metaInfo = processDocsListIfPresent(dto, response);
        } else {
            metaInfo = new HashMap<>();
        }

        final StringBuilder contentCache = new StringBuilder();
        final Pattern pattern = Pattern.compile("<\\$\\d+\\$>");

        Flux<String> flux = getClient(dto.getUrl()).post()
                .accept(MediaType.TEXT_EVENT_STREAM) //接收text/event-stream流的数据
                .header("Authorization", "Bearer " + NING_XIA_API_KEY)
                .body(BodyInserters.fromValue(body)) //参数
                .retrieve()
                .bodyToFlux(String.class)
                .map(s -> {
                    if ("[DONE]".equals(s)) {
                        for (Object o : referenceHistories) {
                            writeToResponse(response, dto.getResponseHandlerProxy(), "message", createMessageJson("referenceRelDoc", o));
                        }

                        writeToResponse(response, dto.getResponseHandlerProxy(), "done", "");
                    } else {
                        JSONObject chatCompletionChunk = JSON.parseObject(s);
                        if (ObjectUtil.isNotEmpty(chatCompletionChunk) && chatCompletionChunk.containsKey("choices")) {
                            JSONObject choice = chatCompletionChunk.getJSONArray("choices").getJSONObject(0);
                            JSONObject message;
                            if (choice.containsKey("delta")) {
                                message = choice.getJSONObject("delta");
                            } else {
                                message = choice.getJSONObject("message");
                            }
                            String reasoningContent = message.getString("reasoning_content");
                            String content = message.getString("content");
                            if (StrUtil.isNotBlank(reasoningContent)) {
                                dto.getReasonHistory().append(reasoningContent);
                                writeToResponse(response, dto.getResponseHandlerProxy(), "message", createMessageJson("reasoningContent", reasoningContent));
                            } else if (StrUtil.isNotBlank(content)) {
                                content = message.getString("content");
                                dto.getHistory().append(content);
                                if (dto.isEnableCitation() && metaInfo != null) {
                                    contentCache.append(content);
                                    if (!contentCache.toString().contains("<")) {
                                        contentCache.setLength(0);
                                    } else {
                                        Matcher matcher = pattern.matcher(contentCache.toString());
                                        if (matcher.find()) {
                                            if (StringUtil.isNotBlank(contentCache.substring(0, matcher.start()))) {
                                                //输出匹配到内容前面的字符
                                                content = contentCache.substring(0, matcher.start());
                                                writeToResponse(response, dto.getResponseHandlerProxy(), "message", createMessageJson("content", content));
                                            }
                                            String numberStr = matcher.group().replace("<$", "").replace("$>", "");
                                            System.out.println("--------拿到结构中的数字输出引用: " + numberStr);
                                            if (metaInfo.containsKey(numberStr)) {
                                                Object o = metaInfo.get(numberStr);
                                                referenceHistories.add(o);
                                                writeToResponse(response, dto.getResponseHandlerProxy(), "message", createMessageJson("refer", o));
                                            }
                                            contentCache.delete(0, matcher.end());
                                            content = null;
                                        } else {
                                            int firstIndex = contentCache.indexOf("<");
                                            int lengthAfterSub = contentCache.length() - firstIndex;
                                            if (lengthAfterSub > 10) {
                                                content = contentCache.substring(0, firstIndex);
                                                contentCache.delete(0, firstIndex + 1);
                                            } else {
                                                content = null;
                                            }

                                        }
                                    }
                                }
                                if (StrUtil.isNotEmpty(content)) {
                                    writeToResponse(response, dto.getResponseHandlerProxy(), "message", createMessageJson("content", content));
                                }
                            }
                        }
                    }
                    return s;
                });
        flux.blockLast();
        dto.getReferenceHistory().append(JSON.toJSONString(referenceHistories));
    }

    @SneakyThrows
    public Map<String, Object> processNonStream(ModelCallDTO dto) {
        JSONObject body = buildBasicProcessDTO(JSON.toJSONString(dto.getParamBody()));
        body.put("stream", false);
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonBody = objectMapper.writeValueAsString(body);

        Unirest.setTimeouts(600000, 600000);
        HttpResponse<String> response = Unirest.post(dto.getUrl())
                .header("Authorization", "Bearer " + NING_XIA_API_KEY)
                .header("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .body(jsonBody)
                .asString();
        String bodyStr = response.getBody();
        JSONObject parseObj = JSON.parseObject(bodyStr);

        HashMap<String, Object> res = new HashMap<>();
        res.put("status", response.getStatus());
        JSONObject msg = parseObj.getJSONArray("choices")
                .getJSONObject(0)
                .getJSONObject("message");

        String reasoningContent = msg.getString("reasoning_content");
        String content = msg.getString("content");

        if (StrUtil.isNotBlank(reasoningContent)) {
            res.put("reasoning_content", reasoningContent);
            dto.getReasonHistory().append(reasoningContent);
        }
        if (StrUtil.isNotBlank(content)) {
            res.put("data", content);
            dto.getHistory().append(content);
        }
        return res;
    }
}
