package com.fytec.model;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fytec.dto.llm.ModelCallDTO;
import com.fytec.handler.IResponseHandlerProxy;
import com.fytec.model.local.FytecModelCallService;
import com.fytec.model.local.NingXiaModelCallService;
import com.fytec.model.local.WjEduModelCallService;
import com.fytec.token.ClientTokenService;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.util.StringUtil;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.fytec.constant.ProjectApiKey.SIP_API_KEY;

@Component
@Slf4j
@RequiredArgsConstructor
public class ModelCallService extends ModelCallBaseService {

    //    private static final String errorMsg = "大模型服务暂时有问题请稍后再试";
    private static final String errorMsg = "";
    private final ClientTokenService clientTokenService;

    //调用豆包流式接口
    public void callFytecStream(ModelCallDTO dto) {
        FytecModelCallService modelCallService = new FytecModelCallService();
        modelCallService.processStream(dto, clientTokenService);
    }

    public Flux<ServerSentEvent<String>> callFytecStreamWithFluxRes(ModelCallDTO dto) {
        FytecModelCallService modelCallService = new FytecModelCallService();
        return modelCallService.processStreamWithFluxRes(dto, clientTokenService);
    }

    //调用豆包非流式接口
    @SneakyThrows
    public Map<String, Object> callFytecNonStream(ModelCallDTO dto) {
        FytecModelCallService modelCallService = new FytecModelCallService();
        return modelCallService.processNonStream(dto, clientTokenService);
    }

    public void callNxStream(ModelCallDTO dto) {
        NingXiaModelCallService ningXiaModelCallService = new NingXiaModelCallService();
        ningXiaModelCallService.processStream(dto);
    }

    public Map<String, Object> callNxNonStream(ModelCallDTO dto) {
        NingXiaModelCallService ningXiaModelCallService = new NingXiaModelCallService();
        return ningXiaModelCallService.processNonStream(dto);
    }

    public void callWjEduStream(ModelCallDTO dto) {
        WjEduModelCallService wjEduModelCallService = new WjEduModelCallService();
        wjEduModelCallService.processStream(dto);
    }

    public Map<String, Object> callWjEduNonStream(ModelCallDTO dto) {
        WjEduModelCallService wjEduModelCallService = new WjEduModelCallService();
        return wjEduModelCallService.processNonStream(dto);
    }

    //调用豆包非流式接口
    @SneakyThrows
    public Map<String, Object> callSipNonStream(ModelCallDTO dto) {
//        BasicProcessDTO basicProcessDTO = JSONObject.parseObject(JSON.toJSONString(dto.getParamBody()), BasicProcessDTO.class);
        JSONObject body = buildSipModelDtoByBasicProcessDTO(dto, JSON.toJSONString(dto.getParamBody()));
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonBody = objectMapper.writeValueAsString(body);
        System.out.println("query model body:");
        System.out.println(jsonBody);

        Unirest.setTimeouts(600000, 600000);
        HttpResponse<String> response = Unirest.post(dto.getUrl())
                .header("Authorization", "Bearer " + SIP_API_KEY)
                .header("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .body(jsonBody)
                .asString();
        JSONObject parseObj = JSON.parseObject(response.getBody());

        HashMap<String, Object> res = new HashMap<>();
        res.put("status", response.getStatus());
        JSONObject msg = null;
        try {
            msg = parseObj.getJSONArray("choices")
                    .getJSONObject(0)
                    .getJSONObject("message");
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("error body:" + response.getBody());
        }

        String content = msg == null ? "" : msg.getString("content").strip();
        // 出现过，思考字段没内容，但是思考内容在content的
        int startIndex = content.indexOf("<think>");
        int endIndex = content.indexOf("</think>");
        if (startIndex != -1 && endIndex != -1 && endIndex > startIndex) {
            String thinkContent = content.substring(startIndex + "<think>".length(), endIndex);
            if (StringUtils.isNotBlank(thinkContent.strip())) {
                res.put("reasoning_content", thinkContent.strip());
                dto.getReasonHistory().append(thinkContent.strip());
            }
            String data = content.substring(endIndex + "</think>".length()).trim();
            res.put("data", data);
            dto.getHistory().append(data);
        } else {
            //判断是否有思考字段
            if (msg != null && msg.containsKey("reasoning_content")) {
                res.put("reasoning_content", msg.getString("reasoning_content"));
                dto.getReasonHistory().append(msg.getString("reasoning_content"));
            }
            res.put("data", msg == null ? "" : msg.getString("content"));
            dto.getHistory().append(msg == null ? "" : msg.getString("content"));
        }
        return res;
    }

    //调用豆包流式接口
    public void callSipStream(ModelCallDTO dto) throws Exception {
        //todo 因为v3没有function call，这边需要阻塞调用一下？根据用户问题判断是否需要调用工作流
        // 可以通过 id：3的智能体，来判断，根据判断结果，调用不同的工作流，然后阻塞获取结果后，给最终的大模型返回。
        // 如果涉及到工作流中的一些问题，得到结果后，根据用户问题和已知的一些信息，来返回？
        // 设置你的 OpenAI API 密钥
        HttpServletResponse response = dto.getResponse();
        response.setContentType("text/event-stream");
        response.setCharacterEncoding("utf-8");
        response.setDateHeader("Expires", 0);
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("X-Accel-Buffering", "no");

        Map<String, Object> metaInfo;
        if (dto.isEnableCitation()) {
            metaInfo = processDocsListIfPresent(dto, response);
        } else {
            metaInfo = new HashMap<>();
        }

        // 创建请求体
        JSONObject body = buildSipModelDtoByBasicProcessDTO(dto, JSON.toJSONString(dto.getParamBody()));
        body.put("stream", true);
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonBody = objectMapper.writeValueAsString(body);
        System.out.println(jsonBody);
        final StringBuilder contentCache = new StringBuilder();
        final Pattern pattern = Pattern.compile("<\\$\\d+\\$>");

        // 将请求体转换为 JSON 字符串
        List<Object> referenceHistories = new ArrayList<>();
        AtomicReference<Integer> currentInThink = new AtomicReference<>(0);

        URL url = new URL(dto.getUrl());
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("Authorization", "Bearer " + SIP_API_KEY);
        connection.setDoOutput(true);
        try (DataOutputStream wr = new DataOutputStream(connection.getOutputStream())) {
//            wr.writeBytes(jsonBody);
//            wr.flush();
            byte[] input = jsonBody.getBytes(StandardCharsets.UTF_8);
            wr.write(input, 0, input.length);
        }
        IResponseHandlerProxy responseHandlerProxy = dto.getResponseHandlerProxy();

        int responseCode = connection.getResponseCode();
        if (responseCode == HttpURLConnection.HTTP_OK) {
            BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
//                response.append(line);
                if (StringUtils.isBlank(line)) {
                    continue;
                }
                String s = line;

                if (StringUtils.isBlank(s)) {
                    continue;
                }
                String anserwer = null;
                if (s.startsWith("data: ")) {
                    s = s.substring(6);
                }
//                anserwer = s;
//                else {
//                    anserwer = s;
//                }
                //去掉最前面和最后面的双引号
//                if (anserwer.startsWith("\"") && anserwer.endsWith("\"") && anserwer.length() > 2) {
//                    anserwer = anserwer.substring(1, anserwer.length() - 1);
//                }
//                anserwer = anserwer.replaceAll("\\\\n", "\n");
//                anserwer = anserwer.replaceAll("\\\\\"", "\"");

                if ("[DONE]".equals(s)) {
//                    s = "event:done\ndata:\n\n";
                    for (Object o : referenceHistories) {
                        writeToResponse(response, dto.getResponseHandlerProxy(), "message", createMessageJson("referenceRelDoc", o));
                    }

                    writeToResponse(response, dto.getResponseHandlerProxy(), "done", "");

                } else {
                    JSONObject parseObj = null;
                    try {
                        parseObj = JSON.parseObject(s);
                    } catch (Exception e) {
                        log.warn("error json:" + s);
                    }
                    if (parseObj != null && parseObj.containsKey("choices")) {
                        JSONObject tmpContentDto = null;
                        try {
                            tmpContentDto = parseObj.getJSONArray("choices")
                                    .getJSONObject(0);
                        } catch (Exception e) {
                            e.printStackTrace();
                            System.out.println("error body:" + s);
                        }

                        JSONObject msg = null;
                        if (tmpContentDto != null) {
                            if (tmpContentDto.containsKey("delta")) {
                                msg = tmpContentDto.getJSONObject("delta");
                            } else {
                                msg = tmpContentDto.getJSONObject("message");
                            }
                        }


                        String content = msg == null ? errorMsg : msg.getString("content");
                        String checkMsg = content.strip();
                        if ("<think>".equals(checkMsg)) {
                            if (currentInThink.get() == 0) {
                                currentInThink.set(1);
                                s = null;
                                continue;
                            }
                        }
                        if (currentInThink.get() == 1) {
                            if ("</think>".equals(checkMsg)) {
                                currentInThink.set(2);
                                s = null;
                                continue;
                            } else {
                                dto.getReasonHistory().append(msg == null ? errorMsg : msg.getString("content"));
                                String string = msg == null ? errorMsg : msg.getString("content").replace("\n", "\\n").replace("\"", "\\\"");
//                                s = "event:message\ndata:{\"message\":{\"type\":\"reasoningContent\",\"content\":\"" + string + "\"}}\n\n";
                                writeToResponse(response, dto.getResponseHandlerProxy(), "message", createMessageJson("reasoningContent", string));

                            }
                        } else {
//                            dto.getHistory().append(msg == null ? errorMsg : msg.getString("content"));
//                            String string = msg == null ? errorMsg : msg.getString("content").replace("\n", "\\n").replace("\"", "\\\"");
//                            s = "event:message\ndata:{\"message\":{\"type\":\"content\",\"content\":\"" + string + "\"}}\n\n";

                            if (msg != null) {
                                content = msg.getString("content").replace("\n", "\\n").replace("\"", "\\\"");
                                dto.getHistory().append(msg.getString("content"));
                                if (dto.isEnableCitation() && metaInfo != null) {
                                    contentCache.append(content);
                                    if (!contentCache.toString().contains("<")) {
                                        contentCache.setLength(0);
                                    } else {
                                        Matcher matcher = pattern.matcher(contentCache.toString());
                                        if (matcher.find()) {
                                            if (StringUtil.isNotBlank(contentCache.substring(0, matcher.start()))) {
                                                //输出匹配到内容前面的字符
                                                content = contentCache.substring(0, matcher.start());
                                                writeToResponse(response, dto.getResponseHandlerProxy(), "message", createMessageJson("content", content));
                                            }
                                            String numberStr = matcher.group().replace("<$", "").replace("$>", "");
                                            System.out.println("--------拿到结构中的数字输出引用: " + numberStr);
                                            if (metaInfo.containsKey(numberStr)) {
                                                Object o = metaInfo.get(numberStr);
                                                referenceHistories.add(o);
                                                writeToResponse(response, dto.getResponseHandlerProxy(), "message", createMessageJson("refer", o));
                                            }
                                            contentCache.delete(0, matcher.end());
                                            content = null;
                                        } else {
                                            int firstIndex = contentCache.indexOf("<");
                                            int lengthAfterSub = contentCache.length() - firstIndex;
                                            if (lengthAfterSub > 10) {
                                                content = contentCache.substring(0, firstIndex);
                                                contentCache.delete(0, firstIndex + 1);
                                            } else {
                                                content = null;
                                            }

                                        }
                                    }
                                }

                            }
                            if (StrUtil.isNotEmpty(content)) {
                                writeToResponse(response, dto.getResponseHandlerProxy(), "message", createMessageJson("content", content));
                            }

                        }
                    } else {
                        // 不知道上面数据格式，直接返回
                        // 这部分格式有问题，忽略
//                        dto.getHistory().append(anserwer);
//                        s = "event:message\ndata:{\"message\":{\"type\":\"content\",\"content\":\"" + anserwer.replace("\n", "\\\\n") + "\"}}\n\n";
                        s = null;
                    }
                }

//                if (responseHandlerProxy != null && s != null) {
//                    s = responseHandlerProxy.handler(s);
//                }
//                if (s != null) {
//                    try {
//                        response.getWriter().write(s);
//                        response.getWriter().flush();
//                    } catch (IOException e) {
//                        throw new RuntimeException(e);
//                    }
//                }

            }

            reader.close();
//            System.out.println("响应数据: " + response.toString());
        } else {
            System.out.println("请求失败，响应码: " + responseCode);
            System.out.println("请求失败，响应码: " + connection.getResponseMessage());
        }

        dto.getReferenceHistory().append(JSON.toJSONString(referenceHistories));
    }

    //    BasicProcessDTO
    private JSONObject buildSipModelDtoByBasicProcessDTO(ModelCallDTO callDTO, String basicProcessDTO) {
        JSONObject body = new JSONObject();
        JSONObject dto = JSON.parseObject(basicProcessDTO);
        if (dto.containsKey("temperature")) {
            body.put("temperature", dto.getDouble("temperature"));
        }
        if (dto.containsKey("topP")) {
            body.put("top_p", dto.getDouble("topP"));
        }
        if (dto.containsKey("maxTokens")) {
            body.put("max_tokens", dto.getInteger("maxTokens"));
        }

//        body.put("model", "dsr1");
        body.put("model", "dsv3");
        // 适配一下不同选项的模型
        if (dto.containsKey("modelType")) {
            String modelType = dto.getString("modelType");
            if (StringUtils.isNotBlank(modelType)) {
                if ("dsv3".equals(modelType)) {
                    body.put("model", "dsv3");
                } else if ("dsr1".equals(modelType)) {
                    body.put("model", "dsr1");
                } else if ("qwen".equals(modelType)) {
                    body.put("model", "qwen");
                } else if ("qwen-vl".equals(modelType)) {
                    body.put("model", "qwen-vl");
                }
            }
        }


        ArrayList<Object> msgList = new ArrayList<>();
        //系统人设
        if (StringUtils.isNotBlank(dto.getString("systemMessage"))) {
            HashMap<String, String> userInputMap = new HashMap<>();
            userInputMap.put("role", "system");
            userInputMap.put("content", dto.getString("systemMessage"));
            msgList.add(userInputMap);
        }
        //历史
//        dto.containsKey("multiTurn") && dto.getBoolean("multiTurn") &&
        if (dto.containsKey("histories")) {
            JSONArray histories = dto.getJSONArray("histories");
            for (Object history : histories) {
                Set<String> strings = ((JSONObject) history).keySet();
                if (strings.contains("role") && strings.contains("content")) {
                    msgList.add(history);
                } else if (strings.size() == 1) {
                    //说明输入和输出，作为key和value的
                    HashMap<String, String> e = new HashMap<>();
                    e.put("role", "user");
                    String userInput = strings.iterator().next();
                    e.put("content", userInput);
                    msgList.add(e);
                    e = new HashMap<>();
                    e.put("role", "assistant");
                    e.put("content", ((JSONObject) history).getString(userInput));
                    msgList.add(e);

                } else {
                    log.warn("history:" + ((JSONObject) history).toString());
                }
            }
        }

        HashMap<String, String> userInputMap = new HashMap<>();
        userInputMap.put("role", "user");
        userInputMap.put("content", dto.getString("userMessage"));
        // 需要拼接userInputMap内容
//        callDTO.
        msgList.add(userInputMap);
        body.put("messages", msgList);
        return body;
    }
}
