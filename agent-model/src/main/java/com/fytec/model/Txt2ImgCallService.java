package com.fytec.model;

import com.fytec.dto.llm.ModelCallDTO;
import com.fytec.dto.llm.Txt2ImgCallDTO;
import com.fytec.dto.response.Txt2ImgDTO;
import com.fytec.model.local.FytecModelCallService;
import com.fytec.token.ClientTokenService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
@Slf4j
@RequiredArgsConstructor
public class Txt2ImgCallService {
    private final ClientTokenService clientTokenService;

    //调用豆包非流式接口
    @SneakyThrows
    public List<Txt2ImgDTO> callFytecTxt2Img(Txt2ImgCallDTO dto) {
        FytecModelCallService modelCallService = new FytecModelCallService();
        return modelCallService.processTxt2Img(dto, clientTokenService);
    }
}
