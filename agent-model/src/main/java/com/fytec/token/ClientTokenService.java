package com.fytec.token;

import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.fytec.config.ClientProperties;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.net.URL;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.Map;

@Component
@AllArgsConstructor
public class ClientTokenService {

    private final ClientProperties clientProperties;

    public String getToken() {
        try {
            URL url = new URL(clientProperties.getTokenUrl() +
                    "&client_secret=" + clientProperties.getClientSecret() +
                    "&client_id=" + clientProperties.getClientId());
            // 构建请求
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(url.toURI()) // 从配置中获取 token URI
                    .GET()
                    .build();

            // 发送请求并获取响应
            HttpResponse<String> response = HttpClient.newHttpClient().send(request, HttpResponse.BodyHandlers.ofString());

            // 检查响应状态码
            if (response.statusCode() == 200) {
                // 假设响应体中直接包含 token
                String result = response.body();
                if (StringUtils.isBlank(result)) {
                    throw new ValidateException("获取token失败");
                }
                Map<String, Object> map = JSON.parseObject(result, Map.class);
                return map.get("client_token") != null ? map.get("client_token").toString() : null;
            } else {
                // 处理错误情况
                throw new RuntimeException("Failed to get token. Status code: " + response.statusCode());
            }

        } catch (Exception e) {
            // 处理请求发送或响应处理过程中的异常
            e.printStackTrace();
            throw new RuntimeException("Error getting token", e);
        }
    }

    @SneakyThrows
    public String getToken4Python() {
        String oauthUrl = clientProperties.getTokenPyUrl();
        JSONObject param = new JSONObject();
        param.put("grant_type", "client_credentials");
        param.put("client_id", clientProperties.getClientId());
        param.put("client_secret", clientProperties.getClientSecret());
        param.put("scope", "fytec:llm");
        com.mashape.unirest.http.HttpResponse<String> response = Unirest.post(oauthUrl)
                .header("Content-Type", "application/json")
                .body(JSON.toJSONString(param))
                .asString();
        if (response.getStatus() != 200) {
            throw new ValidateException("获取token失败");
        }
        JSONObject jsonObject = JSON.parseObject(response.getBody());
        return jsonObject.getString("client_token");
    }

}
