package com.fytec.reranker.local;

import cn.hutool.core.exceptions.ValidateException;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;
import org.springframework.http.MediaType;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class NingXiaRerankCallService {
    public static final String RERANK_URL = "http://111.51.197.134:8082/rerank";

    public void textRerank(String query, List<String> documents, List<Float> valuesFloat) {

        for (String document : documents) {
            List<String> input = new ArrayList<>();
            input.add(document);
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", "BAAI/bge-reranker-large");
            requestBody.put("query", query);
            requestBody.put("texts", input);

            // 将请求体转换为 JSON 字符串
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonBody;
            try {
                jsonBody = objectMapper.writeValueAsString(requestBody);
            } catch (JsonProcessingException e) {
                throw new ValidateException("重排json格式化异常");
            }

            HttpResponse<String> response;
            try {
                response = Unirest.post(RERANK_URL)
                        .header("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                        .body(jsonBody)
                        .asString();
            } catch (UnirestException e) {
                throw new ValidateException("重排模型请求失败");
            }

            if (response.getStatus() != 200) {
                throw new ValidateException("重排模型请求失败");
            }

            JSONArray results = JSON.parseArray(response.getBody());
            if (results != null && !results.isEmpty()) {
                for (Object result : results) {
                    JSONObject t = (JSONObject) result;
                    int index = t.getIntValue("index");
                    float relevanceScore = t.getFloatValue("score");
                    float newValue = valuesFloat.get(index) + relevanceScore * 1000;
                    valuesFloat.set(index, newValue);
                }
            }
        }
    }
}
