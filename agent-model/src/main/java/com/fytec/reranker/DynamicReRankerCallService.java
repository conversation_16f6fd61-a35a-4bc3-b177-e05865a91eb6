package com.fytec.reranker;

import cn.hutool.core.exceptions.ValidateException;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fytec.constant.enums.ReRankerModelEnum;
import com.fytec.doc.ali.AliService;
import com.fytec.dto.llm.VectorResultDTO;
import com.fytec.reranker.local.NingXiaRerankCallService;
import com.fytec.reranker.local.WjEduRerankCallService;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.fytec.constant.ProjectApiKey.SIP_API_KEY;
import static com.fytec.constant.enums.ReRankerModelEnum.ning_xia;

@Slf4j
@Component
@RequiredArgsConstructor
public class DynamicReRankerCallService {

    @Autowired
    private AliService aliService;

    public static void main(String[] args) {
        DynamicReRankerCallService service = new DynamicReRankerCallService();
        VectorResultDTO v = new VectorResultDTO();
        v.setDoc("测试标题1");
        v.setText("台的惠企政策，建立以科技创新为主线涵盖科技、发改、工信、财税、商务、人社、知识产权、农业等领域的政策库，汇聚创新政策方便企业主动找政策。二是利用大数据技术实现政策拆解，主动比对政策要求与企业自身条件，精确计算企业条件与政策的符合度，自主计算、自动匹配，精准推送政策。系统的建成能够进一步落实国务院关于主动精准推送政策的要求，能够有效提升政策的穿透力和传导效应，打通惠企政策落地“最后一公里”，引导企业使用政策，唤醒企业创新意识，激发创新创业动能。“宁夏政策计算器”采用大数据+人工智能分解的方式，定时从宁夏回族自治区人民政府、宁夏科技厅官网、宁夏工信厅官网、国家两化融合服务平台网站、宁夏税务网站、宁夏政务服务网等网站拉取政策。将拉取的政策进行整理清晰，人工拆解客观指标，以便于计算企业与政策的匹配度。- 1 -宁夏政策计算器操作手册第一章 宁夏政策计算器 PC平台1.1宁夏政策计算器首页宁夏政策计算器登陆网址为：https://jsq.nxinfo.org.cn用户在浏览器输入网址打开宁夏政策计算器首页，页面上方标签有智能匹配、政策检索、");
        v.setScore(6f);
        List<VectorResultDTO> docs = new ArrayList<VectorResultDTO>();
        docs.add(v);
        service.filterKnowledgeDocsByReRanker(ning_xia, "政策检索", 10, docs);
    }

    public List<VectorResultDTO> filterKnowledgeDocsByReRanker(ReRankerModelEnum type, String query, int topK,
                                                               List<VectorResultDTO> knowledgeDocs) {
        List<VectorResultDTO> reList;
        List<String> documents = new ArrayList<>(knowledgeDocs.size());
        List<Float> valuesFloat = new ArrayList<>(knowledgeDocs.size());
        int totalLens = 0;
        for (VectorResultDTO knowledgeDoc : knowledgeDocs) {
            if (StringUtils.isBlank(knowledgeDoc.getDoc())) {
                documents.add("片段：%s".formatted(knowledgeDoc.getText()));
            } else {
                documents.add("标题：%s，片段：%s".formatted(knowledgeDoc.getDoc(), knowledgeDoc.getText()));
            }
            totalLens += knowledgeDoc.getDoc().length() + knowledgeDoc.getText().length();
            valuesFloat.add(knowledgeDoc.getScore());
        }
        if (totalLens > 40000) {
            int maxLen = Math.floorDiv(40000, knowledgeDocs.size());
            // 超过3万token了，需要缩减了，超过400字的，直接砍到400,因为最多100个
            documents = new ArrayList<>(knowledgeDocs.size());
            for (VectorResultDTO knowledgeDoc : knowledgeDocs) {
                if (knowledgeDoc.getText().length() > maxLen) {
                    documents.add("标题：%s，片段：%s".formatted(knowledgeDoc.getDoc(), knowledgeDoc.getText().substring(0, maxLen)));
                } else {
                    documents.add("标题：%s，片段：%s".formatted(knowledgeDoc.getDoc(), knowledgeDoc.getText()));
                }
            }
        }
        switch (type) {
            case sip -> {
                // 实验室的
                String apiUrl = "https://espace.sipedu.cn/dmxllm/tmp/v1/rerank";

                Map<String, Object> requestBody = new HashMap<>();
                requestBody.put("model", "reranker");
                requestBody.put("query", query);
                requestBody.put("documents", documents);
                requestBody.put("top_n", documents.size());


                // 将请求体转换为 JSON 字符串
                ObjectMapper objectMapper = new ObjectMapper();
                String jsonBody = null;
                try {
                    jsonBody = objectMapper.writeValueAsString(requestBody);
                } catch (JsonProcessingException e) {
                    throw new ValidateException("重排json格式化异常");
                }

                com.mashape.unirest.http.HttpResponse<String> response = null;
                try {
                    response = Unirest.post(apiUrl)
                            .header("Authorization", "Bearer " + SIP_API_KEY)
                            .header("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                            .body(jsonBody)
                            .asString();
                } catch (UnirestException e) {
                    throw new ValidateException("重排模型请求失败");
                }
                JSONObject parseObj = JSON.parseObject(response.getBody());
                if (parseObj.containsKey("results")) {
                    JSONArray results = parseObj.getJSONArray("results");
                    if (results != null && !results.isEmpty()) {
                        log.info("reranker result len:" + results.size());
                        // todo 现在可能为空
                        for (Object result : results) {
                            JSONObject t = (JSONObject) result;
                            int index = t.getIntValue("index");
                            float relevanceScore = t.getFloatValue("relevance_score");
                            //
                            float newValue = valuesFloat.get(index) + relevanceScore * 1000;
                            valuesFloat.set(index, newValue);
                        }
                    }
                }
            }
            case gte -> {
                try {
                    JSONObject body = new JSONObject();
                    body.put("model", "gte-rerank");
                    body.put("parameters", new JSONObject().put("top_n", topK));
                    JSONObject value = new JSONObject();
                    value.put("query", query);
                    value.put("documents", documents);
                    body.put("input", value);

                    String returnResult = aliService.textRerank(body.toJSONString());
                    JSONArray results = JSON.parseArray(returnResult);
                    // 根据接口快排，value=ranker_score * 1000 + 原本的score，作为结果，topK，20个
                    for (Object result : results) {
                        JSONObject t = (JSONObject) result;
                        int index = t.getIntValue("index");
                        float relevanceScore = t.getFloatValue("relevance_score");
                        float newValue = valuesFloat.get(index) + relevanceScore * 1000;
                        valuesFloat.set(index, newValue);
                    }


                } catch (Exception e) {
                    log.error("reranker 错误", e);
                    throw new ValidateException("reranker 错误");
                }
            }
            case ning_xia -> {
                NingXiaRerankCallService ningXiaRerankCallService = new NingXiaRerankCallService();
                ningXiaRerankCallService.textRerank(query, documents, valuesFloat);
            }
            case wj_edu -> {
                WjEduRerankCallService wjEduRerankCallService = new WjEduRerankCallService();
                wjEduRerankCallService.textRerank(query, documents, valuesFloat);
            }
            default -> throw new ValidateException("未选择重排模型");
        }
        HashMap<Long, Float> sortDict = new HashMap<>();
        for (int i = 0; i < knowledgeDocs.size(); i++) {
            sortDict.put(knowledgeDocs.get(i).getId(), valuesFloat.get(i));
        }
        // 快排 knowledgeDocs ，根据values，然后得到的结果作为reList传输
        knowledgeDocs.sort(Comparator.comparingDouble(o -> -sortDict.get(o.getId())));
        reList = knowledgeDocs.stream().limit(topK).toList();
        return reList;
    }
}
