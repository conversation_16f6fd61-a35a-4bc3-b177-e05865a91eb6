package com.fytec.doc;

import com.fytec.config.ClientProperties;
import com.fytec.dto.llm.DocProcessResultDTO;
import com.fytec.token.ClientTokenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;


@Slf4j
@Component
public class BaiduDocParseService {

    private final WebClient webClient;

    private final ClientTokenService clientTokenService;

    private final ClientProperties clientProperties;

    public BaiduDocParseService(ClientTokenService clientTokenService, ClientProperties clientProperties) {
        this.clientTokenService = clientTokenService;
        this.clientProperties = clientProperties;
        this.webClient = WebClient.create();
    }

    public String submitTask(String fileUrl, String fileName) {
        Map<String, String> params = new HashMap<>();
        params.put("fileUrl", fileUrl);
        params.put("fileName", fileName);

        Mono<String> mono = webClient.post()
                .uri(clientProperties.getDoc().getSubmitUrl())
                .contentType(MediaType.APPLICATION_JSON)
                .headers(httpHeaders -> {
                    httpHeaders.setBearerAuth(clientTokenService.getToken());
                })
                .body(BodyInserters.fromValue(params))
                .retrieve()
                .bodyToMono(String.class);
        String taskId = mono.block();
        log.info("提交百度文档解析任务，任务ID:{}", taskId);
        return taskId;
    }

    public DocProcessResultDTO getTask(String taskId) {
        Mono<DocProcessResultDTO> mono = webClient.post()
                .uri(clientProperties.getDoc().getTaskUrl() + "?taskId=" + taskId)
                .headers(httpHeaders -> {
                    httpHeaders.setBearerAuth(clientTokenService.getToken());
                })
                .retrieve()
                .bodyToMono(DocProcessResultDTO.class);
        DocProcessResultDTO resultDTO = mono.block();
        log.info("获取百度文档解析结果:{}", resultDTO);
        return resultDTO;
    }
}
