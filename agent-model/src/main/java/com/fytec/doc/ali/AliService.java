package com.fytec.doc.ali;

import com.fytec.config.ClientProperties;
import com.fytec.token.ClientTokenService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;


@Slf4j
@Component
@RequiredArgsConstructor
public class AliService {

    private final WebClient webClient = WebClient.create();

    private final ClientTokenService clientTokenService;

    private final ClientProperties clientProperties;

    public String textRerank(String body) {
        Map<String, String> params = new HashMap<>();
        params.put("params", body);

        Mono<String> mono = webClient.post()
                .uri(clientProperties.getText().getRerankUrl())
                .contentType(MediaType.APPLICATION_JSON)
                .headers(httpHeaders -> {
                    httpHeaders.setBearerAuth(clientTokenService.getToken());
                })
                .body(BodyInserters.fromValue(params))
                .retrieve()
                .bodyToMono(String.class);
        return mono.block();
    }
}
