package com.fytec.doc.volcengine;

import com.fytec.config.ClientProperties;
import com.fytec.token.ClientTokenService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;


@Slf4j
@Component
@RequiredArgsConstructor
public class VolcengineService {

    private final WebClient webClient = WebClient.create();

    private final ClientTokenService clientTokenService;

    private final ClientProperties clientProperties;

    public String ocrNormalByUrl(String fileUrl) {
        Map<String, String> params = new HashMap<>();
        params.put("fileUrl", fileUrl);

        Mono<String> mono = webClient.post()
                .uri(clientProperties.getImage().getOrcNormalUrl())
                .contentType(MediaType.APPLICATION_JSON)
                .headers(httpHeaders -> {
                    httpHeaders.setBearerAuth(clientTokenService.getToken());
                })
                .body(BodyInserters.fromValue(params))
                .retrieve()
                .bodyToMono(String.class);
        return mono.block();
    }

    public String ocrNormal(String fileContent) {
        Map<String, String> params = new HashMap<>();
        params.put("fileContent", fileContent);

        Mono<String> mono = webClient.post()
                .uri(clientProperties.getImage().getOrcNormalUrl())
                .contentType(MediaType.APPLICATION_JSON)
                .headers(httpHeaders -> {
                    httpHeaders.setBearerAuth(clientTokenService.getToken());
                })
                .body(BodyInserters.fromValue(params))
                .retrieve()
                .bodyToMono(String.class);
        return mono.block();
    }
}
