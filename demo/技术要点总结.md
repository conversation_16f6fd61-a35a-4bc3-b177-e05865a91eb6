## 5. 流式响应计费技术要点总结

### 5.1 流式计费核心挑战
1. **内容累积**：需要在流过程中收集所有推送内容
2. **异步处理**：流的完成、超时、错误都是异步事件
3. **会话管理**：需要维护流会话状态直到完成
4. **计费时机**：支持流完成后计费或实时增量计费
5. **异常处理**：处理流中断、超时等异常情况

### 5.2 设计模式应用
1. **代理模式**：BillingSseEmitter代理原始SseEmitter
2# AI服务计费需求总结与技术方案

## 1. 服务计费规则表

| 服务名称                   | 计费方式 | 计量单位 | Token换算规则                                | 备注                                                    |
| -------------------------- | -------- | -------- | -------------------------------------------- | ------------------------------------------------------- |
| 精品语音合成服务（短文本） | 按字符数 | 字符     | -                                            | 单次调用≤5个字符，计算公式：合格次数 × 文字实际长度 ÷ 5 |
| 精品语音合成服务（长文本） | 按字符数 | 字符     | -                                            | 长文本语音合成                                          |
| 教育场景文字识别服务       | 按Token  | Token    | 4个英文字符=1个Token<br>2个中文字符=1个Token | OCR文字识别                                             |
| 通用场景文字识别服务       | 按Token  | Token    | 4个英文字符=1个Token<br>2个中文字符=1个Token | 通用OCR识别                                             |
| 语音识别服务（实时）       | 按时长   | 秒       | -                                            | 实时语音转文字，按用户语音输入时长计费                  |
| 语音识别服务（文件）       | 按时长   | 秒       | -                                            | 文件语音转文字，按音频文件时长计费                      |

## 2. 计费策略说明

### 按请求计算
- **语音识别服务**：按音频时长（秒）计费
- **文字识别服务**：按识别出的文字内容Token数计费

### 按响应计算  
- **语音合成服务**：按输入文字字符数计费

### Token换算规则
```
英文：4个字符 = 1个Token
中文：2个字符 = 1个Token
```

### 折算规则
根据图7显示的折算规则：
- 折算1：3分钟音频文字对应力为30个语音识别次数（不足3分钟按3分钟计算）
- 折算2：当输入文字字符长度超过时，按实际字符长度上述折算算 
- 分段设置：
  - 分段1：3分钟音频文字对应为XX（15.5-3次）
  - 分段2：15分钟音频文字对应为XX（15.5-3次）  
  - 分段3：17分钟音频文字对应为XX（17.5-3.4倍计算）

## 3. 业务代码映射关系

| 服务名称                   | 业务代码          | 描述           |
| -------------------------- | ----------------- | -------------- |
| 精品语音合成服务（短文本） | TTS_PREMIUM_SHORT | 短文本语音合成 |
| 精品语音合成服务（长文本） | TTS_PREMIUM_LONG  | 长文本语音合成 |
| 教育场景文字识别服务       | OCR_EDUCATION     | 教育场景OCR    |
| 通用场景文字识别服务       | OCR_GENERAL       | 通用场景OCR    |
| 语音识别服务（实时）       | ASR_REALTIME      | 实时语音识别   |
| 语音识别服务（文件）       | ASR_FILE          | 文件语音识别   |

## 4. 切面监控技术方案

### 4.1 切面注解设计

```java
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface BillingMonitor {
    
    /**
     * 服务类型编码
     */
    ServiceType serviceType();
    
    /**
     * 计费策略
     */
    BillingStrategy strategy() default BillingStrategy.AUTO;
    
    /**
     * 内容参数名（用于获取计费内容）
     */
    String contentParam() default "";
    
    /**
     * 是否异步记录
     */
    boolean async() default true;
    
    /**
     * 自定义计费逻辑类
     */
    Class<? extends BillingCalculator> calculator() default DefaultBillingCalculator.class;
    
    /**
     * 是否为流式响应
     */
    boolean streaming() default false;
    
    /**
     * 流式响应计费模式
     */
    StreamBillingMode streamMode() default StreamBillingMode.ON_COMPLETE;
}

// 流式计费模式枚举
public enum StreamBillingMode {
    ON_COMPLETE,    // 流结束时计费
    INCREMENTAL,    // 增量计费（每个chunk计费）
    PERIODIC        // 定期计费
}

// 服务类型枚举
public enum ServiceType {
    TTS_PREMIUM_SHORT("精品语音合成-短文本"),
    TTS_PREMIUM_LONG("精品语音合成-长文本"),
    OCR_EDUCATION("教育场景OCR"),
    OCR_GENERAL("通用场景OCR"),
    ASR_REALTIME("实时语音识别"),
    ASR_FILE("文件语音识别");
    
    private final String description;
    ServiceType(String description) { this.description = description; }
}

// 计费策略枚举
public enum BillingStrategy {
    AUTO,           // 自动检测
    BY_CHARACTERS,  // 按字符数
    BY_TOKENS,      // 按Token数
    BY_DURATION,    // 按时长
    BY_REQUESTS     // 按请求次数
}
```

### 4.2 切面实现思路

```java
@Aspect
@Component
@Slf4j
public class BillingMonitorAspect {
    
    @Autowired
    private BillingService billingService;
    @Autowired
    private StreamBillingManager streamBillingManager;
    
    @Around("@annotation(billingMonitor)")
    public Object around(ProceedingJoinPoint pjp, BillingMonitor billingMonitor) throws Throwable {
        
        long startTime = System.currentTimeMillis();
        String userId = getCurrentUserId();
        String sessionId = generateSessionId();
        Object result = null;
        
        try {
            // 解析参数内容
            ContentInfo contentInfo = parseContent(pjp.getArgs(), billingMonitor);
            
            // 执行目标方法
            result = pjp.proceed();
            
            // 处理流式响应
            if (billingMonitor.streaming() && result instanceof SseEmitter) {
                return handleStreamingResponse((SseEmitter) result, billingMonitor, 
                                             contentInfo, userId, sessionId, startTime);
            } 
            // 处理普通响应
            else {
                handleNormalResponse(result, billingMonitor, contentInfo, 
                                   userId, sessionId, startTime);
            }
            
        } catch (Exception e) {
            recordFailedBilling(userId, sessionId, billingMonitor.serviceType(), 
                              e.getMessage(), startTime);
            throw e;
        }
        
        return result;
    }
    
    /**
     * 处理流式响应
     */
    private SseEmitter handleStreamingResponse(SseEmitter originalEmitter, 
                                             BillingMonitor monitor,
                                             ContentInfo contentInfo, 
                                             String userId, 
                                             String sessionId, 
                                             long startTime) {
        
        // 创建代理SseEmitter
        BillingSseEmitter proxyEmitter = new BillingSseEmitter(originalEmitter, 
                                                              monitor, 
                                                              contentInfo, 
                                                              userId, 
                                                              sessionId, 
                                                              startTime,
                                                              streamBillingManager);
        
        // 注册流会话
        streamBillingManager.registerSession(sessionId, monitor.serviceType(), 
                                           contentInfo, userId, startTime);
        
        return proxyEmitter;
    }
    
    /**
     * 处理普通响应
     */
    private void handleNormalResponse(Object result, BillingMonitor monitor,
                                    ContentInfo contentInfo, String userId, 
                                    String sessionId, long startTime) {
        
        // 计算计费信息
        BillingInfo billingInfo = calculateBilling(contentInfo, monitor, result);
        
        // 构建计费记录
        BillingRecord record = buildBillingRecord(userId, monitor.serviceType(), 
                                                billingInfo, startTime);
        record.setStatus(BillingStatus.SUCCESS);
        record.setSessionId(sessionId);
        
        // 记录计费日志
        if (monitor.async()) {
            billingService.recordAsync(record);
        } else {
            billingService.record(record);
        }
    }
    
    /**
     * 解析不同类型的内容参数
     */
    private ContentInfo parseContent(Object[] args, BillingMonitor monitor) {
        ContentInfo info = new ContentInfo();
        
        for (Object arg : args) {
            if (arg == null) continue;
            
            if (arg instanceof MultipartFile) {
                info = handleMultipartFile((MultipartFile) arg);
            } else if (arg instanceof File) {
                info = handleFile((File) arg);
            } else if (arg instanceof InputStream) {
                info = handleInputStream((InputStream) arg);
            } else if (arg instanceof String && isBase64(arg.toString())) {
                info = handleBase64(arg.toString());
            } else if (arg instanceof byte[]) {
                info = handleBytes((byte[]) arg);
            } else if (arg instanceof String && isUrl(arg.toString())) {
                info = handleUrl(arg.toString());
            } else if (arg instanceof String) {
                info = handleText(arg.toString());
            }
        }
        
        return info;
    }
}
```

### 4.3 流式响应计费组件

```java
/**
 * 计费代理SseEmitter
 */
public class BillingSseEmitter extends SseEmitter {
    
    private final SseEmitter originalEmitter;
    private final BillingMonitor monitor;
    private final ContentInfo contentInfo;
    private final String userId;
    private final String sessionId;
    private final long startTime;
    private final StreamBillingManager billingManager;
    
    // 流式内容累积
    private final StringBuilder streamContent = new StringBuilder();
    private final AtomicInteger chunkCount = new AtomicInteger(0);
    private final AtomicLong totalBytes = new AtomicLong(0);
    
    public BillingSseEmitter(SseEmitter originalEmitter, BillingMonitor monitor,
                           ContentInfo contentInfo, String userId, 
                           String sessionId, long startTime,
                           StreamBillingManager billingManager) {
        super(originalEmitter.getTimeout());
        this.originalEmitter = originalEmitter;
        this.monitor = monitor;
        this.contentInfo = contentInfo;
        this.userId = userId;
        this.sessionId = sessionId;
        this.startTime = startTime;
        this.billingManager = billingManager;
        
        setupCallbacks();
    }
    
    private void setupCallbacks() {
        // 设置完成回调
        originalEmitter.onCompletion(() -> {
            handleStreamComplete();
            this.complete();
        });
        
        // 设置超时回调
        originalEmitter.onTimeout(() -> {
            handleStreamTimeout();
            this.completeWithError(new TimeoutException("SSE stream timeout"));
        });
        
        // 设置错误回调
        originalEmitter.onError((ex) -> {
            handleStreamError(ex);
            this.completeWithError(ex);
        });
    }
    
    @Override
    public void send(Object object) throws IOException {
        // 拦截发送内容
        interceptSendContent(object);
        
        // 发送到原始emitter
        originalEmitter.send(object);
    }
    
    @Override
    public void send(SseEventBuilder builder) throws IOException {
        // 拦截事件内容
        interceptEventContent(builder);
        
        // 发送到原始emitter
        originalEmitter.send(builder);
    }
    
    /**
     * 拦截发送内容
     */
    private void interceptSendContent(Object content) {
        if (content != null) {
            String contentStr = content.toString();
            streamContent.append(contentStr);
            chunkCount.incrementAndGet();
            totalBytes.addAndGet(contentStr.getBytes(StandardCharsets.UTF_8).length);
            
            // 增量计费模式
            if (monitor.streamMode() == StreamBillingMode.INCREMENTAL) {
                billingManager.recordIncrement(sessionId, contentStr);
            }
        }
    }
    
    /**
     * 拦截事件内容
     */
    private void interceptEventContent(SseEventBuilder builder) {
        // 通过反射或其他方式获取事件内容
        try {
            Field dataField = builder.getClass().getDeclaredField("data");
            dataField.setAccessible(true);
            Object data = dataField.get(builder);
            if (data != null) {
                interceptSendContent(data);
            }
        } catch (Exception e) {
            log.warn("Failed to intercept SSE event content", e);
        }
    }
    
    /**
     * 处理流完成
     */
    private void handleStreamComplete() {
        StreamBillingInfo streamInfo = StreamBillingInfo.builder()
            .sessionId(sessionId)
            .totalContent(streamContent.toString())
            .chunkCount(chunkCount.get())
            .totalBytes(totalBytes.get())
            .duration(System.currentTimeMillis() - startTime)
            .build();
            
        billingManager.completeStream(sessionId, streamInfo);
    }
    
    /**
     * 处理流超时
     */
    private void handleStreamTimeout() {
        billingManager.timeoutStream(sessionId, "Stream timeout");
    }
    
    /**
     * 处理流错误
     */
    private void handleStreamError(Throwable ex) {
        billingManager.errorStream(sessionId, ex.getMessage());
    }
}

/**
 * 流式计费管理器
 */
@Component
@Slf4j
public class StreamBillingManager {
    
    @Autowired
    private BillingService billingService;
    
    // 流会话缓存
    private final Map<String, StreamSession> sessionCache = new ConcurrentHashMap<>();
    
    /**
     * 注册流会话
     */
    public void registerSession(String sessionId, ServiceType serviceType, 
                              ContentInfo contentInfo, String userId, long startTime) {
        StreamSession session = StreamSession.builder()
            .sessionId(sessionId)
            .serviceType(serviceType)
            .contentInfo(contentInfo)
            .userId(userId)
            .startTime(startTime)
            .status(StreamStatus.ACTIVE)
            .build();
            
        sessionCache.put(sessionId, session);
        log.info("Registered stream session: {}", sessionId);
    }
    
    /**
     * 记录增量计费
     */
    public void recordIncrement(String sessionId, String content) {
        StreamSession session = sessionCache.get(sessionId);
        if (session != null) {
            session.appendContent(content);
            
            // 可以在这里实现实时计费逻辑
            BillingInfo incrementBilling = calculateIncrementBilling(content, session);
            session.addIncrementBilling(incrementBilling);
        }
    }
    
    /**
     * 完成流计费
     */
    public void completeStream(String sessionId, StreamBillingInfo streamInfo) {
        StreamSession session = sessionCache.get(sessionId);
        if (session != null) {
            session.setStatus(StreamStatus.COMPLETED);
            
            // 计算最终计费
            BillingInfo finalBilling = calculateFinalBilling(session, streamInfo);
            
            // 记录计费日志
            BillingRecord record = BillingRecord.builder()
                .userId(session.getUserId())
                .sessionId(sessionId)
                .serviceType(session.getServiceType())
                .billingInfo(finalBilling)
                .streamInfo(streamInfo)
                .startTime(session.getStartTime())
                .endTime(System.currentTimeMillis())
                .status(BillingStatus.SUCCESS)
                .build();
                
            billingService.recordAsync(record);
            
            // 清理会话
            sessionCache.remove(sessionId);
            
            log.info("Stream billing completed for session: {}", sessionId);
        }
    }
    
    /**
     * 流超时处理
     */
    public void timeoutStream(String sessionId, String reason) {
        StreamSession session = sessionCache.get(sessionId);
        if (session != null) {
            session.setStatus(StreamStatus.TIMEOUT);
            recordFailedStream(session, reason);
            sessionCache.remove(sessionId);
        }
    }
    
    /**
     * 流错误处理
     */
    public void errorStream(String sessionId, String errorMsg) {
        StreamSession session = sessionCache.get(sessionId);
        if (session != null) {
            session.setStatus(StreamStatus.ERROR);
            recordFailedStream(session, errorMsg);
            sessionCache.remove(sessionId);
        }
    }
    
    /**
     * 计算最终计费
     */
    private BillingInfo calculateFinalBilling(StreamSession session, StreamBillingInfo streamInfo) {
        String totalContent = streamInfo.getTotalContent();
        
        return BillingInfo.builder()
            .characterCount(totalContent.length())
            .tokenCount(TokenUtil.calculateTokens(totalContent))
            .chunkCount(streamInfo.getChunkCount())
            .totalBytes(streamInfo.getTotalBytes())
            .duration(streamInfo.getDuration())
            .build();
    }
    
    /**
     * 记录失败的流
     */
    private void recordFailedStream(StreamSession session, String reason) {
        BillingRecord record = BillingRecord.builder()
            .userId(session.getUserId())
            .sessionId(session.getSessionId())
            .serviceType(session.getServiceType())
            .startTime(session.getStartTime())
            .endTime(System.currentTimeMillis())
            .status(BillingStatus.FAILED)
            .errorMsg(reason)
            .build();
            
        billingService.recordAsync(record);
    }
}

/**
 * 流会话信息
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StreamSession {
    private String sessionId;
    private ServiceType serviceType;
    private ContentInfo contentInfo;
    private String userId;
    private long startTime;
    private StreamStatus status;
    private StringBuilder totalContent = new StringBuilder();
    private List<BillingInfo> incrementBillings = new ArrayList<>();
    
    public void appendContent(String content) {
        this.totalContent.append(content);
    }
    
    public void addIncrementBilling(BillingInfo billing) {
        this.incrementBillings.add(billing);
    }
}

/**
 * 流计费信息
 */
@Data
@Builder
public class StreamBillingInfo {
    private String sessionId;
    private String totalContent;
    private int chunkCount;
    private long totalBytes;
    private long duration;
}

/**
 * 流状态枚举
 */
public enum StreamStatus {
    ACTIVE,     // 活跃
    COMPLETED,  // 完成
    TIMEOUT,    // 超时
    ERROR       // 错误
}

```java
@Component
public class ContentProcessorFactory {
    
    public ContentProcessor getProcessor(Object content) {
        if (content instanceof MultipartFile) {
            return new MultipartFileProcessor();
        } else if (content instanceof File) {
            return new FileProcessor();
        } else if (content instanceof InputStream) {
            return new InputStreamProcessor();
        } else if (content instanceof String && isBase64(content.toString())) {
            return new Base64Processor();
        } else if (content instanceof byte[]) {
            return new BytesProcessor();
        } else if (content instanceof String && isUrl(content.toString())) {
            return new UrlProcessor();
        } else if (content instanceof String) {
            return new TextProcessor();
        }
        return new DefaultProcessor();
    }
}

// 处理器接口
public interface ContentProcessor {
    ContentInfo process(Object content, ServiceType serviceType);
}

// MultipartFile处理器示例
@Component
public class MultipartFileProcessor implements ContentProcessor {
    
    @Override
    public ContentInfo process(Object content, ServiceType serviceType) {
        MultipartFile file = (MultipartFile) content;
        ContentInfo info = new ContentInfo();
        
        try {
            if (isAudioService(serviceType)) {
                // 音频文件：获取时长
                info.setDuration(getAudioDuration(file.getInputStream()));
                info.setFileSize(file.getSize());
            } else if (isImageService(serviceType)) {
                // 图片文件：获取尺寸和大小
                info.setFileSize(file.getSize());
                info.setImageDimensions(getImageDimensions(file.getInputStream()));
            } else if (isTextService(serviceType)) {
                // 文本文件：获取字符数
                String text = new String(file.getBytes(), StandardCharsets.UTF_8);
                info.setCharacterCount(text.length());
                info.setTokenCount(calculateTokens(text));
            }
        } catch (IOException e) {
            log.error("处理MultipartFile失败", e);
        }
        
        return info;
    }
}
```

### 4.4 不同内容类型处理策略

```java
@RestController
public class AIServiceController {
    
    @PostMapping("/tts/premium/short")
    @BillingMonitor(
        serviceType = ServiceType.TTS_PREMIUM_SHORT,
        strategy = BillingStrategy.BY_CHARACTERS,
        contentParam = "text"
    )
    public ResponseEntity<?> textToSpeechShort(@RequestParam String text) {
        // 业务逻辑
        return ResponseEntity.ok(ttsService.synthesize(text));
    }
    
    @PostMapping("/ocr/education")
    @BillingMonitor(
        serviceType = ServiceType.OCR_EDUCATION,
        strategy = BillingStrategy.BY_TOKENS
    )
    public ResponseEntity<?> ocrEducation(@RequestParam MultipartFile image) {
        // 业务逻辑
        return ResponseEntity.ok(ocrService.recognize(image));
    }
    
    @PostMapping("/asr/file")
    @BillingMonitor(
        serviceType = ServiceType.ASR_FILE,
        strategy = BillingStrategy.BY_DURATION
    )
    public ResponseEntity<?> speechToTextFile(@RequestParam MultipartFile audioFile) {
        // 业务逻辑
        return ResponseEntity.ok(asrService.recognize(audioFile));
    }
}
```

### 4.5 使用示例

```java
@RestController
public class AIServiceController {
    
    @PostMapping("/tts/premium/short")
    @BillingMonitor(
        serviceType = ServiceType.TTS_PREMIUM_SHORT,
        strategy = BillingStrategy.BY_CHARACTERS,
        contentParam = "text"
    )
    public ResponseEntity<?> textToSpeechShort(@RequestParam String text) {
        // 业务逻辑
        return ResponseEntity.ok(ttsService.synthesize(text));
    }
    
    @PostMapping("/ocr/education")
    @BillingMonitor(
        serviceType = ServiceType.OCR_EDUCATION,
        strategy = BillingStrategy.BY_TOKENS
    )
    public ResponseEntity<?> ocrEducation(@RequestParam MultipartFile image) {
        // 业务逻辑
        return ResponseEntity.ok(ocrService.recognize(image));
    }
    
    @PostMapping("/asr/file")
    @BillingMonitor(
        serviceType = ServiceType.ASR_FILE,
        strategy = BillingStrategy.BY_DURATION
    )
    public ResponseEntity<?> speechToTextFile(@RequestParam MultipartFile audioFile) {
        // 业务逻辑
        return ResponseEntity.ok(asrService.recognize(audioFile));
    }
    
    // 流式响应示例
    @GetMapping("/chat/stream")
    @BillingMonitor(
        serviceType = ServiceType.CHAT_COMPLETION,
        strategy = BillingStrategy.BY_TOKENS,
        streaming = true,
        streamMode = StreamBillingMode.ON_COMPLETE,
        contentParam = "prompt"
    )
    public SseEmitter chatStream(@RequestParam String prompt) {
        SseEmitter emitter = new SseEmitter(30000L);
        
        // 异步处理流式响应
        CompletableFuture.runAsync(() -> {
            try {
                // 模拟流式响应
                String[] responses = {"Hello", " there", "! How", " can I", " help you", " today?"};
                for (String response : responses) {
                    Thread.sleep(500);
                    emitter.send(SseEmitter.event()
                        .name("message")
                        .data(response));
                }
                emitter.complete();
            } catch (Exception e) {
                emitter.completeWithError(e);
            }
        });
        
        return emitter;
    }
    
    // 实时语音识别流
    @PostMapping("/asr/stream")
    @BillingMonitor(
        serviceType = ServiceType.ASR_REALTIME,
        strategy = BillingStrategy.BY_DURATION,
        streaming = true,
        streamMode = StreamBillingMode.INCREMENTAL
    )
    public SseEmitter realTimeASR(@RequestParam MultipartFile audioStream) {
        return asrService.recognizeStream(audioStream);
    }
    
    // 流式TTS
    @PostMapping("/tts/stream")
    @BillingMonitor(
        serviceType = ServiceType.TTS_STREAMING,
        strategy = BillingStrategy.BY_CHARACTERS,
        streaming = true,
        streamMode = StreamBillingMode.ON_COMPLETE,
        contentParam = "text"
    )
    public SseEmitter textToSpeechStream(@RequestParam String text) {
        return ttsService.synthesizeStream(text);
    }
}

```java
@Configuration
@EnableAsync
public class BillingConfig {
    
    @Bean
    @ConditionalOnMissingBean
    public BillingCalculator defaultBillingCalculator() {
        return new DefaultBillingCalculator();
    }
    
    @Bean
    public TaskExecutor billingTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("billing-");
        executor.initialize();
        return executor;
    }
}
```

## 5. 技术要点总结

### 切面设计原则
1. **灵活性**：支持多种内容类型和计费策略
2. **可扩展性**：通过策略模式支持自定义计费逻辑
3. **性能考虑**：异步记录日志，避免影响业务性能
4. **容错性**：业务异常不影响计费记录，计费异常不影响业务

### 关键技术点
1. **内容识别**：自动识别MultipartFile、File、InputStream、Base64、Bytes、URL等类型
2. **策略模式**：不同服务类型采用不同的计费策略
3. **异步处理**：计费日志异步入库，提升响应速度
4. **参数解析**：通过反射和注解获取方法参数进行内容分析
5. **异常处理**：完善的异常处理机制，确保系统稳定性

## 5. 流式响应计费技术要点总结

### 5.1 流式计费核心挑战
1. **内容累积**：需要在流过程中收集所有推送内容
2. **异步处理**：流的完成、超时、错误都是异步事件
3. **会话管理**：需要维护流会话状态直到完成
4. **计费时机**：支持流完成后计费或实时增量计费
5. **异常处理**：处理流中断、超时等异常情况

## 5. 流式响应计费技术要点总结

### 5.1 流式计费核心挑战
1. **内容累积**：需要在流过程中收集所有推送内容
2. **异步处理**：流的完成、超时、错误都是异步事件
3. **会话管理**：需要维护流会话状态直到完成
4. **计费时机**：支持流完成后计费或实时增量计费
5. **异常处理**：处理流中断、超时等异常情况

### 5.2 设计模式应用
1. **代理模式**：BillingSseEmitter代理原始SseEmitter，拦截所有数据推送
2. **状态模式**：StreamSession维护流的不同状态（活跃、完成、超时、错误）
3. **策略模式**：支持不同的流计费模式（完成计费、增量计费、定期计费）
4. **观察者模式**：流事件回调机制处理不同的流生命周期事件

### 5.3 关键技术实现
1. **内容拦截**：重写SseEmitter的send方法拦截推送内容
2. **回调机制**：利用onCompletion、onTimeout、onError回调处理流结束
3. **会话缓存**：使用ConcurrentHashMap维护活跃流会话
4. **异步计费**：流结束后异步记录最终计费信息
5. **监控指标**：集成Micrometer监控流会话指标

### 5.4 流式计费模式对比

| 计费模式    | 优点                 | 缺点                 | 适用场景           |
| ----------- | -------------------- | -------------------- | ------------------ |
| ON_COMPLETE | 准确计费，性能好     | 流失败时可能丢失计费 | 聊天对话、文档生成 |
| INCREMENTAL | 实时计费，不丢失数据 | 性能开销大，计费复杂 | 实时语音识别       |
| PERIODIC    | 平衡性能和准确性     | 计费时间点不准确     | 长时间流式服务     |

### 5.5 异常处理策略
1. **流超时**：记录部分内容的计费信息
2. **流错误**：根据错误类型决定是否计费
3. **连接断开**：客户端断开时触发计费结算
4. **服务重启**：持久化流会话状态，重启后恢复

### 5.6 性能优化建议
1. **内存管理**：及时清理完成的流会话，避免内存泄漏
2. **异步处理**：计费记录异步处理，不阻塞流响应
3. **批量处理**：增量计费时可批量提交减少数据库操作
4. **缓存策略**：热点数据缓存，减少重复计算

### 5.7 监控告警
1. **活跃会话监控**：监控活跃流会话数量
2. **超时会话告警**：长时间未完成的会话告警
3. **计费异常告警**：计费失败率监控
4. **性能指标**：流处理耗时和吞吐量监控

## 6. 完整使用示例

### 6.1 聊天流式响应
```java
@GetMapping("/ai/chat/stream")
@BillingMonitor(
    serviceType = ServiceType.CHAT_COMPLETION,
    strategy = BillingStrategy.BY_TOKENS,
    streaming = true,
    streamMode = StreamBillingMode.ON_COMPLETE
)
public SseEmitter chatStream(@RequestParam String message) {
    SseEmitter emitter = new SseEmitter(Long.MAX_VALUE);
    
    // 设置跨域头
    HttpServletResponse response = 
        ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes())
            .getResponse();
    response.setHeader("Cache-Control", "no-cache");
    response.setHeader("Connection", "keep-alive");
    
    // 异步处理
    aiChatService.streamChat(message, new StreamCallback() {
        @Override
        public void onToken(String token) {
            try {
                emitter.send(SseEmitter.event()
                    .name("token")
                    .data(token)
                    .id(UUID.randomUUID().toString()));
            } catch (IOException e) {
                emitter.completeWithError(e);
            }
        }
        
        @Override
        public void onComplete() {
            try {
                emitter.send(SseEmitter.event()
                    .name("end")
                    .data("[DONE]"));
                emitter.complete();
            } catch (IOException e) {
                emitter.completeWithError(e);
            }
        }
        
        @Override
        public void onError(Exception error) {
            emitter.completeWithError(error);
        }
    });
    
    return emitter;
}
```

### 6.2 实时语音识别
```java
@PostMapping("/asr/realtime")
@BillingMonitor(
    serviceType = ServiceType.ASR_REALTIME,
    strategy = BillingStrategy.BY_DURATION,
    streaming = true,
    streamMode = StreamBillingMode.INCREMENTAL
)
public SseEmitter realtimeASR(HttpServletRequest request) {
    SseEmitter emitter = new SseEmitter(300000L); // 5分钟超时
    
    // 创建音频流处理器
    AudioStreamProcessor processor = new AudioStreamProcessor();
    
    // 处理音频流
    CompletableFuture.runAsync(() -> {
        try (InputStream audioStream = request.getInputStream()) {
            processor.processStream(audioStream, new ASRCallback() {
                @Override
                public void onPartialResult(String text, long timestamp) {
                    try {
                        emitter.send(SseEmitter.event()
                            .name("partial")
                            .data(Map.of(
                                "text", text,
                                "timestamp", timestamp,
                                "is_final", false
                            )));
                    } catch (IOException e) {
                        emitter.completeWithError(e);
                    }
                }
                
                @Override
                public void onFinalResult(String text, long timestamp) {
                    try {
                        emitter.send(SseEmitter.event()
                            .name("final")
                            .data(Map.of(
                                "text", text,
                                "timestamp", timestamp,
                                "is_final", true
                            )));
                    } catch (IOException e) {
                        emitter.completeWithError(e);
                    }
                }
                
                @Override
                public void onStreamEnd() {
                    emitter.complete();
                }
            });
        } catch (Exception e) {
            emitter.completeWithError(e);
        }
    });
    
    return emitter;
}
```

### 6.3 流式TTS合成
```java
@PostMapping("/tts/stream")
@BillingMonitor(
    serviceType = ServiceType.TTS_STREAMING,
    strategy = BillingStrategy.BY_CHARACTERS,
    streaming = true,
    streamMode = StreamBillingMode.ON_COMPLETE
)
public SseEmitter streamTTS(@RequestParam String text,
                           @RequestParam(defaultValue = "zh") String language,
                           @RequestParam(defaultValue = "female") String voice) {
    
    SseEmitter emitter = new SseEmitter(120000L); // 2分钟超时
    
    TTSStreamRequest request = TTSStreamRequest.builder()
        .text(text)
        .language(language)
        .voice(voice)
        .format("mp3")
        .build();
    
    ttsService.synthesizeStream(request, new TTSStreamCallback() {
        private int chunkIndex = 0;
        
        @Override
        public void onAudioChunk(byte[] audioData) {
            try {
                String base64Audio = Base64.getEncoder().encodeToString(audioData);
                emitter.send(SseEmitter.event()
                    .name("audio_chunk")
                    .data(Map.of(
                        "index", chunkIndex++,
                        "audio", base64Audio,
                        "format", "mp3"
                    )));
            } catch (IOException e) {
                emitter.completeWithError(e);
            }
        }
        
        @Override
        public void onComplete(TTSResult result) {
            try {
                emitter.send(SseEmitter.event()
                    .name("synthesis_complete")
                    .data(Map.of(
                        "total_chunks", chunkIndex,
                        "duration", result.getDuration(),
                        "sample_rate", result.getSampleRate()
                    )));
                emitter.complete();
            } catch (IOException e) {
                emitter.completeWithError(e);
            }
        }
        
        @Override
        public void onError(Exception error) {
            emitter.completeWithError(error);
        }
    });
    
    return emitter;
}
```

### 6.4 前端JavaScript使用示例
```javascript
// 流式聊天
function startStreamChat(message) {
    const eventSource = new EventSource(`/ai/chat/stream?message=${encodeURIComponent(message)}`);
    
    let fullResponse = '';
    
    eventSource.addEventListener('token', function(event) {
        const token = event.data;
        fullResponse += token;
        document.getElementById('chat-content').innerHTML += token;
    });
    
    eventSource.addEventListener('end', function(event) {
        console.log('Chat stream completed:', fullResponse);
        eventSource.close();
    });
    
    eventSource.addEventListener('error', function(event) {
        console.error('Chat stream error:', event);
        eventSource.close();
    });
}

// 实时语音识别
function startRealtimeASR(audioStream) {
    const eventSource = new EventSource('/asr/realtime', {
        method: 'POST',
        body: audioStream,
        headers: {
            'Content-Type': 'audio/wav'
        }
    });
    
    eventSource.addEventListener('partial', function(event) {
        const result = JSON.parse(event.data);
        updateTranscript(result.text, false);
    });
    
    eventSource.addEventListener('final', function(event) {
        const result = JSON.parse(event.data);
        updateTranscript(result.text, true);
    });
    
    eventSource.addEventListener('error', function(event) {
        console.error('ASR stream error:', event);
        eventSource.close();
    });
}

function updateTranscript(text, isFinal) {
    const transcriptDiv = document.getElementById('transcript');
    if (isFinal) {
        transcriptDiv.innerHTML += `<span class="final">${text}</span> `;
    } else {
        // 更新临时结果
        const tempSpan = transcriptDiv.querySelector('.temp');
        if (tempSpan) {
            tempSpan.textContent = text;
        } else {
            transcriptDiv.innerHTML += `<span class="temp">${text}</span>`;
        }
    }
}
```

## 7. 总结

更新后的流式计费方案主要特点：

### 7.1 核心能力
- **透明代理**：通过BillingSseEmitter透明代理原始SseEmitter
- **内容拦截**：自动收集流式推送的所有内容
- **多种计费模式**：支持完成计费、增量计费、定期计费
- **异常处理**：完善的流异常和超时处理机制
- **监控告警**：全面的流会话监控和告警

### 7.2 技术优势
1. **低侵入性**：只需要添加注解，业务代码无感知
2. **高可靠性**：完善的异常处理和状态管理
3. **高性能**：异步处理，不影响流式响应性能
4. **易扩展**：支持自定义计费策略和处理器
5. **易监控**：集成监控指标，便于运维

### 7.3 适用场景
- AI聊天对话流式响应
- 实时语音识别
- 流式文本转语音
- 实时翻译服务
- 流式数据处理服务

这个方案能够很好地处理SSE流式响应的计费需求，同时保持了系统的高性能和可维护性。# AI服务计费需求总结与技术方案

## 1. 服务计费规则表

| 服务名称                   | 计费方式 | 计量单位 | Token换算规则                                | 备注                                                    |
| -------------------------- | -------- | -------- | -------------------------------------------- | ------------------------------------------------------- |
| 精品语音合成服务（短文本） | 按字符数 | 字符     | -                                            | 单次调用≤5个字符，计算公式：合格次数 × 文字实际长度 ÷ 5 |
| 精品语音合成服务（长文本） | 按字符数 | 字符     | -                                            | 长文本语音合成                                          |
| 教育场景文字识别服务       | 按Token  | Token    | 4个英文字符=1个Token<br>2个中文字符=1个Token | OCR文字识别                                             |
| 通用场景文字识别服务       | 按Token  | Token    | 4个英文字符=1个Token<br>2个中文字符=1个Token | 通用OCR识别                                             |
| 语音识别服务（实时）       | 按时长   | 秒       | -                                            | 实时语音转文字，按用户语音输入时长计费                  |
| 语音识别服务（文件）       | 按时长   | 秒       | -                                            | 文件语音转文字，按音频文件时长计费                      |

## 2. 计费策略说明

### 按请求计算
- **语音识别服务**：按音频时长（秒）计费
- **文字识别服务**：按识别出的文字内容Token数计费

### 按响应计算  
- **语音合成服务**：按输入文字字符数计费

### Token换算规则
```
英文：4个字符 = 1个Token
中文：2个字符 = 1个Token
```

### 折算规则
根据图7显示的折算规则：
- 折算1：3分钟音频文字对应力为30个语音识别次数（不足3分钟按3分钟计算）
- 折算2：当输入文字字符长度超过时，按实际字符长度上述折算算 
- 分段设置：
  - 分段1：3分钟音频文字对应为XX（15.5-3次）
  - 分段2：15分钟音频文字对应为XX（15.5-3次）  
  - 分段3：17分钟音频文字对应为XX（17.5-3.4倍计算）

## 3. 业务代码映射关系

| 服务名称                   | 业务代码          | 描述           |
| -------------------------- | ----------------- | -------------- |
| 精品语音合成服务（短文本） | TTS_PREMIUM_SHORT | 短文本语音合成 |
| 精品语音合成服务（长文本） | TTS_PREMIUM_LONG  | 长文本语音合成 |
| 教育场景文字识别服务       | OCR_EDUCATION     | 教育场景OCR    |
| 通用场景文字识别服务       | OCR_GENERAL       | 通用场景OCR    |
| 语音识别服务（实时）       | ASR_REALTIME      | 实时语音识别   |
| 语音识别服务（文件）       | ASR_FILE          | 文件语音识别   |

## 4. 切面监控技术方案

### 4.1 切面注解设计

```java
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface BillingMonitor {
    
    /**
     * 服务类型编码
     */
    ServiceType serviceType();
    
    /**
     * 计费策略
     */
    BillingStrategy strategy() default BillingStrategy.AUTO;
    
    /**
     * 内容参数名（用于获取计费内容）
     */
    String contentParam() default "";
    
    /**
     * 是否异步记录
     */
    boolean async() default true;
    
    /**
     * 自定义计费逻辑类
     */
    Class<? extends BillingCalculator> calculator() default DefaultBillingCalculator.class;
    
    /**
     * 是否为流式响应
     */
    boolean streaming() default false;
    
    /**
     * 流式响应计费模式
     */
    StreamBillingMode streamMode() default StreamBillingMode.ON_COMPLETE;
}

// 流式计费模式枚举
public enum StreamBillingMode {
    ON_COMPLETE,    // 流结束时计费
    INCREMENTAL,    // 增量计费（每个chunk计费）
    PERIODIC        // 定期计费
}

// 服务类型枚举
public enum ServiceType {
    TTS_PREMIUM_SHORT("精品语音合成-短文本"),
    TTS_PREMIUM_LONG("精品语音合成-长文本"),
    OCR_EDUCATION("教育场景OCR"),
    OCR_GENERAL("通用场景OCR"),
    ASR_REALTIME("实时语音识别"),
    ASR_FILE("文件语音识别");
    
    private final String description;
    ServiceType(String description) { this.description = description; }
}

// 计费策略枚举
public enum BillingStrategy {
    AUTO,           // 自动检测
    BY_CHARACTERS,  // 按字符数
    BY_TOKENS,      // 按Token数
    BY_DURATION,    // 按时长
    BY_REQUESTS     // 按请求次数
}
```

### 4.2 切面实现思路

```java
@Aspect
@Component
@Slf4j
public class BillingMonitorAspect {
    
    @Autowired
    private BillingService billingService;
    @Autowired
    private StreamBillingManager streamBillingManager;
    
    @Around("@annotation(billingMonitor)")
    public Object around(ProceedingJoinPoint pjp, BillingMonitor billingMonitor) throws Throwable {
        
        long startTime = System.currentTimeMillis();
        String userId = getCurrentUserId();
        String sessionId = generateSessionId();
        Object result = null;
        
        try {
            // 解析参数内容
            ContentInfo contentInfo = parseContent(pjp.getArgs(), billingMonitor);
            
            // 执行目标方法
            result = pjp.proceed();
            
            // 处理流式响应
            if (billingMonitor.streaming() && result instanceof SseEmitter) {
                return handleStreamingResponse((SseEmitter) result, billingMonitor, 
                                             contentInfo, userId, sessionId, startTime);
            } 
            // 处理普通响应
            else {
                handleNormalResponse(result, billingMonitor, contentInfo, 
                                   userId, sessionId, startTime);
            }
            
        } catch (Exception e) {
            recordFailedBilling(userId, sessionId, billingMonitor.serviceType(), 
                              e.getMessage(), startTime);
            throw e;
        }
        
        return result;
    }
    
    /**
     * 处理流式响应
     */
    private SseEmitter handleStreamingResponse(SseEmitter originalEmitter, 
                                             BillingMonitor monitor,
                                             ContentInfo contentInfo, 
                                             String userId, 
                                             String sessionId, 
                                             long startTime) {
        
        // 创建代理SseEmitter
        BillingSseEmitter proxyEmitter = new BillingSseEmitter(originalEmitter, 
                                                              monitor, 
                                                              contentInfo, 
                                                              userId, 
                                                              sessionId, 
                                                              startTime,
                                                              streamBillingManager);
        
        // 注册流会话
        streamBillingManager.registerSession(sessionId, monitor.serviceType(), 
                                           contentInfo, userId, startTime);
        
        return proxyEmitter;
    }
    
    /**
     * 处理普通响应
     */
    private void handleNormalResponse(Object result, BillingMonitor monitor,
                                    ContentInfo contentInfo, String userId, 
                                    String sessionId, long startTime) {
        
        // 计算计费信息
        BillingInfo billingInfo = calculateBilling(contentInfo, monitor, result);
        
        // 构建计费记录
        BillingRecord record = buildBillingRecord(userId, monitor.serviceType(), 
                                                billingInfo, startTime);
        record.setStatus(BillingStatus.SUCCESS);
        record.setSessionId(sessionId);
        
        // 记录计费日志
        if (monitor.async()) {
            billingService.recordAsync(record);
        } else {
            billingService.record(record);
        }
    }
    
    /**
     * 解析不同类型的内容参数
     */
    private ContentInfo parseContent(Object[] args, BillingMonitor monitor) {
        ContentInfo info = new ContentInfo();
        
        for (Object arg : args) {
            if (arg == null) continue;
            
            if (arg instanceof MultipartFile) {
                info = handleMultipartFile((MultipartFile) arg);
            } else if (arg instanceof File) {
                info = handleFile((File) arg);
            } else if (arg instanceof InputStream) {
                info = handleInputStream((InputStream) arg);
            } else if (arg instanceof String && isBase64(arg.toString())) {
                info = handleBase64(arg.toString());
            } else if (arg instanceof byte[]) {
                info = handleBytes((byte[]) arg);
            } else if (arg instanceof String && isUrl(arg.toString())) {
                info = handleUrl(arg.toString());
            } else if (arg instanceof String) {
                info = handleText(arg.toString());
            }
        }
        
        return info;
    }
}
```

### 4.3 流式响应计费组件

```java
/**
 * 计费代理SseEmitter
 */
public class BillingSseEmitter extends SseEmitter {
    
    private final SseEmitter originalEmitter;
    private final BillingMonitor monitor;
    private final ContentInfo contentInfo;
    private final String userId;
    private final String sessionId;
    private final long startTime;
    private final StreamBillingManager billingManager;
    
    // 流式内容累积
    private final StringBuilder streamContent = new StringBuilder();
    private final AtomicInteger chunkCount = new AtomicInteger(0);
    private final AtomicLong totalBytes = new AtomicLong(0);
    
    public BillingSseEmitter(SseEmitter originalEmitter, BillingMonitor monitor,
                           ContentInfo contentInfo, String userId, 
                           String sessionId, long startTime,
                           StreamBillingManager billingManager) {
        super(originalEmitter.getTimeout());
        this.originalEmitter = originalEmitter;
        this.monitor = monitor;
        this.contentInfo = contentInfo;
        this.userId = userId;
        this.sessionId = sessionId;
        this.startTime = startTime;
        this.billingManager = billingManager;
        
        setupCallbacks();
    }
    
    private void setupCallbacks() {
        // 设置完成回调
        originalEmitter.onCompletion(() -> {
            handleStreamComplete();
            this.complete();
        });
        
        // 设置超时回调
        originalEmitter.onTimeout(() -> {
            handleStreamTimeout();
            this.completeWithError(new TimeoutException("SSE stream timeout"));
        });
        
        // 设置错误回调
        originalEmitter.onError((ex) -> {
            handleStreamError(ex);
            this.completeWithError(ex);
        });
    }
    
    @Override
    public void send(Object object) throws IOException {
        // 拦截发送内容
        interceptSendContent(object);
        
        // 发送到原始emitter
        originalEmitter.send(object);
    }
    
    @Override
    public void send(SseEventBuilder builder) throws IOException {
        // 拦截事件内容
        interceptEventContent(builder);
        
        // 发送到原始emitter
        originalEmitter.send(builder);
    }
    
    /**
     * 拦截发送内容
     */
    private void interceptSendContent(Object content) {
        if (content != null) {
            String contentStr = content.toString();
            streamContent.append(contentStr);
            chunkCount.incrementAndGet();
            totalBytes.addAndGet(contentStr.getBytes(StandardCharsets.UTF_8).length);
            
            // 增量计费模式
            if (monitor.streamMode() == StreamBillingMode.INCREMENTAL) {
                billingManager.recordIncrement(sessionId, contentStr);
            }
        }
    }
    
    /**
     * 拦截事件内容
     */
    private void interceptEventContent(SseEventBuilder builder) {
        // 通过反射或其他方式获取事件内容
        try {
            Field dataField = builder.getClass().getDeclaredField("data");
            dataField.setAccessible(true);
            Object data = dataField.get(builder);
            if (data != null) {
                interceptSendContent(data);
            }
        } catch (Exception e) {
            log.warn("Failed to intercept SSE event content", e);
        }
    }
    
    /**
     * 处理流完成
     */
    private void handleStreamComplete() {
        StreamBillingInfo streamInfo = StreamBillingInfo.builder()
            .sessionId(sessionId)
            .totalContent(streamContent.toString())
            .chunkCount(chunkCount.get())
            .totalBytes(totalBytes.get())
            .duration(System.currentTimeMillis() - startTime)
            .build();
            
        billingManager.completeStream(sessionId, streamInfo);
    }
    
    /**
     * 处理流超时
     */
    private void handleStreamTimeout() {
        billingManager.timeoutStream(sessionId, "Stream timeout");
    }
    
    /**
     * 处理流错误
     */
    private void handleStreamError(Throwable ex) {
        billingManager.errorStream(sessionId, ex.getMessage());
    }
}

/**
 * 流式计费管理器
 */
@Component
@Slf4j
public class StreamBillingManager {
    
    @Autowired
    private BillingService billingService;
    
    // 流会话缓存
    private final Map<String, StreamSession> sessionCache = new ConcurrentHashMap<>();
    
    /**
     * 注册流会话
     */
    public void registerSession(String sessionId, ServiceType serviceType, 
                              ContentInfo contentInfo, String userId, long startTime) {
        StreamSession session = StreamSession.builder()
            .sessionId(sessionId)
            .serviceType(serviceType)
            .contentInfo(contentInfo)
            .userId(userId)
            .startTime(startTime)
            .status(StreamStatus.ACTIVE)
            .build();
            
        sessionCache.put(sessionId, session);
        log.info("Registered stream session: {}", sessionId);
    }
    
    /**
     * 记录增量计费
     */
    public void recordIncrement(String sessionId, String content) {
        StreamSession session = sessionCache.get(sessionId);
        if (session != null) {
            session.appendContent(content);
            
            // 可以在这里实现实时计费逻辑
            BillingInfo incrementBilling = calculateIncrementBilling(content, session);
            session.addIncrementBilling(incrementBilling);
        }
    }
    
    /**
     * 完成流计费
     */
    public void completeStream(String sessionId, StreamBillingInfo streamInfo) {
        StreamSession session = sessionCache.get(sessionId);
        if (session != null) {
            session.setStatus(StreamStatus.COMPLETED);
            
            // 计算最终计费
            BillingInfo finalBilling = calculateFinalBilling(session, streamInfo);
            
            // 记录计费日志
            BillingRecord record = BillingRecord.builder()
                .userId(session.getUserId())
                .sessionId(sessionId)
                .serviceType(session.getServiceType())
                .billingInfo(finalBilling)
                .streamInfo(streamInfo)
                .startTime(session.getStartTime())
                .endTime(System.currentTimeMillis())
                .status(BillingStatus.SUCCESS)
                .build();
                
            billingService.recordAsync(record);
            
            // 清理会话
            sessionCache.remove(sessionId);
            
            log.info("Stream billing completed for session: {}", sessionId);
        }
    }
    
    /**
     * 流超时处理
     */
    public void timeoutStream(String sessionId, String reason) {
        StreamSession session = sessionCache.get(sessionId);
        if (session != null) {
            session.setStatus(StreamStatus.TIMEOUT);
            recordFailedStream(session, reason);
            sessionCache.remove(sessionId);
        }
    }
    
    /**
     * 流错误处理
     */
    public void errorStream(String sessionId, String errorMsg) {
        StreamSession session = sessionCache.get(sessionId);
        if (session != null) {
            session.setStatus(StreamStatus.ERROR);
            recordFailedStream(session, errorMsg);
            sessionCache.remove(sessionId);
        }
    }
    
    /**
     * 计算最终计费
     */
    private BillingInfo calculateFinalBilling(StreamSession session, StreamBillingInfo streamInfo) {
        String totalContent = streamInfo.getTotalContent();
        
        return BillingInfo.builder()
            .characterCount(totalContent.length())
            .tokenCount(TokenUtil.calculateTokens(totalContent))
            .chunkCount(streamInfo.getChunkCount())
            .totalBytes(streamInfo.getTotalBytes())
            .duration(streamInfo.getDuration())
            .build();
    }
    
    /**
     * 记录失败的流
     */
    private void recordFailedStream(StreamSession session, String reason) {
        BillingRecord record = BillingRecord.builder()
            .userId(session.getUserId())
            .sessionId(session.getSessionId())
            .serviceType(session.getServiceType())
            .startTime(session.getStartTime())
            .endTime(System.currentTimeMillis())
            .status(BillingStatus.FAILED)
            .errorMsg(reason)
            .build();
            
        billingService.recordAsync(record);
    }
}

/**
 * 流会话信息
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StreamSession {
    private String sessionId;
    private ServiceType serviceType;
    private ContentInfo contentInfo;
    private String userId;
    private long startTime;
    private StreamStatus status;
    private StringBuilder totalContent = new StringBuilder();
    private List<BillingInfo> incrementBillings = new ArrayList<>();
    
    public void appendContent(String content) {
        this.totalContent.append(content);
    }
    
    public void addIncrementBilling(BillingInfo billing) {
        this.incrementBillings.add(billing);
    }
}

/**
 * 流计费信息
 */
@Data
@Builder
public class StreamBillingInfo {
    private String sessionId;
    private String totalContent;
    private int chunkCount;
    private long totalBytes;
    private long duration;
}

/**
 * 流状态枚举
 */
public enum StreamStatus {
    ACTIVE,     // 活跃
    COMPLETED,  // 完成
    TIMEOUT,    // 超时
    ERROR       // 错误
}

```java
@Component
public class ContentProcessorFactory {
    
    public ContentProcessor getProcessor(Object content) {
        if (content instanceof MultipartFile) {
            return new MultipartFileProcessor();
        } else if (content instanceof File) {
            return new FileProcessor();
        } else if (content instanceof InputStream) {
            return new InputStreamProcessor();
        } else if (content instanceof String && isBase64(content.toString())) {
            return new Base64Processor();
        } else if (content instanceof byte[]) {
            return new BytesProcessor();
        } else if (content instanceof String && isUrl(content.toString())) {
            return new UrlProcessor();
        } else if (content instanceof String) {
            return new TextProcessor();
        }
        return new DefaultProcessor();
    }
}

// 处理器接口
public interface ContentProcessor {
    ContentInfo process(Object content, ServiceType serviceType);
}

// MultipartFile处理器示例
@Component
public class MultipartFileProcessor implements ContentProcessor {
    
    @Override
    public ContentInfo process(Object content, ServiceType serviceType) {
        MultipartFile file = (MultipartFile) content;
        ContentInfo info = new ContentInfo();
        
        try {
            if (isAudioService(serviceType)) {
                // 音频文件：获取时长
                info.setDuration(getAudioDuration(file.getInputStream()));
                info.setFileSize(file.getSize());
            } else if (isImageService(serviceType)) {
                // 图片文件：获取尺寸和大小
                info.setFileSize(file.getSize());
                info.setImageDimensions(getImageDimensions(file.getInputStream()));
            } else if (isTextService(serviceType)) {
                // 文本文件：获取字符数
                String text = new String(file.getBytes(), StandardCharsets.UTF_8);
                info.setCharacterCount(text.length());
                info.setTokenCount(calculateTokens(text));
            }
        } catch (IOException e) {
            log.error("处理MultipartFile失败", e);
        }
        
        return info;
    }
}
```

### 4.4 不同内容类型处理策略

```java
@RestController
public class AIServiceController {
    
    @PostMapping("/tts/premium/short")
    @BillingMonitor(
        serviceType = ServiceType.TTS_PREMIUM_SHORT,
        strategy = BillingStrategy.BY_CHARACTERS,
        contentParam = "text"
    )
    public ResponseEntity<?> textToSpeechShort(@RequestParam String text) {
        // 业务逻辑
        return ResponseEntity.ok(ttsService.synthesize(text));
    }
    
    @PostMapping("/ocr/education")
    @BillingMonitor(
        serviceType = ServiceType.OCR_EDUCATION,
        strategy = BillingStrategy.BY_TOKENS
    )
    public ResponseEntity<?> ocrEducation(@RequestParam MultipartFile image) {
        // 业务逻辑
        return ResponseEntity.ok(ocrService.recognize(image));
    }
    
    @PostMapping("/asr/file")
    @BillingMonitor(
        serviceType = ServiceType.ASR_FILE,
        strategy = BillingStrategy.BY_DURATION
    )
    public ResponseEntity<?> speechToTextFile(@RequestParam MultipartFile audioFile) {
        // 业务逻辑
        return ResponseEntity.ok(asrService.recognize(audioFile));
    }
}
```

### 4.5 使用示例

```java
@RestController
public class AIServiceController {
    
    @PostMapping("/tts/premium/short")
    @BillingMonitor(
        serviceType = ServiceType.TTS_PREMIUM_SHORT,
        strategy = BillingStrategy.BY_CHARACTERS,
        contentParam = "text"
    )
    public ResponseEntity<?> textToSpeechShort(@RequestParam String text) {
        // 业务逻辑
        return ResponseEntity.ok(ttsService.synthesize(text));
    }
    
    @PostMapping("/ocr/education")
    @BillingMonitor(
        serviceType = ServiceType.OCR_EDUCATION,
        strategy = BillingStrategy.BY_TOKENS
    )
    public ResponseEntity<?> ocrEducation(@RequestParam MultipartFile image) {
        // 业务逻辑
        return ResponseEntity.ok(ocrService.recognize(image));
    }
    
    @PostMapping("/asr/file")
    @BillingMonitor(
        serviceType = ServiceType.ASR_FILE,
        strategy = BillingStrategy.BY_DURATION
    )
    public ResponseEntity<?> speechToTextFile(@RequestParam MultipartFile audioFile) {
        // 业务逻辑
        return ResponseEntity.ok(asrService.recognize(audioFile));
    }
    
    // 流式响应示例
    @GetMapping("/chat/stream")
    @BillingMonitor(
        serviceType = ServiceType.CHAT_COMPLETION,
        strategy = BillingStrategy.BY_TOKENS,
        streaming = true,
        streamMode = StreamBillingMode.ON_COMPLETE,
        contentParam = "prompt"
    )
    public SseEmitter chatStream(@RequestParam String prompt) {
        SseEmitter emitter = new SseEmitter(30000L);
        
        // 异步处理流式响应
        CompletableFuture.runAsync(() -> {
            try {
                // 模拟流式响应
                String[] responses = {"Hello", " there", "! How", " can I", " help you", " today?"};
                for (String response : responses) {
                    Thread.sleep(500);
                    emitter.send(SseEmitter.event()
                        .name("message")
                        .data(response));
                }
                emitter.complete();
            } catch (Exception e) {
                emitter.completeWithError(e);
            }
        });
        
        return emitter;
    }
    
    // 实时语音识别流
    @PostMapping("/asr/stream")
    @BillingMonitor(
        serviceType = ServiceType.ASR_REALTIME,
        strategy = BillingStrategy.BY_DURATION,
        streaming = true,
        streamMode = StreamBillingMode.INCREMENTAL
    )
    public SseEmitter realTimeASR(@RequestParam MultipartFile audioStream) {
        return asrService.recognizeStream(audioStream);
    }
    
    // 流式TTS
    @PostMapping("/tts/stream")
    @BillingMonitor(
        serviceType = ServiceType.TTS_STREAMING,
        strategy = BillingStrategy.BY_CHARACTERS,
        streaming = true,
        streamMode = StreamBillingMode.ON_COMPLETE,
        contentParam = "text"
    )
    public SseEmitter textToSpeechStream(@RequestParam String text) {
        return ttsService.synthesizeStream(text);
    }
}

```java
@Configuration
@EnableAsync
public class BillingConfig {
    
    @Bean
    @ConditionalOnMissingBean
    public BillingCalculator defaultBillingCalculator() {
        return new DefaultBillingCalculator();
    }
    
    @Bean
    public TaskExecutor billingTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("billing-");
        executor.initialize();
        return executor;
    }
}
```

## 5. 技术要点总结

### 切面设计原则
1. **灵活性**：支持多种内容类型和计费策略
2. **可扩展性**：通过策略模式支持自定义计费逻辑
3. **性能考虑**：异步记录日志，避免影响业务性能
4. **容错性**：业务异常不影响计费记录，计费异常不影响业务

### 关键技术点
1. **内容识别**：自动识别MultipartFile、File、InputStream、Base64、Bytes、URL等类型
2. **策略模式**：不同服务类型采用不同的计费策略
3. **异步处理**：计费日志异步入库，提升响应速度
4. **参数解析**：通过反射和注解获取方法参数进行内容分析
5. **异常处理**：完善的异常处理机制，确保系统稳定性