spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: **********************************************************************************************************************
    username: root
    password: 7890@uiop
    druid:
      initial-size: 5
      max-active: 20
      min-idle: 5
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      validation-query: SELECT 1 FROM DUAL
      test-on-borrow: false
      test-on-return: false
      test-while-idle: true
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      filters: config,stat
      remove-abandoned: true
      remove-abandoned-timeout: 180
      web-stat-filter:
        enabled: true
        url-pattern: /api/*
        exclusions:
        session-stat-enable: true
        session-stat-max-count: 1000
        profile-enable: true
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: true
        login-username: admin
        login-password: admin
        allow:
        deny:
  data:
    redis:
      host: **************
      password: 7890@uiop
      port: 6379
      database: 1


upload:
  storage: local
  fileServerPath: http://**************/files
  fileGroup: /group1
  localServerPath: /home/<USER>
minio:
  url: http://localhost:9000
  show-url: http://127.0.0.1/
  access-key: yErXmgTrK8Z2y0rM
  secret-key: zVYofJKNu3nzU2Dpm9xXeoLHlcHstavH
  bucket-name: llm-public-file