#server:
#  servlet:
#    context-path: /agent-mgmt
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: ********************************************************************************************************************
    username: root
    password: FY@tetris.2017!
    druid:
      initial-size: 5
      max-active: 20
      min-idle: 5
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      validation-query: SELECT 1 FROM DUAL
      test-on-borrow: false
      test-on-return: false
      test-while-idle: true
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      filters: config,stat
      remove-abandoned: true
      remove-abandoned-timeout: 180
      web-stat-filter:
        enabled: true
        url-pattern: /api/*
        exclusions:
        session-stat-enable: true
        session-stat-max-count: 1000
        profile-enable: true
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: true
        login-username: admin
        login-password: admin
        allow:
        deny:
  data:
    redis:
      host: ************
      password:
      port: 6379
      database: 11


upload:
  storage: minio
  fileServerPath: https://llm.cnsaas.com/files
  fileGroup: /group1
  localServerPath: /home/<USER>
minio:
  url: http://************:9000
  show-url: https://llm.cnsaas.com/file/
  #  access-key: dZK0msQU4sFS33OCwtea
  #  secret-key: stzx818OOIh0W1kqPewBa1OV6ubLDSsDMer6KsI1
  #  bucket-name: llm-public-file
  access-key: 5fBkvRyCOucLidMg
  secret-key: 35WRY30roTAcNZXsgYVDJdESGYQP41Tc
  bucket-name: llm-public-file-test