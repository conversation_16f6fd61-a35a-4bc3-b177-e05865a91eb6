server:
  port: 10086

spring:
  application:
    name: agentManagement

springdoc:
  api-docs:
    enabled: true # 开启OpenApi接口
    path: /v3/api-docs  # 自定义路径，默认为 "/v3/api-docs"
  swagger-ui:
    enabled: true # 开启swagger界面，依赖OpenApi，需要OpenApi同时开启
    path: /swagger-ui/index.html # 自定义路径，默认为"/swagger-ui/index.html"

fytec:
  token:
    secret-key: fytec@ai
  ai_dept: ee94372a924443a6bce8dfaa7b7f9bfd

logging:
  level:
    ROOT: INFO
    com.fytec: DEBUG

sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: fytec
  token-prefix: Bearer
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 86400
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: 7200
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: true
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: uuid
  # 是否输出操作日志
  is-log: true
  is-read-cookie: false
  auto-renew: true

function:
  call:
    workflow: /api/workflow/function/call
    plugin: /api/plugin/function/call
