<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fytec.mapper.llm.AiModelMapper">
    <select id="queryAiModelPaging" resultType="com.fytec.dto.llm.AiModelListDTO">
        select id, name, size, code, enable
        from ai_model
        <where>
            <if test="param.keyword != null and param.keyword != ''">
                and name like concat('%', #{param.keyword}, '%')
            </if>
        </where>
    </select>
</mapper>