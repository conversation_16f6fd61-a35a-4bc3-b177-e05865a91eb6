<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fytec.mapper.system.SysMenuMapper">
    <select id="selectListOrderBySeq" resultType="com.fytec.dto.system.SysMenuTreeDTO">
        select id        as id,
               parent_id as parentid,
               name      as menuname,
               type      as menutype,
               icon      as menuicon,
               component as menucomponent,
               href      as menuhref,
               seq       as menusort
        from t_sys_menu
        where status != 'D'
          and is_visible = 'Y'
        order by seq
    </select>
</mapper>