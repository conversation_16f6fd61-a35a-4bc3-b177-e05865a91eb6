<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fytec.mapper.system.SysRoleMapper">
    <select id="searchRole" resultType="com.fytec.dto.system.SysRoleListDTO">
        select r.id as id,
        r.role as role,
        r.role_name as roleName,
        r.description as roleDesc,
        r.is_sys as isSys,
        r.create_by as createBy,
        r.create_time as createTime,
        (select name from t_sys_user where id = r.create_by) as createByName
        from t_sys_role r
        <where>
            <if test="param.isSys != null and param.isSys != ''">
                and r.is_sys = #{param.isSys}
            </if>
        </where>
    </select>
</mapper>