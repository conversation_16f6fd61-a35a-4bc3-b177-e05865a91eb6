<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fytec.mapper.system.SysUserMapper">
    <select id="checkDuplicateUser" resultType="java.lang.Long">
        select count(*)
        from t_sys_user
        where (login_name = #{nameOrTel} or tel = #{nameOrTel})
        and status = 'A'
    </select>
    <select id="queryUser" resultType="com.fytec.dto.system.SysUserListDTO">
        select user.id as id, user.login_name as loginName, user.tel as tel,
        user.status as status, user.create_by as createBy,
        user.create_time as createTime, user.is_sys as isSys,
        (select `name` from t_sys_user WHERE id = user.create_by) AS createByName,
        (select group_concat(role) from t_sys_role role
        inner join t_sys_user_role ur on ur.role_id = role.id
        where ur.user_id = user.id
        order by role_name) AS roleDesc
        FROM t_sys_user user
        WHERE user.STATUS != 'D'
        <if test="param.loginName != null and param.loginName != ''">
            and user.login_name like #{param.loginname}
        </if>
        <if test="param.name != null and param.name != ''">
            and (user.name like concat('%', #{param.name},'%') or user.name like concat('%', #{param.name},'%'))
        </if>
        <if test="param.status != null and param.status != ''">
            and user.status = #{param.status}
        </if>
    </select>
</mapper>