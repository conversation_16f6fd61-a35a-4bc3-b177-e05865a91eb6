spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: ***************************************************************************************************************************************************
    username: root
    password: 7890@uiop
    druid:
      initial-size: 5
      max-active: 20
      min-idle: 5
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      validation-query: SELECT 1 FROM DUAL
      test-on-borrow: false
      test-on-return: false
      test-while-idle: true
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      filters: config,stat
      remove-abandoned: true
      remove-abandoned-timeout: 180
      web-stat-filter:
        enabled: true
        url-pattern: /api/*
        exclusions:
        session-stat-enable: true
        session-stat-max-count: 1000
        profile-enable: true
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: true
        login-username: admin
        login-password: admin
        allow:
        deny:
  data:
    redis:
      host: **************
      password: 7890@uiop
      port: 6379
      database: 1

upload:
  storage: minio
  fileServerPath: http://127.0.0.1/files
  fileGroup: /group1
  localServerPath: /Users/<USER>/files
minio:
  url: http://**************:9000
  show-url: http://**************:9000/
  access-key: vAxrwsgUoZhAWYtm
  secret-key: U8k0Nr681DIvx8AFy77CFc5B6Sd09I7a
  bucket-name: llm-public-file