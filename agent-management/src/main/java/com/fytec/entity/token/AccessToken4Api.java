package com.fytec.entity.token;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "api认证token")
@Data
@TableName("t_access_token_api")
public class AccessToken4Api {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("name")
    private String name;

    @TableField("access_token")
    private String accessToken;

    @TableField("expire_time")
    private LocalDateTime expireTime;

    @TableField("permissions")
    private String permissions;
}
