package com.fytec.entity.token;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fytec.entity.BaseEntity;
import lombok.Data;

import java.time.LocalDateTime;


@Data
@TableName("t_oauth_client_details")
public class OauthClientDetail extends BaseEntity {
    @TableId(value = "client_id")
    private String clientId;

    @TableField("client_secret")
    private String clientSecret;

    @TableField("client_name")
    private String name;

    @TableField("scope")
    private String scope;

    @TableField("authorized_grant_types")
    private String grantTypes;

    @TableField("allow_redirect_uri")
    private String allowRedirectUri;

    @TableField("status")
    private String status;

    @TableField("host")
    private String host;


}
