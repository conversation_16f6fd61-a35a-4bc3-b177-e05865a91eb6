package com.fytec.entity.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


@Data
@TableName("t_sys_crypto_data")
public class SysCryptoData {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String clientId;

    private String keyName;

    private String encryptContent;
}
