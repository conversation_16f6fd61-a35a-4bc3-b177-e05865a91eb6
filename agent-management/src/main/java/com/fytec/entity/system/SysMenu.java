package com.fytec.entity.system;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fytec.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@TableName("t_sys_menu")
public class SysMenu extends BaseEntity {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("parent_id")
    private Long parentId;

    @TableField("name")
    private String menuName;

    @TableField("type")
    private String menuType;

    @TableField("icon")
    private String menuIcon;

    @TableField("component")
    private String menuComponent;

    @TableField("href")
    private String menuHref;

    @TableField("permission")
    private String menuPermission;

    @TableField("seq")
    private Integer menuSort;

    @TableField("is_visible")
    private String isVisible;

    @TableField("status")
    private String status;

    @TableField("is_operation")
    private String isOperation;
}
