package com.fytec.entity.llm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("ai_model_usage")
public class AiModelUsageToken {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String clientId;

    private String modelCode;

    private String status;

    private String reason;

    private Long promptTokens;

    private Long completionTokens;

    private Long totalTokens;

    private LocalDateTime createTime;
}
