package com.fytec.service.token;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fytec.dto.system.SysCryptoDTO;
import com.fytec.dto.token.AccessToken4ApiDTO;
import com.fytec.dto.token.OauthClientDetailCreateDTO;
import com.fytec.dto.token.OauthClientDetailDTO;
import com.fytec.dto.token.OauthClientDetailUpdateDTO;
import com.fytec.entity.token.AccessToken4Api;
import com.fytec.entity.token.OauthClientDetail;
import com.fytec.mapper.token.AccessTokenMapper;
import com.fytec.mapper.token.OauthClientDetailMapper;
import com.fytec.properties.FytecTokenProperties;
import com.fytec.service.system.SysCryptoService;
import com.fytec.util.HmacUtils;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.List;

@Service
@RequiredArgsConstructor
public class TokenService {
    private final AccessTokenMapper accessTokenMapper;
    private final OauthClientDetailMapper oauthClientDetailMapper;
    private final FytecTokenProperties fytecTokenProperties;
    private final SysCryptoService sysCryptoService;


    @SneakyThrows
    public String generateApiAccessToken(AccessToken4ApiDTO accessToken4ApiDTO) {
        SecureRandom secureRandom = new SecureRandom();
        byte[] tokenBytes = new byte[32];
        secureRandom.nextBytes(tokenBytes);
        String message = Base64.getUrlEncoder().withoutPadding().encodeToString(tokenBytes);
        String token = HmacUtils.generateHmacToken(fytecTokenProperties.getSecretKey(), message);
        AccessToken4Api accessToken4Api = new AccessToken4Api();
        accessToken4Api.setName(accessToken4ApiDTO.getName());
        LocalDate expireDate = LocalDate.parse(accessToken4ApiDTO.getExpireDate());
        LocalDateTime expireTime = expireDate.atTime(23, 59, 59);
        accessToken4Api.setAccessToken(token);
        accessToken4Api.setExpireTime(expireTime);
        accessToken4Api.setPermissions(accessToken4ApiDTO.getPermissions());
        accessTokenMapper.insert(accessToken4Api);
        return token;
    }

    public boolean verifyApiAccessToken(String accessToken) {
        AccessToken4Api accessToken4Api = accessTokenMapper.selectOne(new LambdaQueryWrapper<AccessToken4Api>()
                .eq(AccessToken4Api::getAccessToken, accessToken)
        );
        LocalDateTime now = LocalDateTime.now();
        return !now.isAfter(accessToken4Api.getExpireTime());
    }

    public void createOauthClient(OauthClientDetailCreateDTO oauthClientDetailCreateDTO) {
        OauthClientDetail oauthClientDetail = new OauthClientDetail();
        BeanUtil.copyProperties(oauthClientDetailCreateDTO, oauthClientDetail);
        oauthClientDetail.setClientId(IdUtil.simpleUUID());
        oauthClientDetail.setClientSecret(HmacUtils.generateHmacToken(fytecTokenProperties.getSecretKey(), oauthClientDetail.getClientId()));
        oauthClientDetail.setStatus("A");
        oauthClientDetailMapper.insert(oauthClientDetail);

        sysCryptoService.addSecretKey(oauthClientDetail.getClientId());

        SysCryptoDTO sysCryptoDTO = new SysCryptoDTO();
        sysCryptoDTO.setClientId(oauthClientDetail.getClientId());
        sysCryptoService.copyEncryptContent(sysCryptoDTO);
    }

    public void editOauthClient(OauthClientDetailUpdateDTO oauthClientDetailUpdateDTO) {
        OauthClientDetail oauthClientDetail = oauthClientDetailMapper.selectById(oauthClientDetailUpdateDTO.getClientId());
        if (oauthClientDetail == null) {
            throw new ValidateException("客户端不存在");
        }
        BeanUtil.copyProperties(oauthClientDetailUpdateDTO, oauthClientDetail, "clientId", "clientSecret");
        oauthClientDetailMapper.updateById(oauthClientDetail);
    }

    public void disableOauthClient(String clientId) {
        OauthClientDetail oauthClientDetail = oauthClientDetailMapper.selectById(clientId);
        oauthClientDetail.setStatus("D");
        oauthClientDetailMapper.updateById(oauthClientDetail);
    }

    public void enableOauthClient(String clientId) {
        OauthClientDetail oauthClientDetail = oauthClientDetailMapper.selectById(clientId);
        oauthClientDetail.setStatus("A");
        oauthClientDetailMapper.updateById(oauthClientDetail);
    }

    public OauthClientDetailDTO getOauthClientDetail(String clientId) {
        OauthClientDetailDTO oauthClientDetailDTO = new OauthClientDetailDTO();
        OauthClientDetail oauthClientDetail = oauthClientDetailMapper.selectById(clientId);
        BeanUtil.copyProperties(oauthClientDetail, oauthClientDetailDTO);
        return oauthClientDetailDTO;
    }

    public List<OauthClientDetailDTO> queryOauthClientDetail(String clientName) {
        LambdaQueryWrapper<OauthClientDetail> queryWrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isNotBlank(clientName)) {
            queryWrapper.like(OauthClientDetail::getName, clientName);
        }

        List<OauthClientDetailDTO> list = oauthClientDetailMapper.selectList(queryWrapper)
                .stream().map(oauthClientDetail -> {
                    OauthClientDetailDTO oauthClientDetailDTO = new OauthClientDetailDTO();
                    BeanUtil.copyProperties(oauthClientDetail, oauthClientDetailDTO);
                    return oauthClientDetailDTO;
                }).toList();
        return list;
    }
}
