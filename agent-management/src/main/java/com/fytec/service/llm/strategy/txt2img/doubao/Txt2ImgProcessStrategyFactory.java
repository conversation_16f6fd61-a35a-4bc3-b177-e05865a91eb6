package com.fytec.service.llm.strategy.txt2img.doubao;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class Txt2ImgProcessStrategyFactory {

    private static final Map<String, Txt2ImgProcessStrategy> STRATEGY_CONCURRENT_HASH_MAP = new ConcurrentHashMap<>();

    /**
     * 获取策略的方法
     *
     * @param key 策略类型
     */
    public static Txt2ImgProcessStrategy getStrategy(String key) {
        return STRATEGY_CONCURRENT_HASH_MAP.get(key);
    }

    /**
     * 在Bean属性初始化后执行该方法
     * 注册策略
     *
     * @param key                  策略类型
     * @param txt2ImgProcessStrategy 策略处理接口
     */
    public static void register(String key, Txt2ImgProcessStrategy txt2ImgProcessStrategy) {
        STRATEGY_CONCURRENT_HASH_MAP.put(key, txt2ImgProcessStrategy);
    }
}
