package com.fytec.service.llm.strategy;

import cn.dev33.satoken.SaManager;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fytec.dto.llm.ChatToolDTO;
import com.fytec.entity.token.OauthClientDetail;
import com.fytec.mapper.token.OauthClientDetailMapper;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.util.*;

@Slf4j
@Component
public class FunctionCallService {
    protected final WebClient webClient = WebClient.create();
    @Resource
    protected FunctionCallProperties functionCallProperties;

    @Resource
    protected OauthClientDetailMapper oauthClientDetailMapper;

    protected String callChartTool(String toolName, Map<String, Object> params, List<ChatToolDTO> tools) {
        ChatToolDTO chatToolDTO = tools.stream().filter(item -> item.getChatTool().getFunction().getName().equals(toolName))
                .findFirst().orElse(null);
        if (chatToolDTO == null) {
            return "";
        }
        if (chatToolDTO.getType() == null) {
            return "";
        }

        return switch (chatToolDTO.getType()) {
            case "workflow" -> callWorkflowTool(chatToolDTO, params);
            case "plugin" -> callPluginTool(chatToolDTO, params);
            default -> "";
        };
    }

    private String callWorkflowTool(ChatToolDTO chatToolDTO, Map<String, Object> arguments) {
        Map<String, Object> params = new HashMap<>();

        params.put("resourceId", chatToolDTO.getResourceId());//工作流资源ID
        params.put("id", chatToolDTO.getId()); //发布工作流ID
        params.put("params", arguments);//工作流参数
        OauthClientDetail client = oauthClientDetailMapper.selectOne(new LambdaQueryWrapper<OauthClientDetail>()
                .eq(OauthClientDetail::getClientId, chatToolDTO.getClientId())
                .last("limit 1")
        );
        if (client == null
                || StringUtils.isBlank(client.getHost())
                || StringUtils.isBlank(client.getClientSecret())) {
            log.error("Workflow客户端配置异常clientId:{}", chatToolDTO.getClientId());
            return "";
        }

        Mono<String> mono = webClient.post()
                .uri(client.getHost() + functionCallProperties.getWorkflow())
                .contentType(MediaType.APPLICATION_JSON)
                .headers(httpHeaders -> {
                    httpHeaders.set("sign", getClientSign(params, client.getClientSecret()));
                })
                .body(BodyInserters.fromValue(params))
                .retrieve()
                .bodyToMono(String.class);
        String result = mono.block();
        log.info("执行工作流任务结果:{}", result);
        return result;
    }


    private String callPluginTool(ChatToolDTO chatToolDTO, Map<String, Object> arguments) {
        Map<String, Object> params = new HashMap<>();
        params.put("id", chatToolDTO.getId()); //发布插件api id
        params.put("parameters", JSON.toJSONString(arguments));//插件api参数
        OauthClientDetail client = oauthClientDetailMapper.selectOne(new LambdaQueryWrapper<OauthClientDetail>()
                .eq(OauthClientDetail::getClientId, chatToolDTO.getClientId())
                .last("limit 1")
        );
        if (client == null
                || StringUtils.isBlank(client.getHost())
                || StringUtils.isBlank(client.getClientSecret())) {
            log.error("Plugin客户端配置异常clientId:{}", chatToolDTO.getClientId());
            return "";
        }

        log.info("Plugin客户端请求参数:{}", params);
        Mono<String> mono = webClient.post()
                .uri(client.getHost() + functionCallProperties.getPlugin())
                .contentType(MediaType.APPLICATION_JSON)
                .headers(httpHeaders -> {
                    httpHeaders.set("sign", getClientSign(params, client.getClientSecret()));
                })
                .body(BodyInserters.fromValue(params))
                .retrieve()
                .bodyToMono(String.class);
        String result = mono.block();
        log.info("执行插件api任务结果:{}", result);
        return result;
    }

    private String getClientSign(Map<String, Object> paramsMap, String secretKey) {
        if (paramsMap.containsKey("sign")) {
            paramsMap = new TreeMap<>(paramsMap);
            paramsMap.remove("sign");
        }

        String paramsStr = SaManager.getSaSignTemplate().joinParamsDictSort(paramsMap);
        String fullStr = paramsStr + "&key=" + secretKey;
        return SaManager.getSaSignTemplate().abstractStr(fullStr);
    }


    @SneakyThrows
    public String imageUrlToBase64(String imageUrl) {
        try {
            URI uri = new URI(imageUrl);
            URL url = uri.toURL();
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(5000);
            conn.connect();

            String contentType = conn.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                return null;
            }
            // 提取后缀（如 png, jpeg, webp）
            String suffix = contentType.substring("image/".length());

            // 读取图片字节
            InputStream in = conn.getInputStream();
            byte[] bytes = in.readAllBytes();
            in.close();

            // 转 base64
            String base64 = Base64.getEncoder().encodeToString(bytes);

            // 拼接成 data URI
            return "data:image/" + suffix + ";base64," + base64;
        } catch (Exception e) {
            return null;
        }
    }
}
