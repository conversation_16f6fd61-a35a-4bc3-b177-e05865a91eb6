package com.fytec.service.llm.strategy.chat;


import com.fytec.dto.llm.ModelProcessDTO;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.Map;

/**
 * 任务分配用户策略处理器
 */
public interface ModelProcessStrategy {

    /**
     * 策略注册方法
     *
     * @param key 策略类型
     */
    default void afterPropertiesSet(String key) {
        ModelProcessStrategyFactory.register(key, this);
    }

    /**
     * 抽象方法 处理表达式
     */
    void handleStream(ModelProcessDTO processDTO, SseEmitter sseEmitter);

    String handle(ModelProcessDTO processDTO);
}
