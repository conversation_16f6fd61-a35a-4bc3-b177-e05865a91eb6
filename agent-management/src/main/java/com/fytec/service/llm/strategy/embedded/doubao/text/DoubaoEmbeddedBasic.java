package com.fytec.service.llm.strategy.embedded.doubao.text;

import com.fytec.dto.llm.EmbeddedProcessDTO;
import com.fytec.service.system.SysCryptoService;
import com.volcengine.ark.runtime.model.embeddings.Embedding;
import com.volcengine.ark.runtime.model.embeddings.EmbeddingRequest;
import com.volcengine.ark.runtime.model.embeddings.EmbeddingResult;
import com.volcengine.ark.runtime.service.ArkService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


@Slf4j
@Component
public class DoubaoEmbeddedBasic {
    @Resource
    private SysCryptoService cryptoService;


    public List<Embedding> embeddedBasic(String apiKey, EmbeddedProcessDTO processDTO) {
        String decryptApiKey = cryptoService.decryptContent(processDTO.getOauthClientId(), apiKey);
        ArkService service = ArkService.builder().apiKey(decryptApiKey).build();
        //processDTO.getInputs();数量不能超过256，否则需要循环调用

        if (processDTO.getInputs().size() > 64) {
            List<EmbeddingResult> resList = new ArrayList<>();
            List<Embedding> embeddings = new ArrayList<>();
            for (int i = 0; i < processDTO.getInputs().size(); i += 64) {
                int end = Math.min(i + 64, processDTO.getInputs().size());
                log.info("embedding start, start: {}, end: {}", i, end);
                List<String> subList = processDTO.getInputs().subList(i, end);
                EmbeddingRequest embeddingRequest = EmbeddingRequest.builder()
                        .model(processDTO.getModelId())
                        .input(subList)
                        .build();
                EmbeddingResult res = service.createEmbeddings(embeddingRequest);
                if (res == null) {
                    log.error("embedding failed");
                    continue;
                }
                resList.add(res);
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }

            }
            for (EmbeddingResult embeddingResult : resList) {
                embeddings.addAll(embeddingResult.getData());
            }
            service.shutdownExecutor();
            return embeddings;
        }else{
            EmbeddingRequest embeddingRequest = EmbeddingRequest.builder()
                    .model(processDTO.getModelId())
                    .input(processDTO.getInputs())
                    .build();

            EmbeddingResult res = service.createEmbeddings(embeddingRequest);
            if (res == null) {
                log.error("embedding failed");
                return null;
            }
            // shutdown service
            service.shutdownExecutor();
            return res.getData();
        }
    }
}
