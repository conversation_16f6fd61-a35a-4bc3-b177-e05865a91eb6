package com.fytec.service.llm.strategy.chat.doubao.vision;

import com.fytec.contant.ModelApiKey;
import com.fytec.contant.enums.ModelTypeEnum;
import com.fytec.dto.llm.ModelProcessDTO;
import com.fytec.service.llm.strategy.chat.DoubaoChatBasic;
import com.fytec.service.llm.strategy.chat.ModelProcessStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;


@Slf4j
@Component
public class DoubaoThinkingVisionPro extends DoubaoChatBasic implements InitializingBean, ModelProcessStrategy {
    private final ModelTypeEnum modelType = ModelTypeEnum.doubao_1_5_thinking_vision_pro_250428;

    @Override
    public void handleStream(ModelProcessDTO processDTO, SseEmitter sseEmitter) {
        log.info("\n----- start vision request {} stream -----", modelType.name());
        processDTO.setModelId(modelType.getModelId());
        processDTO.setEnableFunctionCall(modelType.isEnableFunctionCall());
        processDTO.setMultimodal(true);
        processDTO.setUrlSupport(true);
        processStream(ModelApiKey.DOUBAO_API_KEY, processDTO, sseEmitter);
    }

    @Override
    public String handle(ModelProcessDTO processDTO) {
        log.info("\n----- start vision request {}  -----", modelType.name());
        processDTO.setModelId(modelType.getModelId());
        processDTO.setEnableFunctionCall(modelType.isEnableFunctionCall());
        processDTO.setMultimodal(true);
        processDTO.setUrlSupport(true);
        return processNonStream(ModelApiKey.DOUBAO_API_KEY, processDTO);
    }


    @Override
    public void afterPropertiesSet() {
        afterPropertiesSet(modelType.name());
    }
}
