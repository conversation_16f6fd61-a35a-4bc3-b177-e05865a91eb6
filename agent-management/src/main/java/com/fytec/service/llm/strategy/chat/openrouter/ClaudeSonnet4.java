package com.fytec.service.llm.strategy.chat.openrouter;

import com.fytec.contant.ModelApiKey;
import com.fytec.contant.enums.ModelTypeEnum;
import com.fytec.dto.llm.ModelProcessDTO;
import com.fytec.service.llm.strategy.chat.ModelProcessStrategy;
import com.fytec.service.llm.strategy.chat.OpenAiChatBasic;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;


@Slf4j
@Component
public class ClaudeSonnet4 extends OpenAiChatBasic implements InitializingBean, ModelProcessStrategy {
    private final ModelTypeEnum modelType = ModelTypeEnum.claude_sonnet_4;

    @Override
    public void handleStream(ModelProcessDTO processDTO, SseEmitter sseEmitter) {
        log.info("\n----- start request {} stream -----", modelType.name());
        processDTO.setModelId(modelType.getModelId());
        processDTO.setEnableFunctionCall(modelType.isEnableFunctionCall());
        processDTO.setModelBaseUrl("https://openrouter.ai/api/v1");
        processStream(ModelApiKey.OPEN_ROUTER_API_KEY, processDTO, sseEmitter);
    }

    @Override
    public String handle(ModelProcessDTO processDTO) {
        log.info("\n----- start request {}  -----", modelType.name());
        processDTO.setModelId(modelType.getModelId());
        processDTO.setEnableFunctionCall(modelType.isEnableFunctionCall());
        processDTO.setModelBaseUrl("https://openrouter.ai/api/v1");
        return processNonStream(ModelApiKey.OPEN_ROUTER_API_KEY, processDTO);
    }

    @Override
    public void afterPropertiesSet() {
        afterPropertiesSet(modelType.name());
    }
}
