package com.fytec.service.llm.doc.baidu;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class BaiduOcrService extends BaiduBaseService {
    private static final String VAT_INVOICE_URL = "https://aip.baidubce.com/rest/2.0/ocr/v1/vat_invoice?access_token={}";

    @SneakyThrows
    public Map<String, Object> processOrcVatInvoice(String fileUrl, String oauthClientId) {
        Map<String, Object> result = new HashMap<>();
        String url = StrUtil.format(VAT_INVOICE_URL, getAccessToken(oauthClientId));
        String fileData = getFileContentAsBase64(fileUrl, true);

        String fileType = determineFileType(fileUrl);
        String requestBody = "seal_tag=false";
        if (StrUtil.equals("ofd", fileType)) {
            requestBody = requestBody + "&ofd_file=" + fileData;
        } else if (StrUtil.equals("pdf", fileType)) {
            requestBody = requestBody + "&pdf_file=" + fileData;
        } else if (StrUtil.equalsAny(fileType, "jpg", "png", "bmp", "jpeg")) {
            requestBody = requestBody + "&image=" + fileData;
        } else {
            result.put("error", StrUtil.format("ORC增值税发票识别处理失败: {}", "不支持的文件类型"));
            return result;
        }

        RequestBody body = RequestBody.create(requestBody, MEDIA_TYPE);
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .addHeader("Content-Type", CONTENT_TYPE)
                .build();

        // 发送请求
        log.info("发送ORC识别请求，文件类型: {}", fileType);
        try (Response response = HTTP_CLIENT.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                result.put("error", StrUtil.format("ORC增值税发票识别处理失败: {}", response.code()));
                return result;
            }

            ResponseBody responseBody = response.body();
            if (responseBody == null) {
                result.put("error", StrUtil.format("ORC增值税发票识别处理失败: {}", "响应体为空"));
                return result;
            }

            log.info("ORC识别响应内容:{}", responseBody);
            JSONObject jsonObject = JSONObject.parseObject(responseBody.string());
            JSONObject wordsResult = jsonObject.getJSONObject("words_result");
            if (wordsResult != null) {
                result.put("InvoiceType", wordsResult.get("InvoiceType"));
                result.put("InvoiceName", wordsResult.get("InvoiceTypeOrg"));
                result.put("InvoiceCode", wordsResult.get("InvoiceCode"));
                result.put("InvoiceNum", wordsResult.get("InvoiceNum"));
                result.put("InvoiceDate", wordsResult.get("InvoiceDate"));
                result.put("InvoiceAmountAndTax", wordsResult.get("AmountInFiguers"));
            }

            return result;
        } catch (Exception e) {
            result.put("error", StrUtil.format("ORC增值税发票识别处理失败: {}", e.getMessage()));
            return result;
        }
    }

    private String determineFileType(String fileUrl) {
        if (StrUtil.isBlank(fileUrl)) {
            throw new IllegalArgumentException("文件URL不能为空");
        }

        String lowerUrl = fileUrl.toLowerCase();
        String extension = "";

        // 提取文件扩展名
        int lastDotIndex = lowerUrl.lastIndexOf('.');
        int lastSlashIndex = lowerUrl.lastIndexOf('/');
        int queryIndex = lowerUrl.indexOf('?', lastDotIndex);

        if (lastDotIndex > lastSlashIndex && lastDotIndex > 0) {
            extension = queryIndex > 0 ?
                    lowerUrl.substring(lastDotIndex + 1, queryIndex) :
                    lowerUrl.substring(lastDotIndex + 1);
        }
        return extension;
    }

}
