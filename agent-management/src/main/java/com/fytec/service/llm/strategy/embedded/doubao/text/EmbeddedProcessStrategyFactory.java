package com.fytec.service.llm.strategy.embedded.doubao.text;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class EmbeddedProcessStrategyFactory {

    private static final Map<String, EmbeddedProcessStrategy> STRATEGY_CONCURRENT_HASH_MAP = new ConcurrentHashMap<>();

    /**
     * 获取策略的方法
     *
     * @param key 策略类型
     */
    public static EmbeddedProcessStrategy getStrategy(String key) {
        return STRATEGY_CONCURRENT_HASH_MAP.get(key);
    }

    /**
     * 在Bean属性初始化后执行该方法
     * 注册策略
     *
     * @param key                  策略类型
     * @param embeddedProcessStrategy 策略处理接口
     */
    public static void register(String key, EmbeddedProcessStrategy embeddedProcessStrategy) {
        STRATEGY_CONCURRENT_HASH_MAP.put(key, embeddedProcessStrategy);
    }
}
