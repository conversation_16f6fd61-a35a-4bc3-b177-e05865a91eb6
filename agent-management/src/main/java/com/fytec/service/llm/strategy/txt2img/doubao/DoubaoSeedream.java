package com.fytec.service.llm.strategy.txt2img.doubao;

import com.fytec.contant.ModelApiKey;
import com.fytec.contant.enums.ModelTypeEnum;
import com.fytec.contant.enums.Txt2ImgTypeEnum;
import com.fytec.dto.llm.ModelProcessDTO;
import com.fytec.dto.llm.Txt2ImgProcessDTO;
import com.fytec.dto.response.Txt2ImgDTO;
import com.fytec.service.llm.strategy.chat.DoubaoChatBasic;
import com.fytec.service.llm.strategy.chat.ModelProcessStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;


@Slf4j
@Component
public class DoubaoSeedream extends DoubaoTxt2ImgBasic implements InitializingBean, Txt2ImgProcessStrategy {
    private final Txt2ImgTypeEnum txt2ImgType = Txt2ImgTypeEnum.doubao_seedream_t2i;

    @Override
    public List<Txt2ImgDTO> produce(Txt2ImgProcessDTO processDTO) {
        log.info("\n----- start request {}  -----", txt2ImgType.name());
        processDTO.setModelId(txt2ImgType.getModelId());
        return txt2imgBasic(ModelApiKey.DOUBAO_API_KEY, processDTO);
    }

    @Override
    public void afterPropertiesSet() {
        afterPropertiesSet(txt2ImgType.name());
    }
}
