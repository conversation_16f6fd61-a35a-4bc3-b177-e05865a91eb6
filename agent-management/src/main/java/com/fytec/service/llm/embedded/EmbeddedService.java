package com.fytec.service.llm.embedded;

import com.fytec.dto.llm.EmbeddedProcessDTO;
import com.fytec.service.llm.strategy.embedded.doubao.text.EmbeddedProcessStrategyFactory;
import com.volcengine.ark.runtime.model.embeddings.Embedding;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class EmbeddedService {

    public List<Embedding> processEmbeddedByDoubao(EmbeddedProcessDTO dto) {
        return EmbeddedProcessStrategyFactory.getStrategy(dto.getModelKey())
                .embedded(dto);

    }
}
