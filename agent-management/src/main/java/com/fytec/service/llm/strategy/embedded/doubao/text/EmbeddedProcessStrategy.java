package com.fytec.service.llm.strategy.embedded.doubao.text;


import com.fytec.dto.llm.EmbeddedProcessDTO;
import com.volcengine.ark.runtime.model.embeddings.Embedding;

import java.util.List;

/**
 * 任务分配用户策略处理器
 */
public interface EmbeddedProcessStrategy {

    /**
     * 策略注册方法
     *
     * @param key 策略类型
     */
    default void afterPropertiesSet(String key) {
        EmbeddedProcessStrategyFactory.register(key, this);
    }

    List<Embedding> embedded(EmbeddedProcessDTO processDTO);
}
