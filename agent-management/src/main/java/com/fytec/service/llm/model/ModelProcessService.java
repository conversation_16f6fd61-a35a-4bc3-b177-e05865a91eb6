package com.fytec.service.llm.model;


import com.fytec.contant.enums.ModelTypeEnum;
import com.fytec.dto.llm.ModelProcessDTO;
import com.fytec.dto.llm.Txt2ImgProcessDTO;
import com.fytec.dto.response.Txt2ImgDTO;
import com.fytec.service.llm.strategy.bot.BotProcessStrategyFactory;
import com.fytec.service.llm.strategy.chat.ModelProcessStrategyFactory;
import com.fytec.service.llm.strategy.txt2img.doubao.Txt2ImgProcessStrategyFactory;
import jakarta.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
public class ModelProcessService {
    private static final Map<String, SseEmitter> SSE_CACHE = new ConcurrentHashMap<>();

    public SseEmitter getConn(@NotBlank String clientId) {
        final SseEmitter sseEmitter = SSE_CACHE.get(clientId);

        if (sseEmitter != null) {
            return sseEmitter;
        } else {
            // 设置连接超时时间，需要配合配置项 spring.mvc.async.request-timeout: 600000 一起使用
            final SseEmitter emitter = new SseEmitter(600000L);
            // 注册超时回调，超时后触发
            emitter.onTimeout(() -> {
                log.info("连接已超时，正准备关闭，clientId = {}", clientId);
                SSE_CACHE.remove(clientId);
            });
            // 注册完成回调，调用 emitter.complete() 触发
            emitter.onCompletion(() -> {
                log.info("连接已关闭，正准备释放，clientId = {}", clientId);
                SSE_CACHE.remove(clientId);
                log.info("连接已释放，clientId = {}", clientId);
            });
            // 注册异常回调，调用 emitter.completeWithError() 触发
            emitter.onError(throwable -> {
                log.error("连接已异常，正准备关闭，clientId = {}", clientId, throwable);
                SSE_CACHE.remove(clientId);
            });

            SSE_CACHE.put(clientId, emitter);
            return emitter;
        }
    }

    public void processModelByStream(@NotBlank String clientId, ModelProcessDTO modelProcessDTO) {
        final SseEmitter emitter = SSE_CACHE.get(clientId);
        try {
            boolean isModelStrategy = true;
            if (modelProcessDTO.isEnablePluginSearch()) {
                ModelTypeEnum modelTypeEnum = ModelTypeEnum.valueOf(modelProcessDTO.getModelType());
                if (!modelTypeEnum.isEnablePluginSearch()) {
                    // 模型本身不支持互联网索索，则使用bot策略处理
                    isModelStrategy = false;
                }
            }

            if (isModelStrategy) {
                ModelProcessStrategyFactory.getStrategy(modelProcessDTO.getModelType())
                        .handleStream(modelProcessDTO, emitter);
            } else {
                BotProcessStrategyFactory.getStrategy(modelProcessDTO.getModelType())
                        .handleStream(modelProcessDTO, emitter);
            }
        } catch (Exception e) {
            emitter.completeWithError(e);
        }
        // 结束推流
        emitter.complete();
    }

    public String processModelByNonStream(ModelProcessDTO modelProcessDTO) {
        boolean isModelStrategy = true;
        if (modelProcessDTO.isEnablePluginSearch()) {
            ModelTypeEnum modelTypeEnum = ModelTypeEnum.valueOf(modelProcessDTO.getModelType());
            if (!modelTypeEnum.isEnablePluginSearch()) {
                // 模型本身不支持互联网索索，则使用bot策略处理
                isModelStrategy = false;
            }
        }

        if (isModelStrategy) {
            return ModelProcessStrategyFactory.getStrategy(modelProcessDTO.getModelType()).handle(modelProcessDTO);
        } else {
            return BotProcessStrategyFactory.getStrategy(modelProcessDTO.getModelType()).handle(modelProcessDTO);
        }
    }


    public void closeConn(@NotBlank String clientId) {
        final SseEmitter sseEmitter = SSE_CACHE.get(clientId);
        if (sseEmitter != null) {
            sseEmitter.complete();
        }
    }

    public List<Txt2ImgDTO> processTxt2Img(Txt2ImgProcessDTO dto) {
        return Txt2ImgProcessStrategyFactory.getStrategy(dto.getModelKey())
                .produce(dto);

    }
}
