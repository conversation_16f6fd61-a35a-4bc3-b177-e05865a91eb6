package com.fytec.service.llm.doc.volcengine;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fytec.contant.ModelApiKey;
import com.fytec.service.llm.doc.volcengine.auth.Credentials;
import com.fytec.service.llm.doc.volcengine.auth.Signer;
import com.fytec.service.system.SysCryptoService;
import com.volcengine.service.visual.IVisualService;
import com.volcengine.service.visual.impl.VisualServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHeaders;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Service
@Slf4j
@RequiredArgsConstructor
public class VolcengineOcrService {

    private static final String ACTION_OCR_NORMAL = "OCRNormal";

    private final SysCryptoService cryptoService;

    public String processOrcNormal(String fileUrl, String fileContent,
                                   String oauthClientId) {
        IVisualService visualService = VisualServiceImpl.getInstance();

        String decryptApiKey = cryptoService.decryptContent(oauthClientId, ModelApiKey.VOLCENGINE_ACCESS_SECRET);
        visualService.setAccessKey(ModelApiKey.VOLCENGINE_ACCESS_KEY);
        visualService.setSecretKey(decryptApiKey);

        JSONObject request = new JSONObject();
        if (StrUtil.isNotBlank(fileContent)) {
            request.put("image_base64", fileContent);
        } else {
            request.put("image_url", fileUrl);
        }
        try {
            String jsonStr = visualService.ocrApi(ACTION_OCR_NORMAL, request);
            JSONObject result = JSON.parseObject(jsonStr);
            if (result.getInteger("code") == 10000) {
                JSONArray lineTexts = result.getJSONObject("data").getJSONArray("line_texts");
                return StrUtil.join("\n", lineTexts);
            }
        } catch (Exception e) {
            log.error("OCR API error: ", e);
        }
        return null;
    }

    @SneakyThrows
    public Map<String, Object> processOrcVatInvoice(String fileUrl, String fileContent, String oauthClientId) {
        Map<String, Object> result = new HashMap<>();
        String decryptApiKey = cryptoService.decryptContent(oauthClientId, ModelApiKey.VOLCENGINE_ACCESS_SECRET);
        Credentials credentials = createCredentials(decryptApiKey);

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // 创建和签名请求
            HttpPost request = createHttpPost("OcrVatInvoice", "2020-08-26", fileContent, fileUrl);
            new Signer().sign(request, credentials);

            // 执行请求并处理响应
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                handleResponse(response, result);
                return result;
            }
        } catch (Exception e) {
            result.put("error", StrUtil.format("ORC增值税发票识别处理失败: {}", e.getMessage()));
            return result;
        }
    }

    private Credentials createCredentials(String decryptApiKey) {
        /* create credentials */
        Credentials credentials = new Credentials();
        credentials.setAccessKeyID(ModelApiKey.VOLCENGINE_ACCESS_KEY);
        credentials.setSecretAccessKey(decryptApiKey);
        credentials.setRegion("cn-north-1");
        credentials.setService("cv");
        return credentials;
    }

    private HttpPost createHttpPost(String action, String version,
                                    String imageBase64, String imageUrl) throws URISyntaxException {
        HttpPost request = new HttpPost();
        String url = StrUtil.format("https://visual.volcengineapi.com?Action={}&Version={}", action, version);
        request.setURI(new URI(url));
        request.addHeader(HttpHeaders.USER_AGENT, "volc-sdk-java/v1.0.0");

        List<NameValuePair> nameValuePairs = new ArrayList<NameValuePair>();
        if (StrUtil.isNotBlank(imageBase64)) {
            nameValuePairs.add(new BasicNameValuePair("image_base64", imageBase64));
        } else {
            nameValuePairs.add(new BasicNameValuePair("image_url", imageUrl));
        }
        request.setEntity(new UrlEncodedFormEntity(nameValuePairs, StandardCharsets.UTF_8));

        return request;
    }

    private void handleResponse(CloseableHttpResponse response, Map<String, Object> result) throws IOException {
        int statusCode = response.getStatusLine().getStatusCode();

        if (statusCode != 200) {
            result.put("error", StrUtil.format("ORC增值税发票识别处理失败, HttpCode: {}", statusCode));
            return;
        }

        HttpEntity entity = response.getEntity();
        if (entity == null) {
            result.put("error", StrUtil.format("ORC增值税发票识别处理失败: {}", "响应体为空"));
            return;
        }

        String resultStr = EntityUtils.toString(entity, StandardCharsets.UTF_8);
        JSONObject jsonObject = JSON.parseObject(resultStr);
        if (jsonObject.getIntValue("code") != 10000) {
            result.put("error", StrUtil.format("ORC增值税发票识别处理失败, 错误码: {}", jsonObject.getIntValue("code")));
            return;
        }

        JSONObject licenseMain = jsonObject.getJSONObject("data").getJSONObject("license_main");
        if (!licenseMain.containsKey("invoice_no") && !licenseMain.containsKey("totalPriceAndTax")) {
            result.put("error", StrUtil.format("ORC增值税发票识别处理失败, 未提取到有效信息: {}", "发票号码/发票金额"));
            return;
        }

        result.put("InvoiceName", licenseMain.getString("invoice_name"));
        result.put("InvoiceCode", licenseMain.getString("invoice_code"));
        result.put("InvoiceNum", licenseMain.getString("invoice_no"));
        result.put("InvoiceDate", licenseMain.getString("invoice_date"));
        result.put("InvoiceAmountAndTax", licenseMain.getString("total_price_and_tax").replace("¥", ""));
    }

}
