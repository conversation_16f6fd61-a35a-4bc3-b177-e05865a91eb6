package com.fytec.service.llm.doc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fytec.contant.ModelApiKey;
import com.fytec.dto.llm.DocProcessResultDTO;
import com.fytec.service.system.SysCryptoService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Service;

import javax.validation.ValidationException;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

@Service
@Slf4j
public class BaiduDocService {

    static final OkHttpClient HTTP_CLIENT = new OkHttpClient().newBuilder().build();


    @Resource
    private SysCryptoService cryptoService;


    public String submitTask(String fileUrl, String fileName, String oauthClientId) throws IOException {
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        String fileData = getFileContentAsBase64(fileUrl, true);
        RequestBody body = RequestBody.create(mediaType, "file_data=" + fileData + "&file_name=" + fileName);
        Request request = new Request.Builder()
                .url("https://aip.baidubce.com/rest/2.0/brain/online/v2/parser/task?access_token=" + getAccessToken(oauthClientId))
                .method("POST", body)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .addHeader("Accept", "application/json")
                .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        if (response.code() != 200) {
            log.error("提交百度文档解析任务失败，失败原因：{}", response.message());
        }
        String res = response.body().string();
        log.info("提交百度文档解析任务，返回结果：{}", res);
        JSONObject jsonObject = JSONObject.parseObject(res);
        if(jsonObject.getIntValue("error_code")!=0){
            throw new ValidationException("提交百度文档解析任务失败，失败原因：" + jsonObject.getString("error_msg"));
        }
        JSONObject result = jsonObject.getJSONObject("result");
        String taskId = result.getString("task_id");
        return taskId;
        //{"log_id": "12386594202931941018920527825407", "error_code": 0, "error_msg": "", "result": {"task_id": "task-AS65oReyQoRcn2mMgA8Uc54ynlTX8FhC"}}
    }


    public DocProcessResultDTO getTask(String taskId, String oauthClientId) throws IOException {
        DocProcessResultDTO resultDTO = new DocProcessResultDTO();
        resultDTO.setTaskId(taskId);
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        RequestBody body = RequestBody.create(mediaType, "task_id=" + taskId);
        Request request = new Request.Builder()
                .url("https://aip.baidubce.com/rest/2.0/brain/online/v2/parser/task/query?access_token=" + getAccessToken(oauthClientId))
                .method("POST", body)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .addHeader("Accept", "application/json")
                .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        if (response.code() != 200) {
            log.error("查询百度文档解析任务，失败原因：{}", response.message());
        }
        String res = response.body().string();
        log.info("查询百度文档解析任务，返回结果：{}", res);
        JSONObject jsonObject = JSONObject.parseObject(res);
        if(jsonObject.getIntValue("error_code")!=0){
            throw new ValidationException("查询百度文档解析任务，失败原因：" + jsonObject.getString("error_msg"));
        }
        JSONObject result = jsonObject.getJSONObject("result");
        String status = result.getString("status");
        if (status.equals("success")) {
            String parseResultUrl = result.getString("parse_result_url");
            List<String> contents = parseResultUrl(parseResultUrl);
            resultDTO.setContents(contents);
        } else if (status.equals("running")) {
            log.info("百度文档解析任务正在进行中，任务id：{}", taskId);
        } else {
            log.error("百度文档解析任务失败，失败原因：{}", result.getString("task_error"));
        }
        resultDTO.setStatus(status);
        return resultDTO;
    }


    public List<String> parseResultUrl(String parseResultUrl) throws IOException {
        List<String> contents = new ArrayList<>();
        Request request = new Request.Builder()
                .url(parseResultUrl)
                .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        if (response.code() != 200) {
            log.error("调用获取百度文档内容失败，失败原因：{}", response.message());
        }
        String res = response.body().string();
        System.out.println(res);
        JSONObject jsonObject = JSONObject.parseObject(res);
        List<JSONObject> pages = JSON.parseArray(jsonObject.getString("pages"), JSONObject.class);
        for (JSONObject page : pages) {
            String text = page.getString("text");
            System.out.println(text);
            contents.add(text);
        }
        return contents;
    }


    /**
     * 从用户的AK，SK生成鉴权签名（Access Token）
     *
     * @return 鉴权签名（Access Token）
     * @throws IOException IO异常
     */
    private String getAccessToken(String oauthClientId) throws IOException {
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        String decryptApiKey = cryptoService.decryptContent(oauthClientId, ModelApiKey.BAIDU_ACCESS_SECRET);
        RequestBody body = RequestBody.create(mediaType, "grant_type=client_credentials&client_id=" + ModelApiKey.BAIDU_ACCESS_KEY
                + "&client_secret=" + decryptApiKey);
        Request request = new Request.Builder()
                .url("https://aip.baidubce.com/oauth/2.0/token")
                .method("POST", body)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        JSONObject jsonObject = JSONObject.parseObject(response.body().string());
        return jsonObject.getString("access_token");
    }


    /**
     * 获取文件base64编码
     *
     * @param path      文件路径
     * @param urlEncode 如果Content-Type是application/x-www-form-urlencoded时,传true
     * @return base64编码信息，不带文件头
     * @throws IOException IO异常
     */
    public String getFileContentAsBase64(String path, boolean urlEncode) throws IOException {
        URL url = new URL(path);
        URLConnection uc = url.openConnection();
        int contentLength = uc.getContentLength();
        try (InputStream raw = uc.getInputStream()) {
            InputStream in = new BufferedInputStream(raw);
            byte[] data = new byte[contentLength];
            int offset = 0;
            while (offset < contentLength) {
                int bytesRead = in.read(data, offset, data.length - offset);
                if (bytesRead == -1) {
                    break;
                }
                offset += bytesRead;
            }
            if (offset != contentLength) {
                throw new ValidationException("文件为空");
            }
            String base64 = Base64.getEncoder().encodeToString(data);
            if (urlEncode) {
                base64 = URLEncoder.encode(base64, "utf-8");
            }
            return base64;
        }
    }


}
