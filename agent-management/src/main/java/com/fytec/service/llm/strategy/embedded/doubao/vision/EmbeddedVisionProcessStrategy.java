package com.fytec.service.llm.strategy.embedded.doubao.vision;


import com.fytec.dto.llm.EmbeddedProcessDTO;
import com.volcengine.ark.runtime.model.embeddings.Embedding;
import com.volcengine.ark.runtime.model.multimodalembeddings.MultimodalEmbedding;

import java.util.List;

/**
 * 任务分配用户策略处理器
 */
public interface EmbeddedVisionProcessStrategy {

    /**
     * 策略注册方法
     *
     * @param key 策略类型
     */
    default void afterPropertiesSet(String key) {
        EmbeddedVisionProcessStrategyFactory.register(key, this);
    }

    MultimodalEmbedding embedded(EmbeddedProcessDTO processDTO);
}
