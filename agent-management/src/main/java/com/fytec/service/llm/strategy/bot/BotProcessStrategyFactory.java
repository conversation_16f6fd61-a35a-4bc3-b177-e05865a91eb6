package com.fytec.service.llm.strategy.bot;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class BotProcessStrategyFactory {

    private static final Map<String, BotProcessStrategy> STRATEGY_CONCURRENT_HASH_MAP = new ConcurrentHashMap<>();

    /**
     * 获取策略的方法
     *
     * @param key 策略类型
     */
    public static BotProcessStrategy getStrategy(String key) {
        return STRATEGY_CONCURRENT_HASH_MAP.get(key);
    }

    /**
     * 在Bean属性初始化后执行该方法
     * 注册策略
     *
     * @param key                  策略类型
     * @param botProcessStrategy 策略处理接口
     */
    public static void register(String key, BotProcessStrategy botProcessStrategy) {
        STRATEGY_CONCURRENT_HASH_MAP.put(key, botProcessStrategy);
    }
}
