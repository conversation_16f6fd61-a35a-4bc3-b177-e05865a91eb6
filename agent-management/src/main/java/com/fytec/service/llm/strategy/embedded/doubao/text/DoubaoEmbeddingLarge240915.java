package com.fytec.service.llm.strategy.embedded.doubao.text;

import com.fytec.contant.ModelApiKey;
import com.fytec.contant.enums.EmbeddedTypeEnum;
import com.fytec.dto.llm.EmbeddedProcessDTO;
import com.volcengine.ark.runtime.model.embeddings.Embedding;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.List;


@Slf4j
@Component
public class DoubaoEmbeddingLarge240915 extends DoubaoEmbeddedBasic implements InitializingBean, EmbeddedProcessStrategy {
    private final EmbeddedTypeEnum embeddedType = EmbeddedTypeEnum.doubao_embedded_large_240915;


    @Override
    public List<Embedding> embedded(EmbeddedProcessDTO processDTO) {
        log.info("\n----- start request {} -----", embeddedType.name());
        processDTO.setModelId(embeddedType.getModelId());
        return embeddedBasic(ModelApiKey.DOUBAO_API_KEY, processDTO);
    }

    @Override
    public void afterPropertiesSet() {
        afterPropertiesSet(embeddedType.name());
    }
}
