package com.fytec.service.llm.strategy.embedded.doubao.vision;

import com.fytec.dto.llm.EmbeddedProcessDTO;
import com.fytec.service.system.SysCryptoService;
import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionContentPart;
import com.volcengine.ark.runtime.model.embeddings.Embedding;
import com.volcengine.ark.runtime.model.embeddings.EmbeddingRequest;
import com.volcengine.ark.runtime.model.embeddings.EmbeddingResult;
import com.volcengine.ark.runtime.model.multimodalembeddings.MultimodalEmbedding;
import com.volcengine.ark.runtime.model.multimodalembeddings.MultimodalEmbeddingInput;
import com.volcengine.ark.runtime.model.multimodalembeddings.MultimodalEmbeddingRequest;
import com.volcengine.ark.runtime.model.multimodalembeddings.MultimodalEmbeddingResult;
import com.volcengine.ark.runtime.service.ArkService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


@Slf4j
@Component
public class DoubaoEmbeddedVisionBasic {
    @Resource
    private SysCryptoService cryptoService;


    public MultimodalEmbedding embeddedBasic(String apiKey, EmbeddedProcessDTO processDTO) {
        String decryptApiKey = cryptoService.decryptContent(processDTO.getOauthClientId(), apiKey);
        ArkService service = ArkService.builder().apiKey(decryptApiKey).build();

        List<MultimodalEmbeddingInput> inputs = new ArrayList<>();
        for (String url : processDTO.getImageUrls()) {
            inputs.add(
                    MultimodalEmbeddingInput.builder().type("image_url").imageUrl(
                            new MultimodalEmbeddingInput.MultiModalEmbeddingContentPartImageURL(url)
                    ).build()
            );
        }

        for (String input : processDTO.getInputs()) {
            inputs.add(
                    MultimodalEmbeddingInput.builder().type("text").text(input).build()
            );
        }

        MultimodalEmbeddingRequest multiModalEmbeddingRequest = MultimodalEmbeddingRequest.builder()
                .model(processDTO.getModelId())
                .input(inputs)
                .build();

        MultimodalEmbeddingResult res = service.createMultiModalEmbeddings(multiModalEmbeddingRequest);
        return res.getData();
    }
}
