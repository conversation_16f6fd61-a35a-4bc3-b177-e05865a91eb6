package com.fytec.service.llm.doc.ali;

import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.fytec.contant.ModelApiKey;
import com.fytec.service.system.SysCryptoService;
import com.fytec.util.HttpUtil;
import com.mashape.unirest.http.HttpResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


@Service
@Slf4j
@RequiredArgsConstructor
public class AliRerankService {

    private final SysCryptoService cryptoService;

    @SneakyThrows
    public JSONArray textGteRerank(String body, String oauthClientId) {
        String decryptApiKey = cryptoService.decryptContent(oauthClientId, ModelApiKey.ALI_API_KEY);

        HttpResponse<String> response = HttpUtil.sendPostRequest(
                "https://dashscope.aliyuncs.com/api/v1/services/rerank/text-rerank/text-rerank",
                body,
                StrUtil.format("Bearer {}", decryptApiKey));
        if (response.getStatus() != 200) {
            throw new ValidateException("reranker 错误: %d".formatted(response.getStatus()));
        }
        JSONObject jsonObject = JSON.parseObject(response.getBody());
        return jsonObject.getJSONObject("output").getJSONArray("results");
    }
}
