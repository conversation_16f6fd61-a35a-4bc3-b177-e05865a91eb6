package com.fytec.service.llm.strategy.chat;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.dashscope.aigc.generation.*;
import com.alibaba.dashscope.common.Message;
import com.alibaba.dashscope.common.Role;
import com.alibaba.dashscope.tools.*;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fytec.dto.llm.ChatToolDTO;
import com.fytec.dto.llm.ModelProcessDTO;
import com.fytec.entity.llm.AiModelUsageToken;
import com.fytec.mapper.llm.AiModelUsageMapper;
import com.fytec.service.llm.strategy.FunctionCallService;
import com.fytec.service.system.SysCryptoService;
import com.google.gson.JsonParser;
import com.volcengine.ark.runtime.model.completion.chat.ChatTool;
import io.reactivex.Flowable;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@Component
public class AliChatBasic extends FunctionCallService {
    @Resource
    private AiModelUsageMapper aiModelUsageMapper;
    @Resource
    private SysCryptoService cryptoService;

    @SneakyThrows
    public void processStream(String apiKey, ModelProcessDTO processDTO, SseEmitter sseEmitter) {
        String decryptApiKey = cryptoService.decryptContent(processDTO.getOauthClientId(), apiKey);
        processDTO.setDecryptApiKey(decryptApiKey);

        Generation gen = new Generation();
        List<Message> messages = prepareMessage(processDTO);
        GenerationParam param = buildGenerationParam(processDTO, messages);

        Map<String, Integer> toolCallMap = new HashMap<>();

        //如果配置了tools，则根据tools的数量 + 1作为最大次数
        int whileCount = 1;
        if (CollUtil.isNotEmpty(processDTO.getTools())) {
            whileCount = processDTO.getTools().size() + 1;
        }


        AiModelUsageToken aiModelUsageToken = new AiModelUsageToken();
        aiModelUsageToken.setClientId(processDTO.getOauthClientId());
        aiModelUsageToken.setModelCode(processDTO.getModelId());

        long promptTokens = 0;
        long completionTokens = 0;
        long totalTokens = 0;

        StringBuilder argumentsBuffer = new StringBuilder();
        while (whileCount > 0) {
            boolean isCallToos = false;

            // Function Call 重复调用次数限制，限制为1次，后续根据实际情况调整
            for (String key : toolCallMap.keySet()) {
                int functionCallCount = toolCallMap.get(key);
                if (functionCallCount > 1) {
                    log.warn(StrUtil.format("大模型{}的function call，相同的函数调用超过指定限制次数！请更换模型尝试", processDTO.getModelType()));

                    aiModelUsageToken.setStatus("F");
                    aiModelUsageToken.setReason("流式大模型调用工具失败");
                    aiModelUsageToken.setCreateTime(LocalDateTime.now());
                    aiModelUsageMapper.insert(aiModelUsageToken);

                    sseEmitter.send("流式大模型调用工具失败！");
                    sseEmitter.send("[end]");
                    sseEmitter.complete();
                }
            }

            Flowable<GenerationResult> result = gen.streamCall(param);
            Iterable<GenerationResult> generationResults = result.blockingIterable();
            for (GenerationResult generationResult : generationResults) {
                if (generationResult.getUsage() != null) {
                    promptTokens += generationResult.getUsage().getInputTokens();
                    completionTokens += generationResult.getUsage().getOutputTokens();
                    totalTokens += generationResult.getUsage().getTotalTokens();
                }

                if (generationResult.getOutput().getSearchInfo() != null) {
                    SearchInfo searchInfo = generationResult.getOutput().getSearchInfo();
                    Map<String, Object> referenceContentMap = new HashMap<>();
                    referenceContentMap.put("type", "referenceContent");
                    referenceContentMap.put("content", searchInfo.getSearchResults());
                    sseEmitter.send(JSON.toJSONString(referenceContentMap));
                }

                if (CollUtil.isNotEmpty(generationResult.getOutput().getChoices())) {
                    List<ToolCallBase> toolCalls = generationResult.getOutput().getChoices().getFirst().getMessage().getToolCalls();
                    if (CollUtil.isNotEmpty(toolCalls)) {
                        for (ToolCallBase toolCall : toolCalls) {
                            if (StrUtil.isNotBlank(toolCall.getId())) {
                                messages.add(generationResult.getOutput().getChoices().getFirst().getMessage());
                            }
                            if (toolCall.getType().equals("function")) {
                                argumentsBuffer.append(((ToolCallFunction) toolCall).getFunction().getArguments());
                            }
                        }
                        if (StrUtil.isNotBlank(argumentsBuffer.toString())) {
                            try {
                                JSON.parseObject(argumentsBuffer.toString());
                                // 工具参数解析成功，则调用工具
                                prepareToolCallsMessage(messages, argumentsBuffer.toString(), toolCallMap, processDTO.getTools());
                                param.setMessages(messages);
                                isCallToos = true;
                                break;
                            } catch (Exception e) {
                                log.error("解析工具参数错误：{}", argumentsBuffer.toString());
                            }
                        }
                    } else {
                        Message message = generationResult.getOutput().getChoices().getFirst().getMessage();
                        if (message.getReasoningContent() != null && !message.getReasoningContent().isEmpty()) {
                            String reasoningContent = message.getReasoningContent();
                            Map<String, Object> reasoningContentMap = new HashMap<>();
                            reasoningContentMap.put("type", "reasoningContent");
                            reasoningContentMap.put("content", reasoningContent);
                            log.info("ReasoningContent: {}", reasoningContentMap);
                            sseEmitter.send(JSON.toJSONString(reasoningContentMap));
                        }
                        Object content = message.getContent();
                        if (content != null && ObjectUtil.isNotEmpty(content)) {
                            Map<String, Object> contentMap = new HashMap<>();
                            contentMap.put("type", "content");
                            contentMap.put("content", content);
                            log.info("Content: {}", contentMap);
                            sseEmitter.send(JSON.toJSONString(contentMap));
                        }
                    }
                }
            }

            if (!isCallToos) {
                aiModelUsageToken.setStatus("S");
                aiModelUsageToken.setPromptTokens(promptTokens);
                aiModelUsageToken.setCompletionTokens(completionTokens);
                aiModelUsageToken.setTotalTokens(totalTokens);
                aiModelUsageToken.setCreateTime(LocalDateTime.now());
                aiModelUsageMapper.insert(aiModelUsageToken);

                sseEmitter.send("[end]");
                sseEmitter.complete();
                break;
            }

            whileCount--;
            if (whileCount == 0) {
                log.warn(StrUtil.format("大模型{}的循环调用次数超过限定次数，请检查逻辑是否正确！", processDTO.getModelType()));
                aiModelUsageToken.setStatus("F");
                aiModelUsageToken.setReason("流式大模型调用失败");
                aiModelUsageToken.setCreateTime(LocalDateTime.now());
                aiModelUsageMapper.insert(aiModelUsageToken);

                sseEmitter.send("流式大模型调用失败，请稍后重试！");
                sseEmitter.send("[end]");
                sseEmitter.complete();
            }
        }
    }

    private void prepareToolCallsMessage(List<Message> messages, String arguments,
                                         Map<String, Integer> toolCallMap,
                                         List<ChatToolDTO> tools) {
        Message message = messages.getLast();
        if (message.getRole().equals(Role.ASSISTANT.getValue())) {
            if (CollUtil.isNotEmpty(message.getToolCalls())) {
                for (ToolCallBase toolCall : message.getToolCalls()) {
                    if (toolCall.getType().equals("function")) {
                        String toolName = ((ToolCallFunction) toolCall).getFunction().getName();
                        if (StringUtils.isNotBlank(toolName)) {
                            if (StrUtil.isEmpty(arguments)) {
                                arguments = ((ToolCallFunction) toolCall).getFunction().getArguments();
                            }
                            Map<String, Object> argumentsMap = JSON.parseObject(arguments, Map.class);
                            String content = callChartTool(toolName, argumentsMap, tools);
                            Message toolResultMessage = Message.builder().role(Role.TOOL.getValue())
                                    .content(content).toolCallId(toolCall.getId()).build();
                            messages.add(toolResultMessage);
                        }

                        int functionCallCount = toolCallMap.getOrDefault(toolName, 0);
                        toolCallMap.put(toolName, functionCallCount + 1);
                    }
                }
            }
        }
    }

    @SneakyThrows
    public String processNonStream(String apiKey, ModelProcessDTO processDTO) {
        StringBuilder sb = new StringBuilder();

        String decryptApiKey = cryptoService.decryptContent(processDTO.getOauthClientId(), apiKey);
        processDTO.setDecryptApiKey(decryptApiKey);

        Generation gen = new Generation();
        List<Message> messages = prepareMessage(processDTO);
        GenerationParam param = buildGenerationParam(processDTO, messages);

        Map<String, Integer> toolCallMap = new HashMap<>();

        //如果配置了tools，则根据tools的数量 + 1作为最大次数
        int whileCount = 1;
        if (CollUtil.isNotEmpty(processDTO.getTools())) {
            whileCount = processDTO.getTools().size() + 1;
        }

        AiModelUsageToken aiModelUsageToken = new AiModelUsageToken();
        aiModelUsageToken.setClientId(processDTO.getOauthClientId());
        aiModelUsageToken.setModelCode(processDTO.getModelId());

        long promptTokens = 0;
        long completionTokens = 0;
        long totalTokens = 0;
        while (whileCount > 0) {
            boolean isCallToos = false;

            // Function Call 重复调用次数限制，限制为1次，后续根据实际情况调整
            for (String key : toolCallMap.keySet()) {
                int functionCallCount = toolCallMap.get(key);
                if (functionCallCount > 1) {
                    log.warn(StrUtil.format("大模型{}的function call，相同的函数调用超过指定限制次数！请更换模型尝试", processDTO.getModelType()));
                    aiModelUsageToken.setStatus("F");
                    aiModelUsageToken.setReason("大模型调用工具失败");
                    aiModelUsageToken.setPromptTokens(promptTokens);
                    aiModelUsageToken.setCompletionTokens(completionTokens);
                    aiModelUsageToken.setTotalTokens(totalTokens);
                    aiModelUsageToken.setCreateTime(LocalDateTime.now());
                    aiModelUsageMapper.insert(aiModelUsageToken);
                    return "大模型调用工具失败";
                }
            }

            GenerationResult generationResult = gen.call(param);

            for (GenerationOutput.Choice choice : generationResult.getOutput().getChoices()) {
                List<ToolCallBase> toolCalls = choice.getMessage().getToolCalls();
                if (CollUtil.isNotEmpty(toolCalls)) {
                    messages.add(choice.getMessage());
                    prepareToolCallsMessage(messages, null, toolCallMap, processDTO.getTools());
                    param.setMessages(messages);
                    isCallToos = true;
                    break;
                } else {
                    sb.append("{");
                    if (choice.getMessage().getReasoningContent() != null) {
                        sb.append("\"reasoningContent\": %s".formatted(JSON.toJSONString(choice.getMessage().getReasoningContent())));
                        sb.append(",");
                    }
                    if (generationResult.getOutput().getSearchInfo() != null) {
                        SearchInfo searchInfo = generationResult.getOutput().getSearchInfo();
                        sb.append("\"referenceContent\": %s".formatted(JSON.toJSONString(searchInfo.getSearchResults())));
                        sb.append(",");
                    }
                    sb.append("\"content\": %s".formatted(JSON.toJSONString(choice.getMessage().getContent())));
                    sb.append("}");
                }
            }
            promptTokens += generationResult.getUsage().getInputTokens();
            completionTokens += generationResult.getUsage().getOutputTokens();
            totalTokens += generationResult.getUsage().getTotalTokens();
            if (!isCallToos) {
                aiModelUsageToken.setStatus("S");
                aiModelUsageToken.setPromptTokens(promptTokens);
                aiModelUsageToken.setCompletionTokens(completionTokens);
                aiModelUsageToken.setTotalTokens(totalTokens);
                aiModelUsageToken.setCreateTime(LocalDateTime.now());
                aiModelUsageMapper.insert(aiModelUsageToken);
                break;
            }
            whileCount--;
            if (whileCount == 0) {
                log.warn(StrUtil.format("大模型{}的循环调用次数超过限定次数，请检查逻辑是否正确！", processDTO.getModelType()));
                aiModelUsageToken.setStatus("F");
                aiModelUsageToken.setReason("大模型调用失败");
                aiModelUsageToken.setPromptTokens(promptTokens);
                aiModelUsageToken.setCompletionTokens(completionTokens);
                aiModelUsageToken.setTotalTokens(totalTokens);
                aiModelUsageToken.setCreateTime(LocalDateTime.now());
                aiModelUsageMapper.insert(aiModelUsageToken);
                return "大模型调用失败，请稍后重试！";
            }
        }

        return sb.toString();
    }


    private List<Message> prepareMessage(ModelProcessDTO processDTO) {
        final List<Message> messages = new ArrayList<>();
        messages.add(Message.builder().role(Role.SYSTEM.getValue()).content(processDTO.getSystemMessage()).build());
        if (processDTO.isMultiTurn()) {
            for (Map<String, String> history : processDTO.getHistories()) {
                history.keySet().forEach(key -> {
                    messages.add(Message.builder().role(Role.USER.getValue()).content(key).build());
                    messages.add(Message.builder().role(Role.ASSISTANT.getValue()).content(history.get(key)).build());
                });
            }
        }
        messages.add(Message.builder().role(Role.USER.getValue()).content(processDTO.getUserMessage()).build());
        return messages;
    }

    @SneakyThrows
    private GenerationParam buildGenerationParam(ModelProcessDTO processDTO, List<Message> messages) {
        GenerationParam.GenerationParamBuilder<?, ?> paramBuilder = GenerationParam.builder()
                .model(processDTO.getModelId())
                // 若没有配置环境变量，请用百炼API Key将下行替换为：.apiKey("sk-xxx")
                .apiKey(processDTO.getDecryptApiKey())
                .temperature(processDTO.getTemperature().floatValue())
                .topP(processDTO.getTopP())
                .repetitionPenalty(processDTO.getFrequencyPenalty().floatValue() > 0 ? processDTO.getFrequencyPenalty().floatValue() : 1.05F)
                .maxTokens(processDTO.getMaxTokens())
                .enableSearch(processDTO.isEnablePluginSearch())
                .incrementalOutput(processDTO.isStream())
                .messages(messages)
                .resultFormat(GenerationParam.ResultFormat.MESSAGE);
        if (processDTO.isEnablePluginSearch()) {
            paramBuilder.searchOptions(
                    SearchOptions.builder()
                            .enableSource(true)
                            .enableCitation(true)
                            .build()
            );
        }

        if (processDTO.isEnableFunctionCall() && CollUtil.isNotEmpty(processDTO.getTools())) {
            List<ToolBase> tools = new ArrayList<>();
            ObjectMapper objectMapper = new ObjectMapper();
            for (ChatToolDTO toolDTO : processDTO.getTools()) {
                ChatTool tool = toolDTO.getChatTool();
                String jsonNodeAsString = objectMapper.writeValueAsString(tool.getFunction().getParameters());
                FunctionDefinition functionDefinition = FunctionDefinition.builder()
                        .name(tool.getFunction().getName())
                        .description(tool.getFunction().getDescription())
                        .parameters(JsonParser.parseString(jsonNodeAsString).getAsJsonObject())
                        .build();
                tools.add(ToolFunction.builder().function(functionDefinition).build());
            }
            paramBuilder.tools(tools);
        }
        return paramBuilder.build();
    }
}
