package com.fytec.service.llm.strategy.txt2img.doubao;


import com.fytec.dto.llm.EmbeddedProcessDTO;
import com.fytec.dto.llm.Txt2ImgProcessDTO;
import com.fytec.dto.response.Txt2ImgDTO;
import com.volcengine.ark.runtime.model.embeddings.Embedding;

import java.util.List;

/**
 * 任务分配用户策略处理器
 */
public interface Txt2ImgProcessStrategy {

    /**
     * 策略注册方法
     *
     * @param key 策略类型
     */
    default void afterPropertiesSet(String key) {
        Txt2ImgProcessStrategyFactory.register(key, this);
    }

    List<Txt2ImgDTO> produce(Txt2ImgProcessDTO processDTO);
}
