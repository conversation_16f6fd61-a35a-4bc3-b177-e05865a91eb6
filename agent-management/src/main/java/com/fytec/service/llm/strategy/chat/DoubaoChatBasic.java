package com.fytec.service.llm.strategy.chat;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fytec.dto.llm.ChatToolDTO;
import com.fytec.dto.llm.ModelProcessDTO;
import com.fytec.entity.llm.AiModelUsageToken;
import com.fytec.mapper.llm.AiModelUsageMapper;
import com.fytec.service.llm.strategy.FunctionCallService;
import com.fytec.service.system.SysCryptoService;
import com.fytec.util.UUIDGenerator;
import com.volcengine.ark.runtime.model.completion.chat.*;
import com.volcengine.ark.runtime.service.ArkService;
import io.reactivex.Flowable;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@Component
public class DoubaoChatBasic extends FunctionCallService {
    @Resource
    private AiModelUsageMapper aiModelUsageMapper;
    @Resource
    private SysCryptoService cryptoService;

    @SneakyThrows
    public void processStream(String apiKey, ModelProcessDTO processDTO, SseEmitter sseEmitter) {
        ArkService service = buildArkService(processDTO.getOauthClientId(), apiKey);
        List<ChatMessage> messages = prepareMessage(processDTO);
        ChatCompletionRequest chatCompletionRequest = createChatCompletionRequest(processDTO, messages);

        Map<String, Integer> toolCallMap = new HashMap<>();

        //如果配置了tools，则根据tools的数量 + 1作为最大次数
        int whileCount = 1;
        if (CollUtil.isNotEmpty(processDTO.getTools())) {
            whileCount = processDTO.getTools().size() + 1;
        }


        AiModelUsageToken aiModelUsageToken = new AiModelUsageToken();
        aiModelUsageToken.setClientId(processDTO.getOauthClientId());
        aiModelUsageToken.setModelCode(processDTO.getModelId());

        long promptTokens = 0;
        long completionTokens = 0;
        long totalTokens = 0;
        String functionName = "";
        StringBuilder argumentsBuffer = new StringBuilder();
        while (whileCount > 0) {
            boolean isCallToos = false;

            // Function Call 重复调用次数限制，限制为1次，后续根据实际情况调整
            for (String key : toolCallMap.keySet()) {
                int functionCallCount = toolCallMap.get(key);
                if (functionCallCount > 1) {
                    log.warn(StrUtil.format("大模型{}的function call，相同的函数调用超过指定限制次数！请更换模型尝试", processDTO.getModelType()));

                    aiModelUsageToken.setStatus("F");
                    aiModelUsageToken.setReason("流式大模型调用工具失败");
                    aiModelUsageToken.setCreateTime(LocalDateTime.now());
                    aiModelUsageMapper.insert(aiModelUsageToken);

                    sseEmitter.send("流式大模型调用工具失败！");
                    sseEmitter.send("[end]");
                    sseEmitter.complete();
                }
            }

            Flowable<ChatCompletionChunk> chatCompletionChunkFlowable = service.streamChatCompletion(chatCompletionRequest);
            Iterable<ChatCompletionChunk> chatCompletionChunks = chatCompletionChunkFlowable.blockingIterable();
            for (ChatCompletionChunk chatCompletionChunk : chatCompletionChunks) {
                if (chatCompletionChunk.getUsage() != null) {
                    promptTokens += chatCompletionChunk.getUsage().getPromptTokens();
                    completionTokens += chatCompletionChunk.getUsage().getCompletionTokens();
                    totalTokens += chatCompletionChunk.getUsage().getTotalTokens();
                }
                if (CollUtil.isNotEmpty(chatCompletionChunk.getChoices())) {
                    List<ChatToolCall> toolCalls = chatCompletionChunk.getChoices().getFirst().getMessage().getToolCalls();
                    if (CollUtil.isNotEmpty(toolCalls)) {
                        for (ChatToolCall toolCall : toolCalls) {
                            if (StrUtil.isNotBlank(toolCall.getId())) {
                                functionName = toolCall.getFunction().getName();
                                messages.add(chatCompletionChunk.getChoices().getFirst().getMessage());
                            }
                            argumentsBuffer.append(toolCall.getFunction().getArguments());
                        }
                        if (StrUtil.isNotBlank(argumentsBuffer.toString())) {
                            try {
                                JSON.parseObject(argumentsBuffer.toString());
                                // 工具参数解析成功，则调用工具
                                System.out.println("调用工具名称: " + functionName);
                                String finalFunctionName = functionName;
                                ChatToolDTO chatToolDTO = processDTO.getTools().stream()
                                        .filter(item -> item.getChatTool().getFunction().getName().equals(finalFunctionName))
                                        .findFirst().orElse(null);

                                Map<String, Object> toolCallContentMap = new HashMap<>();
                                toolCallContentMap.put("type", "function_call");
                                String uuid = UUIDGenerator.getUUID();
                                JSONObject toolCallContent = new JSONObject();
                                toolCallContent.put("id", uuid);
                                if (chatToolDTO != null) {
                                    toolCallContent.put("type", chatToolDTO.getType());
                                }
                                toolCallContent.put("name", finalFunctionName);
                                toolCallContent.put("arguments", argumentsBuffer.toString());
                                toolCallContentMap.put("content", toolCallContent);
                                sseEmitter.send(JSON.toJSONString(toolCallContentMap));
                                String content = prepareToolCallsMessage(messages, argumentsBuffer.toString(), toolCallMap, processDTO.getTools());

                                toolCallContentMap.put("type", "tool_response");
                                JSONObject toolResponseContent = JSON.parseObject(content);
                                toolResponseContent.put("id", uuid);
                                if (chatToolDTO != null) {
                                    toolResponseContent.put("type", chatToolDTO.getType());
                                }
                                toolCallContentMap.put("content", toolResponseContent);
                                sseEmitter.send(JSON.toJSONString(toolCallContentMap));
                                argumentsBuffer.setLength(0);

                                chatCompletionRequest.setMessages(messages);
                                isCallToos = true;
                                break;
                            } catch (Exception e) {
                                log.error("解析工具参数错误：{}", argumentsBuffer);
                            }
                        }
                    } else {
                        ChatMessage message = chatCompletionChunk.getChoices().getFirst().getMessage();
                        if (message.getReasoningContent() != null && !message.getReasoningContent().isEmpty()) {
                            String reasoningContent = message.getReasoningContent();
                            Map<String, Object> reasoningContentMap = new HashMap<>();
                            reasoningContentMap.put("type", "reasoningContent");
                            reasoningContentMap.put("content", reasoningContent);
                            log.info("ReasoningContent: {}", reasoningContentMap);
                            sseEmitter.send(JSON.toJSONString(reasoningContentMap));
                        }
                        Object content = message.getContent();
                        if (content != null && ObjectUtil.isNotEmpty(content)) {
                            Map<String, Object> contentMap = new HashMap<>();
                            contentMap.put("type", "content");
                            contentMap.put("content", content);
                            log.info("Content: {}", contentMap);
                            sseEmitter.send(JSON.toJSONString(contentMap));
                        }
                    }
                }
            }

            if (!isCallToos) {
                aiModelUsageToken.setStatus("S");
                aiModelUsageToken.setPromptTokens(promptTokens);
                aiModelUsageToken.setCompletionTokens(completionTokens);
                aiModelUsageToken.setTotalTokens(totalTokens);
                aiModelUsageToken.setCreateTime(LocalDateTime.now());
                aiModelUsageMapper.insert(aiModelUsageToken);

                sseEmitter.send("[end]");
                sseEmitter.complete();
                break;
            }

            whileCount--;
            if (whileCount == 0) {
                log.warn(StrUtil.format("大模型{}的循环调用次数超过限定次数，请检查逻辑是否正确！", processDTO.getModelType()));
                aiModelUsageToken.setStatus("F");
                aiModelUsageToken.setReason("流式大模型调用失败");
                aiModelUsageToken.setCreateTime(LocalDateTime.now());
                aiModelUsageMapper.insert(aiModelUsageToken);

                sseEmitter.send("流式大模型调用失败，请稍后重试！");
                sseEmitter.send("[end]");
                sseEmitter.complete();
            }
        }

        // shutdown service
        service.shutdownExecutor();
    }

    private String prepareToolCallsMessage(List<ChatMessage> messages, String arguments,
                                           Map<String, Integer> toolCallMap, List<ChatToolDTO> tools) {
        ChatMessage chatMessage = messages.getLast();
        if (chatMessage.getRole().equals(ChatMessageRole.ASSISTANT)) {
            if (CollUtil.isNotEmpty(chatMessage.getToolCalls())) {
                //Function call
                String toolName = chatMessage.getToolCalls().getFirst().getFunction().getName();
                if (StringUtils.isNotBlank(toolName)) {
                    if (StrUtil.isEmpty(arguments)) {
                        arguments = chatMessage.getToolCalls().getFirst().getFunction().getArguments();
                    }
                    Map<String, Object> argumentsMap = JSON.parseObject(arguments, Map.class);
                    String content = callChartTool(toolName, argumentsMap, tools);
                    ChatMessage _chatMessage = new ChatMessage();
                    _chatMessage.setRole(ChatMessageRole.TOOL);
                    _chatMessage.setToolCallId(chatMessage.getToolCalls().getFirst().getId());
                    _chatMessage.setContent(content);
                    _chatMessage.setName(toolName);
                    messages.add(_chatMessage);

                    int functionCallCount = toolCallMap.getOrDefault(toolName, 0);
                    toolCallMap.put(toolName, functionCallCount + 1);

                    return content;
                }
            }
        }
        return "";
    }

    public String processNonStream(String apiKey, ModelProcessDTO processDTO) {
        StringBuilder sb = new StringBuilder();

        ArkService service = buildArkService(processDTO.getOauthClientId(), apiKey);
        List<ChatMessage> messages = prepareMessage(processDTO);
        ChatCompletionRequest chatCompletionRequest = createChatCompletionRequest(processDTO, messages);

        Map<String, Integer> toolCallMap = new HashMap<>();

        //如果配置了tools，则根据tools的数量 + 1作为最大次数
        int whileCount = 1;
        if (CollUtil.isNotEmpty(processDTO.getTools())) {
            whileCount = processDTO.getTools().size() + 1;
        }

        AiModelUsageToken aiModelUsageToken = new AiModelUsageToken();
        aiModelUsageToken.setClientId(processDTO.getOauthClientId());
        aiModelUsageToken.setModelCode(processDTO.getModelId());

        long promptTokens = 0;
        long completionTokens = 0;
        long totalTokens = 0;
        while (whileCount > 0) {
            boolean isCallToos = false;

            // Function Call 重复调用次数限制，限制为1次，后续根据实际情况调整
            for (String key : toolCallMap.keySet()) {
                int functionCallCount = toolCallMap.get(key);
                if (functionCallCount > 1) {
                    log.warn(StrUtil.format("大模型{}的function call，相同的函数调用超过指定限制次数！请更换模型尝试", processDTO.getModelType()));
                    aiModelUsageToken.setStatus("F");
                    aiModelUsageToken.setReason("大模型调用工具失败");
                    aiModelUsageToken.setPromptTokens(promptTokens);
                    aiModelUsageToken.setCompletionTokens(completionTokens);
                    aiModelUsageToken.setTotalTokens(totalTokens);
                    aiModelUsageToken.setCreateTime(LocalDateTime.now());
                    aiModelUsageMapper.insert(aiModelUsageToken);
                    return "大模型调用工具失败";
                }
            }

            ChatCompletionResult chatCompletionResult = service.createChatCompletion(chatCompletionRequest);
            for (ChatCompletionChoice choice : chatCompletionResult.getChoices()) {
                List<ChatToolCall> toolCalls = choice.getMessage().getToolCalls();
                if (CollUtil.isNotEmpty(toolCalls)) {
                    messages.add(choice.getMessage());

                    if (sb.isEmpty()) {
                        sb.append("{");
                    }

                    String finalFunctionName = toolCalls.getFirst().getFunction().getName();
                    ChatToolDTO chatToolDTO = processDTO.getTools().stream()
                            .filter(item -> item.getChatTool().getFunction().getName().equals(finalFunctionName))
                            .findFirst().orElse(null);

                    String uuid = UUIDGenerator.getUUID();
                    JSONObject toolCallContent = new JSONObject();
                    toolCallContent.put("id", uuid);
                    if (chatToolDTO != null) {
                        toolCallContent.put("type", chatToolDTO.getType());
                    }
                    toolCallContent.put("name", finalFunctionName);
                    toolCallContent.put("arguments", toolCalls.getFirst().getFunction().getArguments());
                    sb.append("\"function_call\": %s".formatted(JSON.toJSONString(toolCallContent)));
                    sb.append(",");

                    String content = prepareToolCallsMessage(messages, null, toolCallMap, processDTO.getTools());
                    log.info("callTool: {}", content);

                    JSONObject toolResponseContent = JSON.parseObject(content);
                    toolResponseContent.put("id", uuid);
                    if (chatToolDTO != null) {
                        toolResponseContent.put("type", chatToolDTO.getType());
                    }
                    sb.append("\"tool_response\": %s".formatted(JSON.toJSONString(toolResponseContent)));
                    sb.append(",");

                    chatCompletionRequest.setMessages(messages);
                    isCallToos = true;
                    break;
                } else {
                    if (sb.isEmpty()) {
                        sb.append("{");
                    }
                    if (choice.getMessage().getReasoningContent() != null) {
                        sb.append("\"reasoningContent\": %s".formatted(JSON.toJSONString(choice.getMessage().getReasoningContent())));
                        sb.append(",");
                    }
                    sb.append("\"content\": %s".formatted(JSON.toJSONString(choice.getMessage().getContent())));
                    sb.append("}");
                }
            }
            promptTokens += chatCompletionResult.getUsage().getPromptTokens();
            completionTokens += chatCompletionResult.getUsage().getCompletionTokens();
            totalTokens += chatCompletionResult.getUsage().getTotalTokens();
            if (!isCallToos) {
                aiModelUsageToken.setStatus("S");
                aiModelUsageToken.setPromptTokens(promptTokens);
                aiModelUsageToken.setCompletionTokens(completionTokens);
                aiModelUsageToken.setTotalTokens(totalTokens);
                aiModelUsageToken.setCreateTime(LocalDateTime.now());
                aiModelUsageMapper.insert(aiModelUsageToken);
                break;
            }
            whileCount--;
            if (whileCount == 0) {
                log.warn(StrUtil.format("大模型{}的循环调用次数超过限定次数，请检查逻辑是否正确！", processDTO.getModelType()));
                aiModelUsageToken.setStatus("F");
                aiModelUsageToken.setReason("大模型调用失败");
                aiModelUsageToken.setPromptTokens(promptTokens);
                aiModelUsageToken.setCompletionTokens(completionTokens);
                aiModelUsageToken.setTotalTokens(totalTokens);
                aiModelUsageToken.setCreateTime(LocalDateTime.now());
                aiModelUsageMapper.insert(aiModelUsageToken);
                return "大模型调用失败，请稍后重试！";
            }
        }

        // shutdown service
        service.shutdownExecutor();
        log.info("大模型调用结果:{}", sb);
        return sb.toString();
    }

    private ArkService buildArkService(String oauthClientId, String apiKey) {
        String decryptApiKey = cryptoService.decryptContent(oauthClientId, apiKey);
        return ArkService.builder().apiKey(decryptApiKey).build();
    }

    private List<ChatMessage> prepareMessage(ModelProcessDTO processDTO) {
        final List<ChatMessage> messages = new ArrayList<>();
        log.info("系统消息:{}", processDTO.getSystemMessage());
        if (StrUtil.isNotBlank(processDTO.getSystemMessage())) {
            messages.add(ChatMessage.builder().role(ChatMessageRole.SYSTEM).content(processDTO.getSystemMessage()).build());
        }
        if (processDTO.isMultiTurn()) {
            for (Map<String, String> history : processDTO.getHistories()) {
                history.keySet().forEach(key -> {
                    messages.add(ChatMessage.builder().role(ChatMessageRole.USER).content(key).build());
                    messages.add(ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content(history.get(key)).build());
                });
            }
        }
        log.info("用户消息:{}", processDTO.getUserMessage());

        if (processDTO.isMultimodal()) {
            final List<ChatCompletionContentPart> multiParts = new ArrayList<>();
            if (CollUtil.isNotEmpty(processDTO.getImageUrls())) {
                for (String url : processDTO.getImageUrls()) {
                    String content;
                    if (!processDTO.isUrlSupport()) {
                        content = imageUrlToBase64(url);
                    } else {
                        content = url;
                    }

                    if (content != null) {
                        multiParts.add(
                                ChatCompletionContentPart.builder().type("image_url").imageUrl(
                                        new ChatCompletionContentPart.ChatCompletionContentPartImageURL(content)
                                ).build()
                        );
                    }
                }
            }
            multiParts.add(ChatCompletionContentPart.builder().type("text").text(processDTO.getUserMessage()).build());
            messages.add(ChatMessage.builder().role(ChatMessageRole.USER).multiContent(multiParts).build());
        } else {
            messages.add(ChatMessage.builder().role(ChatMessageRole.USER).content(processDTO.getUserMessage()).build());
        }
        return messages;
    }

    private ChatCompletionRequest createChatCompletionRequest(ModelProcessDTO processDTO, List<ChatMessage> messages) {
        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest.builder()
                .model(processDTO.getModelId())
                .temperature(processDTO.getTemperature())
                .topP(processDTO.getTopP())
                .frequencyPenalty(processDTO.getFrequencyPenalty())
                .presencePenalty(processDTO.getPresencePenalty())
                .maxTokens(processDTO.getMaxTokens())
                .stream(processDTO.isStream())
                .messages(messages)
                .build();
        if (processDTO.isStream()) {
            chatCompletionRequest.setStreamOptions(
                    ChatCompletionRequest.ChatCompletionRequestStreamOptions.of(processDTO.isIncludeUsage())
            );
        }
        if (processDTO.isEnableFunctionCall() && CollUtil.isNotEmpty(processDTO.getTools())) {
            List<ChatTool> tools = new ArrayList<>();
            for (ChatToolDTO toolDTO : processDTO.getTools()) {
                tools.add(toolDTO.getChatTool());
            }
            chatCompletionRequest.setTools(tools);
        }
        return chatCompletionRequest;
    }
}
