package com.fytec.service.llm.doc.baidu;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.fytec.contant.ModelApiKey;
import com.fytec.service.system.SysCryptoService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.ValidationException;
import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class BaiduBaseService {
    // 优化：配置更合理的超时时间和连接池
    protected static final OkHttpClient HTTP_CLIENT = new OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .connectionPool(new ConnectionPool(10, 5, TimeUnit.MINUTES))
            .build();

    private static final String TOKEN_URL = "https://aip.baidubce.com/oauth/2.0/token";
    protected static final String CONTENT_TYPE = "application/x-www-form-urlencoded";
    protected static final MediaType MEDIA_TYPE = MediaType.get(CONTENT_TYPE);

    @Autowired
    private SysCryptoService cryptoService;

    /**
     * 从用户的AK，SK生成鉴权签名（Access Token）
     *
     * @param oauthClientId OAuth客户端ID
     * @return 鉴权签名（Access Token）
     * @throws BaiduApiException 百度API异常
     */
    public String getAccessToken(String oauthClientId) throws BaiduApiException {
        try {
            // 参数验证
            if (StrUtil.isBlank(oauthClientId)) {
                throw new BaiduApiException("OAuth客户端ID不能为空");
            }

            String decryptApiKey = decryptApiKey(oauthClientId);
            String requestBody = buildRequestBody(decryptApiKey);

            Request request = buildTokenRequest(requestBody);

            return executeTokenRequest(request);

        } catch (Exception e) {
            log.error("获取百度Access Token失败, oauthClientId: {}", oauthClientId, e);
            throw new BaiduApiException("获取Access Token失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解密API密钥
     */
    protected String decryptApiKey(String oauthClientId) throws BaiduApiException {
        try {
            return cryptoService.decryptContent(oauthClientId, ModelApiKey.BAIDU_ACCESS_SECRET);
        } catch (Exception e) {
            throw new BaiduApiException("解密API密钥失败", e);
        }
    }

    /**
     * 构建请求体
     */
    private String buildRequestBody(String decryptApiKey) {
        return StrUtil.format(
                "grant_type=client_credentials&client_id={}&client_secret={}",
                ModelApiKey.BAIDU_ACCESS_KEY, decryptApiKey
        );
    }

    /**
     * 构建Token请求
     */
    private Request buildTokenRequest(String requestBody) {
        RequestBody body = RequestBody.create(requestBody, MEDIA_TYPE);
        return new Request.Builder()
                .url(TOKEN_URL)
                .post(body)  // 使用post()方法更简洁
                .addHeader("Content-Type", CONTENT_TYPE)
                .build();
    }

    /**
     * 执行Token请求并解析响应
     */
    private String executeTokenRequest(Request request) throws IOException, BaiduApiException {
        try (Response response = HTTP_CLIENT.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new BaiduApiException("HTTP请求失败，状态码: " + response.code());
            }

            ResponseBody responseBody = response.body();
            if (responseBody == null) {
                throw new BaiduApiException("响应体为空");
            }

            String responseString = responseBody.string();
            JSONObject jsonObject = JSONObject.parseObject(responseString);

            // 检查是否有错误
            if (jsonObject.containsKey("error")) {
                String error = jsonObject.getString("error");
                String errorDescription = jsonObject.getString("error_description");
                throw new BaiduApiException("百度API返回错误: " + error + " - " + errorDescription);
            }

            String accessToken = jsonObject.getString("access_token");
            if (StrUtil.isBlank(accessToken)) {
                throw new BaiduApiException("Access Token为空");
            }

            log.info("成功获取百度Access Token");
            return accessToken;
        }
    }

    /**
     * 百度API自定义异常类
     */
    public static class BaiduApiException extends Exception {
        public BaiduApiException(String message) {
            super(message);
        }

        public BaiduApiException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    public String getFileContentAsBase64(String path, boolean urlEncode) throws IOException {
        if (path == null || path.trim().isEmpty()) {
            throw new IllegalArgumentException("文件路径不能为空");
        }

        try {
            URI uri = URI.create(path);
            URL url = uri.toURL();
            HttpURLConnection connection = null;

            try {
                // 使用 HttpURLConnection 获得更好的控制
                connection = (HttpURLConnection) url.openConnection();
                connection.setConnectTimeout(10000); // 10秒连接超时
                connection.setReadTimeout(30000);    // 30秒读取超时
                connection.setRequestMethod("GET");

                // 检查响应码
                int responseCode = connection.getResponseCode();
                if (responseCode != HttpURLConnection.HTTP_OK) {
                    throw new IOException("HTTP请求失败，响应码: " + responseCode);
                }

                // 获取内容长度，可能为-1（未知长度）
                long contentLength = connection.getContentLengthLong();

                try (InputStream inputStream = new BufferedInputStream(connection.getInputStream())) {
                    byte[] data;

                    if (contentLength > 0 && contentLength <= Integer.MAX_VALUE) {
                        // 已知长度且合理，预分配数组
                        data = readWithKnownLength(inputStream, (int) contentLength);
                    } else {
                        // 未知长度或过大，使用动态读取
                        data = readWithUnknownLength(inputStream);
                    }

                    if (data.length == 0) {
                        throw new IOException("文件内容为空");
                    }

                    String base64 = Base64.getEncoder().encodeToString(data);

                    if (urlEncode) {
                        return URLEncoder.encode(base64, StandardCharsets.UTF_8);
                    }

                    return base64;
                }
            } finally {
                if (connection != null) {
                    connection.disconnect();
                }
            }
        } catch (MalformedURLException e) {
            throw new IllegalArgumentException("无效的URL: " + path, e);
        }
    }

    /**
     * 已知长度时的优化读取
     */
    private byte[] readWithKnownLength(InputStream inputStream, int contentLength) throws IOException {
        byte[] data = new byte[contentLength];
        int totalBytesRead = 0;

        while (totalBytesRead < contentLength) {
            int bytesRead = inputStream.read(data, totalBytesRead, contentLength - totalBytesRead);
            if (bytesRead == -1) {
                // 实际内容少于声明长度，调整数组大小
                return Arrays.copyOf(data, totalBytesRead);
            }
            totalBytesRead += bytesRead;
        }

        return data;
    }

    /**
     * 未知长度时的动态读取
     */
    private byte[] readWithUnknownLength(InputStream inputStream) throws IOException {
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        byte[] chunk = new byte[8192]; // 8KB缓冲区
        int bytesRead;

        while ((bytesRead = inputStream.read(chunk)) != -1) {
            buffer.write(chunk, 0, bytesRead);
        }

        return buffer.toByteArray();
    }
}
