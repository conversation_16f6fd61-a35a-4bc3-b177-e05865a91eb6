package com.fytec.service.llm.strategy.chat;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class ModelProcessStrategyFactory {

    private static final Map<String, ModelProcessStrategy> STRATEGY_CONCURRENT_HASH_MAP = new ConcurrentHashMap<>();

    /**
     * 获取策略的方法
     *
     * @param key 策略类型
     */
    public static ModelProcessStrategy getStrategy(String key) {
        return STRATEGY_CONCURRENT_HASH_MAP.get(key);
    }

    /**
     * 在Bean属性初始化后执行该方法
     * 注册策略
     *
     * @param key                  策略类型
     * @param modelProcessStrategy 策略处理接口
     */
    public static void register(String key, ModelProcessStrategy modelProcessStrategy) {
        STRATEGY_CONCURRENT_HASH_MAP.put(key, modelProcessStrategy);
    }
}
