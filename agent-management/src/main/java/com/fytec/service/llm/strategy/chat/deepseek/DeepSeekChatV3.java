package com.fytec.service.llm.strategy.chat.deepseek;

import com.fytec.contant.ModelApiKey;
import com.fytec.contant.enums.ModelTypeEnum;
import com.fytec.dto.llm.ModelProcessDTO;
import com.fytec.service.llm.strategy.chat.DoubaoChatBasic;
import com.fytec.service.llm.strategy.chat.ModelProcessStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;


@Slf4j
@Component
public class DeepSeekChatV3 extends DoubaoChatBasic implements InitializingBean, ModelProcessStrategy {
    private final ModelTypeEnum modelType = ModelTypeEnum.deep_seek_chat_v3;

    @Override
    public void handleStream(ModelProcessDTO processDTO, SseEmitter sseEmitter) {
        log.info("\n----- start request {}:{} stream -----", modelType.name(), modelType.getModelId());
        processDTO.setModelId(modelType.getModelId());
        processDTO.setEnableFunctionCall(modelType.isEnableFunctionCall());
        processStream(ModelApiKey.DOUBAO_API_KEY, processDTO, sseEmitter);
    }

    @Override
    public String handle(ModelProcessDTO processDTO) {
        log.info("\n----- start request {}  -----", modelType.name());
        processDTO.setModelId(modelType.getModelId());
        processDTO.setEnableFunctionCall(modelType.isEnableFunctionCall());
        String result = processNonStream(ModelApiKey.DOUBAO_API_KEY, processDTO);
        log.info("\n----- end request {}  -----", modelType.name());
        return result;
    }

    @Override
    public void afterPropertiesSet() {
        afterPropertiesSet(modelType.name());
    }
}
