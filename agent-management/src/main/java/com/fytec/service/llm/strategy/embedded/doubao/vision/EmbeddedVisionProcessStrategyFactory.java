package com.fytec.service.llm.strategy.embedded.doubao.vision;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class EmbeddedVisionProcessStrategyFactory {

    private static final Map<String, EmbeddedVisionProcessStrategy> STRATEGY_CONCURRENT_HASH_MAP = new ConcurrentHashMap<>();

    /**
     * 获取策略的方法
     *
     * @param key 策略类型
     */
    public static EmbeddedVisionProcessStrategy getStrategy(String key) {
        return STRATEGY_CONCURRENT_HASH_MAP.get(key);
    }

    /**
     * 在Bean属性初始化后执行该方法
     * 注册策略
     *
     * @param key                  策略类型
     * @param embeddedProcessStrategy 策略处理接口
     */
    public static void register(String key, EmbeddedVisionProcessStrategy embeddedProcessStrategy) {
        STRATEGY_CONCURRENT_HASH_MAP.put(key, embeddedProcessStrategy);
    }
}
