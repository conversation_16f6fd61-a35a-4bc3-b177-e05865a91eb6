package com.fytec.service.llm.strategy.embedded.doubao.vision;

import com.fytec.contant.ModelApiKey;
import com.fytec.contant.enums.EmbeddedTypeEnum;
import com.fytec.dto.llm.EmbeddedProcessDTO;
import com.fytec.service.llm.strategy.embedded.doubao.text.EmbeddedProcessStrategy;
import com.volcengine.ark.runtime.model.multimodalembeddings.MultimodalEmbedding;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class DoubaoEmbeddingVision241215 extends DoubaoEmbeddedVisionBasic implements InitializingBean, EmbeddedVisionProcessStrategy {
    private final EmbeddedTypeEnum embeddedType = EmbeddedTypeEnum.doubao_embedding_vision_241215;


    @Override
    public MultimodalEmbedding embedded(EmbeddedProcessDTO processDTO) {
        log.info("\n----- start request {}  -----", embeddedType.name());
        processDTO.setModelId(embeddedType.getModelId());
        return embeddedBasic(ModelApiKey.DOUBAO_API_KEY, processDTO);
    }

    @Override
    public void afterPropertiesSet() {
        afterPropertiesSet(embeddedType.name());
    }
}
