package com.fytec.service.llm.strategy.txt2img.doubao;

import com.fytec.dto.llm.Txt2ImgProcessDTO;
import com.fytec.dto.response.Txt2ImgDTO;
import com.fytec.service.system.SysCryptoService;
import com.volcengine.ark.runtime.model.images.generation.GenerateImagesRequest;
import com.volcengine.ark.runtime.model.images.generation.ImagesResponse;
import com.volcengine.ark.runtime.service.ArkService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;


@Slf4j
@Component
public class DoubaoTxt2ImgBasic {
    @Resource
    private SysCryptoService cryptoService;

    public List<Txt2ImgDTO> txt2imgBasic(String apiKey, Txt2ImgProcessDTO processDTO) {
        String decryptApiKey = cryptoService.decryptContent(processDTO.getOauthClientId(), apiKey);

        ConnectionPool connectionPool = new ConnectionPool(5, 1, TimeUnit.SECONDS);
        Dispatcher dispatcher = new Dispatcher();
        ArkService service = ArkService.builder().dispatcher(dispatcher).connectionPool(connectionPool).apiKey(decryptApiKey).build();
        if (StringUtils.isBlank(processDTO.getFormat())) {
            processDTO.setFormat("b64_json");
        }
        GenerateImagesRequest generateRequest = GenerateImagesRequest.builder()
                .model(processDTO.getModelId())
                .prompt(processDTO.getInputs())
                .size(processDTO.getSize())
                .responseFormat(processDTO.getFormat())
                .guidanceScale(processDTO.getGuidanceScale())
                .watermark(processDTO.getWatermark())
                .build();

        ImagesResponse imagesResponse = service.generateImages(generateRequest);
//        // 保存好图片
        ArrayList<Txt2ImgDTO> txt2ImgList = new ArrayList<>();
        for (ImagesResponse.Image data : imagesResponse.getData()) {
            Txt2ImgDTO e = new Txt2ImgDTO();
            e.setB64Json(data.getB64Json());
            e.setUrl(data.getUrl());
            txt2ImgList.add(e);
        }
        return txt2ImgList;
    }
}
