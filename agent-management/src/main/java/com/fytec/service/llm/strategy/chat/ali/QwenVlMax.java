package com.fytec.service.llm.strategy.chat.ali;

import com.fytec.contant.ModelApiKey;
import com.fytec.contant.enums.ModelTypeEnum;
import com.fytec.dto.llm.ModelProcessDTO;
import com.fytec.service.llm.strategy.chat.ModelProcessStrategy;
import com.fytec.service.llm.strategy.chat.OpenAiChatBasic;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;


@Slf4j
@Component
public class QwenVlMax extends OpenAiChatBasic implements InitializingBean, ModelProcessStrategy {
    private final ModelTypeEnum modelType = ModelTypeEnum.ali_qwen_vl_max;

    @Override
    public void handleStream(ModelProcessDTO processDTO, SseEmitter sseEmitter) {
        log.info("\n----- start request {} stream -----", modelType.name());
        processDTO.setModelId(modelType.getModelId());
        processDTO.setModelBaseUrl("https://dashscope.aliyuncs.com/compatible-mode/v1");
        processDTO.setEnableFunctionCall(modelType.isEnableFunctionCall());
        processDTO.setMultimodal(true);
        processDTO.setUrlSupport(true);
        processStream(ModelApiKey.ALI_API_KEY, processDTO, sseEmitter);
    }

    @Override
    public String handle(ModelProcessDTO processDTO) {
        log.info("\n----- start request {}  -----", modelType.name());
        processDTO.setModelId(modelType.getModelId());
        processDTO.setModelBaseUrl("https://dashscope.aliyuncs.com/compatible-mode/v1");
        processDTO.setEnableFunctionCall(modelType.isEnableFunctionCall());
        processDTO.setMultimodal(true);
        processDTO.setUrlSupport(true);
        return processNonStream(ModelApiKey.ALI_API_KEY, processDTO);
    }

    @Override
    public void afterPropertiesSet() {
        afterPropertiesSet(modelType.name());
    }
}
