package com.fytec.service.llm.strategy.chat.kimi;

import com.fytec.contant.ModelApiKey;
import com.fytec.contant.enums.ModelTypeEnum;
import com.fytec.dto.llm.ModelProcessDTO;
import com.fytec.service.llm.strategy.chat.DoubaoChatBasic;
import com.fytec.service.llm.strategy.chat.ModelProcessStrategy;
import com.fytec.service.llm.strategy.chat.OpenAiChatBasic;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;


@Slf4j
@Component
public class KimiK2Chat extends OpenAiChatBasic implements InitializingBean, ModelProcessStrategy {
    private final ModelTypeEnum modelType = ModelTypeEnum.kimi_k2;

    @Override
    public void handleStream(ModelProcessDTO processDTO, SseEmitter sseEmitter) {
        log.info("\n----- start request {} stream -----", modelType.name());
        processDTO.setModelId(modelType.getModelId());
        processDTO.setModelBaseUrl("https://api.moonshot.cn/v1");
        processStream(ModelApiKey.KIMI_API_KEY, processDTO, sseEmitter);
    }

    @Override
    public String handle(ModelProcessDTO processDTO) {
        log.info("\n----- start request {}  -----", modelType.name());
        processDTO.setModelId(modelType.getModelId());
        processDTO.setModelBaseUrl("https://api.moonshot.cn/v1");
        return processNonStream(ModelApiKey.KIMI_API_KEY, processDTO);
    }

    @Override
    public void afterPropertiesSet() {
        afterPropertiesSet(modelType.name());
    }
}
