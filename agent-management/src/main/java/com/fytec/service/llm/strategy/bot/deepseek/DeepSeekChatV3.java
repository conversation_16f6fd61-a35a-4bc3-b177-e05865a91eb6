package com.fytec.service.llm.strategy.bot.deepseek;

import com.fytec.contant.ModelApiKey;
import com.fytec.contant.enums.BotTypeEnum;
import com.fytec.dto.llm.ModelProcessDTO;
import com.fytec.service.llm.strategy.bot.BotProcessStrategy;
import com.fytec.service.llm.strategy.bot.DoubaoBotBasic;
import com.fytec.service.llm.strategy.chat.ModelProcessStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;


@Slf4j
@Component(value = "botDeepSeekChatV3")
public class DeepSeekChatV3 extends DoubaoBotBasic implements InitializingBean, BotProcessStrategy {
    private final BotTypeEnum botType = BotTypeEnum.deep_seek_chat_v3;

    @Override
    public void handleStream(ModelProcessDTO processDTO, SseEmitter sseEmitter) {
        log.info("\n----- start request {} bot stream -----", botType.name());
        processDTO.setModelId(botType.getModelId());
        processDTO.setEnableFunctionCall(botType.isEnableFunctionCall());
        processStream(ModelApiKey.DOUBAO_API_KEY, processDTO, sseEmitter);
    }

    @Override
    public String handle(ModelProcessDTO processDTO) {
        log.info("\n----- start request {} bot -----", botType.name());
        processDTO.setModelId(botType.getModelId());
        processDTO.setEnableFunctionCall(botType.isEnableFunctionCall());
        return processNonStream(ModelApiKey.DOUBAO_API_KEY, processDTO);
    }

    @Override
    public void afterPropertiesSet() {
        afterPropertiesSet(botType.name());
    }
}
