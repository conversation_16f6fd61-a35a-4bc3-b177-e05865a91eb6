package com.fytec.service.llm.strategy.chat;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fytec.dto.llm.ChatToolDTO;
import com.fytec.dto.llm.ModelProcessDTO;
import com.fytec.entity.llm.AiModelUsageToken;
import com.fytec.mapper.llm.AiModelUsageMapper;
import com.fytec.service.llm.strategy.FunctionCallService;
import com.fytec.service.system.SysCryptoService;
import com.fytec.util.UUIDGenerator;
import com.openai.client.OpenAIClient;
import com.openai.client.OpenAIClientAsync;
import com.openai.client.okhttp.OpenAIOkHttpClient;
import com.openai.client.okhttp.OpenAIOkHttpClientAsync;
import com.openai.core.JsonValue;
import com.openai.core.http.StreamResponse;
import com.openai.models.FunctionDefinition;
import com.openai.models.FunctionParameters;
import com.openai.models.chat.completions.*;
import com.volcengine.ark.runtime.model.completion.chat.ChatTool;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.time.LocalDateTime;
import java.util.*;


@Slf4j
@Component
public class OpenAiChatBasic extends FunctionCallService {

    @Resource
    private AiModelUsageMapper aiModelUsageMapper;

    @Resource
    private SysCryptoService cryptoService;

    @SneakyThrows
    public void processStream(String apiKey, ModelProcessDTO processDTO, SseEmitter sseEmitter) {
        OpenAIClient client = buildClient(processDTO.getOauthClientId(), apiKey, processDTO.getModelBaseUrl());
        List<ChatCompletionMessageParam> messages = prepareMessage(processDTO);
        ChatCompletionCreateParams chatCompletionCreateParams = createChatCompletionCreateParams(processDTO, messages);

        Map<String, Integer> toolCallMap = new HashMap<>();

        //如果配置了tools，则根据tools的数量 + 1作为最大次数
        int whileCount = 1;
        if (CollUtil.isNotEmpty(processDTO.getTools())) {
            whileCount = processDTO.getTools().size() + 1;
        }


        AiModelUsageToken aiModelUsageToken = new AiModelUsageToken();
        aiModelUsageToken.setClientId(processDTO.getOauthClientId());
        aiModelUsageToken.setModelCode(processDTO.getModelId());

        long promptTokens = 0;
        long completionTokens = 0;
        long totalTokens = 0;
        String functionName = "";
        StringBuilder argumentsBuffer = new StringBuilder();
        while (whileCount > 0) {
            // Function Call 重复调用次数限制，限制为1次，后续根据实际情况调整
            for (String key : toolCallMap.keySet()) {
                int functionCallCount = toolCallMap.get(key);
                if (functionCallCount > 1) {
                    log.warn(StrUtil.format("大模型{}的function call，相同的函数调用超过指定限制次数！请更换模型尝试", processDTO.getModelType()));
                    aiModelUsageToken.setStatus("F");
                    aiModelUsageToken.setReason("流式大模型调用工具失败");
                    aiModelUsageToken.setCreateTime(LocalDateTime.now());
                    aiModelUsageMapper.insert(aiModelUsageToken);

                    sseEmitter.send("流式大模型调用工具失败！");
                    sseEmitter.send("[end]");
                    sseEmitter.complete();
                }
            }

            boolean isCallToos = false;
            try (StreamResponse<ChatCompletionChunk> chatCompletion = client.chat().completions().createStreaming(chatCompletionCreateParams)) {
                Iterator<ChatCompletionChunk> chatCompletionChunks = chatCompletion.stream().iterator();
                while (chatCompletionChunks.hasNext()) {
                    ChatCompletionChunk chatCompletionChunk = chatCompletionChunks.next();
                    if (chatCompletionChunk.usage().isPresent()) {
                        promptTokens += chatCompletionChunk.usage().get().promptTokens();
                        completionTokens += chatCompletionChunk.usage().get().completionTokens();
                        totalTokens += chatCompletionChunk.usage().get().totalTokens();
                    }
                    if (CollUtil.isNotEmpty(chatCompletionChunk.choices())) {
                        ChatCompletionChunk.Choice.Delta delta = chatCompletionChunk.choices().getFirst().delta();
                        if (delta.toolCalls().isPresent()) {
                            for (ChatCompletionChunk.Choice.Delta.ToolCall toolCall : delta.toolCalls().get()) {
                                if (StrUtil.isNotBlank(toolCall.id().get())) {
                                    functionName = delta.toolCalls().get().getFirst().function().get().name().get();

                                    ChatCompletionAssistantMessageParam.Builder builder = new ChatCompletionAssistantMessageParam.Builder();
                                    builder.role(JsonValue.from("assistant"));
                                    ChatCompletionMessageToolCall.Builder toolCallBuilder = ChatCompletionMessageToolCall.builder();
                                    toolCallBuilder.id(delta.toolCalls().get().getFirst()._id());
                                    toolCallBuilder.type(JsonValue.from("function"));
                                    ChatCompletionMessageToolCall.Function.Builder functionBuilder = ChatCompletionMessageToolCall.Function.builder();
                                    functionBuilder.name(delta.toolCalls().get().getFirst().function().get()._name());
                                    functionBuilder.arguments(delta.toolCalls().get().getFirst().function().get()._arguments());
                                    toolCallBuilder.function(functionBuilder.build());
                                    builder.addToolCall(toolCallBuilder.build());
                                    ChatCompletionAssistantMessageParam chatCompletionAssistantMessageParam = builder.build();
                                    messages.add(ChatCompletionMessageParam.ofAssistant(chatCompletionAssistantMessageParam));
                                }
                                argumentsBuffer.append(toolCall.function().get().arguments().get());
                            }
                            if (StrUtil.isNotBlank(argumentsBuffer.toString())) {
                                try {
                                    JSON.parseObject(argumentsBuffer.toString());
                                    // 工具参数解析成功，则调用工具
                                    String finalFunctionName = functionName;
                                    ChatToolDTO chatToolDTO = processDTO.getTools().stream()
                                            .filter(item -> item.getChatTool().getFunction().getName().equals(finalFunctionName))
                                            .findFirst().orElse(null);

                                    Map<String, Object> toolCallContentMap = new HashMap<>();
                                    toolCallContentMap.put("type", "function_call");
                                    String uuid = UUIDGenerator.getUUID();
                                    JSONObject toolCallContent = new JSONObject();
                                    toolCallContent.put("id", uuid);
                                    if (chatToolDTO != null) {
                                        toolCallContent.put("type", chatToolDTO.getType());
                                    }
                                    toolCallContent.put("name", functionName);
                                    toolCallContent.put("arguments", argumentsBuffer.toString());
                                    toolCallContentMap.put("content", toolCallContent);
                                    sseEmitter.send(JSON.toJSONString(toolCallContentMap));

                                    String content = prepareToolCallsMessage(messages, argumentsBuffer.toString(), toolCallMap, processDTO.getTools());

                                    toolCallContentMap.put("type", "tool_response");
                                    JSONObject toolResponseContent = JSON.parseObject(content);
                                    toolResponseContent.put("id", uuid);
                                    if (chatToolDTO != null) {
                                        toolResponseContent.put("type", chatToolDTO.getType());
                                    }
                                    toolCallContentMap.put("content", toolResponseContent);
                                    sseEmitter.send(JSON.toJSONString(toolCallContentMap));
                                    argumentsBuffer.setLength(0);

                                    chatCompletionCreateParams = chatCompletionCreateParams.toBuilder().messages(messages).build();
                                    isCallToos = true;
                                    break;
                                } catch (Exception e) {
                                    log.error("解析工具参数错误：{}", argumentsBuffer);
                                }
                            }
                            break;
                        } else {
                            if (delta._additionalProperties().containsKey("reasoning_content")
                                    && delta._additionalProperties().get("reasoning_content").asString().isPresent()) {
                                Object reasoningContent = delta._additionalProperties().get("reasoning_content").asString().get();
                                Map<String, Object> reasoningContentMap = new HashMap<>();
                                reasoningContentMap.put("type", "reasoningContent");
                                reasoningContentMap.put("content", reasoningContent);
                                log.info("ReasoningContent: {}", reasoningContentMap);
                                sseEmitter.send(JSON.toJSONString(reasoningContentMap));
                            }

                            if (delta.content().isPresent()) {
                                String content = delta.content().get();
                                Map<String, Object> contentMap = new HashMap<>();
                                contentMap.put("type", "content");
                                contentMap.put("content", content);
                                log.info("Content: {}", contentMap);
                                sseEmitter.send(JSON.toJSONString(contentMap));
                            }
                        }
                    }
                }
            }

            if (!isCallToos) {
                aiModelUsageToken.setStatus("S");
                aiModelUsageToken.setPromptTokens(promptTokens);
                aiModelUsageToken.setCompletionTokens(completionTokens);
                aiModelUsageToken.setTotalTokens(totalTokens);
                aiModelUsageToken.setCreateTime(LocalDateTime.now());
                aiModelUsageMapper.insert(aiModelUsageToken);

                sseEmitter.send("[end]");
                sseEmitter.complete();
                break;
            }
            whileCount--;
            if (whileCount == 0) {
                log.warn(StrUtil.format("大模型{}的循环调用次数超过限定次数，请检查逻辑是否正确！", processDTO.getModelType()));
                aiModelUsageToken.setStatus("F");
                aiModelUsageToken.setReason("流式大模型调用失败");
                aiModelUsageToken.setCreateTime(LocalDateTime.now());
                aiModelUsageMapper.insert(aiModelUsageToken);
                sseEmitter.send("流式大模型调用失败，请稍后重试！");
                sseEmitter.send("[end]");
                sseEmitter.complete();
            }
        }
    }

    private String prepareToolCallsMessage(List<ChatCompletionMessageParam> messages,
                                           String arguments,
                                           Map<String, Integer> toolCallMap,
                                           List<ChatToolDTO> tools) {
        ChatCompletionMessageParam chatMessage = messages.getLast();
        if (chatMessage.isAssistant()) {
            ChatCompletionAssistantMessageParam chatCompletionAssistantMessageParam = chatMessage.asAssistant();
            if (chatCompletionAssistantMessageParam.toolCalls().isPresent()) {
                //Function call
                String toolName = chatCompletionAssistantMessageParam.toolCalls().get().getFirst().function().name();
                if (StrUtil.isEmpty(arguments)) {
                    arguments = chatCompletionAssistantMessageParam.toolCalls().get().getFirst().function().arguments();
                }
                Map<String, Object> argumentsMap = JSON.parseObject(arguments, Map.class);
                String content = callChartTool(toolName, argumentsMap, tools);
                ChatCompletionToolMessageParam toolMessage = ChatCompletionToolMessageParam.builder()
                        .role(JsonValue.from("tool"))
                        .toolCallId(chatCompletionAssistantMessageParam.toolCalls().get().getFirst().id())
                        .content(content)
                        .putAdditionalProperty("name", JsonValue.from(toolName))
                        .build();
                messages.add(ChatCompletionMessageParam.ofTool(toolMessage));

                int functionCallCount = toolCallMap.getOrDefault(toolName, 0);
                toolCallMap.put(toolName, functionCallCount + 1);

                return content;
            }
        }
        return "";
    }


    public String processNonStream(String apiKey, ModelProcessDTO processDTO) {
        StringBuilder sb = new StringBuilder();

        OpenAIClient client = buildClient(processDTO.getOauthClientId(), apiKey, processDTO.getModelBaseUrl());
        List<ChatCompletionMessageParam> messages = prepareMessage(processDTO);
        ChatCompletionCreateParams chatCompletionCreateParams = createChatCompletionCreateParams(processDTO, messages);

        Map<String, Integer> toolCallMap = new HashMap<>();


        //如果配置了tools，则根据tools的数量 + 1作为最大次数
        int whileCount = 1;
        if (CollUtil.isNotEmpty(processDTO.getTools())) {
            whileCount = processDTO.getTools().size() + 1;
        }


        AiModelUsageToken aiModelUsageToken = new AiModelUsageToken();
        aiModelUsageToken.setClientId(processDTO.getOauthClientId());
        aiModelUsageToken.setModelCode(processDTO.getModelId());

        long promptTokens = 0;
        long completionTokens = 0;
        long totalTokens = 0;
        while (whileCount > 0) {
            boolean isCallToos = false;

            // Function Call 重复调用次数限制，限制为1次，后续根据实际情况调整
            for (String key : toolCallMap.keySet()) {
                int functionCallCount = toolCallMap.get(key);
                if (functionCallCount > 1) {
                    log.warn(StrUtil.format("大模型{}的function call，相同的函数调用超过指定限制次数！请更换模型尝试", processDTO.getModelType()));
                    aiModelUsageToken.setStatus("F");
                    aiModelUsageToken.setReason("大模型调用工具失败");
                    aiModelUsageToken.setPromptTokens(promptTokens);
                    aiModelUsageToken.setCompletionTokens(completionTokens);
                    aiModelUsageToken.setTotalTokens(totalTokens);
                    aiModelUsageToken.setCreateTime(LocalDateTime.now());
                    aiModelUsageMapper.insert(aiModelUsageToken);
                    return "大模型调用工具失败";
                }
            }

            ChatCompletion chatCompletion = client.chat().completions().create(chatCompletionCreateParams);
            List<ChatCompletion.Choice> choices = chatCompletion.choices();
            for (ChatCompletion.Choice choice : choices) {
                if (choice.message().toolCalls().isPresent()) {
                    messages.add(ChatCompletionMessageParam.ofAssistant(choice.message().toParam()));

                    if (sb.isEmpty()) {
                        sb.append("{");
                    }

                    String finalFunctionName = choice.message().toolCalls().get().getFirst().function().name();
                    ChatToolDTO chatToolDTO = processDTO.getTools().stream()
                            .filter(item -> item.getChatTool().getFunction().getName().equals(finalFunctionName))
                            .findFirst().orElse(null);

                    String uuid = UUIDGenerator.getUUID();
                    JSONObject toolCallContent = new JSONObject();
                    toolCallContent.put("id", uuid);
                    if (chatToolDTO != null) {
                        toolCallContent.put("type", chatToolDTO.getType());
                    }
                    toolCallContent.put("name", finalFunctionName);
                    toolCallContent.put("arguments", choice.message().toolCalls().get().getFirst().function().arguments());
                    sb.append("\"function_call\": %s".formatted(JSON.toJSONString(toolCallContent)));
                    sb.append(",");

                    String content = prepareToolCallsMessage(messages, null, toolCallMap, processDTO.getTools());

                    JSONObject toolResponseContent = JSON.parseObject(content);
                    toolResponseContent.put("id", uuid);
                    if (chatToolDTO != null) {
                        toolResponseContent.put("type", chatToolDTO.getType());
                    }
                    sb.append("\"tool_response\": %s".formatted(JSON.toJSONString(toolResponseContent)));
                    sb.append(",");

                    chatCompletionCreateParams = chatCompletionCreateParams.toBuilder().messages(messages).build();
                    isCallToos = true;
                    break;
                } else {
                    if (sb.isEmpty()) {
                        sb.append("{");
                    }
                    if (choice.message()._additionalProperties().containsKey("reasoningContent")
                            && choice.message()._additionalProperties().get("reasoningContent").asString().isPresent()) {
                        sb.append("\"reasoningContent\": %s".formatted(
                                        JSON.toJSONString(
                                                choice.message()._additionalProperties().get("reasoningContent").asString().get())
                                )
                        );
                        sb.append(",");
                    }
                    sb.append("\"content\": %s".formatted(JSON.toJSONString(choice.message().content().get())));
                    sb.append("}");
                }
            }
            if (chatCompletion.usage().isPresent()) {
                promptTokens += chatCompletion.usage().get().promptTokens();
                completionTokens += chatCompletion.usage().get().completionTokens();
                totalTokens += chatCompletion.usage().get().totalTokens();
            }
            if (!isCallToos) {
                aiModelUsageToken.setStatus("S");
                aiModelUsageToken.setPromptTokens(promptTokens);
                aiModelUsageToken.setCompletionTokens(completionTokens);
                aiModelUsageToken.setTotalTokens(totalTokens);
                aiModelUsageToken.setCreateTime(LocalDateTime.now());
                aiModelUsageMapper.insert(aiModelUsageToken);
                break;
            }

            whileCount--;
            if (whileCount == 0) {
                log.warn(StrUtil.format("大模型{}的循环调用次数超过限定次数，请检查逻辑是否正确！", processDTO.getModelType()));
                aiModelUsageToken.setStatus("F");
                aiModelUsageToken.setReason("大模型调用失败");
                aiModelUsageToken.setPromptTokens(promptTokens);
                aiModelUsageToken.setCompletionTokens(completionTokens);
                aiModelUsageToken.setTotalTokens(totalTokens);
                aiModelUsageToken.setCreateTime(LocalDateTime.now());
                aiModelUsageMapper.insert(aiModelUsageToken);
                return "大模型调用失败，请稍后重试！";
            }
        }
        return sb.toString();
    }

    private OpenAIClient buildClient(String oauthClientId, String apiKey, String baseUrl) {
        String decryptApiKey = cryptoService.decryptContent(oauthClientId, apiKey);
        return OpenAIOkHttpClient.builder()
                .apiKey(decryptApiKey)
                .baseUrl(baseUrl)
                .build();
    }


    private OpenAIClientAsync buildAsyncClient(String oauthClientId, String apiKey, String baseUrl) {
        String decryptApiKey = cryptoService.decryptContent(oauthClientId, apiKey);
        return OpenAIOkHttpClientAsync.builder()
                .apiKey(decryptApiKey)
                .baseUrl(baseUrl)
                .build();
    }

    private List<ChatCompletionMessageParam> prepareMessage(ModelProcessDTO processDTO) {
        List<ChatCompletionMessageParam> messages = new ArrayList<>();
        if (StrUtil.isNotBlank(processDTO.getSystemMessage())) {
            messages.add(ChatCompletionMessageParam.ofSystem(
                            ChatCompletionSystemMessageParam.builder().role(JsonValue.from("system")).content(processDTO.getSystemMessage()).build()
                    )
            );
        }
        if (processDTO.isMultiTurn()) {
            for (Map<String, String> history : processDTO.getHistories()) {
                for (Map.Entry<String, String> entry : history.entrySet()) {
                    messages.add(ChatCompletionMessageParam.ofUser(
                                    ChatCompletionUserMessageParam.builder().role(JsonValue.from("user")).content(entry.getKey()).build()
                            )
                    );
                    messages.add(ChatCompletionMessageParam.ofAssistant(
                                    ChatCompletionAssistantMessageParam.builder().role(JsonValue.from("assistant")).content(entry.getValue()).build()
                            )
                    );
                }
            }
        }

        if (processDTO.isMultimodal()) {
            final List<ChatCompletionContentPart> multiParts = new ArrayList<>();
            if (CollUtil.isNotEmpty(processDTO.getImageUrls())) {
                for (String url : processDTO.getImageUrls()) {
                    String content;
                    if (!processDTO.isUrlSupport()) {
                        content = imageUrlToBase64(url);
                    } else {
                        content = url;
                    }
                    if (content != null) {
                        multiParts.add(
                                ChatCompletionContentPart.ofImageUrl(
                                        ChatCompletionContentPartImage.builder().imageUrl(
                                                ChatCompletionContentPartImage.ImageUrl.builder().url(content).build()
                                        ).build()
                                )
                        );
                    }
                }
            }
            multiParts.add(
                    ChatCompletionContentPart.ofText(
                            ChatCompletionContentPartText.builder().text(processDTO.getUserMessage()).build()
                    )
            );
            messages.add(
                    ChatCompletionMessageParam.ofUser(
                            ChatCompletionUserMessageParam.builder().role(JsonValue.from("user"))
                                    .content(
                                            ChatCompletionUserMessageParam.Content.ofArrayOfContentParts(multiParts)
                                    )
                                    .build()
                    )
            );
        } else {
            messages.add(ChatCompletionMessageParam.ofUser(
                            ChatCompletionUserMessageParam.builder().role(JsonValue.from("user")).content(processDTO.getUserMessage()).build()
                    )
            );
        }
        return messages;
    }

    private ChatCompletionCreateParams createChatCompletionCreateParams(ModelProcessDTO processDTO, List<ChatCompletionMessageParam> messages) {
        ChatCompletionCreateParams.Builder paramsBuilder = ChatCompletionCreateParams.builder()
                .model(processDTO.getModelId())
                .temperature(processDTO.getTemperature())
                .topP(processDTO.getTopP())
                .frequencyPenalty(processDTO.getFrequencyPenalty())
                .presencePenalty(processDTO.getPresencePenalty())
                .maxCompletionTokens(processDTO.getMaxTokens())
                .messages(messages);
        if (processDTO.isStream()) {
            paramsBuilder.streamOptions(
                    ChatCompletionStreamOptions.builder().includeUsage(processDTO.isIncludeUsage()).build()
            );
        }
        if (processDTO.isEnableFunctionCall() && CollUtil.isNotEmpty(processDTO.getTools())) {
            List<ChatTool> chatTools = new ArrayList<>();
            for (ChatToolDTO toolDTO : processDTO.getTools()) {
                chatTools.add(toolDTO.getChatTool());
            }
            List<ChatCompletionTool> tools = new ArrayList<>();
            for (ChatTool tool : chatTools) {
                ChatCompletionTool.Builder chatCompletionToolBuilder = ChatCompletionTool.builder();
                chatCompletionToolBuilder.type(JsonValue.from("function"));

                FunctionDefinition.Builder functionDefinitionBuilder = FunctionDefinition.builder();
                functionDefinitionBuilder.name(tool.getFunction().getName());
                functionDefinitionBuilder.description(tool.getFunction().getDescription());

                FunctionParameters.Builder functionParametersBuilder = FunctionParameters.builder();
                JsonNode parameters = tool.getFunction().getParameters();
                Map<String, JsonValue> result = new HashMap<>();
                parameters.fields().forEachRemaining(entry -> {
                    result.put(entry.getKey(), JsonValue.from(entry.getValue()));
                });
                functionParametersBuilder.additionalProperties(result);

                functionDefinitionBuilder.parameters(functionParametersBuilder.build());
                chatCompletionToolBuilder.function(functionDefinitionBuilder.build());
                ChatCompletionTool chatCompletionTool = chatCompletionToolBuilder.build();
                tools.add(chatCompletionTool);
            }
            paramsBuilder.tools(tools);
        }

        return paramsBuilder.build();
    }
}
