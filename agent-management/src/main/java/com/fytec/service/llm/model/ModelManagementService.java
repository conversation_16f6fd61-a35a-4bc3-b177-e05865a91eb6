package com.fytec.service.llm.model;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.llm.*;
import com.fytec.entity.llm.AiModel;
import com.fytec.entity.llm.AiModelGroup;
import com.fytec.mapper.llm.AiModelGroupMapper;
import com.fytec.mapper.llm.AiModelMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class ModelManagementService {
    private final AiModelMapper aiModelMapper;
    private final AiModelGroupMapper aiModelGroupMapper;

    private boolean isExist(String code) {
        long count = aiModelMapper.selectCount(
                new LambdaQueryWrapper<AiModel>()
                        .eq(AiModel::getCode, code)
        );
        return count > 0;
    }

    public void addAiModel(AiModelCreateDTO aiModelCreateDTO) {
        if (isExist(aiModelCreateDTO.getCode())) {
            throw new ValidateException("模型编码已存在");
        }
        AiModel aiModel = new AiModel();
        BeanUtil.copyProperties(aiModelCreateDTO, aiModel);
        aiModel.setDiversityParams(JSON.toJSONString(aiModelCreateDTO.getDiversityParams()));
        aiModel.setIoParams(JSON.toJSONString(aiModelCreateDTO.getIoParams()));
        aiModel.setEnable(true);
        aiModelMapper.insert(aiModel);
    }

    public void editAiModel(AiModelUpdateDTO aiModelUpdateDTO) {
        AiModel aiModel = aiModelMapper.selectById(aiModelUpdateDTO.getId());
        if (aiModel == null) {
            throw new ValidateException("模型不存在");
        }
        if (!StrUtil.equals(aiModelUpdateDTO.getCode(), aiModel.getCode())) {
            if (isExist(aiModelUpdateDTO.getCode())) {
                throw new ValidateException("模型编码已存在");
            }
        }
        BeanUtil.copyProperties(aiModelUpdateDTO, aiModel);
        aiModel.setDiversityParams(JSON.toJSONString(aiModelUpdateDTO.getDiversityParams()));
        aiModel.setIoParams(JSON.toJSONString(aiModelUpdateDTO.getIoParams()));
        aiModelMapper.updateById(aiModel);
    }

    public void updateAiModelStatus(Long id, boolean isEnable) {
        AiModel aiModel = aiModelMapper.selectById(id);
        if (aiModel == null) {
            throw new ValidateException("模型不存在");
        }
        aiModel.setEnable(isEnable);
        aiModelMapper.updateById(aiModel);
    }

    public Page<AiModelListDTO> queryAiModelPaging(AiModelQueryDTO aiModelQueryDTO, Page<AiModelListDTO> page) {
        page.setRecords(aiModelMapper.queryAiModelPaging(page, aiModelQueryDTO));
        return page;
    }

    public AiModel queryAiModelDetail(Long id) {
        return aiModelMapper.selectById(id);
    }

    public List<AiModel> queryAiModelSyncList() {
        return aiModelMapper.selectList(null);
    }


    private boolean isGroupExist(String name) {
        long count = aiModelGroupMapper.selectCount(
                new LambdaQueryWrapper<AiModelGroup>()
                        .eq(AiModelGroup::getGroupName, name)
        );
        return count > 0;
    }

    public Long addAiModelGroup(AiModelGroupCreateDTO dto) {
        if (isGroupExist(dto.getGroupName())) {
            throw new ValidateException("模型组已存在");
        }
        AiModelGroup aiModelGroup = new AiModelGroup();
        BeanUtil.copyProperties(dto, aiModelGroup);
        aiModelGroupMapper.insert(aiModelGroup);
        return aiModelGroup.getId();
    }

    public void editAiModelGroup(AiModelGroupUpdateDTO dto) {
        AiModelGroup aiModelGroup = aiModelGroupMapper.selectById(dto.getId());
        if (aiModelGroup == null) {
            throw new ValidateException("模型组不存在");
        }
        if (!StrUtil.equals(dto.getGroupName(), aiModelGroup.getGroupName())) {
            if (isGroupExist(dto.getGroupName())) {
                throw new ValidateException("模型组已存在");
            }
        }
        BeanUtil.copyProperties(dto, aiModelGroup);
        aiModelGroupMapper.updateById(aiModelGroup);
    }

    public void deleteAiModel(Long id) {
        long count = aiModelMapper.selectCount(
                new LambdaQueryWrapper<AiModel>().eq(AiModel::getGroupId, id)
        );
        if (count > 0) {
            throw new ValidateException("该模型组下有模型，请先删除模型组下的所有模型");
        }
        aiModelGroupMapper.deleteById(id);
    }

    public Page<AiModelGroupListDTO> queryAiModelGroupPaging(AiModelGroupQueryDTO dto, Page<AiModelGroupListDTO> page) {
        page.setRecords(aiModelGroupMapper.queryAiModelGroupPaging(page, dto));
        return page;
    }

    public AiModelGroup queryAiModelGroupDetail(Long id) {
        return aiModelGroupMapper.selectById(id);
    }
}
