package com.fytec.service.llm.strategy.bot;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.fytec.dto.llm.ChatToolDTO;
import com.fytec.dto.llm.ModelProcessDTO;
import com.fytec.entity.llm.AiModelUsageToken;
import com.fytec.mapper.llm.AiModelUsageMapper;
import com.fytec.mapper.token.OauthClientDetailMapper;
import com.fytec.service.llm.strategy.FunctionCallProperties;
import com.fytec.service.llm.strategy.FunctionCallService;
import com.fytec.service.system.SysCryptoService;
import com.volcengine.ark.runtime.model.bot.completion.chat.BotChatCompletionChunk;
import com.volcengine.ark.runtime.model.bot.completion.chat.BotChatCompletionRequest;
import com.volcengine.ark.runtime.model.bot.completion.chat.BotChatCompletionResult;
import com.volcengine.ark.runtime.model.bot.completion.chat.reference.BotChatResultReference;
import com.volcengine.ark.runtime.model.bot.completion.chat.usage.BotModelUsage;
import com.volcengine.ark.runtime.model.bot.completion.chat.usage.BotUsage;
import com.volcengine.ark.runtime.model.completion.chat.*;
import com.volcengine.ark.runtime.service.ArkService;
import io.reactivex.Flowable;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;


@Slf4j
public class DoubaoBotBasic extends FunctionCallService {
    private static final List<String> DATE_PATTERNS = Arrays.asList(
            "yyyy年MM月dd日 HH:mm:ss(z) E",  // 如 "2025年06月10日 00:00:00(CST) 星期二"
            "yyyy-MM-dd HH:mm:ss"          // 如 "2025-06-10 00:00:00"
    );
    private final WebClient webClient = WebClient.create();
    @Resource
    private AiModelUsageMapper aiModelUsageMapper;
    @Resource
    private SysCryptoService cryptoService;
    @Resource
    private FunctionCallProperties functionCallProperties;

    @Resource
    private OauthClientDetailMapper oauthClientDetailMapper;

    @SneakyThrows
    public void processStream(String apiKey, ModelProcessDTO processDTO, SseEmitter sseEmitter) {
        ArkService service = buildArkService(processDTO.getOauthClientId(), apiKey);
        List<ChatMessage> messages = prepareMessage(processDTO);
        BotChatCompletionRequest chatCompletionRequest = createBotCompletionRequest(processDTO, messages);

        Map<String, Integer> toolCallMap = new HashMap<>();

        //如果配置了tools，则根据tools的数量 + 1作为最大次数
        int whileCount = 1;
        if (CollUtil.isNotEmpty(processDTO.getTools())) {
            whileCount = processDTO.getTools().size() + 1;
        }


        AiModelUsageToken aiModelUsageToken = new AiModelUsageToken();
        aiModelUsageToken.setClientId(processDTO.getOauthClientId());
        aiModelUsageToken.setModelCode(processDTO.getModelId());

        long promptTokens = 0;
        long completionTokens = 0;
        long totalTokens = 0;
        StringBuilder argumentsBuffer = new StringBuilder();
        while (whileCount > 0) {
            boolean isCallToos = false;

            // Function Call 重复调用次数限制，限制为1次，后续根据实际情况调整
            for (String key : toolCallMap.keySet()) {
                int functionCallCount = toolCallMap.get(key);
                if (functionCallCount > 1) {
                    log.warn(StrUtil.format("大模型{}的function call，相同的函数调用超过指定限制次数！请更换模型尝试", processDTO.getModelType()));

                    aiModelUsageToken.setStatus("F");
                    aiModelUsageToken.setReason("流式大模型调用工具失败");
                    aiModelUsageToken.setCreateTime(LocalDateTime.now());
                    aiModelUsageMapper.insert(aiModelUsageToken);

                    sseEmitter.send("流式大模型调用工具失败！");
                    sseEmitter.send("[end]");
                    sseEmitter.complete();
                }
            }

            Flowable<BotChatCompletionChunk> chatCompletionChunkFlowable = service.streamBotChatCompletion(chatCompletionRequest);
            Iterable<BotChatCompletionChunk> chatCompletionChunks = chatCompletionChunkFlowable.blockingIterable();
            for (BotChatCompletionChunk chatCompletionChunk : chatCompletionChunks) {
                if (chatCompletionChunk.getBotUsage() != null) {
                    BotUsage botUsage = chatCompletionChunk.getBotUsage();
                    for (BotModelUsage botModelUsage : botUsage.getModelUsage()) {
                        promptTokens += botModelUsage.getPromptTokens();
                        completionTokens += botModelUsage.getCompletionTokens();
                        totalTokens += botModelUsage.getTotalTokens();
                    }
                }
                if (CollUtil.isNotEmpty(chatCompletionChunk.getReferences())) {
                    for (BotChatResultReference reference : chatCompletionChunk.getReferences()) {
                        Map<String, Object> referenceContentMap = new HashMap<>();
                        Map<String, Object> referenceMap = new HashMap<>();
                        referenceMap.put("url", reference.getUrl());
                        referenceMap.put("logo_url", reference.getLogoUrl());
                        referenceMap.put("title", reference.getTitle());
                        referenceMap.put("site_name", reference.getSiteName());
                        referenceMap.put("summary", reference.getSummary());

                        String outputDate = reference.getPublishTime();
                        for (String pattern : DATE_PATTERNS) {
                            try {
                                DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(pattern);
                                LocalDateTime dateTime = LocalDateTime.parse(reference.getPublishTime(), inputFormatter);
                                outputDate = dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                            } catch (DateTimeParseException ignored) {
                            }
                        }
                        referenceMap.put("publish_time", outputDate);
                        referenceContentMap.put("type", "referenceContent");
                        referenceContentMap.put("content", referenceMap);
                        sseEmitter.send(JSON.toJSONString(referenceContentMap));
                    }
                }
                if (CollUtil.isNotEmpty(chatCompletionChunk.getChoices())) {
                    List<ChatToolCall> toolCalls = chatCompletionChunk.getChoices().getFirst().getMessage().getToolCalls();
                    if (CollUtil.isNotEmpty(toolCalls)) {
                        for (ChatToolCall toolCall : toolCalls) {
                            if (StrUtil.isNotBlank(toolCall.getId())) {
                                messages.add(chatCompletionChunk.getChoices().getFirst().getMessage());
                            }
                            argumentsBuffer.append(toolCall.getFunction().getArguments());
                        }

                        if (StrUtil.isNotBlank(argumentsBuffer.toString())) {
                            try {
                                JSON.parseObject(argumentsBuffer.toString());
                                // 工具参数解析成功，则调用工具
                                prepareToolCallsMessage(messages, argumentsBuffer.toString(), toolCallMap, processDTO.getTools());
                                chatCompletionRequest.setMessages(messages);
                                isCallToos = true;
                                break;
                            } catch (Exception e) {
                                log.error("解析工具参数错误：{}", argumentsBuffer);
                            }
                        }
                    } else {
                        ChatMessage message = chatCompletionChunk.getChoices().getFirst().getMessage();
                        if (message.getReasoningContent() != null && !message.getReasoningContent().isEmpty()) {
                            String reasoningContent = message.getReasoningContent();
                            Map<String, Object> reasoningContentMap = new HashMap<>();
                            reasoningContentMap.put("type", "reasoningContent");
                            reasoningContentMap.put("content", reasoningContent);
                            log.info("ReasoningContent: {}", reasoningContentMap);
                            sseEmitter.send(JSON.toJSONString(reasoningContentMap));
                        }
                        Object content = message.getContent();
                        if (content != null && ObjectUtil.isNotEmpty(content)) {
                            Map<String, Object> contentMap = new HashMap<>();
                            contentMap.put("type", "content");
                            contentMap.put("content", content);
                            log.info("Content: {}", contentMap);
                            sseEmitter.send(JSON.toJSONString(contentMap));
                        }
                    }
                }
            }

            if (!isCallToos) {
                aiModelUsageToken.setStatus("S");
                aiModelUsageToken.setPromptTokens(promptTokens);
                aiModelUsageToken.setCompletionTokens(completionTokens);
                aiModelUsageToken.setTotalTokens(totalTokens);
                aiModelUsageToken.setCreateTime(LocalDateTime.now());
                aiModelUsageMapper.insert(aiModelUsageToken);

                sseEmitter.send("[end]");
                sseEmitter.complete();
                break;
            }

            whileCount--;
            if (whileCount == 0) {
                log.warn(StrUtil.format("大模型{}的循环调用次数超过限定次数，请检查逻辑是否正确！", processDTO.getModelType()));
                aiModelUsageToken.setStatus("F");
                aiModelUsageToken.setReason("流式大模型调用失败");
                aiModelUsageToken.setCreateTime(LocalDateTime.now());
                aiModelUsageMapper.insert(aiModelUsageToken);

                sseEmitter.send("流式大模型调用失败，请稍后重试！");
                sseEmitter.send("[end]");
                sseEmitter.complete();
            }
        }

        // shutdown service
        service.shutdownExecutor();
    }

    private void prepareToolCallsMessage(List<ChatMessage> messages,
                                         String arguments,
                                         Map<String, Integer> toolCallMap,
                                         List<ChatToolDTO> tools) {
        ChatMessage chatMessage = messages.getLast();
        if (chatMessage.getRole().equals(ChatMessageRole.ASSISTANT)) {
            if (CollUtil.isNotEmpty(chatMessage.getToolCalls())) {
                //Function call
                String toolName = chatMessage.getToolCalls().getFirst().getFunction().getName();
                if (StringUtils.isNotBlank(toolName)) {
                    if (StrUtil.isEmpty(arguments)) {
                        arguments = chatMessage.getToolCalls().getFirst().getFunction().getArguments();
                    }
                    Map<String, Object> argumentsMap = JSON.parseObject(arguments, Map.class);
                    String content = callChartTool(toolName, argumentsMap, tools);
                    ChatMessage _chatMessage = new ChatMessage();
                    _chatMessage.setRole(ChatMessageRole.TOOL);
                    _chatMessage.setToolCallId(chatMessage.getToolCalls().getFirst().getId());
                    _chatMessage.setContent(content);
                    _chatMessage.setName(toolName);
                    messages.add(_chatMessage);
                }
                int functionCallCount = toolCallMap.getOrDefault(toolName, 0);
                toolCallMap.put(toolName, functionCallCount + 1);
            }
        }
    }

    public String processNonStream(String apiKey, ModelProcessDTO processDTO) {
        StringBuilder sb = new StringBuilder();

        ArkService service = buildArkService(processDTO.getOauthClientId(), apiKey);
        List<ChatMessage> messages = prepareMessage(processDTO);
        BotChatCompletionRequest chatCompletionRequest = createBotCompletionRequest(processDTO, messages);

        Map<String, Integer> toolCallMap = new HashMap<>();

        //如果配置了tools，则根据tools的数量 + 1作为最大次数
        int whileCount = 1;
        if (CollUtil.isNotEmpty(processDTO.getTools())) {
            whileCount = processDTO.getTools().size() + 1;
        }

        AiModelUsageToken aiModelUsageToken = new AiModelUsageToken();
        aiModelUsageToken.setClientId(processDTO.getOauthClientId());
        aiModelUsageToken.setModelCode(processDTO.getModelId());

        long promptTokens = 0;
        long completionTokens = 0;
        long totalTokens = 0;
        while (whileCount > 0) {
            boolean isCallToos = false;

            // Function Call 重复调用次数限制，限制为1次，后续根据实际情况调整
            for (String key : toolCallMap.keySet()) {
                int functionCallCount = toolCallMap.get(key);
                if (functionCallCount > 1) {
                    log.warn(StrUtil.format("大模型{}的function call，相同的函数调用超过指定限制次数！请更换模型尝试", processDTO.getModelType()));
                    aiModelUsageToken.setStatus("F");
                    aiModelUsageToken.setReason("大模型调用工具失败");
                    aiModelUsageToken.setPromptTokens(promptTokens);
                    aiModelUsageToken.setCompletionTokens(completionTokens);
                    aiModelUsageToken.setTotalTokens(totalTokens);
                    aiModelUsageToken.setCreateTime(LocalDateTime.now());
                    aiModelUsageMapper.insert(aiModelUsageToken);
                    return "大模型调用工具失败";
                }
            }

            BotChatCompletionResult chatCompletionResult = service.createBotChatCompletion(chatCompletionRequest);
            for (ChatCompletionChoice choice : chatCompletionResult.getChoices()) {
                List<ChatToolCall> toolCalls = choice.getMessage().getToolCalls();
                if (CollUtil.isNotEmpty(toolCalls)) {
                    messages.add(choice.getMessage());
                    prepareToolCallsMessage(messages, null, toolCallMap, processDTO.getTools());
                    chatCompletionRequest.setMessages(messages);
                    isCallToos = true;
                    break;
                } else {
                    sb.append("{");
                    if (choice.getMessage().getReasoningContent() != null) {
                        sb.append("\"reasoningContent\": %s".formatted(JSON.toJSONString(choice.getMessage().getReasoningContent())));
                    }
                    sb.append("\"content\": %s".formatted(JSON.toJSONString(choice.getMessage().getContent())));
                    sb.append("}");
                }
            }
            promptTokens += chatCompletionResult.getUsage().getPromptTokens();
            completionTokens += chatCompletionResult.getUsage().getCompletionTokens();
            totalTokens += chatCompletionResult.getUsage().getTotalTokens();
            if (!isCallToos) {
                aiModelUsageToken.setStatus("S");
                aiModelUsageToken.setPromptTokens(promptTokens);
                aiModelUsageToken.setCompletionTokens(completionTokens);
                aiModelUsageToken.setTotalTokens(totalTokens);
                aiModelUsageToken.setCreateTime(LocalDateTime.now());
                aiModelUsageMapper.insert(aiModelUsageToken);
                break;
            }
            whileCount--;
            if (whileCount == 0) {
                log.warn(StrUtil.format("大模型{}的循环调用次数超过限定次数，请检查逻辑是否正确！", processDTO.getModelType()));
                aiModelUsageToken.setStatus("F");
                aiModelUsageToken.setReason("大模型调用失败");
                aiModelUsageToken.setPromptTokens(promptTokens);
                aiModelUsageToken.setCompletionTokens(completionTokens);
                aiModelUsageToken.setTotalTokens(totalTokens);
                aiModelUsageToken.setCreateTime(LocalDateTime.now());
                aiModelUsageMapper.insert(aiModelUsageToken);
                return "大模型调用失败，请稍后重试！";
            }
        }

        // shutdown service
        service.shutdownExecutor();
        return sb.toString();
    }

    private ArkService buildArkService(String oauthClientId, String apiKey) {
        String decryptApiKey = cryptoService.decryptContent(oauthClientId, apiKey);
        return ArkService.builder().apiKey(decryptApiKey).build();
    }

    private List<ChatMessage> prepareMessage(ModelProcessDTO processDTO) {
        final List<ChatMessage> messages = new ArrayList<>();
        messages.add(ChatMessage.builder().role(ChatMessageRole.SYSTEM).content(processDTO.getSystemMessage()).build());
        if (processDTO.isMultiTurn()) {
            for (Map<String, String> history : processDTO.getHistories()) {
                history.keySet().forEach(key -> {
                    messages.add(ChatMessage.builder().role(ChatMessageRole.USER).content(key).build());
                    messages.add(ChatMessage.builder().role(ChatMessageRole.ASSISTANT).content(history.get(key)).build());
                });
            }
        }
        messages.add(ChatMessage.builder().role(ChatMessageRole.USER).content(processDTO.getUserMessage()).build());
        return messages;
    }

    private BotChatCompletionRequest createBotCompletionRequest(ModelProcessDTO processDTO, List<ChatMessage> messages) {
        BotChatCompletionRequest botCompletionRequest = BotChatCompletionRequest.builder()
                .model(processDTO.getModelId())
                .temperature(processDTO.getTemperature())
                .topP(processDTO.getTopP())
                .frequencyPenalty(processDTO.getFrequencyPenalty())
                .presencePenalty(processDTO.getPresencePenalty())
                .maxTokens(processDTO.getMaxTokens())
                .stream(processDTO.isStream())
                .messages(messages)
                .build();
        if (processDTO.isStream()) {
            botCompletionRequest.setStreamOptions(
                    ChatCompletionRequest.ChatCompletionRequestStreamOptions.of(processDTO.isIncludeUsage())
            );
        }
        if (processDTO.isEnableFunctionCall() && CollUtil.isNotEmpty(processDTO.getTools())) {
            List<ChatTool> tools = new ArrayList<>();
            for (ChatToolDTO toolDTO : processDTO.getTools()) {
                tools.add(toolDTO.getChatTool());
            }
            botCompletionRequest.setTools(tools);
        }
        return botCompletionRequest;
    }
}
