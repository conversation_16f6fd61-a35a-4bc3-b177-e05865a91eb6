package com.fytec.service;

import cn.dev33.satoken.secure.BCrypt;
import cn.dev33.satoken.stp.SaTokenInfo;
import cn.hutool.core.exceptions.ValidateException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fytec.dto.LoginDTO;
import com.fytec.entity.system.SysUser;
import com.fytec.mapper.system.SysUserMapper;
import com.fytec.satoken.StpMgmtUserUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class LoginService {
    private final SysUserMapper sysUserMapper;

    public SaTokenInfo generateLoginAccessToken(LoginDTO loginDTO) {
        SysUser sysUser = sysUserMapper.selectOne(
                new LambdaQueryWrapper<SysUser>()
                        .eq(SysUser::getLoginName, loginDTO.getName())
        );

        if (sysUser == null || !BCrypt.checkpw(loginDTO.getPassword(), sysUser.getPassword())) {
            throw new ValidateException("用户名不存在或密码错误");
        }

        StpMgmtUserUtil.login(sysUser.getId());
        return StpMgmtUserUtil.getTokenInfo();
    }

    public void activeLoginAccessToken() {
        StpMgmtUserUtil.updateLastActiveToNow();
    }
}
