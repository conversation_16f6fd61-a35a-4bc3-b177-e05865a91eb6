package com.fytec.service.system;

import cn.dev33.satoken.secure.BCrypt;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.exceptions.ValidateException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.system.*;
import com.fytec.entity.system.SysUser;
import com.fytec.entity.system.SysUserRole;
import com.fytec.mapper.system.SysMenuMapper;
import com.fytec.mapper.system.SysUserMapper;
import com.fytec.mapper.system.SysUserRoleMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class SysUserService {
    private final SysUserMapper sysUserMapper;
    private final SysUserRoleMapper sysUserRoleMapper;
    private final SysMenuMapper sysMenuMapper;


    public boolean checkDuplicateUser(String nameOrTel) {
        Long count = sysUserMapper.checkDuplicateUser(nameOrTel);
        return count > 0;
    }


    public void addUser(SysUserCreateDTO sysUserCreateDTO) {
        if (checkDuplicateUser(sysUserCreateDTO.getLoginName())) {
            throw new ValidateException("登录名已存在");
        }
        if (checkDuplicateUser(sysUserCreateDTO.getTel())) {
            throw new ValidateException("电话已存在");
        }
        SysUser sysUser = new SysUser();
        BeanUtil.copyProperties(sysUserCreateDTO, sysUser, "password");
        sysUser.setPassword(BCrypt.hashpw(sysUserCreateDTO.getPassword()));
        sysUserMapper.insert(sysUser);

        //给用户添加角色
        if (sysUserCreateDTO.getRoleIds() != null) {
            for (Long roleId : sysUserCreateDTO.getRoleIds()) {
                SysUserRole sysUserRole = new SysUserRole();
                sysUserRole.setUserId(sysUser.getId());
                sysUserRole.setRoleId(roleId);
                sysUserRoleMapper.insert(sysUserRole);
            }
        }
    }

    public void updateUser(SysUserUpdateDTO updateSysUserDTO) {
        SysUser sysUser = sysUserMapper.selectById(updateSysUserDTO.getId());
        if (sysUser == null) {
            throw new ValidateException("用户不存在");
        }

        String loginName = updateSysUserDTO.getLoginName();
        if (!sysUser.getLoginName().equals(loginName)) {
            if (checkDuplicateUser(updateSysUserDTO.getLoginName())) {
                throw new ValidateException("登录名已存在");
            }
        }

        String tel = updateSysUserDTO.getTel();
        if (!sysUser.getTel().equals(tel)) {
            if (checkDuplicateUser(updateSysUserDTO.getTel())) {
                throw new ValidateException("电话已存在");
            }
        }

        BeanUtil.copyProperties(updateSysUserDTO, sysUser, "password");
        if (StringUtils.isNotBlank(updateSysUserDTO.getPassword())) {
            sysUser.setPassword(BCrypt.hashpw(updateSysUserDTO.getPassword()));
        }
        sysUserMapper.updateById(sysUser);

        sysUserRoleMapper.delete(
                new LambdaQueryWrapper<SysUserRole>()
                        .eq(SysUserRole::getUserId, sysUser.getId())
        );
        if (updateSysUserDTO.getRoleIds() != null) {
            for (Long roleId : updateSysUserDTO.getRoleIds()) {
                SysUserRole sysUserRole = new SysUserRole();
                sysUserRole.setUserId(sysUser.getId());
                sysUserRole.setRoleId(roleId);
                sysUserRoleMapper.insert(sysUserRole);
            }
        }

    }

    public void deleteUser(Long id) {
        SysUser sysUser = sysUserMapper.selectById(id);
        if (sysUser == null) {
            throw new ValidateException("用户不存在");
        }
        sysUserRoleMapper.delete(
                new LambdaQueryWrapper<SysUserRole>()
                        .eq(SysUserRole::getUserId, sysUser.getId())
        );
        sysUserMapper.deleteById(id);
    }

    public Page<SysUserListDTO> queryUser(Page<SysUserListDTO> page, SysUserQueryDTO searchUserDTO) {
        List<SysUserListDTO> sysUserListDTOList = sysUserMapper.queryUser(page, searchUserDTO);
        page.setRecords(sysUserListDTOList);
        return page;
    }

    public SysUserDetailDTO getUserDetail(Long id) {
        SysUser sysUser = sysUserMapper.selectById(id);

        List<SysUserRole> sysUserRoles = sysUserRoleMapper.selectList(
                new LambdaQueryWrapper<SysUserRole>()
                        .eq(SysUserRole::getUserId, id)
                        .select(SysUserRole::getRoleId)
        );
        List<Long> roleIds = sysUserRoles.stream().map(SysUserRole::getRoleId).toList();

        //设置返回对象
        SysUserDetailDTO result = new SysUserDetailDTO();
        if (sysUser != null) {
            BeanUtils.copyProperties(sysUser, result);
            result.setRoleIds(roleIds);
        }
        return result;
    }
}
