package com.fytec.service.system;

import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.IdUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fytec.dto.system.SysCryptoDTO;
import com.fytec.entity.system.SysCrypto;
import com.fytec.entity.system.SysCryptoData;
import com.fytec.mapper.system.SysCryptoDataMapper;
import com.fytec.mapper.system.SysCryptoMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.util.List;


@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class SysCryptoService {
    private static final String KEY_ALGORITHM = "AES";

    private final SysCryptoMapper cryptoMapper;
    private final SysCryptoDataMapper cryptoDataMapper;

    @Value("${fytec.ai_dept}")
    private String aiDeptClientId;

    public void addSecretKey(String clientId) {
        long count = cryptoMapper.selectCount(
                new LambdaQueryWrapper<SysCrypto>()
                        .eq(SysCrypto::getClientId, clientId)
        );
        if (count > 0) {
            throw new ValidateException("该客户端ID已存在");
        }

        SysCrypto crypto = new SysCrypto();
        crypto.setClientId(clientId);
        crypto.setSecretKey(IdUtil.nanoId(16));
        cryptoMapper.insert(crypto);
    }

    public void encryptContent(SysCryptoDTO sysCryptoDTO) {
        SysCrypto crypto = cryptoMapper.selectOne(
                new LambdaQueryWrapper<SysCrypto>()
                        .eq(SysCrypto::getClientId, sysCryptoDTO.getClientId())
        );
        if (crypto == null) {
            throw new ValidateException("该客户端ID密钥不存在");
        }
        AES aes = new AES(Mode.CFB, Padding.NoPadding,
                new SecretKeySpec(crypto.getSecretKey().getBytes(), KEY_ALGORITHM),
                new IvParameterSpec(crypto.getSecretKey().getBytes())
        );
        String encryptContent = aes.encryptBase64(sysCryptoDTO.getContent());

        SysCryptoData sysCryptoData = new SysCryptoData();
        sysCryptoData.setClientId(sysCryptoDTO.getClientId());
        sysCryptoData.setKeyName(sysCryptoDTO.getKeyName());
        sysCryptoData.setEncryptContent(encryptContent);
        cryptoDataMapper.insert(sysCryptoData);
    }

    public void copyEncryptContent(SysCryptoDTO sysCryptoDTO) {
        SysCrypto crypto = cryptoMapper.selectOne(
                new LambdaQueryWrapper<SysCrypto>()
                        .eq(SysCrypto::getClientId, sysCryptoDTO.getClientId())
        );
        if (crypto == null) {
            throw new ValidateException("该客户端ID密钥不存在");
        }
        cryptoDataMapper.delete(
                new LambdaQueryWrapper<SysCryptoData>()
                        .eq(SysCryptoData::getClientId, sysCryptoDTO.getClientId())
        );
        AES aes_client = new AES(Mode.CFB, Padding.NoPadding,
                new SecretKeySpec(crypto.getSecretKey().getBytes(), KEY_ALGORITHM),
                new IvParameterSpec(crypto.getSecretKey().getBytes())
        );


        SysCrypto aiDeptCrypto = cryptoMapper.selectOne(
                new LambdaQueryWrapper<SysCrypto>()
                        .eq(SysCrypto::getClientId, aiDeptClientId)
        );
        List<SysCryptoData> aiDeptCryptoDataList = cryptoDataMapper.selectList(
                new LambdaQueryWrapper<SysCryptoData>()
                        .eq(SysCryptoData::getClientId, aiDeptClientId)
        );
        AES aes_ai_dept = new AES(Mode.CFB, Padding.NoPadding,
                new SecretKeySpec(aiDeptCrypto.getSecretKey().getBytes(), KEY_ALGORITHM),
                new IvParameterSpec(aiDeptCrypto.getSecretKey().getBytes())
        );
        for (SysCryptoData aiDeptCryptoData : aiDeptCryptoDataList) {
            String decryptContent = aes_ai_dept.decryptStr(aiDeptCryptoData.getEncryptContent());
            String newEncryptContent = aes_client.encryptBase64(decryptContent);
            SysCryptoData newSysCryptoData = new SysCryptoData();
            newSysCryptoData.setClientId(sysCryptoDTO.getClientId());
            newSysCryptoData.setKeyName(aiDeptCryptoData.getKeyName());
            newSysCryptoData.setEncryptContent(newEncryptContent);
            cryptoDataMapper.insert(newSysCryptoData);
        }
    }

    public String decryptContent(String clientId, String keyName) {
        SysCrypto crypto = cryptoMapper.selectOne(
                new LambdaQueryWrapper<SysCrypto>()
                        .eq(SysCrypto::getClientId, clientId)
        );
        AES aes = new AES(Mode.CFB, Padding.NoPadding,
                new SecretKeySpec(crypto.getSecretKey().getBytes(), KEY_ALGORITHM),
                new IvParameterSpec(crypto.getSecretKey().getBytes())
        );

        SysCryptoData cryptoData = cryptoDataMapper.selectOne(
                new LambdaQueryWrapper<SysCryptoData>()
                        .eq(SysCryptoData::getClientId, clientId)
                        .eq(SysCryptoData::getKeyName, keyName)

        );

        return aes.decryptStr(cryptoData.getEncryptContent());
    }
}
