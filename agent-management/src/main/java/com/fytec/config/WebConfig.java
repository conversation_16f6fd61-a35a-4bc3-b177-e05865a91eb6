package com.fytec.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import com.fytec.interceptor.ApiAccessTokenInterceptor;
import com.fytec.interceptor.ApiClientTokenInterceptor;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig implements WebMvcConfigurer {
    @Resource
    private ApiAccessTokenInterceptor apiAccessTokenInterceptor;
    @Resource
    private ApiClientTokenInterceptor apiClientTokenInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new SaInterceptor())
                .addPathPatterns("/**")
                .excludePathPatterns("/fytec/api/**", "/login/**");
        registry.addInterceptor(apiClientTokenInterceptor)
                .addPathPatterns("/**");
        registry.addInterceptor(apiAccessTokenInterceptor)
                .addPathPatterns("/fytec/api/**");
    }
}
