package com.fytec.config;

import jakarta.annotation.PreDestroy;
import jakarta.annotation.Resource;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.concurrent.Executor;

@Component
public class ExecutorServiceManager {
    @Resource
    private Executor executor;

    @PreDestroy
    public void shutdown() {
        if (executor instanceof ThreadPoolTaskExecutor) {
            ((ThreadPoolTaskExecutor) executor).shutdown();
        }
    }
}
