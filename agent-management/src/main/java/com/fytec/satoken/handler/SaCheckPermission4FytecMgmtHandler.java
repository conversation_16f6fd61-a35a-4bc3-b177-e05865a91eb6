package com.fytec.satoken.handler;

import cn.dev33.satoken.annotation.handler.SaAnnotationHandlerInterface;
import cn.dev33.satoken.annotation.handler.SaCheckPermissionHandler;
import com.fytec.satoken.StpMgmtUserUtil;
import com.fytec.satoken.annotation.SaCheckPermission4FytecMgmt;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

@Component
public class SaCheckPermission4FytecMgmtHandler implements SaAnnotationHandlerInterface<SaCheckPermission4FytecMgmt> {
    @Override
    public Class<SaCheckPermission4FytecMgmt> getHandlerAnnotationClass() {
        return SaCheckPermission4FytecMgmt.class;
    }

    @Override
    public void checkMethod(SaCheckPermission4FytecMgmt saCheckPermission4FytecMgmt, Method method) {
        SaCheckPermissionHandler._checkMethod(StpMgmtUserUtil.TYPE, saCheckPermission4FytecMgmt.value(), saCheckPermission4FytecMgmt.mode(), saCheckPermission4FytecMgmt.orRole());
    }
}
