package com.fytec.satoken.annotation;

import cn.dev33.satoken.annotation.SaMode;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD, ElementType.TYPE})
public @interface SaCheckPermission4FytecMgmt {

    String type() default "";

    String[] value() default {};

    SaMode mode() default SaMode.AND;

    String[] orRole() default {};
}
