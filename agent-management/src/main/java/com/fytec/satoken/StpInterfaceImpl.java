package com.fytec.satoken;

import cn.dev33.satoken.SaManager;
import cn.dev33.satoken.stp.StpInterface;
import cn.hutool.core.collection.CollUtil;
import com.fytec.mapper.system.SysUserRoleMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

@Component    // 保证此类被 SpringBoot 扫描，完成 Sa-Token 的自定义权限验证扩展
public class StpInterfaceImpl implements StpInterface {
    @Resource
    private SysUserRoleMapper sysUserRoleMapper;

    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        return List.of();
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<String> getRoleList(Object loginId, String loginType) {
        List<String> roleList = (List<String>) SaManager.getSaTokenDao().getObject("fytec:loginId-find-role:" + loginId);
        if (CollUtil.isEmpty(roleList)) {
            // 从数据库查询这个账号id拥有的角色列表，
            roleList = sysUserRoleMapper.selectRoleListByUserId(loginId);
            // 查好后，set 到缓存中
            SaManager.getSaTokenDao().setObject("fytec:loginId-find-role:" + loginId, roleList, 60 * 60 * 24 * 30);
        }
        return roleList;
    }
}
