package com.fytec.satoken;

import cn.dev33.satoken.oauth2.data.loader.SaOAuth2DataLoader;
import cn.dev33.satoken.oauth2.data.model.loader.SaClientModel;
import cn.hutool.core.util.StrUtil;
import com.fytec.entity.token.OauthClientDetail;
import com.fytec.mapper.token.OauthClientDetailMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class SaOAuth2DataLoaderImpl implements SaOAuth2DataLoader {
    @Resource
    private OauthClientDetailMapper oauthClientDetailMapper;

    @Override
    public SaClientModel getClientModel(String clientId) {
        OauthClientDetail oauthClientDetail = oauthClientDetailMapper.selectById(clientId);
        if (oauthClientDetail == null) {
            return null;
        }

        if (!StrUtil.equals("A", oauthClientDetail.getStatus())) {
            return null;
        }

        return new SaClientModel()
                .setClientId(oauthClientDetail.getClientId())    // client id
                .setClientSecret(oauthClientDetail.getClientSecret())    // client 秘钥
                .addAllowRedirectUris(oauthClientDetail.getAllowRedirectUri().split(";"))    // 所有允许授权的 url
                .addContractScopes(oauthClientDetail.getScope().split(";"))    // 所有签约的权限
                .addAllowGrantTypes(oauthClientDetail.getGrantTypes().split(";")); // 所有允许的授权模式
    }
}
