package com.fytec.mapper.system;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.system.SysRoleListDTO;
import com.fytec.dto.system.SysRoleSearchDTO;
import com.fytec.entity.system.SysRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SysRoleMapper extends BaseMapper<SysRole> {
    List<SysRoleListDTO> searchRole(Page<SysRoleListDTO> page, @Param("param") SysRoleSearchDTO sysRoleSearchDTO);
}
