package com.fytec.mapper.system;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.system.SysUserQueryDTO;
import com.fytec.dto.system.SysUserListDTO;
import com.fytec.entity.system.SysUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SysUserMapper extends BaseMapper<SysUser> {
    Long checkDuplicateUser(String nameOrTel);

    List<SysUserListDTO> queryUser(Page<SysUserListDTO> page, @Param("param") SysUserQueryDTO searchUserDTO);
}
