package com.fytec.mapper.llm;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.llm.AiModelGroupListDTO;
import com.fytec.dto.llm.AiModelGroupQueryDTO;
import com.fytec.entity.llm.AiModelGroup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AiModelGroupMapper extends BaseMapper<AiModelGroup> {
    List<AiModelGroupListDTO> queryAiModelGroupPaging(Page<AiModelGroupListDTO> page, @Param("param") AiModelGroupQueryDTO dto);
}