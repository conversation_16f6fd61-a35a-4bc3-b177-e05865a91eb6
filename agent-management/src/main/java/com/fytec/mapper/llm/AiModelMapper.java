package com.fytec.mapper.llm;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.llm.AiModelListDTO;
import com.fytec.dto.llm.AiModelQueryDTO;
import com.fytec.entity.llm.AiModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AiModelMapper extends BaseMapper<AiModel> {
    List<AiModelListDTO> queryAiModelPaging(Page<AiModelListDTO> page, @Param("param") AiModelQueryDTO aiModelQueryDTO);
}