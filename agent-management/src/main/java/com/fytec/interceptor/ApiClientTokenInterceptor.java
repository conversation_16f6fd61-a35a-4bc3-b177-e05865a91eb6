package com.fytec.interceptor;

import cn.dev33.satoken.oauth2.annotation.SaCheckClientToken;
import cn.hutool.core.util.ArrayUtil;
import com.fytec.mapper.llm.AiModelMapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import java.lang.reflect.Method;

@Component
@RequiredArgsConstructor
public class ApiClientTokenInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 如果不是映射到方法直接通过
        if (!(handler instanceof HandlerMethod handlerMethod)) {
            return true;
        }
        Method method = handlerMethod.getMethod();
        // 判断接口是否有SaCheckClientToken注解
        SaCheckClientToken saCheckClientToken = method.getAnnotation(SaCheckClientToken.class);
        if (saCheckClientToken != null
                && ArrayUtil.contains(saCheckClientToken.scope(), "fytec:llm")) {
//            System.out.println("这里验证大模型是否有权限");
        }
        return true;
    }

}
