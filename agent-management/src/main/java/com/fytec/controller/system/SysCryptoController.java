package com.fytec.controller.system;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.config.validate.Mandatory;
import com.fytec.config.validate.Optional;
import com.fytec.dto.system.*;
import com.fytec.satoken.annotation.SaCheckPermission4FytecMgmt;
import com.fytec.service.system.SysCryptoService;
import com.fytec.service.system.SysUserService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.crypto.KeyGenerator;


@Slf4j
@Validated
@Tag(name = "用户管理")
@RequestMapping("/api/crypto")
@RestController
@RequiredArgsConstructor
public class SysCryptoController {
    private final SysCryptoService cryptoService;

    @PostMapping("/secret-key")
    @Operation(summary = "生成密钥")
    @SaCheckPermission4FytecMgmt(value = "crypto.add", orRole = {"admin"})
    public R<Void> addSecretKey(@Validated @RequestBody SysCryptoDTO sysCryptoDTO) {
        cryptoService.addSecretKey(sysCryptoDTO.getClientId());
        return R.ok();
    }


    @PostMapping("/encrypt")
    @Operation(summary = "加密")
    @SaCheckPermission4FytecMgmt(value = "crypto.encrypt", orRole = {"admin"})
    public R<String> encryptContent(@Validated @RequestBody SysCryptoDTO sysCryptoDTO) {
        cryptoService.encryptContent(sysCryptoDTO);
        return R.ok();
    }


    @PostMapping("/encrypt/copy")
    @Operation(summary = "加密复制")
    @SaCheckPermission4FytecMgmt(value = "crypto.encrypt", orRole = {"admin"})
    public R<String> copyEncryptContent(@Validated @RequestBody SysCryptoDTO sysCryptoDTO) {
        cryptoService.copyEncryptContent(sysCryptoDTO);
        return R.ok();
    }
}
