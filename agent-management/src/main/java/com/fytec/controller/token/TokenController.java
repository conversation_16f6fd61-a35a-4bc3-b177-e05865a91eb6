package com.fytec.controller.token;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.fytec.dto.token.AccessToken4ApiDTO;
import com.fytec.dto.token.OauthClientDetailCreateDTO;
import com.fytec.dto.token.OauthClientDetailDTO;
import com.fytec.dto.token.OauthClientDetailUpdateDTO;
import com.fytec.satoken.StpMgmtUserUtil;
import com.fytec.satoken.annotation.SaCheckPermission4FytecMgmt;
import com.fytec.service.token.TokenService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Validated
@Tag(name = "访问令牌管理")
@RequestMapping("/api/token")
@RestController
@RequiredArgsConstructor
public class TokenController {
    private final TokenService tokenService;

    //#################oauth客户端管理#################
    @PostMapping("/oauth_client/create")
    @Operation(summary = "生成oauth客户端")
    @SaCheckPermission4FytecMgmt(value = "oauth_client:add", orRole = "admin")
    public R<Void> createOauthClient(@Validated @RequestBody OauthClientDetailCreateDTO oauthClientDetailCreateDTO) {
        tokenService.createOauthClient(oauthClientDetailCreateDTO);
        return R.ok();
    }

    @PostMapping("/oauth_client/edit")
    @Operation(summary = "编辑oauth客户端")
    @SaCheckPermission4FytecMgmt(value = "oauth_client:edit", orRole = "admin")
    public R<Void> editOauthClient(@Validated @RequestBody OauthClientDetailUpdateDTO oauthClientDetailUpdateDTO) {
        tokenService.editOauthClient(oauthClientDetailUpdateDTO);
        return R.ok();
    }

    @GetMapping("/oauth_client/enable")
    @Operation(summary = "启用oauth客户端")
    @SaCheckPermission4FytecMgmt(value = "oauth_client:disable", orRole = "admin")
    public R<Void> enableOauthClient(String clientId) {
        tokenService.enableOauthClient(clientId);
        return R.ok();
    }

    @GetMapping("/oauth_client/disable")
    @Operation(summary = "禁用oauth客户端")
    @SaCheckPermission4FytecMgmt(value = "oauth_client:disable", orRole = "admin")
    public R<Void> disableOauthClient(String clientId) {
        tokenService.disableOauthClient(clientId);
        return R.ok();
    }

    @GetMapping("/oauth_client/detail")
    @Operation(summary = "oauth客户端详情")
    @SaCheckPermission4FytecMgmt(value = "oauth_client:detail", orRole = "admin")
    public R<OauthClientDetailDTO> getOauthClientDetail(String clientId) {
        return R.ok(tokenService.getOauthClientDetail(clientId));
    }

    @GetMapping("/oauth_client/list")
    @Operation(summary = "oauth客户端列表")
    @SaCheckPermission4FytecMgmt(value = "oauth_client:list", orRole = "admin")
    public R<List<OauthClientDetailDTO>> queryOauthClientDetail(String clientName) {
        return R.ok(tokenService.queryOauthClientDetail(clientName));
    }
    //#################oauth客户端管理#################


    @PostMapping("/generate_api_access_token")
    @Operation(summary = "生成API访问令牌")
    @SaCheckPermission4FytecMgmt(value = "access_token:create", orRole = "admin")
    public R<String> generateApiAccessToken(@Validated @RequestBody AccessToken4ApiDTO accessToken4ApiDTO) {
        return R.ok(tokenService.generateApiAccessToken(accessToken4ApiDTO));
    }
}
