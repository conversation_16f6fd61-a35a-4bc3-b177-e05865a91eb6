package com.fytec.controller.token;

import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.context.model.SaRequest;
import cn.dev33.satoken.oauth2.SaOAuth2Manager;
import cn.dev33.satoken.oauth2.config.SaOAuth2ServerConfig;
import cn.dev33.satoken.oauth2.consts.GrantType;
import cn.dev33.satoken.oauth2.consts.SaOAuth2Consts;
import cn.dev33.satoken.oauth2.dao.SaOAuth2Dao;
import cn.dev33.satoken.oauth2.data.model.ClientTokenModel;
import cn.dev33.satoken.oauth2.data.model.loader.SaClientModel;
import cn.dev33.satoken.oauth2.data.model.request.ClientIdAndSecretModel;
import cn.dev33.satoken.oauth2.exception.SaOAuth2ClientModelScopeException;
import cn.dev33.satoken.oauth2.exception.SaOAuth2Exception;
import cn.dev33.satoken.oauth2.processor.SaOAuth2ServerProcessor;
import cn.dev33.satoken.oauth2.strategy.SaOAuth2Strategy;
import cn.dev33.satoken.oauth2.template.SaOAuth2Template;
import com.alibaba.fastjson2.JSON;
import com.fytec.util.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@RestController
@Slf4j
public class SaOAuth2ServerController {
    // OAuth2-Server 端：处理所有 OAuth2 相关请求
    @RequestMapping("/oauth2/client_token")
    public Object clientToken() {
        System.out.println("------- 进入请求: " + SaHolder.getRequest().getUrl());
        try {
            SaRequest req = SaHolder.getRequest();
            ClientIdAndSecretModel clientIdAndSecret = SaOAuth2Manager.getDataResolver().readClientIdAndSecret(req);
            String clientId = clientIdAndSecret.clientId;

            SaOAuth2Dao dao = SaOAuth2Manager.getDao();
            ClientTokenModel ct = dao.getClientToken(dao.getClientTokenValue(clientId));
            Object clientToken;
            if (ct == null) {
                clientToken = SaOAuth2ServerProcessor.instance.clientToken();
            } else {
                clientToken = SaOAuth2Manager.getDataResolver().buildClientTokenReturnValue(ct);
            }
            if (clientToken != null) {
                String clientTokenStr = JSON.toJSONString(clientToken);
                Map<String, Object> tokenMap = JSON.parseObject(clientTokenStr);
                tokenMap.put("token_type", "Bearer");
                return tokenMap;
            }
            return R.failed("获取token失败");
        } catch (SaOAuth2ClientModelScopeException e) {
            return R.unauthorized("客户端未授权");
        } catch (Exception e) {
            log.error("获取token失败:{}", e.getMessage());
            return R.failed("获取token失败");
        }
    }
}
