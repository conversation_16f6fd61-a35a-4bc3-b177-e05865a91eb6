package com.fytec.controller;

import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.util.SaResult;
import com.fytec.dto.LoginDTO;
import com.fytec.service.LoginService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Validated
@RequestMapping("")
@RestController
@RequiredArgsConstructor
public class LoginController {
    private final LoginService loginService;


    @PostMapping("/login")
    @Operation(summary = "登录")
    public R<SaTokenInfo> generateLoginAccessToken(@Validated @RequestBody LoginDTO loginDTO) {
        return R.ok(loginService.generateLoginAccessToken(loginDTO));
    }


    @PostMapping("/login/active")
    @Operation(summary = "冻结激活")
    public R<Void> activeLoginAccessToken() {
        loginService.activeLoginAccessToken();
        return R.ok();
    }
}
