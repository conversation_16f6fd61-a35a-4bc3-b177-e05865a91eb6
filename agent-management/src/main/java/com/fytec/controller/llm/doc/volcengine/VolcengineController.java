package com.fytec.controller.llm.doc.volcengine;

import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.oauth2.SaOAuth2Manager;
import cn.dev33.satoken.oauth2.annotation.SaCheckClientToken;
import cn.dev33.satoken.oauth2.template.SaOAuth2Util;
import com.fytec.dto.llm.DocProcessDTO;
import com.fytec.service.llm.doc.volcengine.VolcengineOcrService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Slf4j
@Validated
@Tag(name = "火山引擎OCR服务")
@RequestMapping("/api/volcengine")
@RestController
@RequiredArgsConstructor
public class VolcengineController {

    private final VolcengineOcrService volcengineOcrService;

    @PostMapping(path = "/ocr/normal")
    @Operation(summary = "提交解析任务")
    @SaCheckClientToken(scope = "fytec:llm")
    public String processOrcNormal(@Validated @RequestBody DocProcessDTO dto) {
        String clientToken = SaOAuth2Manager.getDataResolver().readClientToken(SaHolder.getRequest());
        String oauthClientId = SaOAuth2Util.getClientToken(clientToken).getClientId();
        return volcengineOcrService.processOrcNormal(dto.getFileUrl(), dto.getFileContent(), oauthClientId);
    }

    @PostMapping(path = "/ocr/vat-invoice")
    @Operation(summary = "增值税发票解析")
    @SaCheckClientToken(scope = "fytec:llm")
    public Map<String, Object> processOrcVatInvoice(@Validated @RequestBody DocProcessDTO dto) {
        String clientToken = SaOAuth2Manager.getDataResolver().readClientToken(SaHolder.getRequest());
        String oauthClientId = SaOAuth2Util.getClientToken(clientToken).getClientId();
        return volcengineOcrService.processOrcVatInvoice(dto.getFileUrl(), dto.getFileContent(), oauthClientId);
    }
}
