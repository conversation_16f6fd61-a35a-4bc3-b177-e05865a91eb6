package com.fytec.controller.llm.model;

import cn.dev33.satoken.oauth2.annotation.SaCheckClientToken;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fytec.dto.llm.*;
import com.fytec.entity.llm.AiModel;
import com.fytec.entity.llm.AiModelGroup;
import com.fytec.satoken.annotation.SaCheckPermission4FytecMgmt;
import com.fytec.service.llm.model.ModelManagementService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Validated
@Tag(name = "大模型管理")
@RequestMapping("/api/llm-model")
@RestController
@RequiredArgsConstructor
public class ModelManagementController {
    private final ModelManagementService modelManagementService;

    @PostMapping(path = "/group/add")
    @Operation(summary = "添加大模型分组")
    @SaCheckPermission4FytecMgmt(value = "llm:model-group:add", orRole = "admin")
    public R<Long> addAiModelGroup(@RequestBody @Validated AiModelGroupCreateDTO dto) {
        return R.ok(modelManagementService.addAiModelGroup(dto));
    }

    @PostMapping(path = "/group/update")
    @Operation(summary = "编辑大模型分组")
    @SaCheckPermission4FytecMgmt(value = "llm:model-group:edit", orRole = "admin")
    public R<Void> editAiModelGroup(@RequestBody @Validated AiModelGroupUpdateDTO dto) {
        modelManagementService.editAiModelGroup(dto);
        return R.ok();
    }

    @GetMapping(path = "/group/delete")
    @Operation(summary = "删除大模型组")
    @SaCheckPermission4FytecMgmt(value = "llm:model-group:delete", orRole = "admin")
    @Parameter(name = "id", description = "模型组ID")
    public R<Void> deleteAiModel(Long id) {
        modelManagementService.deleteAiModel(id);
        return R.ok();
    }


    @GetMapping(path = "/group/page")
    @Operation(summary = "大模型组列表")
    @SaCheckPermission4FytecMgmt(value = "llm:model-group:page", orRole = "admin")
    public R<Page<AiModelGroupListDTO>> queryAiModelGroupPaging(AiModelGroupQueryDTO dto, Page<AiModelGroupListDTO> page) {
        return R.ok(modelManagementService.queryAiModelGroupPaging(dto, page));
    }

    @GetMapping(path = "/group/detail")
    @Operation(summary = "大模型列表")
    @SaCheckPermission4FytecMgmt(value = "llm:model-group:page", orRole = "admin")
    @Parameter(name = "id", description = "模型ID")
    public R<AiModelGroup> queryAiModelGroupDetail(Long id) {
        return R.ok(modelManagementService.queryAiModelGroupDetail(id));
    }


    @PostMapping(path = "/add")
    @Operation(summary = "添加大模型")
    @SaCheckPermission4FytecMgmt(value = "llm:model:add", orRole = "admin")
    public R<Void> addAiModel(@RequestBody @Validated AiModelCreateDTO aiModelCreateDTO) {
        modelManagementService.addAiModel(aiModelCreateDTO);
        return R.ok();
    }

    @PostMapping(path = "/update")
    @Operation(summary = "编辑大模型")
    @SaCheckPermission4FytecMgmt(value = "llm:model:edit", orRole = "admin")
    public R<Void> editAiModel(@RequestBody @Validated AiModelUpdateDTO aiModelUpdateDTO) {
        modelManagementService.editAiModel(aiModelUpdateDTO);
        return R.ok();
    }

    @GetMapping(path = "/enable")
    @Operation(summary = "启用大模型")
    @SaCheckPermission4FytecMgmt(value = "llm:model:enable", orRole = "admin")
    @Parameter(name = "id", description = "模型ID")
    public R<Void> enableAiModel(Long id) {
        modelManagementService.updateAiModelStatus(id, true);
        return R.ok();
    }

    @GetMapping(path = "/disable")
    @Operation(summary = "禁用大模型")
    @SaCheckPermission4FytecMgmt(value = "llm:model:disable", orRole = "admin")
    @Parameter(name = "id", description = "模型ID")
    public R<Void> disableAiModel(Long id) {
        modelManagementService.updateAiModelStatus(id, false);
        return R.ok();
    }

    @GetMapping(path = "/page")
    @Operation(summary = "大模型列表")
    @SaCheckPermission4FytecMgmt(value = "llm:model:page", orRole = "admin")
    public R<Page<AiModelListDTO>> queryAiModelPaging(AiModelQueryDTO aiModelQueryDTO, Page<AiModelListDTO> page) {
        return R.ok(modelManagementService.queryAiModelPaging(aiModelQueryDTO, page));
    }

    @GetMapping(path = "/detail")
    @Operation(summary = "大模型列表")
    @SaCheckPermission4FytecMgmt(value = "llm:model:page", orRole = "admin")
    @Parameter(name = "id", description = "模型ID")
    public R<AiModel> queryAiModelDetail(Long id) {
        return R.ok(modelManagementService.queryAiModelDetail(id));
    }

    @GetMapping(path = "/sync")
    @Operation(summary = "大模型列表")
    @SaCheckClientToken(scope = "fytec:llm-model")
    public R<List<AiModel>> queryAiModelSyncList() {
        return R.ok(modelManagementService.queryAiModelSyncList());
    }

}
