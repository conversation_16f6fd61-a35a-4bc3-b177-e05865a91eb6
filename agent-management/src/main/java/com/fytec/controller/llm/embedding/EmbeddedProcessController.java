package com.fytec.controller.llm.embedding;

import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.oauth2.SaOAuth2Manager;
import cn.dev33.satoken.oauth2.annotation.SaCheckClientToken;
import cn.dev33.satoken.oauth2.template.SaOAuth2Util;
import com.fytec.dto.llm.EmbeddedProcessDTO;
import com.fytec.service.llm.embedded.EmbeddedService;
import com.fytec.service.llm.model.ModelProcessService;
import com.fytec.util.R;
import com.volcengine.ark.runtime.model.embeddings.Embedding;
import com.volcengine.ark.runtime.model.embeddings.EmbeddingResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;

@Slf4j
@Validated
@Tag(name = "大模型向量化")
@RequestMapping("/api/llm")
@RestController
public class EmbeddedProcessController {

    @Resource
    private EmbeddedService embeddedService;

    @PostMapping(path = "/doubao/embedded")
    @Operation(summary = "调用豆包向量化模型")
    @SaCheckClientToken(scope = "fytec:llm")
    public List<Embedding> processEmbeddedByDoubao(@Validated @RequestBody EmbeddedProcessDTO dto) {
        String clientToken = SaOAuth2Manager.getDataResolver().readClientToken(SaHolder.getRequest());
        String oauthClientId = SaOAuth2Util.getClientToken(clientToken).getClientId();
        dto.setOauthClientId(oauthClientId);

        return embeddedService.processEmbeddedByDoubao(dto);
    }
}
