package com.fytec.controller.llm.model;

import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.oauth2.SaOAuth2Manager;
import cn.dev33.satoken.oauth2.annotation.SaCheckClientToken;
import cn.dev33.satoken.oauth2.template.SaOAuth2Util;
import com.alibaba.fastjson.JSON;
import com.fytec.dto.llm.ModelProcessDTO;
import com.fytec.dto.llm.Txt2ImgProcessDTO;
import com.fytec.dto.response.Txt2ImgDTO;
import com.fytec.service.llm.model.ModelProcessService;
import com.fytec.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Validated
@Tag(name = "大模型处理")
@RequestMapping("/api/llm")
@RestController
public class ModelProcessController {
    @Resource
    private ModelProcessService modelProcessService;

    //###################### Start ChatCompletions ######################
    @PostMapping(path = "/stream")
    @Operation(summary = "调用大模型")
    @SaCheckClientToken(scope = "fytec:llm")
    public SseEmitter processModelByStream(@Validated @RequestBody ModelProcessDTO modelProcessDTO) {
        String clientToken = SaOAuth2Manager.getDataResolver().readClientToken(SaHolder.getRequest());
        String oauthClientId = SaOAuth2Util.getClientToken(clientToken).getClientId();
        modelProcessDTO.setOauthClientId(oauthClientId);

        modelProcessDTO.setStream(true);
        modelProcessDTO.setIncludeUsage(true);

        log.info("processModelByStream: {}", JSON.toJSONString(modelProcessDTO));

        final SseEmitter emitter = modelProcessService.getConn(modelProcessDTO.getClientId());
        CompletableFuture.runAsync(() -> {
            modelProcessService.processModelByStream(modelProcessDTO.getClientId(), modelProcessDTO);
        });
        return emitter;
    }

    @PostMapping(path = "/non-stream")
    @Operation(summary = "调用大模型")
    @SaCheckClientToken(scope = "fytec:llm")
    public R<String> processModelByNonStream(@Validated @RequestBody ModelProcessDTO modelProcessDTO) {
        String clientToken = SaOAuth2Manager.getDataResolver().readClientToken(SaHolder.getRequest());
        String oauthClientId = SaOAuth2Util.getClientToken(clientToken).getClientId();
        modelProcessDTO.setOauthClientId(oauthClientId);

        modelProcessDTO.setStream(false);
        String result = modelProcessService.processModelByNonStream(modelProcessDTO);
        return R.ok(result);
    }
    //###################### End ChatCompletions ######################

    @PostMapping(path = "/txt2img")
    @Operation(summary = "文生图模型")
    @SaCheckClientToken(scope = "fytec:llm")
    public R<List<Txt2ImgDTO>> processTxt2Img(@Validated @RequestBody Txt2ImgProcessDTO dto) {
        String clientToken = SaOAuth2Manager.getDataResolver().readClientToken(SaHolder.getRequest());
        String oauthClientId = SaOAuth2Util.getClientToken(clientToken).getClientId();
        dto.setOauthClientId(oauthClientId);

        return R.ok(modelProcessService.processTxt2Img(dto));
    }
}
