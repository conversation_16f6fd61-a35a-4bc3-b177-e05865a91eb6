package com.fytec.controller.llm.doc.ali;

import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.oauth2.SaOAuth2Manager;
import cn.dev33.satoken.oauth2.annotation.SaCheckClientToken;
import cn.dev33.satoken.oauth2.template.SaOAuth2Util;
import com.alibaba.fastjson2.JSONArray;
import com.fytec.dto.llm.DocProcessDTO;
import com.fytec.service.llm.doc.ali.AliRerankService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Validated
@Tag(name = "阿里云服务")
@RequestMapping("/api/ali")
@RestController
@RequiredArgsConstructor
public class AliController {

    private final AliRerankService aliRerankService;

    @PostMapping(path = "/text/rerank")
    @Operation(summary = "提交排序任务")
    @SaCheckClientToken(scope = "fytec:llm")
    public JSONArray processTextRerank(@Validated @RequestBody DocProcessDTO dto) {
        String clientToken = SaOAuth2Manager.getDataResolver().readClientToken(SaHolder.getRequest());
        String oauthClientId = SaOAuth2Util.getClientToken(clientToken).getClientId();
        return aliRerankService.textGteRerank(dto.getParams(), oauthClientId);
    }
}
