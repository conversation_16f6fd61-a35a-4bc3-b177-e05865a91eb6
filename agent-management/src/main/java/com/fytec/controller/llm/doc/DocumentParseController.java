package com.fytec.controller.llm.doc;

import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.oauth2.SaOAuth2Manager;
import cn.dev33.satoken.oauth2.annotation.SaCheckClientToken;
import cn.dev33.satoken.oauth2.template.SaOAuth2Util;
import com.fytec.dto.llm.DocProcessDTO;
import com.fytec.dto.llm.DocProcessResultDTO;
import com.fytec.service.llm.doc.BaiduDocService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;
import java.io.IOException;

@Slf4j
@Validated
@Tag(name = "百度文档解析")
@RequestMapping("/api/llm")
@RestController
public class DocumentParseController {

    @Resource
    private BaiduDocService baiduDocService;

    @PostMapping(path = "/baidu/task/submit")
    @Operation(summary = "提交解析任务")
    @SaCheckClientToken(scope = "fytec:llm")
    public String submit(@Validated @RequestBody DocProcessDTO dto) {
        try {

            String clientToken = SaOAuth2Manager.getDataResolver().readClientToken(SaHolder.getRequest());
            String oauthClientId = SaOAuth2Util.getClientToken(clientToken).getClientId();

            return baiduDocService.submitTask(dto.getFileUrl(), dto.getFileName(), oauthClientId);
        } catch (IOException e) {
            log.error("提交文档解析任务失败，失败原因：{}", e.getMessage());
        }
        return null;
    }

    @PostMapping(path = "/baidu/task")
    @Operation(summary = "获取文档解析解析结果地址")
    @SaCheckClientToken(scope = "fytec:llm")
    public DocProcessResultDTO getTask(@NotBlank(message = "taskId参数不能为空") String taskId) {
        try {
            String clientToken = SaOAuth2Manager.getDataResolver().readClientToken(SaHolder.getRequest());
            String oauthClientId = SaOAuth2Util.getClientToken(clientToken).getClientId();
            return baiduDocService.getTask(taskId, oauthClientId);
        } catch (IOException e) {
            log.error("获取文档解析解析结果地址失败，失败原因：{}", e.getMessage());
        }
        return null;
    }
}
