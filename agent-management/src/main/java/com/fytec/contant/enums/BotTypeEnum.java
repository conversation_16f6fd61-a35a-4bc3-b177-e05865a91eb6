/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.fytec.contant.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BotTypeEnum {
    deep_seek_chat_v3("bot-20250219093523-srvkj", "DeepSeek V3 + 互联网插件", false),
    deep_seek_reasoner_r1("bot-20250219093850-jwdv7", "DeepSeek R1 + 互联网插件", false),
    ;

    private final String modelId;
    private final String desc;
    private final boolean enableFunctionCall;

}
