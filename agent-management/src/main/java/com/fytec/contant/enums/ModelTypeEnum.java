/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.fytec.contant.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ModelTypeEnum {
    //########################### 火山引擎 ###########################

    doubao_pro_4k_240515("ep-20250106161329-qf5pz", "4K PRO 主线模型，最大输出4K", true, false),
    doubao_pro_32k_241215("ep-20250106161406-hkfdb", "32K PRO 主线模型，最大输出4K", true, false),
    doubao_pro_32k_funcall_241028("ep-20250106161602-7btmk", "32K PRO 工具调用模型，最大输出4K", true, false),
    doubao_pro_128k_240628("ep-20250106161142-qg85g", "128K PRO 主线模型，最大输出4K", true, false),

    doubao_pro_32k_250115_1_5("ep-20250122134159-8958t", "32K PRO 主线模型, 最大输出12K", true, false),
    doubao_lite_32k_250115_1_5("ep-20250122134502-d6vbc", "32K LITE 主线模型, 最大输出12K", true, false),
    doubao_pro_256k_250115_1_5("ep-20250122140854-5ppdg", "256K PRO 主线模型，最大输出12K", true, false),


    doubao_vision_lite_241015("ep-20250301181103-gnkzm", "32K Lite 视觉模型，最大输出4K", false, false),
    doubao_1_5_vision_pro_250115("ep-20250301171509-49bc6", "32K PRO 视觉模型，最大输出12K", false, false),
    doubao_1_5_thinking_vision_pro_250428("ep-20250717101847-c6qtz", "128K PRO 视觉模型，最大输出16K", false, false),

    doubao_seed_1_6_250615("ep-20250611172935-kqwwt", "256K上下文，最大输入224K，最大输出16K", true, false),


    //########################### Kimi ###########################
    kimi_8k_v1("ep-20250122150217-5cdgr", "8K V1, 最大输出4K", true, false),
    kimi_32k_v1("ep-20250122150141-tdczm", "32K V1, 最大输出4K", true, false),
    kimi_128k_v1("ep-20250122150158-vf4cr", "128K V1, 最大输出4K", true, false),


    kimi_latest("kimi-latest", "128K上下文, 最大输出128K", true, false),
    kimi_k2("kimi-k2-0711-preview", "128K上下文, 最大输出128K", true, false),


    //########################### Deep Seek ###########################
    deep_seek_chat_v3_official("deepseek-chat", "64K v3，最大输出8K", true, false),
    deep_seek_reasoner_r1_official("deepseek-reasoner", "64K R1, 思维长度32K, 最大输出8K", false, false),
    //deep_seek_chat_v3("ep-20250205085916-n9t9p", "64K v3，最大输出8K", false, false),
    deep_seek_chat_v3("ep-20250327155355-nkzcv", "64K v3，最大输出8K，250324", true, false),
    deep_seek_reasoner_r1("ep-20250205085942-dhrzl", "64K R1, 思维长度32K, 最大输出8K", false, false),

    //########################## 通义千问 ##########################
    ali_qwen_max_250125("qwen-max-0125", "通义千问，最大输出8K", true, true),
    ali_qwen_vl_max("qwen-vl-max", "上下文128K，最大输出8K", true, true),
    ali_qwen_3_coder("qwen3-coder-plus", "上下文1M，最大输出64K", true, true),


    //########################## ChatGPT ##########################
    gpt_4_1("openai/gpt-4.1", "上下文1.05M，最大输出33K", true, false),
    claude_sonnet_4("anthropic/claude-sonnet-4", "上下文200K，最大输出64K", true, false),
    gemini_pro_2_5("google/gemini-2.5-pro", "上下文1.05M，最大输出66K", true, false),

    ;

    private final String modelId;
    private final String desc;
    private final boolean enableFunctionCall;
    private final boolean enablePluginSearch;

}
