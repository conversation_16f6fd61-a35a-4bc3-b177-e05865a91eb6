package com.fytec.dto.system;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fytec.dto.AbstractDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version version 1.0
 */
@Data
public class SysRoleListDTO extends AbstractDTO {
    private Long id;

    @Schema(description = "角色标识")
    private String role;

    @Schema(description = "角色名称")
    private String roleName;

    @Schema(description = "角色描述")
    private String roleDesc;

    @Schema(description = "标识是否是系统角色")
    private String isSys;

    @Schema(hidden = true)
    private String createBy;

    @Schema(description = "创建者")
    private String createByName;

    @Schema(description = "创建日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

}
