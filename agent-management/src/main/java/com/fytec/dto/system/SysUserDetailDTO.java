package com.fytec.dto.system;

import com.fytec.annotation.DictData;
import com.fytec.dto.AbstractDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version version 1.0
 */
@Data
public class SysUserDetailDTO extends AbstractDTO {
    private Long id;

    @Schema(description = "用户名称")
    private String name;

    @Schema(description = "用户名")
    private String loginName;

    @Schema(description = "手机号")
    private String tel;

    @Schema(description = "状态值")
    @DictData(codeType = "COMMON_STATUS", target = "statusStr")
    private String status;

    @Schema(description = "状态名")
    private String statusStr;

    @Schema(description = "角色id")
    private List<Long> roleIds;
}
