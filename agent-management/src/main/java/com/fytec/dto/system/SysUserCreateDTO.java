package com.fytec.dto.system;


import com.fytec.config.validate.Mandatory;
import com.fytec.config.validate.Optional;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.util.List;

@Data
public class SysUserCreateDTO {
    @Schema(description = "用户名")
    @NotBlank(message = "用户名不能为空", groups = {Mandatory.class})
    private String name;

    @Schema(description = "登录名")
    @NotBlank(message = "登录名不能为空", groups = {Mandatory.class})
    private String loginName;

    @Schema(description = "密码")
    @NotBlank(message = "密码不能为空", groups = {Optional.class})
    private String password;

    @Schema(description = "手机号")
    @NotBlank(message = "手机号不能为空", groups = {Mandatory.class})
    @Pattern(regexp = "^(\\+\\d+)?1[34578]\\d{9}$", message = "{error.tel.format}", groups = {Mandatory.class})
    private String tel;

    @Schema(description = "角色")
    private List<Long> roleIds;

    @Schema(description = "状态")
    @NotBlank(message = "状态不能为空", groups = {Mandatory.class})
    private String status;
}
