package com.fytec.dto.system;

import com.fytec.dto.AbstractDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version version 1.0
 */
@Data
public class SysRoleDetailDTO extends AbstractDTO {
    private Long id;

    @Schema(description = "角色标识")
    private String role;

    @Schema(description = "角色名称")
    private String roleName;

    @Schema(description = "角色描述")
    private String roleDesc;

    @Schema(description = "菜单权限")
    private List<Long> menuIds;
}
