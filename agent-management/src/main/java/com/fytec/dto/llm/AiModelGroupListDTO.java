package com.fytec.dto.llm;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fytec.dto.AbstractDTO;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AiModelGroupListDTO extends AbstractDTO {
    private Long id;
    private String groupName;
    private String logo;
    private String description;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
