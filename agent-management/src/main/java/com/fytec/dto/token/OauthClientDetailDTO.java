package com.fytec.dto.token;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Data
public class OauthClientDetailDTO {

    private String clientId;

    private String clientSecret;

    private String name;

    @Schema(description = "scope(多值分号分隔)")
    private String scope;

    @Schema(description = "authorized_grant_types(多值分号分隔)")
    private String grantTypes;

    @Schema(description = "allow_redirect_uri(多值分号分隔)")
    private String allowRedirectUri;
}
