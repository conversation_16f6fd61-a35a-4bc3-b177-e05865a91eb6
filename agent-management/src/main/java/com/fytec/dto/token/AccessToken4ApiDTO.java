package com.fytec.dto.token;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class AccessToken4ApiDTO {
    @Schema(description = "令牌名称")
    @NotBlank(message = "令牌名称不能为空")
    private String name;

    @Schema(description = "令牌有效期(yyyy-MM-dd)")
    @NotBlank(message = "令牌有效期不能为空")
    private String expireDate;

    @Schema(description = "令牌权限列表(逗号分隔)")
    private String permissions;
}
