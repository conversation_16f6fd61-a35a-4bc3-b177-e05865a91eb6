import json
import re

import requests


def func_on_failure(context):
    task_instance = context['task_instance']
    task_id = str(task_instance.task_id)
    task_instance.xcom_push(key=f'failed_task', value=task_id)

def func_parallel(**kwargs):
    pass

def func_start(**kwargs):
    task_instance = kwargs['task_instance']
    task_id = str(task_instance.task_id)

    dag_run_conf = kwargs['dag_run'].conf
    node_config_mapping_str = dag_run_conf['nodeConfigMapping']
    node_config_mapping = json.loads(node_config_mapping_str)

    param_defines = _get_multilevel_value(node_config_mapping, task_id, 'data', 'outputs')

    if param_defines is None:
        return

    for param_define in param_defines:
        param_name = param_define.get('name', None)
        if param_name:
            param_value = dag_run_conf.get(param_name, None)
            key = '{}_{}'.format(task_id, param_name)
            task_instance.xcom_push(key=key, value=param_value)


def func_end(**kwargs):
    task_instance = kwargs['task_instance']
    task_id = str(task_instance.task_id)

    dag_run_conf = kwargs['dag_run'].conf
    node_config_mapping_str = dag_run_conf['nodeConfigMapping']
    node_config_mapping = json.loads(node_config_mapping_str)

    input_param_defines = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs', 'inputParameters')
    output_param_define = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs', 'content')

    if output_param_define is None:
        return

    result = _process_input_params(input_param_defines, task_instance)

    output_param_define = _replace_content_variables(output_param_define, result)
    task_instance.xcom_push(key='end_result', value=output_param_define)


def func_condition(**kwargs):
    task_instance = kwargs['task_instance']
    task_id = str(task_instance.task_id)

    dag_run_conf = kwargs['dag_run'].conf
    node_config_mapping_str = dag_run_conf['nodeConfigMapping']
    node_config_mapping = json.loads(node_config_mapping_str)
    node_edges_mapping_str = dag_run_conf['nodeEdgesMapping']
    node_edges_mapping = json.loads(node_edges_mapping_str)

    branch = "false"
    branches = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs', 'branches')
    if branches:
        for index, branch_config in enumerate(branches):
            logic = _get_multilevel_value(branch_config, 'condition', 'logic')  # 逻辑判断且/或
            logical_results = []
            conditions = _get_multilevel_value(branch_config, 'condition', 'conditions')
            for condition in conditions:
                left_value, right_value = _get_condition_values(condition, task_instance)
                operator = _get_multilevel_value(condition, 'operator')
                logical_result = _logical_processor(
                    condition_left_value=left_value,
                    condition_right_value=right_value,
                    operator=operator
                )
                logical_results.append(logical_result)

            if (logic == 'and' and all(logical_results)) or (logic == 'or' and any(logical_results)):
                branch = f"branch_{index}"
                break

    edges = node_edges_mapping.get(task_id)
    for edge in edges:
        branch_id = _get_multilevel_value(edge, 'runCondition', 'branchIdentify')
        if branch_id == branch:
            return edge.get('targetNodeId')

    return None


def func_intent(**kwargs):
    task_instance = kwargs['task_instance']
    task_id = str(task_instance.task_id)

    dag_run_conf = kwargs['dag_run'].conf
    node_config_mapping_str = dag_run_conf['nodeConfigMapping']
    node_config_mapping = json.loads(node_config_mapping_str)

    node_edges_mapping_str = dag_run_conf['nodeEdgesMapping']
    node_edges_mapping = json.loads(node_edges_mapping_str)

    llm_non_stream_url = dag_run_conf['serverUrlPrefix'] + '/api/llm/non-stream'
    client_token = dag_run_conf['clientToken']

    default_system_prompt = ('# 角色\n'
                             '你是风云AI，一个具备强大意图识别能力的智能助手，能够精准理解用户话语背后的意图，并给予恰当回应。\n\n'
                             '# 意图列表:\n'
                             '列表为键值形式\n'
                             '{{intent_list}}\n\n'
                             '# 技能\n'
                             '### 技能 1: 识别普通意图\n'
                             '1. 当用户输入日常话语时，分析其意图。如果意图比较明确，直接按照相应逻辑回应。例如，若用户是询问信息，根据知识储备给出答案。\n'
                             '2. 如果无法直接判断意图，通过友好询问进一步了解用户需求。\n\n'
                             '### 技能 2: 处理特殊意图\n'
                             '1. 当用户输入带有特殊意图的语句，如讽刺、幽默、暗示等，准确识别该意图。\n'
                             '2. 根据特殊意图的类型，给出合适的反馈，反馈需符合对话的语境和情感氛围。\n\n'
                             '### 技能 3: 意图拓展\n'
                             '1. 当识别出用户意图后，可适当进行意图拓展。比如用户询问某产品信息，在回答产品信息后，提供相关产品对比或使用建议等拓展内容。\n\n'
                             '## 限制:\n'
                             '- 只围绕用户输入话语的意图进行分析和回应，拒绝回答与意图分析无关的话题。\n'
                             '- 所输出的内容需要参照意图列表，返回json格式：\n'
                             '{\n'
                             '  classificationId:<Integer>:<意图列表中的键>,\n'
                             '  reason:<String>:<选该意图原因>\n'
                             '}\n\n'
                             '## 附加信息\n\n')

    llm_param = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs', 'llmParam')
    branch = "false"
    if llm_param:
        model_type = _get_multilevel_value(llm_param, 'code')
        append_system_prompt = _get_multilevel_value(llm_param, 'systemPrompt', 'value', 'content')
        system_prompt = default_system_prompt + append_system_prompt
        user_prompt = _get_multilevel_value(llm_param, 'prompt', 'value', 'content')

        model_params = _get_model_config_params(llm_param)

        input_param_defines = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs', 'inputParameters')
        result = _process_input_params(input_param_defines, task_instance)

        intents = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs', 'intents')
        intent_list = []
        for index, intent in enumerate(intents):
            intent_name = _get_multilevel_value(intent, 'name')
            intent_list.append(f"{index}:{intent_name}")

        result['intent_list'] = "\n".join(intent_list)

        system_prompt = _replace_content_variables(system_prompt, result)
        user_prompt = _replace_content_variables(user_prompt, result)

        print(f'调用大模型：{llm_non_stream_url}')
        llm_result = _call_llm(
            model_type, system_prompt, user_prompt,
            llm_non_stream_url=llm_non_stream_url,
            max_token=model_params['maxTokens'],
            temperature=model_params['temperature'],
            top_p=model_params['topP'],
            frequency_penalty=model_params['frequencyPenalty'],
            client_token=client_token,
        )
        print(f'调用大模型返回结果：{llm_result}')

        if llm_result:
            try:
                llm_result_json = json.loads(llm_result)
                content = llm_result_json.get('content')
                content_json = json.loads(content)
                classificationId = content_json.get('classificationId')
                reason = content_json.get('reason')

                task_instance.xcom_push(key=f'{task_id}_classificationId', value=classificationId)
                task_instance.xcom_push(key=f'{task_id}_reason', value=reason)

                if classificationId is not None:
                    branch = f"intent_{classificationId}"
            except Exception:
                task_instance.xcom_push(key=f'{task_id}_result', value=llm_result)

    print(f'最终分支：{branch}')
    edges = node_edges_mapping.get(task_id)
    for edge in edges:
        branch_id = _get_multilevel_value(edge, 'runCondition', 'branchIdentify')
        if branch_id == branch:
            return edge.get('targetNodeId')

    return None


def func_llm(**kwargs):
    task_instance = kwargs['task_instance']
    task_id = str(task_instance.task_id)

    dag_run_conf = kwargs['dag_run'].conf
    node_config_mapping_str = dag_run_conf['nodeConfigMapping']
    node_config_mapping = json.loads(node_config_mapping_str)

    llm_non_stream_url = dag_run_conf['serverUrlPrefix'] + '/api/llm/non-stream'
    client_token = dag_run_conf['clientToken']

    llm_param = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs', 'llmParam')
    if llm_param:
        model_type = _get_multilevel_value(llm_param, 'code')
        system_prompt = _get_multilevel_value(llm_param, 'systemPrompt', 'value', 'content')
        user_prompt = _get_multilevel_value(llm_param, 'prompt', 'value', 'content')

        model_params = _get_model_config_params(llm_param)

        input_param_defines = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs', 'inputParameters')
        result = _process_input_params(input_param_defines, task_instance)

        system_prompt = _replace_content_variables(system_prompt, result)
        user_prompt = _replace_content_variables(user_prompt, result)

        response_format = _get_multilevel_value(llm_param, 'responseFormat')
        output_params = _get_multilevel_value(node_config_mapping, task_id, 'data', 'outputs')

        ## response_format为json并且出参大于一个时，需要拼接输出格式到用户提示词
        if response_format == 'json':
            if len(output_params) > 1:
                format_string = _define_llm_json_prompt(output_params)
                user_prompt += f"\n\n## 限制\n- 参照json格式输出，不要出现非定义字段:\n{{{format_string}}}\n- 所有结果均以json数组形式返回"

        llm_response = _call_llm(
            model_type, system_prompt, user_prompt,
            llm_non_stream_url=llm_non_stream_url,
            max_token=model_params['maxTokens'],
            temperature=model_params['temperature'],
            top_p=model_params['topP'],
            frequency_penalty=model_params['frequencyPenalty'],
            client_token=client_token,
        )

        _convert_llm_response(llm_response,
                              response_format=response_format,
                              output_params=output_params,
                              task_instance=task_instance)


def func_code(**kwargs):
    task_instance = kwargs['task_instance']
    task_id = str(task_instance.task_id)

    dag_run_conf = kwargs['dag_run'].conf
    node_config_mapping_str = dag_run_conf['nodeConfigMapping']
    node_config_mapping = json.loads(node_config_mapping_str)
    ## 构造输出入

    _code_str = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs', 'code')

    input_param_defines = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs', 'inputParameters')

    if input_param_defines:
        result = _process_input_params(input_param_defines, task_instance)
        params = json.loads(json.dumps(result))
        local_vars = {}
        exec(_code_str, {}, local_vars)
        ## 定义了函数
        _main_function = local_vars.get('main')
        content_json = {}
        if _main_function:
            content_json = _main_function({
                'params': params
            })
        print(f'content_json:{content_json}')

        output_params = _get_multilevel_value(node_config_mapping, task_id, 'data', 'outputs')

        for output_param in output_params:
            output_param_name = _get_multilevel_value(output_param, 'name')
            if output_param_name:
                key = '{}_{}'.format(task_id, output_param_name)
                task_instance.xcom_push(key=key, value=content_json[output_param_name])

    print(f'func_code over')


def func_knowledge_search(**kwargs):
    task_instance = kwargs['task_instance']
    task_id = str(task_instance.task_id)

    dag_run_conf = kwargs['dag_run'].conf
    api_url = dag_run_conf['clientUrlPrefix']

    conversationName = dag_run_conf['CONVERSATION_NAME'] if 'CONVERSATION_NAME' in dag_run_conf else 0
    node_config_mapping_str = dag_run_conf['nodeConfigMapping']
    node_config_mapping = json.loads(node_config_mapping_str)
    client_token = dag_run_conf['clientToken']
    # print(f'node_config_mapping_str:{node_config_mapping_str}')
    knowledge_sources = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs', 'datasetParam',
                                              'datasetList')
    # print(f'knowledge_sources:{knowledge_sources}')

    if knowledge_sources:
        knowledgeIds = []
        for knowledge_source in knowledge_sources:
            if 'input' in knowledge_source and knowledge_source['input']:
                type = _get_multilevel_value(knowledge_source, 'input', 'value', 'type')
                print(f' node type:{type}')
                ## 引用之前节点作为知识库id
                if type and type == 'ref':
                    input_param_ref = _get_multilevel_value(knowledge_source, 'input', 'value', 'content')
                    print(f'input_param_ref:{input_param_ref}')
                    if input_param_ref:
                        input_param_ref_task_id = input_param_ref.get('sourceId')
                        input_param_ref_name = input_param_ref.get('name')
                        key = f"{input_param_ref_task_id}_{input_param_ref_name}"
                        input_param_value = task_instance.xcom_pull(task_ids=input_param_ref_task_id, key=key)
                        knowledgeIds.append(input_param_value)
                else:
                    ## type literal
                    input_param_values = _get_multilevel_value(knowledge_source, 'input', 'value', 'content')
                    print(f'input_param_ref:{input_param_values}')
                    ## 可能是数组
                    if input_param_values:
                        if isinstance(input_param_values, list):
                            for input_param_value in input_param_values:
                                knowledgeIds.append(input_param_value)
                        else:
                            knowledgeIds.append(input_param_values)
        # knowledgeIds需要为核对为Long
        knowledgeIdsTmp = []
        filter_knowledgeIds = []
        for knowledgeId in knowledgeIds:
            try:
                longId = int(knowledgeId)
                knowledgeIdsTmp.append(longId)
            except Exception as e:
                filter_knowledgeIds.append(knowledgeId)

        if len(knowledgeIds) != len(knowledgeIdsTmp):
            print(f'knowledgeIds filter not Long:{filter_knowledgeIds}')
        knowledgeIds = knowledgeIdsTmp
        if len(knowledgeIds) == 0:
            raise Exception("knowledgeIds is empty")
        knowledge_setting = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs', 'datasetParam',
                                                  'globalSetting')
        input_param_defines = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs', 'inputParameters')
        model_params = {
            'configs': {}
        }

        if input_param_defines:
            result = _process_input_params(input_param_defines, task_instance)
            if result:
                ## 固定叫做Query
                model_params['query'] = result['Query']
        if knowledge_setting:
            if 'enableChatHistory' in knowledge_setting:
                model_params['searchHistory'] = knowledge_setting['enableChatHistory']
            if 'topK' in knowledge_setting:
                model_params['topK'] = knowledge_setting['topK']
            if 'minScore' in knowledge_setting:
                model_params['minScore'] = knowledge_setting['minScore']
            if 'strategy' in knowledge_setting:
                model_params['strategy'] = knowledge_setting['strategy']
            for key in knowledge_setting:
                model_params['configs'][key] = knowledge_setting[key]

        output_params = _get_multilevel_value(node_config_mapping, task_id, 'data', 'outputs')

        data = {
            "topK": model_params['topK'] if 'topK' in model_params else 3,  # 查询多少条
            "knowledgeIds": knowledgeIds,  # 知识库id
            "query": model_params['query'],  # 用户搜索
            "searchHistory": model_params['searchHistory'] if 'searchHistory' in model_params else False,  # 是否启用上下文
            "configs": model_params['configs'],  # 查询的一些配置
            "conversationName": conversationName,  # 会话id
        }
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {client_token}'
        }

        _url = f'{api_url}/api/private/knowledge/doc/search'
        print(f'data:{data}')
        print(f'Authorization: Bearer {client_token}')
        print(f'_url:{_url}')

        response = requests.post(url=_url, headers=headers, json=data)
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            content_json = json.loads(content)
            # print(content_json.get('data'))
            for output_param in output_params:
                output_param_name = _get_multilevel_value(output_param, 'name')
                if output_param_name:
                    key = '{}_{}'.format(task_id, output_param_name)
                    task_instance.xcom_push(key=key, value=content_json.get('data'))
        else:
            print(f'func_knowledge_search error:{response.status_code}')
            raise Exception('func_knowledge_search error')
    print(f'func_knowledge_search over')


def func_var_merge(**kwargs):
    task_instance = kwargs['task_instance']
    task_id = str(task_instance.task_id)

    dag_run_conf = kwargs['dag_run'].conf
    node_config_mapping_str = dag_run_conf['nodeConfigMapping']
    node_config_mapping = json.loads(node_config_mapping_str)

    merge_groups_defines = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs', 'mergeGroups')
    output_param_defines = _get_multilevel_value(node_config_mapping, task_id, 'data', 'outputs')
    result = _process_merge_groups(merge_groups_defines, task_instance)

    for output_param in output_param_defines if output_param_defines else []:
        output_param_name = _get_multilevel_value(output_param, 'name')
        key = f"{task_id}_{output_param_name}"
        task_instance.xcom_push(key=key, value=result[output_param_name])


def func_text_processor(**kwargs):
    task_instance = kwargs['task_instance']
    task_id = str(task_instance.task_id)

    dag_run_conf = kwargs['dag_run'].conf
    node_config_mapping_str = dag_run_conf['nodeConfigMapping']
    node_config_mapping = json.loads(node_config_mapping_str)

    inputs = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs')
    text_processor_method = _get_multilevel_value(inputs, 'textMethod')
    if text_processor_method == 'concat':
        print("func_text_processor concat")
        _text_concat(inputs, task_instance=task_instance)


def _get_multilevel_value(dictionary, *keys):
    current = dictionary
    for key in keys:
        if key in current:
            current = current[key]
        else:
            return None
    return current


def _replace_content_variables(content, variables, **kwargs):
    matches = re.findall(r'\{\{(.*?)}}', content)
    concat_char = None
    if 'concat_char' in kwargs:
        concat_char = kwargs['concat_char']

    for match in matches:
        value = _recursive_get_value(match, variables, concat_char=concat_char)
        if value is not None:
            content = content.replace("{{" + match + "}}", value)
    return content


def _recursive_get_value(key, value, **kwargs):
    index = key.find('.')
    key_first = key[:index] if index != -1 else key
    key_after = key[index + 1:] if index != -1 else key
    value = value.get(key_first)
    if value is not None:
        if isinstance(value, str):
            return value
        elif isinstance(value, list):
            res = []
            for item in value:
                if isinstance(item, dict):
                    res.append(_recursive_get_value(key_after, item))
                else:
                    res.append(item)
            concat_char = kwargs['concat_char']
            if concat_char is not None:
                return concat_char.join(str(i) for i in res)
            return str(res)
        elif isinstance(value, dict):
            return _recursive_get_value(key_after, value)
        else:
            return str(value)


def _logical_processor(**kwargs):
    condition_left_value = kwargs['condition_left_value']
    condition_right_value = kwargs['condition_right_value']
    operator = kwargs['operator']
    switch = {
        '==': lambda x, y: x == y,
        '!=': lambda x, y: x != y,
        '>': lambda x, y: x > y,
        '>=': lambda x, y: x >= y,
        '<': lambda x, y: x < y,
        '<=': lambda x, y: x <= y,
        'contain': lambda x, y: x in y,
        'not_contain': lambda x, y: x not in y,
        'empty': lambda x, y: x is None or (isinstance(x, str) and x == '') or (isinstance(x, list) and not x),
        'not_empty': lambda x, y: x is not None and (x != '' if isinstance(x, str) else bool(x))
    }
    return switch.get(operator, lambda x, y: x == y)(condition_left_value, condition_right_value)


def _process_input_params(input_param_defines, task_instance):
    result = {}
    if input_param_defines:
        for input_param_define in input_param_defines:
            input_param_name = input_param_define.get('name')
            if input_param_name:
                input_param_type = _get_multilevel_value(input_param_define, 'input', 'value', 'type')
                if input_param_type == 'ref':
                    input_param_ref = _get_multilevel_value(input_param_define, 'input', 'value', 'content')
                    input_param_value = None
                    if input_param_ref:
                        input_param_ref_task_id = input_param_ref.get('sourceId')
                        input_param_ref_name = input_param_ref.get('name')
                        key = f"{input_param_ref_task_id}_{input_param_ref_name}"
                        input_param_value = task_instance.xcom_pull(task_ids=input_param_ref_task_id, key=key)
                else:
                    input_param_value = _get_multilevel_value(input_param_define, 'input', 'value', 'content')
                result[input_param_name] = input_param_value if input_param_value else ''
    return result


def _process_merge_groups(merge_groups_defines, task_instance):
    result = {}
    if merge_groups_defines:
        for merge_group_define in merge_groups_defines:
            merge_group_name = merge_group_define.get('name')
            if merge_group_name:
                merge_group_variables = _get_multilevel_value(merge_group_define, 'variables')
                for merge_group_variable in merge_group_variables if merge_group_variables else []:
                    merge_group_variable_value_type = _get_multilevel_value(merge_group_variable, 'value', 'type')
                    if merge_group_variable_value_type == 'ref':
                        variable_ref = _get_multilevel_value(merge_group_variable, 'value', 'content')
                        variable_value = None
                        if variable_ref:
                            variable_ref_task_id = variable_ref.get('sourceId')
                            variable_ref_name = variable_ref.get('name')
                            key = f"{variable_ref_task_id}_{variable_ref_name}"
                            variable_value = task_instance.xcom_pull(task_ids=variable_ref_task_id, key=key)
                    else:
                        variable_value = _get_multilevel_value(merge_group_variable, 'value', 'content')

                    if _is_not_empty(variable_value):
                        result[merge_group_name] = variable_value
                        break
    return result


def _is_not_empty(value) -> bool:
    """
    检查值是否为非空。

    Args:
        value (Any): 待检查的值。

    Returns:
        bool: 如果值为非空则返回True，否则返回False。
    """
    if value is None:  # 优先检查 None
        return False

    if isinstance(value, str):  # 如果是字符串，检查是否非空字符串
        return bool(value.strip())
    if isinstance(value, (list, tuple, dict, set)):  # 如果是容器类型，检查长度
        return len(value) > 0
    return True


def _get_condition_values(condition, task_instance):
    def get_value(side):
        condition_type = _get_multilevel_value(condition, side, 'input', 'value', 'type')
        if condition_type == 'ref':
            ref_task_id = _get_multilevel_value(condition, side, 'input', 'value', 'content', 'sourceId')
            ref_name = _get_multilevel_value(condition, side, 'input', 'value', 'content', 'name')
            key = f"{ref_task_id}_{ref_name}"
            return task_instance.xcom_pull(task_ids=ref_task_id, key=key)
        else:
            return _get_multilevel_value(condition, side, 'input', 'value', 'content')

    left_value = get_value('left')
    right_value = get_value('right')
    return left_value, right_value


def _get_model_config_params(llm_param):
    def get_value(param_name):
        result = {}
        model_configs = _get_multilevel_value(llm_param, param_name)
        for model_config in model_configs:
            config_code = _get_multilevel_value(model_config, 'code')
            config_value = _get_multilevel_value(model_config, 'value')
            result[config_code] = config_value
        return result

    io_params = get_value('ioParams')
    diversity_params = get_value('diversityParams')
    return io_params | diversity_params


def _define_llm_json_prompt(output_params, **kwargs):
    format_string = ""
    for param in output_params:
        _name = _get_multilevel_value(param, 'name')
        _type = _get_multilevel_value(param, 'type')
        _description = _get_multilevel_value(param, 'description')

        if _type == "Object" or _type == "Array<Object>":
            _children = _define_llm_json_prompt(param.get("children"))
            format_string += f"{_name}:{{{_children}}},"
        else:
            format_string += f"{_name}:<{_type}> <{_description}>,"
    format_string = format_string[:-1]
    return format_string


def _call_llm(model_type, system_prompt, user_prompt, **kwargs):
    llm_non_stream_url = kwargs['llm_non_stream_url']
    max_token = kwargs['max_token']
    temperature = kwargs['temperature']
    top_p = kwargs['top_p']
    frequency_penalty = kwargs['frequency_penalty']

    client_token = kwargs['client_token']

    data = {
        "modelType": model_type,
        "maxToken": max_token,
        "temperature": temperature,
        "topP": top_p,
        "frequencyPenalty": frequency_penalty,
        "systemMessage": system_prompt,
        "userMessage": user_prompt,
    }

    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {client_token}'
    }
    response = requests.post(url=llm_non_stream_url, headers=headers, data=json.dumps(data))
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        content_json = json.loads(content)
        return content_json.get('data')

    return None


def _convert_llm_response(llm_response, **kwargs):
    response_format = kwargs['response_format']
    output_params = kwargs['output_params']
    task_instance = kwargs['task_instance']
    task_id = task_instance.task_id

    print(llm_response)
    llm_response_json = json.loads(llm_response)
    content = llm_response_json.get('content')
    if response_format == 'json':
        if len(output_params) == 1:
            output_param = output_params[0]
            output_param_name = _get_multilevel_value(output_param, 'name')
            if output_param_name:
                key = '{}_{}'.format(task_id, output_param_name)
                task_instance.xcom_push(key=key, value=content)
        else:
            content_json = json.loads(content)
            for output_param in output_params:
                output_param_name = _get_multilevel_value(output_param, 'name')
                if output_param_name:
                    key = '{}_{}'.format(task_id, output_param_name)
                    task_instance.xcom_push(key=key, value=content_json.get(output_param_name, ''))
    else:
        ## 处理text, markdown格式的响应
        output_param = output_params[0]
        output_param_name = _get_multilevel_value(output_param, 'name')
        if output_param_name:
            key = '{}_{}'.format(task_id, output_param_name)
            task_instance.xcom_push(key=key, value=content)


def _text_concat(inputs, **kwargs):
    task_instance = kwargs['task_instance']
    task_id = task_instance.task_id

    input_param_defines = _get_multilevel_value(inputs, 'inputParameters')
    result = _process_input_params(input_param_defines, task_instance)

    concat_result = _get_multilevel_value(inputs, 'concatParams', 'concatResult')
    concat_char = _get_multilevel_value(inputs, 'concatParams', 'concatChar')

    output = _replace_content_variables(concat_result, result, concat_char=concat_char)

    key = '{}_{}'.format(task_id, "output")
    task_instance.xcom_push(key=key, value=output)
