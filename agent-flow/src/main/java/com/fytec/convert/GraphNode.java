package com.fytec.convert;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class GraphNode {
    private String id;        // 节点ID
    private String shape;     // 节点类型
    private JsonNode data;    // 节点数据
}
