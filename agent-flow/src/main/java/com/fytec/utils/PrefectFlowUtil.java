package com.fytec.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fytec.config.MinioProperties;
import com.fytec.convert.GraphEdge;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.fytec.utils.AirFlowUtil.NODE_REVERSE_EDGES_MAPPING_KEY;
import static com.fytec.utils.FlowEnum.*;

@Component
@RequiredArgsConstructor
public class PrefectFlowUtil {
    private final MinioProperties minioProperties;

    @Value("${flow.dag.path}")
    private String filePath;

    @SneakyThrows
    public Map<String, Object> convertToPrefectFlow(String graphJsonStr, String convertJsonStr, String flowName, String flowDescription) {
        AirFlowUtil flowUtil = new AirFlowUtil();
        Map<String, Object> result = flowUtil.parseFlow(graphJsonStr, flowName, flowDescription);
        Map<String, List<GraphEdge>> reverseEdgeMap = (Map<String, List<GraphEdge>>) result.get(NODE_REVERSE_EDGES_MAPPING_KEY);

        ObjectMapper mapper = new ObjectMapper();
        JsonNode root = mapper.readTree(convertJsonStr);

        StringBuilder dag = new StringBuilder();
        StringBuilder sb = new StringBuilder();
        Set<String> importedModules = new HashSet<>();
        buildAllNode(reverseEdgeMap, importedModules, root, sb, 1);
        generateImport(dag, importedModules);
        generateFlow(flowName, dag, sb);

        // 保存生成的代码
        saveFlowPythonFile(filePath, flowName, dag.toString());
        return result;
    }

    private void generateImport(StringBuilder dag, Set<String> importedModules) {
        dag.append("import argparse").append("\n");
        dag.append("import json").append("\n");
        dag.append("import prefect").append("\n");
        dag.append("from minio import Minio").append("\n");
        dag.append("from prefect import flow").append("\n");

        // 生成导入语句
        for (String module : importedModules) {
            dag.append(String.format("from function_node import %s", module)).append("\n");
        }
        dag.append("\n");
    }

    private void generateFlow(String flowName, StringBuilder dag, StringBuilder sb) {
        dag.append("@flow(name='%s', log_prints=True)".formatted(flowName)).append("\n");
        dag.append("def flow():").append("\n");
        dag.append("\tparser = argparse.ArgumentParser(description=\"调用Python脚本\")").append("\n");
        dag.append("\tparser.add_argument(\"--accessKey\", help=\"minio accessKey\", default=\"%s\")".formatted(minioProperties.getAccessKey())).append("\n");
        dag.append("\tparser.add_argument(\"--secretKey\", help=\"minio secretKey\", default=\"%s\")".formatted(minioProperties.getSecretKey())).append("\n");
        dag.append("\tparser.add_argument(\"--endpoint\", help=\"minio endpoint\", default=\"%s\")".formatted(minioProperties.getEndpoint())).append("\n");
        dag.append("\tparser.add_argument(\"--bucketName\", help=\"minio bucketName\", default=\"prefect-variables\")").append("\n");
        dag.append("\tparser.add_argument(\"--runId\", required=True, help=\"runId\")").append("\n");
        dag.append("\tparser.add_argument(\"--file\", required=True, help=\"参数文件名\")").append("\n");
        dag.append("\targs = parser.parse_args()").append("\n\n");

        dag.append("\tclient = Minio(").append("\n");
        dag.append("\t\tf'{args.endpoint}',").append("\n");
        dag.append("\t\taccess_key=f'{args.accessKey}',").append("\n");
        dag.append("\t\tsecret_key=f'{args.secretKey}',").append("\n");
        dag.append("\t\tsecure=False").append("\n");
        dag.append("\t)").append("\n\n");

        dag.append("\tresponse = client.get_object(args.bucketName, args.file)").append("\n");
        dag.append("\tjsonStr = response.read().decode('utf-8')").append("\n");
        dag.append("\trun_param = json.loads(jsonStr)").append("\n");
        dag.append("\trun_param['minioClient'] = client").append("\n");
        dag.append("\trun_param['minioBucketName'] = args.bucketName").append("\n");
        dag.append("\trun_id = f'{args.runId}'").append("\n");
        dag.append("\tflow_run_id = prefect.runtime.flow_run.id").append("\n");
        dag.append("\trun_param['run_id'] = run_id").append("\n");
        dag.append("\tprint(f'{flow_run_id} -> {run_id}')").append("\n\n");
        dag.append(sb).append("\n\n");
        dag.append("flow()");
    }


    private void saveFlowPythonFile(String filePath, String flowName, String dag) throws IOException {
        Path path = Paths.get(filePath, "%s.py".formatted(flowName));
        Files.writeString(path, dag);
    }


    private void buildAllNode(Map<String, List<GraphEdge>> reverseEdgeMap,
                              Set<String> importedModules,
                              JsonNode node, StringBuilder sb, int indent) {
        buildNode(reverseEdgeMap, importedModules, node, sb, indent);

        String taskType = node.get("shape").asText();

        //子节点
        if (StrUtil.equalsAny(taskType, ParallelNode.name(),
                ParallelGateway.name(),
                ConditionNode.name(),
                IntentNode.name())) {
            //条件分支
            JsonNode branches = node.get("branches");
            if (branches.isArray()) {
                if (StrUtil.equalsAny(taskType, ConditionNode.name(), IntentNode.name())) {
                    sb.append("\t".repeat(indent)).append("match task_%s:\n".formatted(node.get("id").asText()));
                }

                for (JsonNode branch : branches) {
                    if (StrUtil.equalsAny(taskType, ParallelNode.name(), ParallelGateway.name())) {
                        buildAllNode(reverseEdgeMap, importedModules, branch, sb, indent);
                    } else {
                        buildConditionBranch(reverseEdgeMap, importedModules, branch, sb, indent + 1);
                    }
                }
            }
        }

        JsonNode next = node.get("next");
        if (next != null) {
            buildAllNode(reverseEdgeMap, importedModules, next, sb, indent);
        }
    }

    private void buildConditionBranch(Map<String, List<GraphEdge>> reverseEdgeMap,
                                      Set<String> importedModules,
                                      JsonNode node, StringBuilder sb, int indent) {
        sb.append("\t".repeat(indent)).append("case '%s':\n".formatted(node.get("branchPort").asText()));
        indent++;
        buildAllNode(reverseEdgeMap, importedModules, node, sb, indent);
    }

    private void buildNode(Map<String, List<GraphEdge>> reverseEdgeMap,
                           Set<String> importedModules,
                           JsonNode node, StringBuilder sb, int indent) {
        if (!isFunctionNode(node)) {
            return;
        }

        String nodeId = node.get("id").asText();

        StringBuilder waitFor = new StringBuilder();
        List<GraphEdge> reverseEdges = reverseEdgeMap.get(nodeId);
        if (CollUtil.isNotEmpty(reverseEdges)) {
            reverseEdges.forEach(edge -> {
                waitFor.append("task_%s if 'task_%s' in locals() else None,".formatted(edge.getSourceNodeId(), edge.getSourceNodeId()));
            });
            waitFor.deleteCharAt(waitFor.length() - 1);
        }

        String taskType = node.get("shape").asText();
        FlowEnum taskTypeEnum = FlowEnum.valueOf(taskType);
        switch (taskTypeEnum) {
            case StartNode:
                importedModules.add("func_start");
                sb.append("\t".repeat(indent)).append("task_%s =func_start.submit(task_id=\"%s\", run_id=run_id, run_param=run_param)\n".formatted(nodeId, nodeId));
                break;
            case EndNode:
                importedModules.add("func_end");
                sb.append("\t".repeat(indent)).append("task_%s =func_end.submit(task_id=\"%s\", run_id=run_id, run_param=run_param, wait_for=[%s]).result()\n".formatted(nodeId, nodeId, waitFor));
                break;
            case ConditionNode:
                importedModules.add("func_condition");
                sb.append("\t".repeat(indent)).append("task_%s =func_condition.submit(task_id=\"%s\", run_id=run_id, run_param=run_param, wait_for=[%s]).result()\n".formatted(nodeId, nodeId, waitFor));
                break;
            case IntentNode:
                importedModules.add("func_intent");
                sb.append("\t".repeat(indent)).append("task_%s =func_intent.submit(task_id=\"%s\", run_id=run_id, run_param=run_param, wait_for=[%s]).result()\n".formatted(nodeId, nodeId, waitFor));
                break;
            case LLMNode:
                importedModules.add("func_llm");
                sb.append("\t".repeat(indent)).append("task_%s =func_llm.submit(task_id=\"%s\", run_id=run_id, run_param=run_param, wait_for=[%s])\n".formatted(nodeId, nodeId, waitFor));
                break;
            case VariableMergeNode:
                importedModules.add("func_var_merge");
                sb.append("\t".repeat(indent)).append("task_%s =func_var_merge.submit(task_id=\"%s\", run_id=run_id, run_param=run_param, wait_for=[%s])\n".formatted(nodeId, nodeId, waitFor));
                break;
            case TextNode:
                importedModules.add("func_text_processor");
                sb.append("\t".repeat(indent)).append("task_%s =func_text_processor.submit(task_id=\"%s\", run_id=run_id, run_param=run_param, wait_for=[%s])\n".formatted(nodeId, nodeId, waitFor));
                break;
            case KnowledgeSearchNode:
                importedModules.add("func_knowledge_search");
                sb.append("\t".repeat(indent)).append("task_%s =func_knowledge_search.submit(task_id=\"%s\", run_id=run_id, run_param=run_param, wait_for=[%s])\n".formatted(nodeId, nodeId, waitFor));
                break;
            case CodeNode:
                importedModules.add("func_code");
                sb.append("\t".repeat(indent)).append("task_%s =func_code.submit(task_id=\"%s\", run_id=run_id, run_param=run_param, wait_for=[%s])\n".formatted(nodeId, nodeId, waitFor));
                break;
            case ParallelNode:
                importedModules.add("func_parallel_node");
                sb.append("\t".repeat(indent)).append("task_%s =func_parallel_node.submit(task_id=\"%s\", run_id=run_id, run_param=run_param, wait_for=[%s])\n".formatted(nodeId, nodeId, waitFor));
                break;
            case PluginNode:
                importedModules.add("func_plugin_execute");
                sb.append("\t".repeat(indent)).append("task_%s =func_plugin_execute.submit(task_id=\"%s\", run_id=run_id, run_param=run_param, wait_for=[%s])\n".formatted(nodeId, nodeId, waitFor));
                break;
            case DatabaseSqlNode:
                importedModules.add("func_db_execute_sql");
                sb.append("\t".repeat(indent)).append("task_%s =func_db_execute_sql.submit(task_id=\"%s\", run_id=run_id, run_param=run_param, wait_for=[%s])\n".formatted(nodeId, nodeId, waitFor));
                break;

        }
    }

    public boolean isFunctionNode(JsonNode node) {
        return node != null
                && node.has("id")
                && node.has("shape")
                && !ParallelGateway.name().equals(node.get("shape").asText());
    }
}
