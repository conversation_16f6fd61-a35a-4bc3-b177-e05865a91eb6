package com.fytec.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ValidateException;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fytec.convert.Graph;
import com.fytec.convert.GraphEdge;
import com.fytec.convert.GraphNode;
import com.fytec.convert.RunCondition;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.fytec.utils.FlowEnum.StartNode;

@Component
public class AirFlowUtil {

    public static final String NODE_EDGES_MAPPING_KEY = "edges";
    public static final String NODE_REVERSE_EDGES_MAPPING_KEY = "reverseEdges";
    public static final String NODE_CONFIG_MAPPING_KEY = "nodes";
    public static final String FUNCTION_CALL_TOOL = "chain_tool";

    @Value("${flow.dag.path}")
    private String filePath;

    @SneakyThrows
    public Map<String, Object> parseFlow(String graphJsonStr, String flowName, String flowDescription) {
        Map<String, Object> result = new HashMap<>();

        ObjectMapper mapper = new ObjectMapper();
        JsonNode root = mapper.readTree(graphJsonStr);

        Graph graph = parseGraphConfig(root);

        // 初始化边映射和反向边映射
        Map<String, List<GraphEdge>> edgeMap = new HashMap<>();
        Map<String, List<GraphEdge>> reverseEdgeMap = new HashMap<>();
        // 解析边配置，填充边映射、反向边映射和目标节点ID集合
        parseEdges(graph.getGraphEdges(), edgeMap, reverseEdgeMap);
        result.put(NODE_EDGES_MAPPING_KEY, edgeMap);
        result.put(NODE_REVERSE_EDGES_MAPPING_KEY, reverseEdgeMap);

        // 初始化根节点配置列表和所有节点配置映射
        Map<String, GraphNode> nodeConfigMap = new HashMap<>();
        // 解析节点配置，填充根节点配置列表和所有节点配置映射
        parseNodes(graph.getGraphNodes(), nodeConfigMap);
        result.put(NODE_CONFIG_MAPPING_KEY, nodeConfigMap);

        // 初始化根节点
        String rootNodeId = findRootNodeId(graph.getGraphNodes());
        GraphNode rootNode = nodeConfigMap.get(rootNodeId);
        String chainTool = generateChainTool(rootNode, flowName, flowDescription);
        result.put(FUNCTION_CALL_TOOL, chainTool);
        return result;
    }

    @SneakyThrows
    public Map<String, Object> convertToAirFlow(String graphJsonStr, String flowName, String flowDescription) {
        Map<String, Object> result = new HashMap<>();

        ObjectMapper mapper = new ObjectMapper();
        JsonNode root = mapper.readTree(graphJsonStr);

        Graph graph = parseGraphConfig(root);

        // 初始化边映射和反向边映射
        Map<String, List<GraphEdge>> edgeMap = new HashMap<>();
        Map<String, List<GraphEdge>> reverseEdgeMap = new HashMap<>();
        // 解析边配置，填充边映射、反向边映射和目标节点ID集合
        parseEdges(graph.getGraphEdges(), edgeMap, reverseEdgeMap);
        result.put(NODE_EDGES_MAPPING_KEY, edgeMap);

        // 初始化根节点配置列表和所有节点配置映射
        Map<String, GraphNode> nodeConfigMap = new HashMap<>();
        // 解析节点配置，填充根节点配置列表和所有节点配置映射
        parseNodes(graph.getGraphNodes(), nodeConfigMap);
        result.put(NODE_CONFIG_MAPPING_KEY, nodeConfigMap);

        // 初始化根节点
        String rootNodeId = findRootNodeId(graph.getGraphNodes());
        GraphNode rootNode = nodeConfigMap.get(rootNodeId);
        String chainTool = generateChainTool(rootNode, flowName, flowDescription);
        result.put(FUNCTION_CALL_TOOL, chainTool);

        // 检查根节点是否连接到前一个节点（防止循环）
        checkConnectedToPreviousNode(CollUtil.newArrayList(rootNodeId), edgeMap);

        Map<String, Object> taskMap = generateAirflowTask(nodeConfigMap);
        String pyDag = generateAirFlowPythonFile(flowName, flowDescription, edgeMap, taskMap);

        // 保存生成的代码
        saveAirFlowPythonFile(filePath, flowName, pyDag);
        return result;
    }

    private Graph parseGraphConfig(JsonNode root) {
        List<GraphNode> graphNodeList = parseFlowJsonNodes(root);
        List<GraphEdge> edgeList = parseFlowJsonEdges(root);
        return new Graph().setGraphNodes(graphNodeList).setGraphEdges(edgeList);
    }

    private void saveAirFlowPythonFile(String filePath, String flowName, String pyDag) throws IOException {
        Path path = Paths.get(filePath, "%s.py".formatted(flowName));
        Files.writeString(path, pyDag);
    }


    private List<GraphNode> parseFlowJsonNodes(JsonNode root) {
        // 1) 解析 nodes
        List<GraphNode> graphNodeList = new ArrayList<>();
        if (!root.has("nodes")) {
            throw new ValidateException("工作流最少包含一个节点");
        }

        for (JsonNode n : root.get("nodes")) {
            String id = n.get("id").asText();
            String shape = n.get("shape").asText();
            graphNodeList.add(new GraphNode().setId(id).setShape(shape).setData(n.get("data")));
        }
        return graphNodeList;
    }


    private List<GraphEdge> parseFlowJsonEdges(JsonNode root) {
        // 2) 解析 edges
        List<GraphEdge> edgeList = new ArrayList<>();
        if (root.has("edges")) {
            for (JsonNode e : root.get("edges")) {
                String id = e.get("id").asText();
                String sourceId = e.get("source").get("cell").asText();
                String targetId = e.get("target").get("cell").asText();

                String sourcePort = e.get("source").get("port").asText();
                RunCondition runCondition = null;
                if (StrUtil.startWith(sourcePort, "branch")
                        || StrUtil.startWith(sourcePort, "intent")
                        || StrUtil.startWith(sourcePort, "false")) {
                    runCondition = new RunCondition();
                    runCondition.setType("branch_identify");
                    runCondition.setBranchIdentify(sourcePort);
                }

                edgeList.add(new GraphEdge(id, sourceId, targetId, runCondition));
            }
        }
        return edgeList;
    }

    /**
     * 解析边配置并更新边映射和反向边映射
     * 此方法遍历边配置数组，解析每条边的源节点和目标节点，并根据解析结果更新边映射和反向边映射
     * 还会将目标节点ID添加到目标边ID集合中，以便于后续处理
     *
     * @param edgeConfigs    边配置的JSONArray
     * @param edgeMap        边映射，从源节点ID到边列表的映射
     * @param reverseEdgeMap 反向边映射，从目标节点ID到边列表的映射
     */
    private void parseEdges(List<GraphEdge> edgeConfigs,
                            Map<String, List<GraphEdge>> edgeMap,
                            Map<String, List<GraphEdge>> reverseEdgeMap) {
        // 如果边配置为空，则直接返回
        if (CollUtil.isEmpty(edgeConfigs)) {
            return;
        }

        // 遍历边配置数组
        for (GraphEdge graphEdge : edgeConfigs) {
            // 解析边的源节点配置
            String sourceNodeId = graphEdge.getSourceNodeId();
            // 如果源节点ID为空，则忽略当前边配置
            if (StrUtil.isBlank(sourceNodeId)) {
                continue;
            }
            // 确保源节点ID对应的边列表存在
            edgeMap.computeIfAbsent(sourceNodeId, k -> new ArrayList<>());

            // 解析边的目标节点配置
            String targetNodeId = graphEdge.getTargetNodeId();
            // 如果目标节点ID为空，则忽略当前边配置
            if (StrUtil.isBlank(targetNodeId)) {
                continue;
            }
            // 确保目标节点ID对应的反向边列表存在
            reverseEdgeMap.computeIfAbsent(targetNodeId, k -> new ArrayList<>());

            edgeMap.get(sourceNodeId).add(graphEdge);
            reverseEdgeMap.get(targetNodeId).add(graphEdge);
        }
    }

    /**
     * 解析节点配置并更新相关数据结构
     * 该方法的主要目的是遍历给定的节点配置数组，识别出根节点配置，并将其添加到根节点配置列表中，
     * 同时将所有节点配置添加到节点配置映射中
     *
     * @param nodeConfigs      节点配置的JSONArray数组，包含所有节点的配置信息
     * @param allNodeConfigMap 所有节点配置的映射，键为节点ID，值为节点配置
     */
    private void parseNodes(List<GraphNode> nodeConfigs,
                            Map<String, GraphNode> allNodeConfigMap) {
        // 遍历节点配置数组
        for (GraphNode graphNode : nodeConfigs) {
            // 获取节点ID和类型
            String nodeId = graphNode.getId();
            // 忽略节点ID为空的配置项
            if (StrUtil.isBlank(nodeId)) {
                continue;
            }
            // 将节点配置添加到所有节点配置映射中
            allNodeConfigMap.put(nodeId, graphNode);
        }
    }

    private String findRootNodeId(List<GraphNode> graphNodes) {
        String rootNodeId = null;
        for (GraphNode graphNode : graphNodes) {
            if (StartNode.name().equals(graphNode.getShape())) {
                rootNodeId = graphNode.getId();
                break;
            }
        }
        if (rootNodeId == null) {
            throw new ValidateException("工作流中未找到Root节点");
        }
        return rootNodeId;
    }


    /**
     * 检查图中的节点是否连接到前一个节点
     * 此方法旨在防止在图中形成循环，并确保每个节点只能连接到它前面的节点
     *
     * @param route   从起始节点到当前节点的路径，表示为节点ID的列表
     * @param edgeMap 一个映射，每个键是节点ID，值是从该节点出发的边的列表
     */
    private void checkConnectedToPreviousNode(List<String> route, Map<String, List<GraphEdge>> edgeMap) {
        // 获取当前路径的最后一个节点ID
        String nodeId = route.getLast();

        // 遍历当前节点的所有出边
        for (GraphEdge graphEdge : edgeMap.getOrDefault(nodeId, new ArrayList<>())) {
            // 忽略目标节点ID为空的边
            if (StrUtil.isEmpty(graphEdge.targetNodeId)) {
                continue;
            }

            // 如果当前边的目标节点已经在路径中出现过，则说明存在循环，抛出异常
            if (CollUtil.contains(route, graphEdge.targetNodeId)) {
                throw new ValidateException("检测到循环路径: " + route + " -> " + graphEdge.targetNodeId);
            }

            // 创建一个新的路径列表，包含当前边的目标节点
            List<String> newRoute = new ArrayList<>(route);
            newRoute.add(graphEdge.targetNodeId);

            // 递归调用，继续检查新的路径是否连接到前一个节点
            checkConnectedToPreviousNode(newRoute, edgeMap);
        }
    }

    private String generateAirFlowPythonFile(String flowName, String flowDescription,
                                             Map<String, List<GraphEdge>> edgeMap,
                                             Map<String, Object> taskMap) {
        // 开始生成 AirFlow DAG 代码
        StringBuilder dag = new StringBuilder();
        // 生成导入语句
        Set<String> importedModules = (Set<String>) taskMap.get("importedModules");
        generateAirflowImport(dag, importedModules);
        // 生成默认参数定义语句
        generateAirflowDagDefaultArgs(dag);

        String task = (String) taskMap.get("task");
        // 生成 DAG 对象定义
        generateAirflowDag(dag, flowName, flowDescription, null, task, edgeMap);
        return dag.toString();
    }

    private void generateAirflowImport(StringBuilder dag, Set<String> importedModules) {
        dag.append("from datetime import datetime, timedelta\n");
        dag.append("from airflow.decorators import dag\n");
        dag.append("from airflow.operators.python import PythonOperator, BranchPythonOperator\n");

        // 生成导入语句
        for (String module : importedModules) {
            dag.append(String.format("from function_node import %s\n", module));
        }
        dag.append("\n");
    }

    private void generateAirflowDagDefaultArgs(StringBuilder dag) {
        // 定义默认参数
        String defaultArgs = """
                default_args = {
                    'owner': 'fytec_ai',
                    'depends_on_past': False,
                    'email': ['<EMAIL>'],
                    'email_on_failure': False,
                    'email_on_retry': False,
                    'retries': 0,
                    'retry_delay': timedelta(minutes=5)
                }
                
                
                """;
        // 默认参数
        dag.append(defaultArgs);
    }


    private void generateAirflowDag(StringBuilder dag, String flowName,
                                    String flowDescription, String schedule,
                                    String task, Map<String, List<GraphEdge>> edgeMap) {
        if (schedule == null || schedule.isEmpty()) {
            schedule = "None";
        }
        // 定义DAG对象
        String dagDefinition = """
                @dag(
                    dag_id='%s',
                    default_args=default_args,
                    description='%s',
                    schedule=%s,
                    start_date=datetime.now(),
                    catchup=False,
                    tags=['fytec']
                )
                def airflow_dag():
                %s
                
                airflow_dag()
                """;

        String taskDependencies = generateAirflowTaskDependencies(task, edgeMap);

        dag.append(dagDefinition.formatted(flowName, flowDescription, schedule, taskDependencies));
    }

    private String generateAirflowTaskDependencies(String task, Map<String, List<GraphEdge>> edgeMap) {
        StringBuilder taskDependencies = new StringBuilder();
        taskDependencies.append(task);

        // 生成任务依赖关系
        for (String nodeId : edgeMap.keySet()) {
            List<GraphEdge> edges = edgeMap.get(nodeId);
            for (GraphEdge edge : edges) {
                String sourceNodeId = edge.getSourceNodeId().replaceAll("-", "");
                String targetNodeId = edge.getTargetNodeId().replaceAll("-", "");
                taskDependencies.append(String.format("\ttask_%s >> task_%s\n", sourceNodeId, targetNodeId));
            }
        }
        return taskDependencies.toString();
    }

    private Map<String, Object> generateAirflowTask(Map<String, GraphNode> nodeConfigMap) {
        Map<String, Object> taskMap = new HashMap<>();
        StringBuilder task = new StringBuilder();
        Set<String> importedModules = new HashSet<>();
        for (String nodeId : nodeConfigMap.keySet()) {
            String _nodeId = nodeId.replaceAll("-", "");
            GraphNode node = nodeConfigMap.get(nodeId);
            String taskType = node.getShape();
            FlowEnum taskTypeEnum = FlowEnum.valueOf(taskType);

            importedModules.add("func_on_failure");
            switch (taskTypeEnum) {
                case StartNode:
                    importedModules.add("func_start");
                    task.append("\ttask_%s = PythonOperator(task_id=\"%s\", python_callable=func_start, trigger_rule=\"none_failed\", on_failure_callback=func_on_failure)\n".formatted(_nodeId, nodeId));
                    break;
                case EndNode:
                    importedModules.add("func_end");
                    task.append("\ttask_%s = PythonOperator(task_id=\"%s\", python_callable=func_end, trigger_rule=\"none_failed\", on_failure_callback=func_on_failure)\n".formatted(_nodeId, nodeId));
                    break;
                case ParallelNode:
                    importedModules.add("func_parallel");
                    task.append("\ttask_%s = PythonOperator(task_id=\"%s\", python_callable=func_parallel, trigger_rule=\"none_failed\", on_failure_callback=func_on_failure)\n".formatted(_nodeId, nodeId));
                    break;
                case ConditionNode:
                    importedModules.add("func_condition");
                    task.append("\ttask_%s = BranchPythonOperator(task_id=\"%s\", python_callable=func_condition, trigger_rule=\"none_failed\", on_failure_callback=func_on_failure)\n".formatted(_nodeId, nodeId));
                    break;
                case IntentNode:
                    importedModules.add("func_intent");
                    task.append("\ttask_%s = BranchPythonOperator(task_id=\"%s\", python_callable=func_intent, trigger_rule=\"none_failed\", on_failure_callback=func_on_failure)\n".formatted(_nodeId, nodeId));
                    break;
                case LLMNode:
                    importedModules.add("func_llm");
                    task.append("\ttask_%s = PythonOperator(task_id=\"%s\", python_callable=func_llm, trigger_rule=\"none_failed\", on_failure_callback=func_on_failure)\n".formatted(_nodeId, nodeId));
                    break;
                case VariableMergeNode:
                    importedModules.add("func_var_merge");
                    task.append("\ttask_%s = PythonOperator(task_id=\"%s\", python_callable=func_var_merge, trigger_rule=\"none_failed\", on_failure_callback=func_on_failure)\n".formatted(_nodeId, nodeId));
                    break;
                case TextNode:
                    importedModules.add("func_text_processor");
                    task.append("\ttask_%s = PythonOperator(task_id=\"%s\", python_callable=func_text_processor, trigger_rule=\"none_failed\", on_failure_callback=func_on_failure)\n".formatted(_nodeId, nodeId));
                    break;
                case KnowledgeSearchNode:
                    importedModules.add("func_knowledge_search");
                    task.append("\ttask_%s = PythonOperator(task_id=\"%s\", python_callable=func_knowledge_search, trigger_rule=\"none_failed\", on_failure_callback=func_on_failure)\n".formatted(_nodeId, nodeId));
                    break;
                case CodeNode:
                    importedModules.add("func_code");
                    task.append("\ttask_%s = PythonOperator(task_id=\"%s\", python_callable=func_code, trigger_rule=\"none_failed\", on_failure_callback=func_on_failure)\n".formatted(_nodeId, nodeId));
                    break;
                default:
                    throw new IllegalArgumentException("Unsupported task type: " + taskType);
            }
        }
        task.append("\n");

        taskMap.put("task", task.toString());
        taskMap.put("importedModules", importedModules);

        return taskMap;
    }

    private String generateChainTool(GraphNode rootNode, String flowName, String flowDescription) {
        String chainTool = "{\"type\": \"function\",\"function\": {\"name\": \"%s\",\"description\": \"%s\",\"parameters\": {\"type\": \"object\", %s}}}";
        String parameters = prepareParameters(rootNode.getData().get("outputs"));
        chainTool = chainTool.formatted(flowName, flowDescription, parameters);
        return chainTool;
    }

    private String prepareParameters(JsonNode inputs) {
        if (inputs == null || inputs.isEmpty()) {
            return "";
        }

        String parameters = "\"properties\": {%s},\"required\": [%s]";
        StringBuilder inputProperties = new StringBuilder();
        StringBuilder inputRequiredFields = new StringBuilder();
        for (JsonNode input : inputs) {
            String inputType = input.get("type").asText();
            String inputName = input.get("name").asText();
            String inputDescription = "";
            if (input.has("description")) {
                inputDescription = input.get("description").asText();
            }

            if (StrUtil.equals(inputType, "object")) {
                String _inputProperties = prepareParameters(input.get("children"));
                inputProperties.append(String.format(
                        "\"%s\": {\"type\": \"%s\", %s},",
                        inputName, inputType.toLowerCase(), _inputProperties
                ));
            } else if (StrUtil.startWith(inputType, "Array")) {
                Pattern pattern = Pattern.compile("Array<([^>]+)>");
                Matcher matcher = pattern.matcher(inputType);
                if (matcher.find()) {
                    String itemType = matcher.group(1);
                    if (StrUtil.equals(itemType, "Object")) {
                        String _inputProperties = prepareParameters(input.get("children"));
                        inputProperties.append(String.format(
                                "\"%s\": {\"type\": \"%s\", \"items\": {\"type\": \"object\", %s}},",
                                inputName, "array", _inputProperties
                        ));
                    } else {
                        inputProperties.append(String.format(
                                "\"%s\": {\"type\": \"%s\", \"items\": {\"type\": \"%s\"}},",
                                inputName, "array", itemType
                        ));
                    }
                }
            } else {
                inputProperties.append(String.format(
                        "\"%s\": {\"type\": \"%s\", \"description\": \"%s\"},",
                        inputName, inputType, inputDescription
                ));
            }

            if (input.has("required")) {
                boolean inputRequired = input.get("required").asBoolean();
                if (inputRequired) {
                    inputRequiredFields.append("\"").append(inputName).append("\",");
                }
            }
        }
        if (inputProperties.length() > 1) {
            inputProperties.deleteCharAt(inputProperties.length() - 1);
        }
        if (inputRequiredFields.length() > 1) {
            inputRequiredFields.deleteCharAt(inputRequiredFields.length() - 1);
        }
        parameters = parameters.formatted(inputProperties, inputRequiredFields);
        return parameters;
    }

}
