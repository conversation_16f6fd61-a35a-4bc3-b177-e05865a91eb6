package com.fytec.utils;

import cn.hutool.core.util.StrUtil;
import com.fytec.config.FlowProperties;
import com.fytec.config.MinioProperties;
import com.fytec.convert.GraphEdge;
import com.fytec.convert.GraphNode;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

import static com.fytec.utils.AirFlowUtil.NODE_CONFIG_MAPPING_KEY;
import static com.fytec.utils.AirFlowUtil.NODE_EDGES_MAPPING_KEY;

@Component
@RequiredArgsConstructor
public class LangGraphUtil {
    private final MinioProperties minioProperties;
    private final FlowProperties flowProperties;
    @Value("${flow.dag.path}")
    private String filePath;

    @SneakyThrows
    public Map<String, Object> convertToLangGraph(String graphJsonStr, String flowName, String flowDescription) {
        AirFlowUtil flowUtil = new AirFlowUtil();
        Map<String, Object> result = flowUtil.parseFlow(graphJsonStr, flowName, flowDescription);


        Map<String, GraphNode> nodeConfigMap = (Map<String, GraphNode>) result.get(NODE_CONFIG_MAPPING_KEY);
        Map<String, Object> taskMap = buildGraphNode(nodeConfigMap);

        Set<String> branchNodesId = (Set<String>) taskMap.get("branchNodesId");
        Map<String, List<GraphEdge>> edgeMap = (Map<String, List<GraphEdge>>) result.get(NODE_EDGES_MAPPING_KEY);
        String taskEdges = buildNodeEdge(edgeMap, branchNodesId);

        StringBuilder dag = new StringBuilder();
        Set<String> importedModules = (Set<String>) taskMap.get("importedModules");
        generateImport(dag, importedModules);

        generateFlow(dag, (String) taskMap.get("task"), taskEdges);

        // 保存生成的代码
        saveFlowPythonFile(filePath, flowName, dag.toString());
        return result;
    }

    private Map<String, Object> buildGraphNode(Map<String, GraphNode> nodeConfigMap) {
        Map<String, Object> taskMap = new HashMap<>();
        StringBuilder task = new StringBuilder();
        Set<String> importedModules = new HashSet<>();
        Set<String> branchNodesId = new HashSet<>();
        for (String nodeId : nodeConfigMap.keySet()) {
            String _nodeId = nodeId.replaceAll("-", "");
            GraphNode node = nodeConfigMap.get(nodeId);
            String taskType = node.getShape();
            FlowEnum taskTypeEnum = FlowEnum.valueOf(taskType);

            switch (taskTypeEnum) {
                case StartNode:
                    importedModules.add("func_start");
                    task.append("builder.add_node(\"%s\", lambda state: func_start(state, \"%s\", client))\n".formatted(_nodeId, _nodeId));
                    break;
                case EndNode:
                    importedModules.add("func_end");
                    task.append("builder.add_node(\"%s\", lambda state: func_end(state, \"%s\", client))\n".formatted(_nodeId, _nodeId));
                    break;
                case ConditionNode:
                    importedModules.add("func_condition");
                    branchNodesId.add(nodeId);
                    task.append("builder.add_node(\"%s\", lambda state: func_condition(state, \"%s\", client))\n".formatted(_nodeId, _nodeId));
                    break;
                case IntentNode:
                    importedModules.add("func_intent");
                    branchNodesId.add(nodeId);
                    task.append("builder.add_node(\"%s\", lambda state: func_intent(state, \"%s\", client))\n".formatted(_nodeId, _nodeId));
                    break;
                case LLMNode:
                    importedModules.add("func_llm");
                    task.append("builder.add_node(\"%s\", lambda state: func_llm(state, \"%s\", client))\n".formatted(_nodeId, _nodeId));
                    break;
                case VariableMergeNode:
                    importedModules.add("func_var_merge");
                    task.append("builder.add_node(\"%s\", lambda state: func_var_merge(state, \"%s\", client))\n".formatted(_nodeId, _nodeId));
                    break;
                case TextNode:
                    importedModules.add("func_text_processor");
                    task.append("builder.add_node(\"%s\", lambda state: func_text_processor(state, \"%s\", client))\n".formatted(_nodeId, _nodeId));
                    break;
                case KnowledgeSearchNode:
                    importedModules.add("func_knowledge_search");
                    task.append("builder.add_node(\"%s\", lambda state: func_knowledge_search(state, \"%s\", client))\n".formatted(_nodeId, _nodeId));
                    break;
                case CodeNode:
                    importedModules.add("func_code");
                    task.append("builder.add_node(\"%s\", lambda state: func_code(state, \"%s\", client))\n".formatted(_nodeId, _nodeId));
                    break;
                case PluginNode:
                    importedModules.add("func_plugin_execute");
                    task.append("builder.add_node(\"%s\", lambda state: func_plugin_execute(state, \"%s\", client))\n".formatted(_nodeId, _nodeId));
                    break;
                case DatabaseSqlNode:
                    importedModules.add("func_db_execute_sql");
                    task.append("builder.add_node(\"%s\", lambda state: func_db_execute_sql(state, \"%s\", client))\n".formatted(_nodeId, _nodeId));
                    break;
            }
        }
        task.append("\n");

        taskMap.put("task", task.toString());
        taskMap.put("branchNodesId", branchNodesId);
        taskMap.put("importedModules", importedModules);

        return taskMap;
    }

    private void generateImport(StringBuilder dag, Set<String> importedModules) {
        dag.append("import argparse").append("\n");
        dag.append("import json").append("\n");
        //mysql checkpoint
        //dag.append("import pymysql").append("\n");
        //dag.append("from langgraph.checkpoint.mysql.pymysql import PyMySQLSaver").append("\n");\
        // redis
        dag.append("from langgraph.checkpoint.redis import RedisSaver").append("\n");
        dag.append("from langgraph.constants import START, END").append("\n");
        dag.append("from langgraph.graph import StateGraph").append("\n");
        dag.append("from minio import Minio").append("\n");
        dag.append("from state import State").append("\n");
        dag.append("from langgraph_node import branch_choose").append("\n");

        // 生成导入语句
        for (String module : importedModules) {
            dag.append(String.format("from langgraph_node import %s", module)).append("\n");
        }
        dag.append("\n");
    }

    private void generateFlow(StringBuilder dag, String task, String taskEdges) {
        dag.append("parser = argparse.ArgumentParser(description=\"调用Python脚本\")").append("\n");
        dag.append("parser.add_argument(\"--accessKey\", help=\"minio accessKey\", default=\"%s\")".formatted(minioProperties.getAccessKey())).append("\n");
        dag.append("parser.add_argument(\"--secretKey\", help=\"minio secretKey\", default=\"%s\")".formatted(minioProperties.getSecretKey())).append("\n");
        dag.append("parser.add_argument(\"--endpoint\", help=\"minio endpoint\", default=\"%s\")".formatted(minioProperties.getEndpoint())).append("\n");
        dag.append("parser.add_argument(\"--bucketName\", help=\"minio bucketName\", default=\"prefect-variables\")").append("\n");
        dag.append("parser.add_argument(\"--runId\", help=\"runId\")").append("\n");
        dag.append("parser.add_argument(\"--file\", help=\"参数文件名\")").append("\n");
        dag.append("args = parser.parse_args()").append("\n\n");

        dag.append("client = Minio(").append("\n");
        dag.append("\tf'{args.endpoint}',").append("\n");
        dag.append("\taccess_key=f'{args.accessKey}',").append("\n");
        dag.append("\tsecret_key=f'{args.secretKey}',").append("\n");
        dag.append("\tsecure=False").append("\n");
        dag.append(")").append("\n\n");

        dag.append("response = client.get_object(args.bucketName, args.file)").append("\n");
        dag.append("jsonStr = response.read().decode('utf-8')").append("\n");
        dag.append("run_param = json.loads(jsonStr)").append("\n");
        dag.append("run_param['minioBucketName'] = args.bucketName").append("\n");
        dag.append("run_id = f'{args.runId}'").append("\n");
        dag.append("run_param['run_id'] = run_id").append("\n");
        dag.append("_input = {\"config\": run_param}").append("\n\n");

        dag.append("builder = StateGraph(State)").append("\n");
        dag.append(task).append("\n");
        dag.append(taskEdges).append("\n");

        dag.append("ttl_config = {\"default_ttl\": 60, \"refresh_on_read\": True}").append("\n");
        dag.append(
                StrUtil.format(
                        "with RedisSaver.from_conn_string(\"redis://{}:{}\", ttl=ttl_config) as checkpointer:",
                        flowProperties.getLangGraph().getCheckPointer().getHost(),
                        flowProperties.getLangGraph().getCheckPointer().getPort()
                )
        ).append("\n");
        //# Call setup to initialize indices
        //dag.append("\tcheckpointer.setup()").append("\n");
        dag.append("\tgraph = builder.compile(checkpointer=checkpointer)").append("\n");
        dag.append("\tgraph.invoke(_input, config={\"thread_id\": run_id})").append("\n");

        /* mysql checkpoint
        dag.append(StrUtil.format("connection = pymysql.connect(host='{}', port={}, user='{}', password='{}', database='{}', autocommit=True)",
                        flowProperties.getLangGraph().getCheckPointer().getHost(),
                        flowProperties.getLangGraph().getCheckPointer().getPort(),
                        flowProperties.getLangGraph().getCheckPointer().getUser(),
                        flowProperties.getLangGraph().getCheckPointer().getPassword(),
                        flowProperties.getLangGraph().getCheckPointer().getDatabase()
                )
        ).append("\n");
        dag.append("checkpointer = PyMySQLSaver(connection)").append("\n");
        dag.append("graph = builder.compile(checkpointer=checkpointer)").append("\n");
        dag.append("graph.invoke(_input, config={\"thread_id\": run_id})").append("\n");*/
    }


    private String buildNodeEdge(Map<String, List<GraphEdge>> edgeMap, Set<String> branchNodesId) {
        StringBuilder taskEdges = new StringBuilder();
        taskEdges.append("builder.add_edge(START, \"10001\")\n");
        // 生成任务依赖关系

        Set<String> filterBranchNodesId = new HashSet<>();
        for (String nodeId : edgeMap.keySet()) {
            List<GraphEdge> edges = edgeMap.get(nodeId);
            for (GraphEdge edge : edges) {
                if (filterBranchNodesId.contains(nodeId)) {
                    continue;
                }
                String sourceNodeId = edge.getSourceNodeId().replaceAll("-", "");
                String targetNodeId = edge.getTargetNodeId().replaceAll("-", "");
                if (branchNodesId.contains(nodeId)) {
                    filterBranchNodesId.add(nodeId);
                    taskEdges.append(String.format("builder.add_conditional_edges(\"%s\", lambda state: branch_choose(state, \"%s\"))\n", sourceNodeId, sourceNodeId));
                } else {
                    taskEdges.append(String.format("builder.add_edge(\"%s\", \"%s\")\n", sourceNodeId, targetNodeId));
                }
            }
        }
        taskEdges.append("builder.add_edge(\"90001\", END)\n");
        return taskEdges.toString();
    }


    private void saveFlowPythonFile(String filePath, String flowName, String dag) throws IOException {
        Path path = Paths.get(filePath, "%s.py".formatted(flowName));
        Files.writeString(path, dag);
    }
}
