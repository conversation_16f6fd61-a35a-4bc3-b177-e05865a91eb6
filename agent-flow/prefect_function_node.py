import json
import re
import tempfile

import requests
from minio import S3Error
from prefect import task
from prefect.cache_policies import NO_CACHE


@task(cache_policy=NO_CACHE, log_prints=True)
def func_parallel(**kwargs):
    pass


@task(cache_policy=NO_CACHE, log_prints=True)
def func_start(**kwargs):
    run_id = kwargs['run_id']
    task_id = kwargs['task_id']
    run_param = kwargs['run_param']
    dag_run_conf = run_param['conf']
    client = run_param['minioClient']
    bucket_name = run_param['minioBucketName']

    node_config_mapping = dag_run_conf['nodeConfigMapping']
    param_defines = _get_multilevel_value(node_config_mapping, task_id, 'data', 'outputs')

    if param_defines is None:
        return

    for param_define in param_defines:
        param_name = param_define.get('name', None)
        if param_name:
            param_value = dag_run_conf.get(param_name, None)
            key = '{}_{}'.format(task_id, param_name)
            _put_value_from_minio(client=client,
                                  bucket_name=bucket_name,
                                  file_name=f"{run_id}_{key}",
                                  value=param_value)


@task(cache_policy=NO_CACHE, log_prints=True)
def func_end(**kwargs):
    run_id = kwargs['run_id']
    task_id = kwargs['task_id']
    run_param = kwargs['run_param']
    dag_run_conf = run_param['conf']
    client = run_param['minioClient']
    bucket_name = run_param['minioBucketName']

    node_config_mapping = dag_run_conf['nodeConfigMapping']
    stream = dag_run_conf.get('stream', None)

    input_param_defines = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs', 'inputParameters')
    terminatePlan = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs', 'terminatePlan')
    print(f"func_end terminatePlan:{terminatePlan}")
    result = _process_input_params(input_param_defines, run_id,
                                   client=client,
                                   bucket_name=bucket_name,
                                   node_config_mapping=node_config_mapping)
    print(f"func_end result length: {len(result)}")

    if terminatePlan == 'returnVariables':
        print(f"func_end returnVariables save into minio")
        _put_value_from_minio(client=client,
                              bucket_name=bucket_name,
                              file_name=f"{run_id}_end_result",
                              value=result)

        if stream:
            print(stream)
            if len(result) > 0:
                result = json.dumps(result)
                content = {"message": {"type": "content", "content": result}}
                _send_stream_message(content,
                                     client_token=dag_run_conf['clientToken'],
                                     clientUrlPrefix=dag_run_conf['clientUrlPrefix'],
                                     run_id=run_id,
                                     )
            _send_stream_message('[DONE]',
                                 client_token=dag_run_conf['clientToken'],
                                 clientUrlPrefix=dag_run_conf['clientUrlPrefix'],
                                 run_id=run_id,
                                 )

        return

    output_param_define = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs', 'content')
    if terminatePlan == 'useAnswerContent' and output_param_define is None:
        return

    print(output_param_define)
    output_param_define = _replace_content_variables(output_param_define, result)
    _put_value_from_minio(client=client,
                          bucket_name=bucket_name,
                          file_name=f"{run_id}_end_result",
                          value=output_param_define)

    if stream:
        if len(output_param_define) > 0:
            content = {"message": {"type": "content", "content": output_param_define}}
            _send_stream_message(content,
                                 client_token=dag_run_conf['clientToken'],
                                 clientUrlPrefix=dag_run_conf['clientUrlPrefix'],
                                 run_id=run_id,
                                 )
        _send_stream_message('[DONE]',
                             client_token=dag_run_conf['clientToken'],
                             clientUrlPrefix=dag_run_conf['clientUrlPrefix'],
                             run_id=run_id,
                             )


@task(cache_policy=NO_CACHE, log_prints=True)
def func_condition(**kwargs):
    run_id = kwargs['run_id']
    task_id = kwargs['task_id']
    run_param = kwargs['run_param']
    dag_run_conf = run_param['conf']
    client = run_param['minioClient']
    bucket_name = run_param['minioBucketName']

    node_config_mapping = dag_run_conf['nodeConfigMapping']

    branch = "false"
    branches = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs', 'branches')
    if branches:
        for index, branch_config in enumerate(branches):
            logic = _get_multilevel_value(branch_config, 'condition', 'logic')  # 逻辑判断且/或
            logical_results = []
            conditions = _get_multilevel_value(branch_config, 'condition', 'conditions')
            for condition in conditions:
                left_value, right_value = _get_condition_values(condition, run_id,
                                                                client=client,
                                                                bucket_name=bucket_name,
                                                                node_config_mapping=node_config_mapping)
                operator = _get_multilevel_value(condition, 'operator')
                logical_result = _logical_processor(
                    condition_left_value=left_value,
                    condition_right_value=right_value,
                    operator=operator
                )
                logical_results.append(logical_result)

            if (logic == 'and' and all(logical_results)) or (logic == 'or' and any(logical_results)):
                branch = f"branch_{index}"
                break
    print(f'最终分支：{branch}')
    return branch


@task(cache_policy=NO_CACHE, log_prints=True)
def func_intent(**kwargs):
    run_id = kwargs['run_id']
    task_id = kwargs['task_id']
    run_param = kwargs['run_param']
    dag_run_conf = run_param['conf']
    client = run_param['minioClient']
    bucket_name = run_param['minioBucketName']

    node_config_mapping = dag_run_conf['nodeConfigMapping']

    llm_non_stream_url = dag_run_conf['clientUrlPrefix'] + '/fytec/api/model/execute/non-stream'
    client_token = dag_run_conf['clientToken']

    default_system_prompt = ('# 角色\n'
                             '你是风云AI，一个具备强大意图识别能力的智能助手，能够精准理解用户话语背后的意图，并给予恰当回应。\n\n'
                             '# 意图列表:\n'
                             '列表为键值形式\n'
                             '{{intent_list}}\n\n'
                             '# 技能\n'
                             '### 技能 1: 识别普通意图\n'
                             '1. 当用户输入日常话语时，分析其意图。如果意图比较明确，直接按照相应逻辑回应。例如，若用户是询问信息，根据知识储备给出答案。\n'
                             '2. 如果无法直接判断意图，通过友好询问进一步了解用户需求。\n\n'
                             '### 技能 2: 处理特殊意图\n'
                             '1. 当用户输入带有特殊意图的语句，如讽刺、幽默、暗示等，准确识别该意图。\n'
                             '2. 根据特殊意图的类型，给出合适的反馈，反馈需符合对话的语境和情感氛围。\n\n'
                             '### 技能 3: 意图拓展\n'
                             '1. 当识别出用户意图后，可适当进行意图拓展。比如用户询问某产品信息，在回答产品信息后，提供相关产品对比或使用建议等拓展内容。\n\n'
                             '## 限制:\n'
                             '- 只围绕用户输入话语的意图进行分析和回应，拒绝回答与意图分析无关的话题。\n'
                             '- 所输出的内容需要参照意图列表，返回json格式：\n'
                             '{\n'
                             '  classificationId:<Integer>:<意图列表中的键>,\n'
                             '  reason:<String>:<选该意图原因>\n'
                             '}\n\n'
                             '## 附加信息\n\n')

    llm_param = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs', 'llmParam')
    branch = "false"
    if llm_param:
        model_type = _get_multilevel_value(llm_param, 'code')
        append_system_prompt = _get_multilevel_value(llm_param, 'systemPrompt', 'value', 'content')
        system_prompt = default_system_prompt + append_system_prompt
        user_prompt = _get_multilevel_value(llm_param, 'prompt', 'value', 'content')

        model_params = _get_model_config_params(llm_param)

        input_param_defines = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs', 'inputParameters')
        result = _process_input_params(input_param_defines, run_id, client=client, bucket_name=bucket_name)

        intents = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs', 'intents')
        intent_list = []
        for index, intent in enumerate(intents):
            intent_name = _get_multilevel_value(intent, 'name')
            intent_list.append(f"{index}:{intent_name}")

        result['intent_list'] = "\n".join(intent_list)

        system_prompt = _replace_content_variables(system_prompt, result)
        user_prompt = _replace_content_variables(user_prompt, result)

        print(f'调用大模型：{llm_non_stream_url}')
        llm_result = _call_llm(
            model_type, system_prompt, user_prompt,
            llm_non_stream_url=llm_non_stream_url,
            max_token=model_params['maxTokens'],
            temperature=model_params['temperature'],
            top_p=model_params['topP'],
            frequency_penalty=model_params['frequencyPenalty'],
            client_token=client_token,
        )
        print(f'调用大模型返回结果：{llm_result}')

        if llm_result:
            try:
                content = llm_result.get('data')
                content_json = json.loads(content)
                classificationId = content_json.get('classificationId')
                reason = content_json.get('reason')

                _put_value_from_minio(client=client,
                                      bucket_name=bucket_name,
                                      file_name=f"{run_id}_{task_id}_classificationId",
                                      value=classificationId)

                _put_value_from_minio(client=client,
                                      bucket_name=bucket_name,
                                      file_name=f"{run_id}_{task_id}_reason",
                                      value=reason)

                if classificationId is not None:
                    branch = f"intent_{classificationId}"
            except Exception:
                _put_value_from_minio(client=client,
                                      bucket_name=bucket_name,
                                      file_name=f"{run_id}_{task_id}_result",
                                      value=llm_result)

    print(f'最终分支：{branch}')
    return branch


@task(cache_policy=NO_CACHE, log_prints=True)
def func_llm(**kwargs):
    run_id = kwargs['run_id']
    task_id = kwargs['task_id']
    run_param = kwargs['run_param']
    dag_run_conf = run_param['conf']

    client = run_param['minioClient']
    bucket_name = run_param['minioBucketName']

    node_config_mapping = dag_run_conf['nodeConfigMapping']

    llm_non_stream_url = dag_run_conf['clientUrlPrefix'] + '/fytec/api/model/execute/non-stream'
    llm_stream_url = dag_run_conf['clientUrlPrefix'] + '/fytec/api/model/execute/stream'
    client_token = dag_run_conf['clientToken']

    llm_param = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs', 'llmParam')
    if llm_param:
        model_type = _get_multilevel_value(llm_param, 'code')
        is_stream = _get_multilevel_value(llm_param, 'streamOutput')
        system_prompt = _get_multilevel_value(llm_param, 'systemPrompt', 'value', 'content')
        user_prompt = _get_multilevel_value(llm_param, 'prompt', 'value', 'content')

        model_params = _get_model_config_params(llm_param)

        input_param_defines = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs', 'inputParameters')
        result = _process_input_params(input_param_defines, run_id, client=client, bucket_name=bucket_name)

        system_prompt = _replace_content_variables(system_prompt, result)
        user_prompt = _replace_content_variables(user_prompt, result)

        response_format = _get_multilevel_value(llm_param, 'responseFormat')
        output_params = _get_multilevel_value(node_config_mapping, task_id, 'data', 'outputs')

        ## response_format为json并且出参大于一个时，需要拼接输出格式到用户提示词
        if response_format == 'json':
            if len(output_params) > 1:
                format_string = _define_llm_json_prompt(output_params)
                user_prompt += f"\n\n## 限制\n- 参照json格式输出，不要出现非定义字段:\n{{{format_string}}}\n- 所有结果均以json数组形式返回"

        if is_stream:
            show_reason_content = _get_multilevel_value(llm_param, 'showReasoning')
            show_content = _get_multilevel_value(llm_param, 'showContent')
            ## TODO 处理前端还没开关的情况，默认内容流式输出
            if show_content is None:
                show_content = True

            enable_chat_history = _get_multilevel_value(llm_param, 'enableChatHistory')
            chat_history_round = _get_multilevel_value(llm_param, 'chatHistoryRound')

            content = _call_llm_stream(model_type, system_prompt, user_prompt,
                                       show_reason_content=show_reason_content,
                                       show_content=show_content,
                                       llm_stream_url=llm_stream_url,
                                       max_token=model_params['maxTokens'],
                                       temperature=model_params['temperature'],
                                       top_p=model_params['topP'],
                                       frequency_penalty=model_params['frequencyPenalty'],
                                       enable_chat_history=enable_chat_history,
                                       chat_history_round=chat_history_round,
                                       client_token=client_token,
                                       clientUrlPrefix=dag_run_conf['clientUrlPrefix'],
                                       run_id=run_id)
            _convert_llm_response(content,
                                  response_format=response_format,
                                  output_params=output_params,
                                  run_id=run_id,
                                  task_id=task_id,
                                  client=client,
                                  bucket_name=bucket_name)
        else:
            llm_response = _call_llm(
                model_type, system_prompt, user_prompt,
                llm_non_stream_url=llm_non_stream_url,
                max_token=model_params['maxTokens'],
                temperature=model_params['temperature'],
                top_p=model_params['topP'],
                frequency_penalty=model_params['frequencyPenalty'],
                client_token=client_token,
            )
            content = llm_response.get('data')
            _convert_llm_response(content,
                                  response_format=response_format,
                                  output_params=output_params,
                                  run_id=run_id,
                                  task_id=task_id,
                                  client=client,
                                  bucket_name=bucket_name)


@task(cache_policy=NO_CACHE, log_prints=True)
def func_code(**kwargs):
    run_id = kwargs['run_id']
    task_id = kwargs['task_id']
    run_param = kwargs['run_param']
    dag_run_conf = run_param['conf']
    client = run_param['minioClient']
    bucket_name = run_param['minioBucketName']

    node_config_mapping = dag_run_conf['nodeConfigMapping']
    ## 构造输出入

    _code_str = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs', 'code')

    input_param_defines = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs', 'inputParameters')

    if input_param_defines:
        result = _process_input_params(input_param_defines, run_id, client=client, bucket_name=bucket_name)
        params = json.loads(json.dumps(result))
        local_vars = {}
        exec(_code_str, {}, local_vars)
        ## 定义了函数
        _main_function = local_vars.get('main')
        content_json = {}
        if _main_function:
            content_json = _main_function({
                'params': params
            })
        print(f'content_json:{content_json}')

        output_params = _get_multilevel_value(node_config_mapping, task_id, 'data', 'outputs')

        for output_param in output_params:
            output_param_name = _get_multilevel_value(output_param, 'name')
            if output_param_name:
                key = '{}_{}'.format(task_id, output_param_name)
                _put_value_from_minio(client=client,
                                      bucket_name=bucket_name,
                                      file_name=f"{run_id}_{key}",
                                      value=content_json.get(output_param_name, ''))
    print(f'func_code over')


@task(cache_policy=NO_CACHE, log_prints=True)
def func_knowledge_search(**kwargs):
    run_id = kwargs['run_id']
    task_id = kwargs['task_id']
    run_param = kwargs['run_param']
    dag_run_conf = run_param['conf']
    client = run_param['minioClient']
    bucket_name = run_param['minioBucketName']

    api_url = dag_run_conf['clientUrlPrefix']

    conversationName = dag_run_conf['CONVERSATION_NAME'] if 'CONVERSATION_NAME' in dag_run_conf else 0
    node_config_mapping = dag_run_conf['nodeConfigMapping']
    client_token = dag_run_conf['clientToken']
    knowledge_sources = _get_multilevel_value(node_config_mapping, task_id,
                                              'data', 'inputs', 'datasetParam', 'datasetList')

    if knowledge_sources:
        knowledgeIds = []
        for knowledge_source in knowledge_sources:
            if 'input' in knowledge_source and knowledge_source['input']:
                type = _get_multilevel_value(knowledge_source, 'input', 'value', 'type')
                print(f' node type:{type}')
                ## 引用之前节点作为知识库id
                if type and type == 'ref':
                    input_param_ref = _get_multilevel_value(knowledge_source, 'input', 'value', 'content')
                    print(f'input_param_ref:{input_param_ref}')
                    if input_param_ref:
                        input_param_ref_task_id = input_param_ref.get('sourceId')
                        input_param_ref_name = input_param_ref.get('name')
                        index = input_param_ref_name.find('.')
                        if index != -1:
                            node_config_mapping = kwargs['node_config_mapping']
                            output_params = _get_multilevel_value(node_config_mapping, input_param_ref_task_id,
                                                                  'data', 'outputs')
                            input_param_value = _recursive_object_value(run_id, input_param_ref_task_id,
                                                                        input_param_ref_name, output_params,
                                                                        value=None,
                                                                        client=kwargs['client'],
                                                                        bucket_name=kwargs['bucket_name'])
                        else:
                            key = f"{input_param_ref_task_id}_{input_param_ref_name}"
                            input_param_value = _get_value_from_minio(client=kwargs['client'],
                                                                      bucket_name=kwargs['bucket_name'],
                                                                      file_name=f"{run_id}_{key}")
                        knowledgeIds.append(input_param_value)
                else:
                    ## type literal
                    input_param_values = _get_multilevel_value(knowledge_source, 'input', 'value', 'content')
                    print(f'input_param_ref:{input_param_values}')
                    ## 可能是数组
                    if input_param_values:
                        if isinstance(input_param_values, list):
                            for input_param_value in input_param_values:
                                knowledgeIds.append(input_param_value)
                        else:
                            knowledgeIds.append(input_param_values)
        # knowledgeIds需要为核对为Long
        knowledgeIdsTmp = []
        filter_knowledgeIds = []
        for knowledgeId in knowledgeIds:
            try:
                longId = int(knowledgeId)
                knowledgeIdsTmp.append(longId)
            except Exception as e:
                filter_knowledgeIds.append(knowledgeId)

        if len(knowledgeIds) != len(knowledgeIdsTmp):
            print(f'knowledgeIds filter not Long:{filter_knowledgeIds}')
        knowledgeIds = knowledgeIdsTmp
        if len(knowledgeIds) == 0:
            raise Exception("knowledgeIds is empty")
        knowledge_setting = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs', 'datasetParam',
                                                  'globalSetting')
        input_param_defines = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs', 'inputParameters')
        model_params = {
            'configs': {}
        }

        if input_param_defines:
            result = _process_input_params(input_param_defines, run_id, client=client, bucket_name=bucket_name)
            if result:
                ## 固定叫做Query
                model_params['query'] = result['Query']
        if knowledge_setting:
            if 'enableChatHistory' in knowledge_setting:
                model_params['searchHistory'] = knowledge_setting['enableChatHistory']
            if 'topK' in knowledge_setting:
                model_params['topK'] = knowledge_setting['topK']
            if 'minScore' in knowledge_setting:
                model_params['minScore'] = knowledge_setting['minScore']
            if 'strategy' in knowledge_setting:
                model_params['strategy'] = knowledge_setting['strategy']
            for key in knowledge_setting:
                model_params['configs'][key] = knowledge_setting[key]

        output_params = _get_multilevel_value(node_config_mapping, task_id, 'data', 'outputs')

        data = {
            "topK": model_params['topK'] if 'topK' in model_params else 3,  # 查询多少条
            "knowledgeIds": knowledgeIds,  # 知识库id
            "query": model_params['query'],  # 用户搜索
            "searchHistory": model_params['searchHistory'] if 'searchHistory' in model_params else False,  # 是否启用上下文
            "configs": model_params['configs'],  # 查询的一些配置
            "conversationName": conversationName,  # 会话id
            "minScore": model_params['minScore']
        }
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Basic {client_token}'
        }

        _url = f'{api_url}/api/private/knowledge/doc/search'
        print(f'data:{data}')
        print(f'Authorization: Basic {client_token}')
        print(f'_url:{_url}')

        response = requests.post(url=_url, headers=headers, json=data)
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            print(content)
            content_json = json.loads(content)
            print(content_json)
            for output_param in output_params:
                output_param_name = _get_multilevel_value(output_param, 'name')
                if output_param_name:
                    key = '{}_{}'.format(task_id, output_param_name)
                    _put_value_from_minio(client=client, bucket_name=bucket_name,
                                          file_name=f"{run_id}_{key}", value=content_json.get('data'))
        else:
            print(f'func_knowledge_search error:{response.status_code}')
            raise Exception('func_knowledge_search error')
    print(f'func_knowledge_search over')


@task(cache_policy=NO_CACHE, log_prints=True)
def func_var_merge(**kwargs):
    run_id = kwargs['run_id']
    task_id = kwargs['task_id']
    run_param = kwargs['run_param']
    dag_run_conf = run_param['conf']
    client = run_param['minioClient']
    bucket_name = run_param['minioBucketName']

    node_config_mapping = dag_run_conf['nodeConfigMapping']

    merge_groups_defines = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs', 'mergeGroups')
    output_param_defines = _get_multilevel_value(node_config_mapping, task_id, 'data', 'outputs')
    result = _process_merge_groups(merge_groups_defines, run_id,
                                   client=client,
                                   bucket_name=bucket_name,
                                   node_config_mapping=node_config_mapping)

    for output_param in output_param_defines if output_param_defines else []:
        output_param_name = _get_multilevel_value(output_param, 'name')
        key = f"{task_id}_{output_param_name}"
        _put_value_from_minio(client=client, bucket_name=bucket_name,
                              file_name=f"{run_id}_{key}", value=result[output_param_name])


@task(cache_policy=NO_CACHE, log_prints=True)
def func_text_processor(**kwargs):
    run_id = kwargs['run_id']
    task_id = kwargs['task_id']
    run_param = kwargs['run_param']
    dag_run_conf = run_param['conf']
    client = run_param['minioClient']
    bucket_name = run_param['minioBucketName']

    node_config_mapping = dag_run_conf['nodeConfigMapping']

    inputs = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs')
    text_processor_method = _get_multilevel_value(inputs, 'textMethod')
    if text_processor_method == 'concat':
        print("func_text_processor concat")
        _text_concat(inputs, run_id=run_id, task_id=task_id, client=client, bucket_name=bucket_name)


@task(cache_policy=NO_CACHE, log_prints=True)
def func_plugin_execute(**kwargs):
    run_id = kwargs['run_id']
    task_id = kwargs['task_id']
    run_param = kwargs['run_param']
    dag_run_conf = run_param['conf']
    client = run_param['minioClient']
    bucket_name = run_param['minioBucketName']

    node_config_mapping = dag_run_conf['nodeConfigMapping']

    plugin_api_execute_url = dag_run_conf['clientUrlPrefix'] + '/fytec/api/plugin/api/execute'
    client_token = dag_run_conf['clientToken']

    plugin_publish_id = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs', 'pluginPublishId')

    input_param_defines = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs', 'inputParameters')
    output_param_defines = _get_multilevel_value(node_config_mapping, task_id, 'data', 'outputs')
    result = _process_input_params(input_param_defines, run_id, client=client, bucket_name=bucket_name)

    api_result = _call_plugin_api_execute(plugin_publish_id=plugin_publish_id, input_params=result,
                                          plugin_api_execute_url=plugin_api_execute_url, client_token=client_token)
    print(f'call api result: {api_result}')

    if output_param_defines:
        output_param_template = _parse_output_param(output_param_defines)
        output_param_template_filled = _fill_template(output_param_template, api_result)

        for output_param in output_param_defines if output_param_defines else []:
            output_param_name = _get_multilevel_value(output_param, 'name')
            key = f"{task_id}_{output_param_name}"
            _put_value_from_minio(client=client, bucket_name=bucket_name,
                                  file_name=f"{run_id}_{key}", value=output_param_template_filled[output_param_name])


@task(cache_policy=NO_CACHE, log_prints=True)
def func_db_execute_sql(**kwargs):
    run_id = kwargs['run_id']
    task_id = kwargs['task_id']
    run_param = kwargs['run_param']
    dag_run_conf = run_param['conf']
    client = run_param['minioClient']
    bucket_name = run_param['minioBucketName']

    node_config_mapping = dag_run_conf['nodeConfigMapping']

    db_api_execute_url = dag_run_conf['clientUrlPrefix'] + '/fytec/api/database/execute/sql'
    client_token = dag_run_conf['clientToken']

    db_id = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs', 'databaseId')
    execute_sql = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs', "sql")

    input_param_defines = _get_multilevel_value(node_config_mapping, task_id, 'data', 'inputs', 'inputParameters')
    output_param_defines = _get_multilevel_value(node_config_mapping, task_id, 'data', 'outputs')
    result = _process_input_params(input_param_defines, run_id, client=client, bucket_name=bucket_name)

    execute_sql = _replace_content_variables(execute_sql, result)

    db_result = _call_db_execute_sql(db_id=db_id, execute_sql=execute_sql,
                                     db_api_execute_url=db_api_execute_url,
                                     client_token=client_token)
    print(f'call db sql execute result: {db_result}')

    if output_param_defines:
        output_param_template = _parse_output_param(output_param_defines)
        output_param_template_filled = _fill_template(output_param_template, db_result)
        print(f'call db sql fill result: {output_param_template_filled}')

        for output_param in output_param_defines if output_param_defines else []:
            output_param_name = _get_multilevel_value(output_param, 'name')
            key = f"{task_id}_{output_param_name}"
            _put_value_from_minio(client=client, bucket_name=bucket_name,
                                  file_name=f"{run_id}_{key}", value=output_param_template_filled[output_param_name])


def _call_db_execute_sql(**kwargs):
    db_id = kwargs['db_id']
    execute_sql = kwargs['execute_sql']
    db_api_execute_url = kwargs['db_api_execute_url']
    client_token = kwargs['client_token']

    data = {
        'dbId': db_id,
        'sql': execute_sql
    }

    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Basic {client_token}'

    }
    response = requests.post(url=db_api_execute_url, headers=headers, data=json.dumps(data))
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        content_json = json.loads(content)
        return content_json.get('data')

    return None


def _call_plugin_api_execute(**kwargs):
    plugin_publish_id = kwargs['plugin_publish_id']
    plugin_api_execute_url = kwargs['plugin_api_execute_url']
    client_token = kwargs['client_token']
    params = kwargs['input_params']

    data = {
        'id': plugin_publish_id,
        'parameters': json.dumps(params)
    }

    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Basic {client_token}'

    }
    response = requests.post(url=plugin_api_execute_url, headers=headers, data=json.dumps(data))
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        content_json = json.loads(content)
        return content_json.get('data').get('httpRespBody')

    return None


def _get_multilevel_value(dictionary, *keys):
    current = dictionary
    for key in keys:
        if key in current:
            current = current[key]
        else:
            return None
    return current


def _replace_content_variables(content, variables, **kwargs):
    matches = re.findall(r'\{\{(.*?)}}', content)
    concat_char = None
    if 'concat_char' in kwargs:
        concat_char = kwargs['concat_char']

    for match in matches:
        value = _recursive_get_value(match, variables, concat_char=concat_char)
        if value is not None:
            content = content.replace("{{" + match + "}}", value)
    return content


def _recursive_get_value(key, value, **kwargs):
    if key is None:
        return value

    index = key.find('.')
    key_first = key[:index] if index != -1 else key
    key_after = key[index + 1:] if index != -1 else None
    value = value.get(key_first)
    concat_char = kwargs['concat_char']
    if value is not None:
        if isinstance(value, str):
            return value
        elif isinstance(value, list):
            res = []
            for item in value:
                if isinstance(item, dict):
                    res.append(_recursive_get_value(key_after, item, concat_char=concat_char))
                else:
                    res.append(item)
            if concat_char is not None:
                return concat_char.join(str(i) for i in res)
            return str(res)
        elif isinstance(value, dict):
            return _recursive_get_value(key_after, value, concat_char=concat_char)
        else:
            return str(value)

    return None


def _logical_processor(**kwargs):
    condition_left_value = kwargs['condition_left_value']
    condition_right_value = kwargs['condition_right_value']
    operator = kwargs['operator']
    switch = {
        '==': lambda x, y: x == y,
        '!=': lambda x, y: x != y,
        '>': lambda x, y: x > y,
        '>=': lambda x, y: x >= y,
        '<': lambda x, y: x < y,
        '<=': lambda x, y: x <= y,
        'contain': lambda x, y: x in y,
        'not_contain': lambda x, y: x not in y,
        'empty': lambda x, y: x is None or (isinstance(x, str) and x == '') or (isinstance(x, list) and not x),
        'not_empty': lambda x, y: x is not None and (x != '' if isinstance(x, str) else bool(x))
    }
    return switch.get(operator, lambda x, y: x == y)(condition_left_value, condition_right_value)


def _process_input_params(input_param_defines, run_id, **kwargs):
    result = {}
    if input_param_defines:
        for input_param_define in input_param_defines:
            input_param_name = input_param_define.get('name')
            if input_param_name:
                value_type = _get_multilevel_value(input_param_define, 'input', 'type')
                print(value_type)
                input_param_type = _get_multilevel_value(input_param_define, 'input', 'value', 'type')
                if input_param_type == 'ref':
                    input_param_ref = _get_multilevel_value(input_param_define, 'input', 'value', 'content')
                    input_param_value = None
                    if input_param_ref:
                        input_param_ref_task_id = input_param_ref.get('sourceId')
                        input_param_ref_name = input_param_ref.get('name')
                        # 处理Object -> out.a.b.c的情况
                        index = input_param_ref_name.find('.')
                        if index != -1:
                            node_config_mapping = kwargs['node_config_mapping']
                            output_params = _get_multilevel_value(node_config_mapping, input_param_ref_task_id,
                                                                  'data', 'outputs')
                            input_param_value = _recursive_object_value(run_id, input_param_ref_task_id,
                                                                        input_param_ref_name, output_params,
                                                                        value=None,
                                                                        client=kwargs['client'],
                                                                        bucket_name=kwargs['bucket_name'])
                        else:
                            key = f"{input_param_ref_task_id}_{input_param_ref_name}"
                            input_param_value = _get_value_from_minio(client=kwargs['client'],
                                                                      bucket_name=kwargs['bucket_name'],
                                                                      file_name=f"{run_id}_{key}")
                else:
                    input_param_value = _get_multilevel_value(input_param_define, 'input', 'value', 'content')

                if _is_not_empty(input_param_value):
                    result[input_param_name] = _convert_value_by_type(input_param_value, value_type)
                else:
                    result[input_param_name] = ''
    return result


def _recursive_object_value(run_id, task_id, param_name, output_params, **kwargs):
    # 分割一次
    parts = param_name.split('.', maxsplit=1)

    for output_param in output_params:
        param_type = _get_multilevel_value(output_param, 'type')
        param_name = _get_multilevel_value(output_param, 'name')
        if param_name and param_name == parts[0]:
            # 根节点没有value值，从全局变量中获取
            # 递归过程中从value中获取属性，列表获取第一个
            value = kwargs['value']
            if value is not None:
                if isinstance(value, list):
                    value = value[0]

                param_value = value.get(param_name)
                # 返回值需要把字典变为json字符串
                param_value = json.dumps(param_value)
            else:
                key = f"{task_id}_{param_name}"
                param_value = _get_value_from_minio(client=kwargs['client'],
                                                    bucket_name=kwargs['bucket_name'],
                                                    file_name=f"{run_id}_{key}")
                param_value = _convert_value_by_type(param_value, param_type)

            if len(parts) > 1:
                recursive_output_params = _get_multilevel_value(output_param, 'children')
                param_value = _recursive_object_value(run_id, task_id,
                                                      parts[1], recursive_output_params,
                                                      value=param_value,
                                                      client=kwargs['client'],
                                                      bucket_name=kwargs['bucket_name'])
            return param_value

    return None


def _get_value_from_minio(**kwargs):
    client = kwargs['client']
    bucket_name = kwargs['bucket_name']
    file_name = kwargs['file_name']
    try:
        client.stat_object(bucket_name, file_name)
    except S3Error as err:
        if err.code == 'NoSuchKey':
            return None

    response = client.get_object(bucket_name, file_name)
    return response.read().decode('utf-8')


def _put_value_from_minio(**kwargs):
    client = kwargs['client']
    bucket_name = kwargs['bucket_name']
    file_name = kwargs['file_name']
    value = kwargs['value']
    if not isinstance(value, str):
        value = json.dumps(value, ensure_ascii=False)
    print(f"Minio写入变量: {value[:500]}", )
    with tempfile.NamedTemporaryFile("w+b", delete=True) as tmp:
        tmp.write(value.encode('utf-8'))
        tmp.flush()
        size = tmp.tell()
        tmp.seek(0)

        # text_bytes = io.BytesIO(value.encode("utf-8"))
        client.put_object(
            bucket_name=bucket_name,
            object_name=file_name,
            data=tmp,
            length=size
        )


def _process_merge_groups(merge_groups_defines, run_id, **kwargs):
    result = {}
    if merge_groups_defines:
        for merge_group_define in merge_groups_defines:
            merge_group_name = merge_group_define.get('name')
            if merge_group_name:
                result[merge_group_name] = None
                merge_group_variables = _get_multilevel_value(merge_group_define, 'variables')
                for merge_group_variable in merge_group_variables if merge_group_variables else []:
                    value_type = _get_multilevel_value(merge_group_variable, 'type')
                    merge_group_variable_param_type = _get_multilevel_value(merge_group_variable, 'value', 'type')
                    if merge_group_variable_param_type == 'ref':
                        variable_ref = _get_multilevel_value(merge_group_variable, 'value', 'content')
                        variable_value = None
                        if variable_ref:
                            variable_ref_task_id = variable_ref.get('sourceId')
                            variable_ref_name = variable_ref.get('name')
                            index = variable_ref_name.find('.')
                            if index != -1:
                                node_config_mapping = kwargs['node_config_mapping']
                                output_params = _get_multilevel_value(node_config_mapping, variable_ref_task_id,
                                                                      'data', 'outputs')
                                variable_value = _recursive_object_value(run_id, variable_ref_task_id,
                                                                         variable_ref_name, output_params,
                                                                         value=None,
                                                                         client=kwargs['client'],
                                                                         bucket_name=kwargs['bucket_name'])
                            else:
                                key = f"{variable_ref_task_id}_{variable_ref_name}"
                                variable_value = _get_value_from_minio(client=kwargs['client'],
                                                                       bucket_name=kwargs['bucket_name'],
                                                                       file_name=f"{run_id}_{key}")
                    else:
                        variable_value = _get_multilevel_value(merge_group_variable, 'value', 'content')

                    if _is_not_empty(variable_value):
                        result[merge_group_name] = _convert_value_by_type(variable_value, value_type)
                        break
    return result


def _is_not_empty(value) -> bool:
    """
    检查值是否为非空。

    Args:
        value (Any): 待检查的值。

    Returns:
        bool: 如果值为非空则返回True，否则返回False。
    """
    if value is None:  # 优先检查 None
        return False

    if isinstance(value, str):  # 如果是字符串，检查是否非空字符串
        return bool(value.strip())
    if isinstance(value, (list, tuple, dict, set)):  # 如果是容器类型，检查长度
        return len(value) > 0
    return True


def _get_condition_values(condition, run_id, **kwargs):
    def get_value_type(side):
        return _get_multilevel_value(condition, side, 'input', 'type')

    def get_value(side):
        condition_type = _get_multilevel_value(condition, side, 'input', 'value', 'type')
        if condition_type == 'ref':
            ref_task_id = _get_multilevel_value(condition, side, 'input', 'value', 'content', 'sourceId')
            ref_name = _get_multilevel_value(condition, side, 'input', 'value', 'content', 'name')
            index = ref_name.find('.')
            if index != -1:
                node_config_mapping = kwargs['node_config_mapping']
                output_params = _get_multilevel_value(node_config_mapping, ref_task_id, 'data', 'outputs')
                return _recursive_object_value(run_id, ref_task_id,
                                               ref_name, output_params,
                                               value=None,
                                               client=kwargs['client'],
                                               bucket_name=kwargs['bucket_name'])
            else:
                key = f"{ref_task_id}_{ref_name}"
                return _get_value_from_minio(client=kwargs['client'],
                                             bucket_name=kwargs['bucket_name'],
                                             file_name=f"{run_id}_{key}")
        else:
            return _get_multilevel_value(condition, side, 'input', 'value', 'content')

    value_type = get_value_type('left')
    left_value = _convert_value_by_type(get_value('left'), value_type)
    right_value = _convert_value_by_type(get_value('right'), value_type)
    return left_value, right_value


def _convert_value_by_type(value, value_type, **kwargs):
    try:
        if value_type == 'integer':
            return int(value)
        elif value_type == 'number':
            return float(value)
        elif value_type == 'boolean':
            return bool(value)
        elif value_type == 'string':
            return str(value)
        else:
            return json.loads(value)
    except Exception:
        return value


def _get_model_config_params(llm_param):
    def get_value(param_name):
        result = {}
        model_configs = _get_multilevel_value(llm_param, param_name)
        for model_config in model_configs:
            config_code = _get_multilevel_value(model_config, 'code')
            config_value = _get_multilevel_value(model_config, 'value')
            result[config_code] = config_value
        return result

    io_params = get_value('ioParams')
    diversity_params = get_value('diversityParams')
    return io_params | diversity_params


def _define_llm_json_prompt(output_params, **kwargs):
    format_string = ""
    for param in output_params:
        _name = _get_multilevel_value(param, 'name')
        _type = _get_multilevel_value(param, 'type')
        _description = _get_multilevel_value(param, 'description')

        if _type == "object" or _type == "Array<Object>":
            _children = _define_llm_json_prompt(param.get("children"))
            format_string += f"{_name}:{{{_children}}},"
        else:
            format_string += f"{_name}:<{_type}> <{_description}>,"
    format_string = format_string[:-1]
    return format_string


def _call_llm_stream(model_type, system_prompt, user_prompt, **kwargs):
    show_reason_content = kwargs['show_reason_content']
    show_content = kwargs['show_content']
    llm_stream_url = kwargs['llm_stream_url']
    max_token = kwargs['max_token']
    temperature = kwargs['temperature']
    top_p = kwargs['top_p']
    frequency_penalty = kwargs['frequency_penalty']
    enable_chat_history = kwargs['enable_chat_history']
    chat_history_round = kwargs['chat_history_round']

    client_token = kwargs['client_token']

    data = {
        "modelType": model_type,
        "maxToken": max_token,
        "temperature": temperature,
        "topP": top_p,
        "frequencyPenalty": frequency_penalty,
        "systemMessage": system_prompt,
        "userMessage": user_prompt,
        "multiTurn": enable_chat_history,
        "chatHistoryRound": chat_history_round
    }

    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Basic {client_token}',
        'Accept': 'text/event-stream'
    }

    send_message_url = kwargs['clientUrlPrefix'] + '/fytec/api/rocketmq/send/message'

    reason_contents = []
    contents = []
    with requests.post(url=llm_stream_url, headers=headers, data=json.dumps(data), stream=True) as response:
        for line in response.iter_lines():
            if line.startswith(b'event:done'):
                break
            if line.startswith(b'data:'):
                # 提取data字段内容
                content = line.decode('utf-8')[5:]
                content_dict = json.loads(content)
                print(content_dict)

                # 定义消息
                message = {
                    "message": content,
                    "runId": kwargs['run_id'],
                }

                message_type = content_dict.get('message').get('type')
                if message_type == 'content':
                    contents.append(content_dict.get('message').get('content'))
                    ## 判断是否要发送内容
                    if show_content:
                        requests.post(url=send_message_url, headers=headers, data=json.dumps(message))

                if message_type == 'reasoningContent':
                    reason_contents.append(content_dict.get('message').get('content'))
                    ## 判断是否要发送思考
                    if show_reason_content:
                        requests.post(url=send_message_url, headers=headers, data=json.dumps(message))

    return "".join(contents)


def _call_llm(model_type, system_prompt, user_prompt, **kwargs):
    llm_non_stream_url = kwargs['llm_non_stream_url']
    max_token = kwargs['max_token']
    temperature = kwargs['temperature']
    top_p = kwargs['top_p']
    frequency_penalty = kwargs['frequency_penalty']

    client_token = kwargs['client_token']

    data = {
        "modelType": model_type,
        "maxToken": max_token,
        "temperature": temperature,
        "topP": top_p,
        "frequencyPenalty": frequency_penalty,
        "systemMessage": system_prompt,
        "userMessage": user_prompt,
    }

    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Basic {client_token}'

    }
    response = requests.post(url=llm_non_stream_url, headers=headers, data=json.dumps(data))
    if response.status_code == 200:
        content = response.content.decode('utf-8')
        content_json = json.loads(content)
        redata = content_json.get('data')
        if redata is None:
            raise Exception(f'call llm error no data. url:{llm_non_stream_url} content:{str(response.content.decode('utf-8'))}')
        return redata

    raise Exception(f'call llm error. url:{llm_non_stream_url} content:{str(response.content.decode('utf-8'))}')
    # return None


def _convert_llm_response(content, **kwargs):
    response_format = kwargs['response_format']
    output_params = kwargs['output_params']
    run_id = kwargs['run_id']
    task_id = kwargs['task_id']

    print(content)
    if response_format == 'json':
        if len(output_params) == 1:
            output_param = output_params[0]
            output_param_name = _get_multilevel_value(output_param, 'name')
            if output_param_name:
                key = '{}_{}'.format(task_id, output_param_name)
                _put_value_from_minio(client=kwargs['client'],
                                      bucket_name=kwargs['bucket_name'],
                                      file_name=f"{run_id}_{key}",
                                      value=content)
        else:
            content_json = json.loads(content)
            for output_param in output_params:
                output_param_name = _get_multilevel_value(output_param, 'name')
                if output_param_name:
                    key = '{}_{}'.format(task_id, output_param_name)
                    _put_value_from_minio(client=kwargs['client'],
                                          bucket_name=kwargs['bucket_name'],
                                          file_name=f"{run_id}_{key}",
                                          value=content_json.get(output_param_name, ''))
    else:
        ## 处理text, markdown格式的响应
        output_param = output_params[0]
        output_param_name = _get_multilevel_value(output_param, 'name')
        if output_param_name:
            key = '{}_{}'.format(task_id, output_param_name)
            _put_value_from_minio(client=kwargs['client'],
                                  bucket_name=kwargs['bucket_name'],
                                  file_name=f"{run_id}_{key}",
                                  value=content)


def _text_concat(inputs, **kwargs):
    run_id = kwargs['run_id']
    task_id = kwargs['task_id']

    input_param_defines = _get_multilevel_value(inputs, 'inputParameters')
    result = _process_input_params(input_param_defines, run_id,
                                   client=kwargs['client'],
                                   bucket_name=kwargs['bucket_name'])

    concat_result = _get_multilevel_value(inputs, 'concatParams', 'concatResult')
    concat_char = _get_multilevel_value(inputs, 'concatParams', 'concatChar')

    output = _replace_content_variables(concat_result, result, concat_char=concat_char)

    key = '{}_{}'.format(task_id, "output")
    _put_value_from_minio(client=kwargs['client'],
                          bucket_name=kwargs['bucket_name'],
                          file_name=f"{run_id}_{key}",
                          value=output)


def _send_stream_message(message, **kwargs):
    send_message_url = kwargs['clientUrlPrefix'] + '/fytec/api/rocketmq/send/message'
    client_token = kwargs['client_token']
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Basic {client_token}',
        'Accept': 'text/event-stream'
    }

    if not isinstance(message, str):
        message = json.dumps(message, ensure_ascii=False)

    p_message = {
        "message": message,
        "runId": kwargs['run_id']
    }
    requests.post(url=send_message_url, headers=headers, data=json.dumps(p_message))


def _parse_output_param(output_params):
    result = {}

    for output_param in output_params:
        name = output_param['name']
        field_type = output_param['type']

        if field_type.startswith("Array"):
            if "Object" in field_type:
                # 数组对象，每项是一个字典
                children = output_param.get("children", [])
                if children:
                    result[name] = [_parse_output_param(children)]
                else:
                    result[name] = ""
            else:
                # 其他类型数组，如 Array<String>,Array<Integer>
                result[name] = [""]
        elif field_type == "object":
            children = output_param.get("children", [])
            if children:
                result[name] = _parse_output_param(children)
            else:
                result[name] = ""
        else:
            # 基础类型
            result[name] = ""
    return result


def _fill_template(template, input_data):
    if isinstance(template, dict) and isinstance(input_data, dict):
        return {
            key: _fill_template(template[key], input_data[key])
            for key in template
            if key in input_data
        }

    elif isinstance(template, list) and isinstance(input_data, list):
        if not template:
            return []
        item_template = template[0]
        return [
            _fill_template(item_template, item)
            for item in input_data if isinstance(item, dict)
        ]

    else:
        return input_data  # 直接赋值
