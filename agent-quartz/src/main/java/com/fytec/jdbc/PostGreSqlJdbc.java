package com.fytec.jdbc;

import lombok.extern.slf4j.Slf4j;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class PostGreSqlJdbc {

    public static List<Map<String,Object>> query(String url,
                                          String username,
                                          String password,
                                          String driver,
                                          String sql,
                                          List<String> columns,
                                          List<Object> conditionVals){
        PreparedStatement preparedStatement = null;
        ResultSet resultSet  = null;
        Connection connection = null;
        List<Map<String,Object>> result = null;
        try {
            Class.forName(driver);
            connection = DriverManager.getConnection(url, username, password);
            preparedStatement = connection.prepareStatement(sql);
            if(conditionVals != null && !conditionVals.isEmpty()) {
                for (int i = 0; i < conditionVals.size(); i++) {
                    preparedStatement.setObject(i + 1, conditionVals.get(i));
                }
            }
            resultSet = preparedStatement.executeQuery();
            result = new ArrayList<>();
            while (resultSet.next()) {
                if (columns != null && !columns.isEmpty()) {
                    Map<String,Object> map = new HashMap<>();
                    for (String column : columns) {
                        map.put(column, resultSet.getObject(column));
                    }
                    result.add(map);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            try {
                if(resultSet!=null){
                    resultSet.close();
                }
                if(preparedStatement!=null){
                    preparedStatement.close();
                }
                if(connection!=null){
                    connection.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return result;
    }

}
