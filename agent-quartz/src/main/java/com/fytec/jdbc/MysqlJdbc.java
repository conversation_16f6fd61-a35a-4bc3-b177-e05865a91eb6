package com.fytec.jdbc;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MysqlJdbc {
    public static List<Map<String, Object>> query(String url,
                                                  String username,
                                                  String password,
                                                  String driver,
                                                  String sql,
                                                  List<String> columns,
                                                  List<Object> conditionVals) {
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        Connection connection = null;
        List<Map<String, Object>> result = null;
        try {
            Class.forName(driver);
            connection = DriverManager.getConnection(url, username, password);
            preparedStatement = connection.prepareStatement(sql);
            if (conditionVals != null && !conditionVals.isEmpty()) {
                for (int i = 0; i < conditionVals.size(); i++) {
                    preparedStatement.setObject(i + 1, conditionVals.get(i));
                }
            }
            resultSet = preparedStatement.executeQuery();
            result = new ArrayList<>();
            while (resultSet.next()) {
                if (columns != null && !columns.isEmpty()) {
                    Map<String, Object> map = new HashMap<>();
                    for (String column : columns) {
                        map.put(column, resultSet.getObject(column));
                    }
                    result.add(map);
                }
            }
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (resultSet != null) {
                    resultSet.close();
                }
                if (preparedStatement != null) {
                    preparedStatement.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return result;
    }

    public static boolean exist(String stuExistSql, String stuId, String url, String username, String password, String driver) {
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        Connection connection = null;
        try {
            Class.forName(driver);
            connection = DriverManager.getConnection(url, username, password);
            preparedStatement = connection.prepareStatement(stuExistSql);
            preparedStatement.setObject(1, stuId);
            resultSet = preparedStatement.executeQuery();
            if (resultSet.next()) {
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (resultSet != null) {
                    resultSet.close();
                }
                if (preparedStatement != null) {
                    preparedStatement.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return false;
    }

    public static boolean batchUpsert(String url,
                                      String username,
                                      String password,
                                      String driver,
                                      String sql,
                                      List<List<Object>> vals) {
        PreparedStatement preparedStatement = null;
        Connection connection = null;
        try {
            Class.forName(driver);
            connection.setAutoCommit(false);
            connection = DriverManager.getConnection(url, username, password);
            preparedStatement = connection.prepareStatement(sql);
            if (vals != null && !vals.isEmpty()) {
                for (List<Object> val : vals) {
                    for (int i = 0; i < val.size(); i++) {
                        preparedStatement.setObject(i + 1, val.get(i));
                    }
                    preparedStatement.addBatch();
                }
            }
            int[] count = preparedStatement.executeBatch();
            connection.commit();
            if (count.length > 0) {
                return true;
            }
        } catch (Exception e) {
            if (connection != null) {
                try {
                    connection.rollback();
                } catch (SQLException ex) {
                    throw new RuntimeException(ex);
                }
            }
            e.printStackTrace();
        } finally {
            try {
                if (preparedStatement != null) {
                    preparedStatement.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return false;
    }

    public static boolean execute(String url,
                                  String username,
                                  String password,
                                  String driver,
                                  String sql,
                                  List<Object> vals) {
        PreparedStatement preparedStatement = null;
        Connection connection = null;
        try {
            Class.forName(driver);
            connection = DriverManager.getConnection(url, username, password);
            preparedStatement = connection.prepareStatement(sql);
            if (vals != null && !vals.isEmpty()) {
                for (int i = 0; i < vals.size(); i++) {
                    preparedStatement.setObject(i + 1, vals.get(i));
                }
            }
            int count = preparedStatement.executeUpdate();
            if (count > 0) {
                return true;
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (preparedStatement != null) {
                    preparedStatement.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return false;
    }


}
