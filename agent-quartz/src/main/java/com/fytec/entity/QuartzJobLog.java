package com.fytec.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("t_quartz_job_log")
public class QuartzJobLog {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    //任务Id
    private String jobId;

    //任务开始时间
    private LocalDateTime startTime;

    //任务结束时间时间
    private LocalDateTime endTime ;

    //任务状态（ERROR、SUCCESS）
    private String status;

    //错误信息
    private String errorMsg;

}