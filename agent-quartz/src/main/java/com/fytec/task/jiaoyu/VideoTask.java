package com.fytec.task.jiaoyu;

import com.alibaba.fastjson.JSON;
import com.fytec.jdbc.MysqlJdbc;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.scheduling.quartz.QuartzJobBean;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * view_file
 * （门户文件视频信息）
 */
@Slf4j
public class VideoTask extends QuartzJobBean {

    @Override
    protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        JobDetail jobDetail = jobExecutionContext.getJobDetail();
        JobKey key = jobDetail.getKey();
        JobDataMap jobDataMap = jobExecutionContext.getTrigger().getJobDataMap();
        boolean initialized = false;
        String columnsStr = "";
        if (jobDataMap != null) {
            initialized = jobDataMap.get("initialized").equals("true");
            columnsStr = (String) jobDataMap.get("columns");
        }
        log.info("任务执行开始：任务名称为:{}，执行参数为：{},执行时间为:{}", key.getName(), JSON.toJSONString(jobDataMap), LocalDateTime.now());
        execute(initialized,columnsStr);
        log.info("任务执行结束：任务名称为:{}，执行参数为：{},执行时间为:{}", key.getName(), JSON.toJSONString(jobDataMap), LocalDateTime.now());
    }

    public void execute(boolean initialized, String columnsStr) {
        String url="**************************************";
        String username="resipdsj";
        String password="dlmdsP2adnJ+0";
        String driver="com.mysql.cj.jdbc.Driver";
        //查询基本信息
        String sql = "select * from view_file " ;
        if(!initialized){
            sql = sql + " where updatedDt  >= CURDATE() - INTERVAL 3 DAY";
        }
        log.info("sql:{}",sql);
        List<String> columns = new ArrayList<>();
//        String columnsStr = "id,app_name,platform_code_name,remark,updated_dt,icon_url,service_url";
        columns = Arrays.asList(columnsStr.split(","));
        List<Map<String, Object>> basicData = MysqlJdbc.query(url, username, password, driver, sql, columns, null);

        //插入/更新基本信息
        String mastUrl="*************************************";
        String mastUsername="root";
        String mastPassword="Yjjy_fykj_7890";
        String mastDriver="com.mysql.cj.jdbc.Driver";
        //插入或更新到数据库
        if(basicData != null && !basicData.isEmpty()) {
            log.info("文件视频同步结果：日期：{},同步数据量：{}", LocalDateTime.now(),basicData.size());
            for (Map<String, Object> basicDatum : basicData) {
                String stuId = basicDatum.get("id")==null?"":basicDatum.get("id").toString();
                String existSql = "select * from view_file where id=?";
                StringBuilder executeSql = new StringBuilder();
                List<Object> values = new ArrayList<>();
                if(MysqlJdbc.exist(existSql,stuId,mastUrl,mastUsername,mastPassword,mastDriver)){
                    //更新字段
                    executeSql.append("update view_file set ");

                    for (String column : columns) {
                        executeSql.append(column).append("=?,");
                        values.add(basicDatum.get(column));
                    }
                    executeSql.deleteCharAt(executeSql.length()-1);
                    executeSql.append(" where id=?");
                    values.add(stuId);
                }else{
                    //插入数据
                    executeSql.append("insert into view_file (").append(columnsStr).append(") ");
                    executeSql.append(" values(");
                    columns.forEach(column->{
                        executeSql.append("?").append(",");
                    });
                    executeSql.deleteCharAt(executeSql.length()-1);
                    executeSql.append(")");
                    for (String column : columns) {
                        values.add(basicDatum.get(column));
                    }
                }
                boolean result = MysqlJdbc.execute(mastUrl,mastUsername,mastPassword,mastDriver,executeSql.toString(),values);
                if(!result){
                    log.error("文件视频ID：{},同步失败",stuId);
                }
            }
        }
    }

}
