package com.fytec.task.jiaoyu;

import com.alibaba.fastjson.JSON;
import com.fytec.jdbc.MysqlJdbc;
import com.fytec.jdbc.PostGreSqlJdbc;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.scheduling.quartz.QuartzJobBean;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * v_school_class
 * (班级基本信息)
 */
@Slf4j
public class ClassBasicTask extends QuartzJobBean {

    @Override
    protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        JobDetail jobDetail = jobExecutionContext.getJobDetail();
        JobKey key = jobDetail.getKey();
        JobDataMap jobDataMap = jobExecutionContext.getTrigger().getJobDataMap();
        boolean initialized = false;
        boolean deleted = false;
        String columnsStr = "";
        if (jobDataMap != null) {
            initialized = jobDataMap.get("initialized").equals("true");
            columnsStr = (String) jobDataMap.get("columns");
            deleted = jobDataMap.get("initialized").equals("true");

        }
        log.info("任务执行开始：任务名称为:{}，执行参数为：{},执行时间为:{}", key.getName(), JSON.toJSONString(jobDataMap), LocalDateTime.now());
        execute(initialized, columnsStr, deleted);
        log.info("任务执行结束：任务名称为:{}，执行参数为：{},执行时间为:{}", key.getName(), JSON.toJSONString(jobDataMap), LocalDateTime.now());


    }

    public void execute(boolean initialized, String columnsStr, boolean deleted) throws JobExecutionException {
        String url = "jdbc:postgresql://**********:5432/NEW_SIPDATATHEME";
        String username = "large_model_read_only";
        String password = "nS8Eom!6";
        String driver = "org.postgresql.Driver";
        //查询基本信息
        String sql = "select * from large_model_view.v_school_class  ";
        if (!initialized) {
            sql = sql + "WHERE update_time BETWEEN current_date - interval '3 day' AND current_date";
            ;
        }
        List<String> columns = new ArrayList<>();
//        String columnsStr = "school_id,school_name,school_jc,school_location,school_lb,school_xz,leader_xz,leader_sj," +
//                "student_num,teacher_num,class_num,update_time,data_status";
        columns = Arrays.asList(columnsStr.split(","));
        List<Map<String, Object>> basicData = PostGreSqlJdbc.query(url, username, password, driver, sql, columns, null);
        log.info("basicData nums:" + (basicData == null ? 0 : basicData.size()));
        //插入/更新基本信息
        String mastUrl = "*************************************";
        String mastUsername = "root";
        String mastPassword = "Yjjy_fykj_7890";
        String mastDriver = "com.mysql.cj.jdbc.Driver";
        //插入或更新到数据库
        if (basicData != null && !basicData.isEmpty()) {
            log.info("班级基本信息同步结果：日期：{},同步数据量：{}", LocalDateTime.now(), basicData.size());
            if (deleted) {
                log.info("先清空班级基本信息表");
                boolean result = MysqlJdbc.execute(mastUrl, mastUsername, mastPassword, mastDriver, "TRUNCATE TABLE v_school_class;", null);
                if (result) {
                    log.info("清空空班级基本信息表成功");
                }
            }
            for (Map<String, Object> basicDatum : basicData) {
                String stuId = basicDatum.get("uuid") == null ? "" : basicDatum.get("uuid").toString();
                String existSql = "select * from v_school_class where uuid=?";
                StringBuilder executeSql = new StringBuilder();
                List<Object> values = new ArrayList<>();
                if (MysqlJdbc.exist(existSql, stuId, mastUrl, mastUsername, mastPassword, mastDriver)) {
                    //更新字段
                    executeSql.append("update v_school_class set ");

                    for (String column : columns) {
                        executeSql.append(column).append("=?,");
                        values.add(basicDatum.get(column));
                    }
                    executeSql.deleteCharAt(executeSql.length() - 1);
                    executeSql.append(" where uuid=?");
                    values.add(stuId);
                } else {
                    //插入数据
                    executeSql.append("insert into v_school_class (").append(columnsStr).append(") ");
                    executeSql.append(" values(");
                    columns.forEach(column -> {
                        executeSql.append("?").append(",");
                    });
                    executeSql.deleteCharAt(executeSql.length() - 1);
                    executeSql.append(")");
                    for (String column : columns) {
                        values.add(basicDatum.get(column));
                    }
                }
                boolean result = MysqlJdbc.execute(mastUrl, mastUsername, mastPassword, mastDriver, executeSql.toString(), values);
                if (!result) {
                    log.error("班级ID：{},同步失败", stuId);
                }
            }
        }
    }

}
