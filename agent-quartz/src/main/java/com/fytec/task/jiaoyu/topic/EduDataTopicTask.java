package com.fytec.task.jiaoyu.topic;

import ch.qos.logback.core.util.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fytec.jdbc.MysqlJdbc;
import com.fytec.jdbc.PostGreSqlJdbc;
import com.mchange.v2.lang.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.scheduling.quartz.QuartzJobBean;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 主题数据同步，8个表都是统计，一起批量处理了
 */
@Slf4j
public class EduDataTopicTask extends QuartzJobBean {
    private static final String totalTables = """
            [
              {
                "name": "v_area_ydtj",
                "fields": "bdrq, ydyy, school_id, xxmc,xxlb, ydrs, type"
              },
              {
                "name": "v_teacher_jyrczb",
                "fields": "id, year, schoolid, xxmc, ggjszs, jxnlyjxcgzs, dyglrczs, jyjsrczs, jygjhzs, gxljszs, gzcjszs, qnggjszs, qnglrxzs, qyrcjlzs, rcczhjzs, xztdzhzs, jyrcjxzs, jyrcwdzs, gxzbhz, rxzbhz, total, updatedate, tablestatus"
              },
              {
                "name": "v_area_yjzs",
                "fields": "id, year, schooltypename, quotaname, parentquotaname, score, updatedate, semester"
              },
              {
                "name": "v_school_yjzs",
                "fields": "id, year, schoolid, xxmc, quotaname, parentquotaname, score, semester"
              },
              {
                "name": "v_hot_resource",
                "fields": "id, schoolid, xxmc, subjectname, title, year, month, hotcount, updatedate"
              },
              {
                "name": "v_area_development",
                "fields": "id, year, publishtime, title, content, url, image"
              },
              {
                "name": "v_area_bklq",
                "fields": "year, bk1plql, bk2plql"
              },
              {
                "name": "v_area_illness",
                "fields": "id,day, schoolid, xxmc, gmrs, sbrs, szkb, gzrs, otrs, fsrs, qtrs, xxlb"
              },
              {
                "name": "v_area_platform",
                "fields": "name, totalcount, studentcount, teachercount, othercount, type, year, datevalue"
              }
            ]
            """;


    @Override
    protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        JobDetail jobDetail = jobExecutionContext.getJobDetail();
        JobKey key = jobDetail.getKey();
        JobDataMap jobDataMap = jobExecutionContext.getTrigger().getJobDataMap();
        boolean initialized = true;
        String columnsStr = "";
        if (jobDataMap != null) {
            initialized = jobDataMap.get("initialized").equals("true");
            columnsStr = (String) jobDataMap.get("columns");
        }
        log.info("任务执行开始：任务名称为:{}，执行参数为：{},执行时间为:{}", key.getName(), JSON.toJSONString(jobDataMap), LocalDateTime.now());
        execute(initialized,columnsStr);
        log.info("任务执行结束：任务名称为:{}，执行参数为：{},执行时间为:{}", key.getName(), JSON.toJSONString(jobDataMap), LocalDateTime.now());

    }

    public void execute( boolean initialized,String columnsStr) throws JobExecutionException {
        JSONArray objects = JSON.parseArray(totalTables);
        for (Object object : objects) {
            JSONObject obj = (JSONObject)object;
            String tablename = obj.getString("name");
            String[] fields = obj.getString("fields").split(",");


            String url="**************************************************";
            String username="large_model_read_only";
            String password="nS8Eom!6";
            String driver="org.postgresql.Driver";
            //查询基本信息
            String sql = String.format("select * from large_model_view.%s  ", tablename);
//            if(!initialized){
//                sql = sql + "WHERE update_time BETWEEN current_date - interval '3 day' AND current_date";;
//            }
            List<String> columns = new ArrayList<>();
            for (String field : fields) {
                columns.add(field.strip());
            }
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < fields.length; i++) {
                if(i>=1)
                    sb.append(",");
                sb.append("`");
                sb.append(fields[i].strip());
                sb.append("`");

            }
            columnsStr =sb.toString();
            List<Map<String, Object>> basicData = PostGreSqlJdbc.query(url, username, password, driver, sql, columns, null);

            //插入/更新基本信息
            String mastUrl="*************************************";
            String mastUsername="root";
            String mastPassword="Yjjy_fykj_7890";
            String mastDriver="com.mysql.cj.jdbc.Driver";
            //插入或更新到数据库
            if(basicData != null && !basicData.isEmpty()) {
                log.info("主题数据 ，表：{},同步结果：日期：{},同步数据量：{}",tablename,
                        LocalDateTime.now(),basicData.size());
                // 清空表
                String delSql = "delete from " + tablename;
                MysqlJdbc.execute(mastUrl, mastUsername, mastPassword, mastDriver, delSql, null);
                boolean isfirst = true;
                for (Map<String, Object> basicDatum : basicData) {
                    StringBuilder executeSql = new StringBuilder();
                    List<Object> values = new ArrayList<>();
                    //插入数据
                    executeSql.append(String.format("insert into %s (", tablename)).append(columnsStr).append(") ");
                    executeSql.append(" values(");
                    columns.forEach(column->{
                        executeSql.append("?").append(",");
                    });
                    executeSql.deleteCharAt(executeSql.length()-1);
                    executeSql.append(")");
                    for (String column : columns) {
                        values.add(basicDatum.get(column));
                    }
                    if(isfirst){
                        isfirst = false;
                        log.info("first sql:" + executeSql.toString());
                        log.info("values len {},values:{}",values.size(), JSON.toJSONString(values));
                    }
                    boolean result = MysqlJdbc.execute(mastUrl,mastUsername,mastPassword,mastDriver,executeSql.toString(),values);
                    if(!result){
                        log.error("主题数据，表：{},同步失败",tablename);
                        break;
                    }
                }
            }
        }
    }
}
