package com.fytec.task.jiaoyu;

import com.alibaba.fastjson.JSON;
import com.fytec.jdbc.MysqlJdbc;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.scheduling.quartz.QuartzJobBean;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * v_area_teacher_analysis
 * （学段教师统计）
 */
@Slf4j
public class TeacherAnalysisTjTask extends QuartzJobBean {

    @Override
    protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        JobDetail jobDetail = jobExecutionContext.getJobDetail();
        JobKey key = jobDetail.getKey();
        JobDataMap jobDataMap = jobExecutionContext.getTrigger().getJobDataMap();
        String columnsStr = "";
        if (jobDataMap != null) {
            columnsStr = (String) jobDataMap.get("columns");
        }
        log.info("任务执行开始：任务名称为:{}，执行参数为：{},执行时间为:{}", key.getName(), JSON.toJSONString(jobDataMap), LocalDateTime.now());
        execute(columnsStr);
        log.info("任务执行结束：任务名称为:{}，执行参数为：{},执行时间为:{}", key.getName(), JSON.toJSONString(jobDataMap), LocalDateTime.now());
    }

    public void execute(String columnsStr) {
        String url="**************************************************";
        String username="large_model_read_only";
        String password="nS8Eom!6";
        String driver="org.postgresql.Driver";
        //查询基本信息
        String sql = "select * from large_model_view.v_area_teacher_analysis " ;
        log.info("sql:{}",sql);
        List<String> columns = new ArrayList<>();
//        String columnsStr = "xxlbname,xxjbzname,njssl,vjssl,njs_30,njs_30_35,njs_35_40,njs_40_45,njs_45_50,njs_50," +
//                "vjs_30,vjs_30_35,vjs_35_40,vjs_40_45,vjs_45_50,vjs_50,bksl,yjssl,bssl,yjjssl,gjjssl,zgjjssl,stjjssl," +
//                "qxkdtrsl,sxkdtrsl,sxkdtrsl_,qmjssl,smjssl";
        columns = Arrays.asList(columnsStr.split(","));
        List<Map<String, Object>> basicData = MysqlJdbc.query(url, username, password, driver, sql, columns, null);

        //插入/更新基本信息
        String mastUrl="*************************************";
        String mastUsername="root";
        String mastPassword="Yjjy_fykj_7890";
        String mastDriver="com.mysql.cj.jdbc.Driver";
        //插入或更新到数据库
        if(basicData != null && !basicData.isEmpty()) {
            log.info("学段教师统计统计同步结果：日期：{},同步数据量：{}", LocalDateTime.now(),basicData.size());
            String delSql = "delete from v_area_teacher_analysis";
            MysqlJdbc.execute(mastUrl, mastUsername, mastPassword, mastDriver, delSql, null);
            for (Map<String, Object> basicDatum : basicData) {
                List<Object> values = new ArrayList<>();
                //插入数据
                StringBuilder executeSql = new StringBuilder();
                executeSql.append("insert into v_area_teacher_analysis (").append(columnsStr).append(") ");;
                executeSql.append(" values(");
                columns.forEach(column->{
                    executeSql.append("?").append(",");
                });
                executeSql.deleteCharAt(executeSql.length()-1);
                executeSql.append(")");
                for (String column : columns) {
                    values.add(basicDatum.get(column));
                }
                boolean result = MysqlJdbc.execute(mastUrl,mastUsername,mastPassword,mastDriver, executeSql.toString(),values);
                if(!result){
                    log.error("学段教师统计统计：{},同步失败", LocalDateTime.now());
                }
            }
        }
    }

}
