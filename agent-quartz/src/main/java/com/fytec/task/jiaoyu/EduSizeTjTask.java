package com.fytec.task.jiaoyu;

import com.alibaba.fastjson.JSON;
import com.fytec.jdbc.MysqlJdbc;
import com.fytec.jdbc.PostGreSqlJdbc;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.scheduling.quartz.QuartzJobBean;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * v_area_edu_size
 * （教育规模）
 */
@Slf4j
public class EduSizeTjTask extends QuartzJobBean {

    @Override
    protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        JobDetail jobDetail = jobExecutionContext.getJobDetail();
        JobKey key = jobDetail.getKey();
        JobDataMap jobDataMap = jobExecutionContext.getTrigger().getJobDataMap();
        String columnsStr = "";
        if (jobDataMap != null) {
            columnsStr = (String) jobDataMap.get("columns");
        }
        log.info("任务执行开始：任务名称为:{}，执行参数为：{},执行时间为:{}", key.getName(), JSON.toJSONString(jobDataMap), LocalDateTime.now());
        execute(columnsStr);
        log.info("任务执行结束：任务名称为:{}，执行参数为：{},执行时间为:{}", key.getName(), JSON.toJSONString(jobDataMap), LocalDateTime.now());
    }

    public void execute(String columnsStr) {
        String url="jdbc:postgresql://**********:5432/NEW_SIPDATATHEME";
        String username="large_model_read_only";
        String password="nS8Eom!6";
        String driver="org.postgresql.Driver";
        //查询基本信息
        String sql = "select * from large_model_view.v_area_edu_size " ;
        log.info("sql:{}",sql);
        List<String> columns = new ArrayList<>();
//        String columnsStr = "year,xxsl,gbxxsl,mbxxsl,jzgsl,njzgsl,vjzgsl,xssl,nxssl,vxssl," +
//                "gbjss,mbjss,gbsybjzgs,gbqyhjzgs";
        columns = Arrays.asList(columnsStr.split(","));
        List<Map<String, Object>> basicData = PostGreSqlJdbc.query(url, username, password, driver, sql, columns, null);

        //插入/更新基本信息
        String mastUrl="jdbc:mysql://***********:3306/yj_data";
        String mastUsername="root";
        String mastPassword="Yjjy_fykj_7890";
        String mastDriver="com.mysql.cj.jdbc.Driver";
        //插入或更新到数据库
        if(basicData != null && !basicData.isEmpty()) {
            log.info("教育规模统计同步结果：日期：{},同步数据量：{}", LocalDateTime.now(),basicData.size());
            String delSql = "delete from v_area_edu_size";
            MysqlJdbc.execute(mastUrl, mastUsername, mastPassword, mastDriver, delSql, null);
            for (Map<String, Object> basicDatum : basicData) {
                List<Object> values = new ArrayList<>();
                //插入数据
                StringBuilder executeSql = new StringBuilder();
                executeSql.append("insert into v_area_edu_size (").append(columnsStr).append(") ");;
                executeSql.append(" values(");
                columns.forEach(column->{
                    executeSql.append("?").append(",");
                });
                executeSql.deleteCharAt(executeSql.length()-1);
                executeSql.append(")");

                for (String column : columns) {
                    values.add(basicDatum.get(column));
                }
                boolean result = MysqlJdbc.execute(mastUrl,mastUsername,mastPassword,mastDriver, executeSql.toString(),values);
                if(!result){
                    log.error("教育规模统计：{},同步失败", LocalDateTime.now());
                }
            }
        }
    }

}
