package com.fytec.task.jiaoyu;

import com.alibaba.fastjson.JSON;
import com.fytec.jdbc.MysqlJdbc;
import com.fytec.jdbc.PostGreSqlJdbc;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.scheduling.quartz.QuartzJobBean;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * v_student_reward
 * （学生荣誉）
 */
@Slf4j
public class StudentRewardTask extends QuartzJobBean {

    @Override
    protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        JobDetail jobDetail = jobExecutionContext.getJobDetail();
        JobKey key = jobDetail.getKey();
        JobDataMap jobDataMap = jobExecutionContext.getTrigger().getJobDataMap();
        boolean initialized = false;
        String columnsStr = "";
        if (jobDataMap != null) {
            initialized = jobDataMap.get("initialized").equals("true");
            columnsStr = (String) jobDataMap.get("columns");
        }
        log.info("任务执行开始：任务名称为:{}，执行参数为：{},执行时间为:{}", key.getName(), JSON.toJSONString(jobDataMap), LocalDateTime.now());
        execute(initialized,columnsStr);
        log.info("任务执行结束：任务名称为:{}，执行参数为：{},执行时间为:{}", key.getName(), JSON.toJSONString(jobDataMap), LocalDateTime.now());



    }

    public void execute(boolean initialized, String columnsStr) {
        String url="**************************************************";
        String username="large_model_read_only";
        String password="nS8Eom!6";
        String driver="org.postgresql.Driver";

        //查询学生荣誉
        String rewardSql = "select * from large_model_view.v_student_reward ";
        if(!initialized){
            rewardSql = rewardSql + "WHERE update_time BETWEEN current_date - interval '3 day' AND current_date";
        }
        List<String> rewardColumns = new ArrayList<>();
//        String columnsStr = "uuid,xm,student_id,rymc,level_,sysj,bfdw,update_time";
        rewardColumns = Arrays.asList(columnsStr.split(","));
        List<Map<String, Object>> rewardData = PostGreSqlJdbc.query(url, username, password, driver, rewardSql, rewardColumns, null);


        String mastUrl="*************************************";
        String mastUsername="root";
        String mastPassword="Yjjy_fykj_7890";
        String mastDriver="com.mysql.cj.jdbc.Driver";
        //插入或更新到数据库
        if(rewardData != null && !rewardData.isEmpty()) {
            log.info("学生荣誉同步结果：日期：{},同步数据量：{}", LocalDateTime.now(),rewardData.size());
            for (Map<String, Object> rewardDatum : rewardData) {
                String stuId = rewardDatum.get("uuid")==null?"":rewardDatum.get("uuid").toString();
                String stuExistSql = "select * from v_student_reward where uuid=?";
                StringBuilder stuBasicSql = new StringBuilder();
                List<Object> values = new ArrayList<>();
                if(MysqlJdbc.exist(stuExistSql,stuId,mastUrl,mastUsername,mastPassword,mastDriver)){
                    //更新字段
                    stuBasicSql.append("update v_student_reward set ");
                    for (String rewardColumn : rewardColumns) {
                        stuBasicSql.append(rewardColumn).append("=?,");
                        values.add(rewardDatum.get(rewardColumn));
                    }
                    stuBasicSql.deleteCharAt(stuBasicSql.length()-1);
                    stuBasicSql.append(" where uuid=?");
                    values.add(stuId);
                }else{
                    //插入数据
                    stuBasicSql.append("insert into v_student_reward (").append(columnsStr).append(")");
                    stuBasicSql.append(" values(");
                    rewardColumns.forEach(column->{
                        stuBasicSql.append("?").append(",");
                    });
                    stuBasicSql.deleteCharAt(stuBasicSql.length()-1);
                    stuBasicSql.append(")");
                    for (String rewardColumn : rewardColumns) {
                        values.add(rewardDatum.get(rewardColumn));
                    }
                }
                boolean result = MysqlJdbc.execute(mastUrl,mastUsername,mastPassword,mastDriver,stuBasicSql.toString(),values);
                if(!result){
                    log.error("学生荣誉ID：{},同步失败",stuId);
                }
            }
        }
    }

}
