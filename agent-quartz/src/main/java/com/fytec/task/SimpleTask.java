package com.fytec.task;

import com.alibaba.fastjson.JSON;
import com.fytec.entity.QuartzJobLog;
import com.fytec.mapper.QuartzJobLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.UUID;

@Slf4j
@Component
public class SimpleTask extends QuartzJobBean {

    @Autowired
    private QuartzJobLogMapper quartzJobLogMapper;


    @Override
    protected void executeInternal(JobExecutionContext jobExecutionContext){
        QuartzJobLog quartzJobLog = new QuartzJobLog();
        JobDetail jobDetail = jobExecutionContext.getJobDetail();
        JobKey key = jobDetail.getKey();
        quartzJobLog.setJobId(key.getName());
        quartzJobLog.setStartTime(LocalDateTime.now());
        try {
            log.info("任务名称为:{}，执行参数为：{},执行时间为:{}", key.getName(),  JSON.toJSONString(jobExecutionContext.getTrigger().getJobDataMap()), LocalDateTime.now());
            System.out.println("=======================添加数据===================================");
            quartzJobLog.setStatus("SUCCESS");
            quartzJobLog.setEndTime(LocalDateTime.now());
        }catch (Exception e) {
            log.error(e.getMessage());
            quartzJobLog.setStatus("ERROR");
            quartzJobLog.setErrorMsg(e.getMessage());
        }
        quartzJobLogMapper.insert(quartzJobLog);
    }

}
